<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        // Remove web-specific middleware for API-only application
        $middleware->remove([
            \Illuminate\Foundation\Http\Middleware\VerifyCsrfToken::class,
            \Illuminate\Cookie\Middleware\EncryptCookies::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
        ]);

        // API-specific middleware
        $apiMiddleware = [
            \App\Http\Middleware\LocaleMiddleware::class,
            \App\Http\Middleware\RequestLoggingMiddleware::class,
            \App\Http\Middleware\ApiMiddleware::class,
            \Laravel\Sanctum\Http\Middleware\EnsureFrontendRequestsAreStateful::class,
        ];

        // Only add SQL logging middleware in non-testing environments
        // Check if we're running under PHPUnit or if APP_ENV is set to testing
        $isTestingEnvironment = (defined('PHPUNIT_COMPOSER_INSTALL') || defined('__PHPUNIT_PHAR__')) ||
                               ($_ENV['APP_ENV'] ?? $_SERVER['APP_ENV'] ?? null) === 'testing';

        if (!$isTestingEnvironment) {
            array_unshift($apiMiddleware, \App\Http\Middleware\LogSqlQueries::class);
        }

        $middleware->api(prepend: $apiMiddleware);

        // Register middleware aliases
        $middleware->alias([
            'role' => \App\Http\Middleware\RoleMiddleware::class,
            'admin' => \App\Http\Middleware\AdminAccessMiddleware::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        // Handle API exceptions with standardized JSON responses using ApiExceptionService

        // Helper function to set locale from request
        $setLocaleFromRequest = function ($request) {
            $locale = \App\Http\Middleware\LocaleMiddleware::getDefaultLocale();
            if ($request->hasHeader('X-Locale')) {
                $customLocale = $request->header('X-Locale');
                if (in_array($customLocale, \App\Http\Middleware\LocaleMiddleware::getSupportedLocales())) {
                    $locale = $customLocale;
                }
            } elseif ($request->hasHeader('Accept-Language')) {
                $acceptLanguage = $request->header('Accept-Language');
                $languages = explode(',', $acceptLanguage);
                foreach ($languages as $language) {
                    $lang = trim(explode(';', $language)[0]);
                    $primaryLang = explode('-', $lang)[0];
                    if (in_array($primaryLang, \App\Http\Middleware\LocaleMiddleware::getSupportedLocales())) {
                        $locale = $primaryLang;
                        break;
                    }
                }
            }
            app()->setLocale($locale);
        };

        // Validation errors
        $exceptions->render(function (\Illuminate\Validation\ValidationException $e, $request) use ($setLocaleFromRequest) {
            $setLocaleFromRequest($request);
            return \App\Services\ApiExceptionService::validationError($e->errors(), __('errors.validation'));
        });

        // Authentication errors
        $exceptions->render(function (\Illuminate\Auth\AuthenticationException $e, $request) use ($setLocaleFromRequest) {
            $setLocaleFromRequest($request);
            return \App\Services\ApiExceptionService::authenticationError(__('errors.authentication'));
        });

        // Authorization errors
        $exceptions->render(function (\Illuminate\Auth\Access\AuthorizationException $e, $request) use ($setLocaleFromRequest) {
            $setLocaleFromRequest($request);
            return \App\Services\ApiExceptionService::authorizationError(__('errors.authorization'));
        });

        // Access denied errors (403)
        $exceptions->render(function (\Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException $e) {
            return \App\Services\ApiExceptionService::authorizationError(__('errors.authorization'));
        });

        // Permission errors (Spatie)
        $exceptions->render(function (\Spatie\Permission\Exceptions\UnauthorizedException $e) {
            return \App\Services\ApiExceptionService::permissionError(__('errors.permission'));
        });

        // Not found errors
        $exceptions->render(function (\Symfony\Component\HttpKernel\Exception\NotFoundHttpException $e, $request) use ($setLocaleFromRequest) {
            $setLocaleFromRequest($request);
            return \App\Services\ApiExceptionService::notFoundError(__('errors.not_found'));
        });

        // Method not allowed errors
        $exceptions->render(function (\Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException $e) {
            return \App\Services\ApiExceptionService::methodNotAllowedError(__('errors.method_not_allowed'));
        });

        // Throttle errors
        $exceptions->render(function (\Illuminate\Http\Exceptions\ThrottleRequestsException $e) {
            return \App\Services\ApiExceptionService::throttleError(__('errors.throttle'));
        });

        // General HTTP exceptions
        $exceptions->render(function (\Symfony\Component\HttpKernel\Exception\HttpException $e) {
            return \App\Services\ApiExceptionService::httpError($e->getStatusCode());
        });

        // Handle all other exceptions
        $exceptions->render(function (\Throwable $e, $request) {
            // Log the exception for debugging
            \App\Services\ApiExceptionService::logException($e);

            // Only show detailed error information in debug mode
            if (config('app.debug')) {
                return null; // Let Laravel handle it with full details
            }

            // In production, return a generic error response
            return \App\Services\ApiExceptionService::genericError();
        });
    })->create();
