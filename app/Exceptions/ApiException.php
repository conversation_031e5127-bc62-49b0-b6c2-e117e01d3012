<?php

declare(strict_types=1);

namespace App\Exceptions;

use Exception;
use Illuminate\Http\JsonResponse;

/**
 * Custom API Exception
 * 
 * Provides standardized error responses for API endpoints
 */
final class ApiException extends Exception
{
    private mixed $errors;
    private int $statusCode;

    public function __construct(
        string $message = 'An error occurred',
        mixed $errors = null,
        int $statusCode = 400,
        ?Exception $previous = null
    ) {
        parent::__construct($message, $statusCode, $previous);
        $this->errors = $errors;
        $this->statusCode = $statusCode;
    }

    /**
     * Render the exception as an HTTP response
     */
    public function render(): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => $this->getMessage(),
            'errors' => $this->errors,
            'timestamp' => now()->toISOString(),
        ], $this->statusCode);
    }

    /**
     * Create a validation error exception
     */
    public static function validation(array $errors, string $message = 'Validation failed'): self
    {
        return new self($message, $errors, 422);
    }

    /**
     * Create a not found exception
     */
    public static function notFound(string $message = 'Resource not found'): self
    {
        return new self($message, null, 404);
    }

    /**
     * Create an unauthorized exception
     */
    public static function unauthorized(string $message = 'Unauthorized'): self
    {
        return new self($message, null, 401);
    }

    /**
     * Create a forbidden exception
     */
    public static function forbidden(string $message = 'Forbidden'): self
    {
        return new self($message, null, 403);
    }

    /**
     * Create a server error exception
     */
    public static function serverError(string $message = 'Internal server error'): self
    {
        return new self($message, null, 500);
    }
}
