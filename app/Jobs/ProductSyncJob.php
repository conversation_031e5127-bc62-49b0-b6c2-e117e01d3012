<?php

declare(strict_types=1);

namespace App\Jobs;

use App\Contracts\ProductSyncServiceInterface;
use App\Models\SyncLog;
use App\Services\SyncProgressService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

final class ProductSyncJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $timeout = 3600; // 1 hour timeout
    public int $tries = 1; // No automatic retries for sync jobs
    public bool $failOnTimeout = true;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public readonly array $config,
        public readonly string $batchId,
        public readonly bool $isRetry = false,
        public readonly ?string $originalBatchId = null
    ) {
        $this->onQueue('sync');
    }

    /**
     * Execute the job.
     */
    public function handle(
        ProductSyncServiceInterface $syncService,
        SyncProgressService $progressService
    ): void {
        $jobId = (string) $this->job->getJobId();
        
        Log::info('Product sync job started', [
            'job_id' => $jobId,
            'batch_id' => $this->batchId,
            'is_retry' => $this->isRetry,
            'config' => $this->config,
        ]);

        try {
            // Initialize progress tracking
            $progressService->initializeProgress($jobId, $this->batchId);

            // Create progress callback that updates cache
            $progressCallback = function($processedChunks, $totalChunks, $elapsedTime) use ($progressService, $jobId) {
                $percentage = round(($processedChunks / $totalChunks) * 100, 1);
                $progressData = [
                    'processed_chunks' => $processedChunks,
                    'total_chunks' => $totalChunks,
                    'percentage' => $percentage,
                    'elapsed_time' => $elapsedTime,
                    'status' => 'processing',
                    'updated_at' => now()->toISOString(),
                ];

                $progressService->updateProgress($jobId, $progressData);

                Log::info("Sync progress update", [
                    'job_id' => $jobId,
                    'batch_id' => $this->batchId,
                    'progress' => $progressData,
                ]);
            };

            // Execute the sync
            if ($this->isRetry && $this->originalBatchId) {
                $syncLog = $syncService->reSyncInQueue($this->originalBatchId, $this->config, $progressCallback);
            } else {
                $syncLog = $syncService->syncInQueue($this->config, $this->batchId, $progressCallback);
            }

            // Mark progress as completed
            $progressService->markCompleted($jobId, [
                'sync_log_id' => $syncLog->id,
                'batch_id' => $syncLog->batch_id,
                'status' => $syncLog->status,
                'total_records' => $syncLog->total_records,
                'success_records' => $syncLog->success_records,
                'failed_records' => $syncLog->failed_records,
                'progress_percentage' => $syncLog->progress_percentage,
            ]);

            Log::info('Product sync job completed successfully', [
                'job_id' => $jobId,
                'batch_id' => $this->batchId,
                'sync_log_id' => $syncLog->id,
                'status' => $syncLog->status,
            ]);

        } catch (Exception $e) {
            Log::error('Product sync job failed', [
                'job_id' => $jobId,
                'batch_id' => $this->batchId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // Mark progress as failed
            $progressService->markFailed($jobId, [
                'error' => $e->getMessage(),
                'failed_at' => now()->toISOString(),
            ]);

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(Exception $exception): void
    {
        $jobId = $this->job?->getJobId();
        
        Log::error('Product sync job failed permanently', [
            'job_id' => $jobId,
            'batch_id' => $this->batchId,
            'exception' => $exception->getMessage(),
        ]);

        // Update sync log status if it exists
        $syncLog = SyncLog::where('batch_id', $this->batchId)->first();
        if ($syncLog) {
            $syncLog->update([
                'status' => 'failed',
                'completed_at' => now(),
                'error_message' => $exception->getMessage(),
            ]);
        }
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        return [
            'sync',
            'product-sync',
            "batch:{$this->batchId}",
            $this->isRetry ? 'retry' : 'initial',
        ];
    }
}
