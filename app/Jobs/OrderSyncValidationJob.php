<?php

declare(strict_types=1);

namespace App\Jobs;

use App\Models\SyncLog;
use App\Services\OrderSyncValidationService;
use App\Services\SyncProgressService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

final class OrderSyncValidationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $timeout = 3600; // 1 hour timeout for validation
    public int $tries = 1; // No automatic retries for validation jobs
    public bool $failOnTimeout = true;
    public int $maxExceptions = 1; // Fail after 1 exception

    /**
     * Create a new job instance.
     */
    public function __construct(
        public readonly int $syncLogId,
        public readonly string $batchId,
        public readonly string $originalJobId
    ) {
        $this->onQueue('validation');
    }

    /**
     * Execute the job.
     */
    public function handle(
        OrderSyncValidationService $validationService,
        SyncProgressService $progressService
    ): void {
        $jobId = (string) $this->job->getJobId();

        Log::info('Order sync validation job started', [
            'job_id' => $jobId,
            'sync_log_id' => $this->syncLogId,
            'batch_id' => $this->batchId,
            'original_job_id' => $this->originalJobId,
        ]);

        try {
            // Find the sync log
            $syncLog = SyncLog::findOrFail($this->syncLogId);

            // Update progress to indicate validation is starting
            $progressService->markValidationStarted($this->originalJobId, [
                'validation_job_id' => $jobId,
                'validation_started_at' => now()->toISOString(),
            ]);

            // Execute validation
            $validationResults = $validationService->validateSync($syncLog);

            // Determine overall validation status
            $overallStatus = $this->determineOverallStatus($validationResults);

            // Save validation results to sync_logs table
            $syncLog->update([
                'validation_results' => $validationResults,
            ]);

            if ($overallStatus === 'passed') {
                // All validations passed
                $progressService->markValidationCompleted($this->originalJobId, [
                    'validation_results' => $validationResults,
                    'validation_completed_at' => now()->toISOString(),
                ]);

                Log::info('Order sync validation completed successfully', [
                    'job_id' => $jobId,
                    'sync_log_id' => $this->syncLogId,
                    'batch_id' => $this->batchId,
                    'validation_status' => 'passed',
                ]);
            } else {
                // Some validations failed
                $errorMessage = $this->buildErrorMessage($validationResults);

                $progressService->markValidationFailed($this->originalJobId, [
                    'validation_results' => $validationResults,
                    'error_message' => $errorMessage,
                    'validation_failed_at' => now()->toISOString(),
                ]);

                Log::warning('Order sync validation failed', [
                    'job_id' => $jobId,
                    'sync_log_id' => $this->syncLogId,
                    'batch_id' => $this->batchId,
                    'validation_status' => 'failed',
                    'error_message' => $errorMessage,
                ]);
            }

        } catch (Exception $exception) {
            // Save error validation results to sync_logs table
            $errorValidationResults = [
                'sync_log_id' => $this->syncLogId,
                'batch_id' => $this->batchId,
                'validation_started_at' => now(),
                'overall_status' => 'error',
                'error_message' => $exception->getMessage(),
                'validation_completed_at' => now(),
            ];

            $syncLog->update([
                'validation_results' => $errorValidationResults,
            ]);

            // Mark validation as failed due to exception
            $progressService->markValidationFailed($this->originalJobId, [
                'error_message' => 'Validation job failed: ' . $exception->getMessage(),
                'error_trace' => $exception->getTraceAsString(),
                'validation_failed_at' => now()->toISOString(),
            ]);

            Log::error('Order sync validation job failed', [
                'job_id' => $jobId,
                'sync_log_id' => $this->syncLogId,
                'batch_id' => $this->batchId,
                'exception' => $exception->getMessage(),
                'trace' => $exception->getTraceAsString(),
            ]);

            throw $exception;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(Exception $exception): void
    {
        $jobId = $this->job?->getJobId();
        
        Log::error('Order sync validation job failed permanently', [
            'job_id' => $jobId,
            'sync_log_id' => $this->syncLogId,
            'batch_id' => $this->batchId,
            'exception' => $exception->getMessage(),
        ]);

        // Update progress service to reflect permanent failure
        $progressService = app(SyncProgressService::class);
        $progressService->markValidationFailed($this->originalJobId, [
            'error_message' => 'Validation job failed permanently: ' . $exception->getMessage(),
            'validation_failed_at' => now()->toISOString(),
        ]);
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        return [
            'validation',
            'order-sync-validation',
            "batch:{$this->batchId}",
            "sync-log:{$this->syncLogId}",
        ];
    }

    /**
     * Determine overall validation status from results.
     */
    private function determineOverallStatus(array $validationResults): string
    {
        $overallStatus = $validationResults['overall_status'] ?? 'failed';
        
        // Check if there are any issues
        if (!empty($validationResults['issues'])) {
            return 'failed';
        }

        // Check individual validation categories
        $categories = ['data_integrity', 'sampling_validation', 'business_logic', 'relationship_consistency'];
        
        foreach ($categories as $category) {
            if (isset($validationResults[$category]['status']) && $validationResults[$category]['status'] !== 'passed') {
                return 'failed';
            }
        }

        return $overallStatus === 'passed' ? 'passed' : 'failed';
    }

    /**
     * Build error message from validation results.
     */
    private function buildErrorMessage(array $validationResults): string
    {
        $errors = [];

        // Add general issues
        if (!empty($validationResults['issues'])) {
            foreach ($validationResults['issues'] as $issue) {
                $errors[] = $issue;
            }
        }

        // Add category-specific errors
        $categories = [
            'data_integrity' => 'Data Integrity',
            'sampling_validation' => 'Sampling Validation',
            'business_logic' => 'Business Logic',
            'relationship_consistency' => 'Relationship Consistency'
        ];

        foreach ($categories as $key => $label) {
            if (isset($validationResults[$key]['status']) && $validationResults[$key]['status'] !== 'passed') {
                $categoryErrors = $validationResults[$key]['errors'] ?? [];
                foreach ($categoryErrors as $error) {
                    $errors[] = "{$label}: {$error}";
                }
            }
        }

        return empty($errors) ? 'Validation failed with unknown errors' : implode('; ', $errors);
    }
}
