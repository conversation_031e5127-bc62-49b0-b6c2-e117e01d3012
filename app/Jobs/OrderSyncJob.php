<?php

declare(strict_types=1);

namespace App\Jobs;

use App\Contracts\OrderSyncServiceInterface;
use App\Jobs\OrderSyncValidationJob;
use App\Models\SyncLog;
use App\Services\SyncProgressService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

final class OrderSyncJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $timeout = 7200; // 2 hour timeout for large datasets
    public int $tries = 1; // No automatic retries for sync jobs
    public bool $failOnTimeout = true;
    public int $maxExceptions = 1; // Fail after 1 exception

    /**
     * Create a new job instance.
     */
    public function __construct(
        public readonly array $config,
        public readonly string $batchId,
        public readonly bool $isRetry = false,
        public readonly ?string $originalBatchId = null
    ) {
        $this->onQueue('sync');
    }

    /**
     * Execute the job.
     */
    public function handle(
        OrderSyncServiceInterface $syncService,
        SyncProgressService $progressService
    ): void {
        // Temporarily increase memory limit for this job only
        $originalMemoryLimit = ini_get('memory_limit');
        $syncMemoryLimit = config('sync.memory_limit', '512M');
        ini_set('memory_limit', $syncMemoryLimit);

        // Set execution time limit for this job
        $originalTimeLimit = ini_get('max_execution_time');
        ini_set('max_execution_time', $this->timeout);

        $jobId = (string) $this->job->getJobId();

        Log::info('Order sync job started with temporary limits', [
            'job_id' => $jobId,
            'batch_id' => $this->batchId,
            'is_retry' => $this->isRetry,
            'config' => $this->config,
            'original_memory_limit' => $originalMemoryLimit,
            'sync_memory_limit' => $syncMemoryLimit,
            'execution_timeout' => $this->timeout,
        ]);

        try {
            // Initialize progress tracking
            $progressService->initializeProgress($jobId, $this->batchId);

            // Define progress callback
            $progressCallback = function($processedChunks, $totalChunks, $elapsedTime) use ($progressService, $jobId) {
                $percentage = round(($processedChunks / $totalChunks) * 100, 1);
                
                $progressService->updateProgress($jobId, [
                    'status' => 'processing',
                    'percentage' => $percentage,
                    'processed_chunks' => $processedChunks,
                    'total_chunks' => $totalChunks,
                    'elapsed_time' => $elapsedTime,
                ]);

                Log::debug('Order sync progress update', [
                    'job_id' => $jobId,
                    'batch_id' => $this->batchId,
                    'percentage' => $percentage,
                    'processed_chunks' => $processedChunks,
                    'total_chunks' => $totalChunks,
                ]);
            };

            // Execute the sync
            if ($this->isRetry && $this->originalBatchId) {
                $syncLog = $syncService->reSyncInQueue($this->originalBatchId, $this->config, $progressCallback);
            } else {
                $syncLog = $syncService->syncInQueue($this->config, $this->batchId, $progressCallback);
            }

            // Check if sync was successful and dispatch validation job
            if ($syncLog->status === 'completed') {
                // Mark progress as sync completed but validation pending
                $progressService->markSyncCompleted($jobId, [
                    'sync_log_id' => $syncLog->id,
                    'batch_id' => $syncLog->batch_id,
                    'status' => 'validating',
                    'total_records' => $syncLog->total_records,
                    'success_records' => $syncLog->success_records,
                    'failed_records' => $syncLog->failed_records,
                    'progress_percentage' => $syncLog->progress_percentage,
                ]);

                // Dispatch validation job
                OrderSyncValidationJob::dispatch($syncLog->id, $syncLog->batch_id, $jobId);

                Log::info('Order sync job completed, validation job dispatched', [
                    'job_id' => $jobId,
                    'batch_id' => $syncLog->batch_id,
                    'status' => $syncLog->status,
                    'total_records' => $syncLog->total_records,
                    'success_records' => $syncLog->success_records,
                    'failed_records' => $syncLog->failed_records,
                ]);
            } else {
                // Sync failed, mark as completed with failed status
                $progressService->markCompleted($jobId, [
                    'sync_log_id' => $syncLog->id,
                    'batch_id' => $syncLog->batch_id,
                    'status' => $syncLog->status,
                    'total_records' => $syncLog->total_records,
                    'success_records' => $syncLog->success_records,
                    'failed_records' => $syncLog->failed_records,
                    'progress_percentage' => $syncLog->progress_percentage,
                ]);

                Log::info('Order sync job completed with failed status', [
                    'job_id' => $jobId,
                    'batch_id' => $syncLog->batch_id,
                    'status' => $syncLog->status,
                    'total_records' => $syncLog->total_records,
                    'success_records' => $syncLog->success_records,
                    'failed_records' => $syncLog->failed_records,
                ]);
            }

        } catch (Exception $exception) {
            // Mark progress as failed
            $progressService->markFailed($jobId, [
                'error_message' => $exception->getMessage(),
                'error_trace' => $exception->getTraceAsString(),
            ]);

            Log::error('Order sync job failed', [
                'job_id' => $jobId,
                'batch_id' => $this->batchId,
                'exception' => $exception->getMessage(),
                'trace' => $exception->getTraceAsString(),
            ]);

            throw $exception;
        } finally {
            // Always restore original PHP settings
            ini_set('memory_limit', $originalMemoryLimit);
            ini_set('max_execution_time', $originalTimeLimit);

            Log::debug('Restored original PHP limits', [
                'job_id' => $jobId,
                'restored_memory_limit' => $originalMemoryLimit,
                'restored_time_limit' => $originalTimeLimit,
            ]);
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(Exception $exception): void
    {
        $jobId = $this->job?->getJobId();
        
        Log::error('Order sync job failed permanently', [
            'job_id' => $jobId,
            'batch_id' => $this->batchId,
            'exception' => $exception->getMessage(),
        ]);

        // Update sync log status if it exists
        $syncLog = SyncLog::where('batch_id', $this->batchId)->first();
        if ($syncLog) {
            $syncLog->update([
                'status' => 'failed',
                'completed_at' => now(),
                'error_message' => $exception->getMessage(),
            ]);
        }
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        return [
            'sync',
            'order-sync',
            "batch:{$this->batchId}",
            $this->isRetry ? 'retry' : 'initial',
        ];
    }
}
