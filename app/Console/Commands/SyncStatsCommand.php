<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Services\ProductSyncService;
use Illuminate\Console\Command;

final class SyncStatsCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'sync:stats';

    /**
     * The console command description.
     */
    protected $description = 'Display synchronization statistics';

    /**
     * Execute the console command.
     */
    public function handle(ProductSyncService $syncService): int
    {
        $this->info('📊 Synchronization Statistics');
        $this->newLine();

        $stats = $syncService->getSyncStatistics();

        $this->table(['Metric', 'Value'], [
            ['Total Syncs', $stats['total_syncs']],
            ['Successful Syncs', $stats['successful_syncs']],
            ['Failed Syncs', $stats['failed_syncs']],
            ['Success Rate', $stats['success_rate'] . '%'],
        ]);

        if ($stats['last_sync']) {
            $this->newLine();
            $this->info('🕒 Last Sync Information:');
            $this->table(['Property', 'Value'], [
                ['Batch ID', $stats['last_sync']['batch_id']],
                ['Status', $stats['last_sync']['status']],
                ['Started At', $stats['last_sync']['created_at']],
                ['Completed At', $stats['last_sync']['completed_at'] ?? 'N/A'],
            ]);
        } else {
            $this->newLine();
            $this->warn('No sync records found.');
        }

        return 0;
    }
}
