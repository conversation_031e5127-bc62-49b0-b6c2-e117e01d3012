<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Contracts\OrderSyncServiceInterface;
use App\Jobs\OrderSyncJob;
use App\Services\SyncProgressService;

final class SyncOrdersCommand extends BaseSyncCommand
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'sync:orders
                            {--incremental : Execute incremental sync}
                            {--batch-size=100 : Batch processing size}
                            {--timeout=300 : Timeout in seconds}
                            {--force : Force execution without confirmation or safety checks}
                            {--queue : Execute in queue instead of synchronously}
                            {--scheduled : Indicates this is a scheduled execution (enables additional safety checks)}';

    /**
     * The console command description.
     */
    protected $description = 'Synchronize order data from remote data source';

    private OrderSyncServiceInterface $syncService;

    /**
     * Execute the console command.
     */
    public function handle(SyncProgressService $progressService): int
    {
        $this->syncService = app(OrderSyncServiceInterface::class);
        return parent::handle($progressService);
    }

    /**
     * Get the sync service instance.
     */
    protected function getSyncService(): OrderSyncServiceInterface
    {
        return $this->syncService;
    }

    /**
     * Get the sync job class name.
     */
    protected function getSyncJobClass(): string
    {
        return OrderSyncJob::class;
    }

    /**
     * Get the sync type name for logging.
     */
    protected function getSyncTypeName(): string
    {
        return 'Order';
    }

    /**
     * Get the sync type key for translation.
     */
    protected function getSyncTypeKey(): string
    {
        return 'order_sync';
    }

    /**
     * Check if incremental sync can be performed.
     */
    protected function canPerformIncrementalSync(): bool
    {
        return $this->syncService->canPerformIncrementalSync();
    }
}
