<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Exception;

final class TestSyncConnectionCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'sync:test-connection';

    /**
     * The console command description.
     */
    protected $description = 'Test the remote database connection for sync operations';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🔍 Testing sync database connections...');
        $this->newLine();

        // Test main database connection
        $this->testConnection('mysql', 'Main Database');

        // Test store database connection
        $this->testConnection('store', 'Store Database (Sylius)');

        $this->newLine();
        $this->info('✅ Connection tests completed!');

        return 0;
    }

    /**
     * Test a specific database connection.
     */
    private function testConnection(string $connection, string $name): void
    {
        try {
            $this->info("Testing {$name}...");

            $pdo = DB::connection($connection)->getPdo();
            $this->line("  ✅ Connection successful");

            // Test basic query
            $result = DB::connection($connection)->select('SELECT 1 as test');
            $this->line("  ✅ Query test successful");

            // For store connection, test specific tables
            if ($connection === 'store') {
                $tables = ['sylius_product', 'sylius_product_variant', 'sylius_channel_pricing'];
                foreach ($tables as $table) {
                    try {
                        $count = DB::connection($connection)->table($table)->count();
                        $this->line("  ✅ Table '{$table}': {$count} records");
                    } catch (Exception $e) {
                        $this->line("  ⚠️  Table '{$table}': " . $e->getMessage());
                    }
                }
            }

        } catch (Exception $e) {
            $this->error("  ❌ {$name} connection failed: " . $e->getMessage());
        }

        $this->newLine();
    }
}
