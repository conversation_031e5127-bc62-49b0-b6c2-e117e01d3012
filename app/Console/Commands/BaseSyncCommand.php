<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Services\SyncProgressService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

abstract class BaseSyncCommand extends Command
{
    /**
     * Get the sync service instance.
     */
    abstract protected function getSyncService(): object;

    /**
     * Get the sync job class name.
     */
    abstract protected function getSyncJobClass(): string;

    /**
     * Get the sync type name for logging.
     */
    abstract protected function getSyncTypeName(): string;

    /**
     * Get the sync type key for translation.
     */
    abstract protected function getSyncTypeKey(): string;

    /**
     * Check if incremental sync can be performed.
     */
    abstract protected function canPerformIncrementalSync(): bool;

    /**
     * Execute the console command.
     */
    public function handle(SyncProgressService $progressService): int
    {
        $isScheduled = $this->option('scheduled');
        $isIncremental = $this->option('incremental');
        $isForced = $this->option('force');

        // Scheduled execution safety checks
        if ($isScheduled) {
            return $this->handleScheduledExecution($progressService);
        }

        // Manual execution
        return $this->handleManualExecution($isIncremental, $isForced);
    }

    /**
     * Handle scheduled execution with safety checks
     */
    protected function handleScheduledExecution(SyncProgressService $progressService): int
    {
        // Check if scheduled sync is enabled
        if (!config('sync.scheduled_sync_enabled', true) && !$this->option('force')) {
            $this->info('Scheduled sync is disabled. Use --force to override.');
            Log::info("Scheduled incremental {$this->getSyncTypeName()} sync skipped - disabled in configuration");
            return 0;
        }

        // Check if there are any active sync jobs
        if ($progressService->hasActiveSyncJobs()) {
            $this->warn('Another sync job is currently running. Skipping scheduled sync.');
            Log::warning("Scheduled incremental {$this->getSyncTypeName()} sync skipped - another sync job is active");
            return 0;
        }

        // For scheduled execution, force incremental sync
        if (!$this->option('incremental')) {
            $this->input->setOption('incremental', true);
        }

        // Check if full sync has been completed before (for incremental sync)
        if (!$this->canPerformIncrementalSync()) {
            $this->error('Cannot perform incremental sync: No completed full sync found.');
            $this->info("Please run a full sync first: php artisan sync:{$this->getSyncTypeKey()}");
            Log::error("Scheduled incremental {$this->getSyncTypeName()} sync failed - no completed full sync found");
            return 1;
        }

        $this->info('Starting scheduled incremental synchronization...');
        Log::info("Scheduled incremental {$this->getSyncTypeName()} sync started");

        return $this->executeSync(true, true);
    }

    /**
     * Handle manual execution
     */
    protected function handleManualExecution(bool $isIncremental, bool $isForced): int
    {
        $this->info(__("sync.{$this->getSyncTypeKey()}_sync_started"));

        // Check prerequisites for incremental sync
        if ($isIncremental && !$this->canPerformIncrementalSync()) {
            $this->error('Cannot perform incremental sync: No completed full sync found.');
            $this->info('Please run a full sync first by removing the --incremental option.');
            return 1;
        }

        return $this->executeSync($isIncremental, $isForced);
    }

    /**
     * Execute the synchronization
     */
    protected function executeSync(bool $isIncremental, bool $isForced): int
    {
        $config = [
            'incremental' => $isIncremental,
            'batch_size' => (int) $this->option('batch-size'),
            'timeout' => (int) $this->option('timeout'),
        ];

        // Display configuration information
        $this->table(['Configuration Item', 'Value'], [
            ['Sync Type', $config['incremental'] ? 'Incremental Sync' : 'Full Sync'],
            ['Batch Size', $config['batch_size']],
            ['Timeout', $config['timeout'] . ' seconds'],
            ['Execution Mode', $this->option('queue') ? 'Queued' : 'Synchronous'],
        ]);

        // Confirmation for manual execution (skip for scheduled)
        if (!$isForced && !$this->option('scheduled') && !$this->confirm('Confirm to start synchronization?')) {
            $this->info('Synchronization cancelled');
            return 1;
        }

        try {
            if ($this->option('queue')) {
                return $this->executeInQueue($config);
            } else {
                return $this->executeSynchronously($config);
            }
        } catch (\Exception $e) {
            $this->error("Synchronization failed: {$e->getMessage()}");
            Log::error("{$this->getSyncTypeName()} sync exception", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'config' => $config,
            ]);
            return 1;
        }
    }

    /**
     * Execute synchronization in queue
     */
    protected function executeInQueue(array $config): int
    {
        $batchId = Str::uuid()->toString();
        $jobClass = $this->getSyncJobClass();
        $jobClass::dispatch($config, $batchId);

        $syncType = $config['incremental'] ? 'Incremental' : 'Full';
        $this->info("{$syncType} sync queued successfully with batch ID: {$batchId}");

        Log::info("{$this->getSyncTypeName()} sync queued", [
            'batch_id' => $batchId,
            'config' => $config,
        ]);

        return 0;
    }

    /**
     * Execute synchronization synchronously
     */
    protected function executeSynchronously(array $config): int
    {
        $syncService = $this->getSyncService();

        // Define progress callback
        $progressCallback = function($processedChunks, $totalChunks, $elapsedTime) {
            $percentage = round(($processedChunks / $totalChunks) * 100, 1);
            $elapsedMinutes = floor($elapsedTime / 60);
            $elapsedSeconds = round($elapsedTime % 60);
            $timeStr = $elapsedMinutes > 0 ? "{$elapsedMinutes}m {$elapsedSeconds}s" : "{$elapsedSeconds}s";

            $this->info("Progress: {$processedChunks}/{$totalChunks} batches ({$percentage}%) - Elapsed: {$timeStr}");
        };

        $syncLog = $syncService->sync($config, $progressCallback);

        // Log the result
        if ($syncLog->status === 'completed') {
            Log::info("{$this->getSyncTypeName()} sync completed", [
                'batch_id' => $syncLog->batch_id,
                'total_records' => $syncLog->total_records,
                'success_records' => $syncLog->success_records,
                'failed_records' => $syncLog->failed_records,
                'config' => $config,
            ]);
        } else {
            Log::error("{$this->getSyncTypeName()} sync failed", [
                'batch_id' => $syncLog->batch_id,
                'status' => $syncLog->status,
                'error_message' => $syncLog->error_message,
                'config' => $config,
            ]);
        }

        // Display results
        $this->newLine();
        if ($syncLog->status === 'completed') {
            $this->info("✅ Synchronization completed successfully!");
        } else {
            $this->error("❌ Synchronization failed!");
        }

        $this->table(['Item', 'Value'], [
            ['Batch ID', $syncLog->batch_id],
            ['Status', $this->option('scheduled') ? $syncLog->status : __("sync.sync_status.{$syncLog->status}")],
            ['Total Records', $syncLog->total_records],
            ['Success Records', $syncLog->success_records],
            ['Failed Records', $syncLog->failed_records],
            ['Skipped Records', $syncLog->total_records - $syncLog->success_records - $syncLog->failed_records],
            ['Progress', $syncLog->progress_percentage . '%'],
        ]);

        if ($syncLog->failed_records > 0) {
            $this->warn("⚠️  {$syncLog->failed_records} records failed to sync, please check logs");
        }

        return $syncLog->status === 'completed' ? 0 : 1;
    }
}
