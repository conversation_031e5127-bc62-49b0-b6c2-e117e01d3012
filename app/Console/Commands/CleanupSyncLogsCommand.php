<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Models\SyncLog;
use App\Models\SyncRecord;
use Illuminate\Console\Command;
use Carbon\Carbon;

final class CleanupSyncLogsCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'sync:cleanup {--days=30 : Days to keep logs}';

    /**
     * The console command description.
     */
    protected $description = 'Clean up old sync logs and records';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $days = (int) $this->option('days');
        $cutoffDate = Carbon::now()->subDays($days);

        $this->info("Cleaning up sync logs older than {$days} days...");

        // Delete old sync records first (foreign key constraint)
        $recordsDeleted = SyncRecord::where('created_at', '<', $cutoffDate)->delete();
        $this->info("Deleted {$recordsDeleted} sync records");

        // Delete old sync logs
        $logsDeleted = SyncLog::where('created_at', '<', $cutoffDate)->delete();
        $this->info("Deleted {$logsDeleted} sync logs");

        $this->info('Cleanup completed!');

        return 0;
    }
}
