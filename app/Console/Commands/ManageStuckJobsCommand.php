<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

final class ManageStuckJobsCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'queue:manage-stuck-jobs 
                            {action : Action to perform: list, delete-all, delete-old}
                            {--hours=2 : Hours to consider a job as stuck (for delete-old action)}
                            {--queue= : Specific queue to check (default: all)}
                            {--force : Force deletion without confirmation}
                            {--cleanup-progress : Also cleanup progress tracking data}';

    /**
     * The console command description.
     */
    protected $description = 'Manage stuck jobs in the queue system';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $action = $this->argument('action');
        
        switch ($action) {
            case 'list':
                return $this->listStuckJobs();
            case 'delete-all':
                return $this->deleteAllStuckJobs();
            case 'delete-old':
                return $this->deleteOldStuckJobs();
            default:
                $this->error("Invalid action: {$action}");
                $this->info("Available actions: list, delete-all, delete-old");
                return 1;
        }
    }

    /**
     * List all stuck jobs
     */
    private function listStuckJobs(): int
    {
        $this->info('=== Stuck Jobs Report ===');
        
        // Get stuck jobs from database
        $databaseJobs = $this->getStuckJobsFromDatabase();
        
        // Get stuck jobs from Redis
        $redisJobs = $this->getStuckJobsFromRedis();
        
        if (empty($databaseJobs) && empty($redisJobs)) {
            $this->info('✓ No stuck jobs found.');
            return 0;
        }
        
        if (!empty($databaseJobs)) {
            $this->info("\nDatabase Queue Stuck Jobs:");
            $this->displayDatabaseJobs($databaseJobs);
        }
        
        if (!empty($redisJobs)) {
            $this->info("\nRedis Queue Stuck Jobs:");
            $this->displayRedisJobs($redisJobs);
        }
        
        $totalStuck = count($databaseJobs) + count($redisJobs);
        $this->warn("\nTotal stuck jobs: {$totalStuck}");
        
        return 0;
    }

    /**
     * Delete all stuck jobs
     */
    private function deleteAllStuckJobs(): int
    {
        $force = $this->option('force');
        $cleanupProgress = $this->option('cleanup-progress');
        
        $databaseJobs = $this->getStuckJobsFromDatabase();
        $redisJobs = $this->getStuckJobsFromRedis();
        
        $totalJobs = count($databaseJobs) + count($redisJobs);
        
        if ($totalJobs === 0) {
            $this->info('✓ No stuck jobs found to delete.');
            return 0;
        }
        
        $this->warn("Found {$totalJobs} stuck jobs to delete.");
        
        if (!$force && !$this->confirm('Do you want to delete ALL stuck jobs?')) {
            $this->info('Operation cancelled.');
            return 0;
        }
        
        $deleted = 0;
        
        // Delete from database
        foreach ($databaseJobs as $job) {
            if ($this->deleteJobFromDatabase($job->id)) {
                $deleted++;
                if ($cleanupProgress) {
                    $this->cleanupProgressData((string) $job->id);
                }
            }
        }
        
        // Delete from Redis
        foreach ($redisJobs as $job) {
            if ($this->deleteJobFromRedis($job)) {
                $deleted++;
                if ($cleanupProgress && isset($job['data']['id'])) {
                    $this->cleanupProgressData((string) $job['data']['id']);
                }
            }
        }
        
        $this->info("✓ Deleted {$deleted} stuck jobs.");
        
        Log::info('Bulk stuck jobs deletion', [
            'total_deleted' => $deleted,
            'cleanup_progress' => $cleanupProgress,
            'deleted_by' => 'console_command',
        ]);
        
        return 0;
    }

    /**
     * Delete old stuck jobs
     */
    private function deleteOldStuckJobs(): int
    {
        $hours = (int) $this->option('hours');
        $force = $this->option('force');
        $cleanupProgress = $this->option('cleanup-progress');
        
        $cutoffTime = Carbon::now()->subHours($hours);
        
        $this->info("Searching for jobs stuck for more than {$hours} hours...");
        
        $databaseJobs = $this->getOldStuckJobsFromDatabase($cutoffTime);
        $redisJobs = $this->getOldStuckJobsFromRedis($cutoffTime);
        
        $totalJobs = count($databaseJobs) + count($redisJobs);
        
        if ($totalJobs === 0) {
            $this->info('✓ No old stuck jobs found to delete.');
            return 0;
        }
        
        $this->warn("Found {$totalJobs} jobs stuck for more than {$hours} hours.");
        
        if (!$force && !$this->confirm("Do you want to delete these old stuck jobs?")) {
            $this->info('Operation cancelled.');
            return 0;
        }
        
        $deleted = 0;
        
        // Delete from database
        foreach ($databaseJobs as $job) {
            if ($this->deleteJobFromDatabase($job->id)) {
                $deleted++;
                if ($cleanupProgress) {
                    $this->cleanupProgressData((string) $job->id);
                }
            }
        }
        
        // Delete from Redis
        foreach ($redisJobs as $job) {
            if ($this->deleteJobFromRedis($job)) {
                $deleted++;
                if ($cleanupProgress && isset($job['data']['id'])) {
                    $this->cleanupProgressData((string) $job['data']['id']);
                }
            }
        }
        
        $this->info("✓ Deleted {$deleted} old stuck jobs.");
        
        Log::info('Old stuck jobs deletion', [
            'hours_threshold' => $hours,
            'total_deleted' => $deleted,
            'cleanup_progress' => $cleanupProgress,
            'deleted_by' => 'console_command',
        ]);
        
        return 0;
    }

    /**
     * Get stuck jobs from database
     */
    private function getStuckJobsFromDatabase(): array
    {
        $query = DB::table('jobs')
            ->whereNotNull('reserved_at')
            ->where('reserved_at', '<', Carbon::now()->subMinutes(30)->timestamp);
        
        if ($queue = $this->option('queue')) {
            $query->where('queue', $queue);
        }
        
        return $query->get()->toArray();
    }

    /**
     * Get old stuck jobs from database
     */
    private function getOldStuckJobsFromDatabase(Carbon $cutoffTime): array
    {
        $query = DB::table('jobs')
            ->whereNotNull('reserved_at')
            ->where('reserved_at', '<', $cutoffTime->timestamp);
        
        if ($queue = $this->option('queue')) {
            $query->where('queue', $queue);
        }
        
        return $query->get()->toArray();
    }

    /**
     * Get stuck jobs from Redis (simplified - checks reserved jobs)
     */
    private function getStuckJobsFromRedis(): array
    {
        $redis = Redis::connection();
        $stuckJobs = [];
        $queueNames = $this->option('queue') ? [$this->option('queue')] : ['default', 'sync'];
        
        foreach ($queueNames as $queueName) {
            $reservedKey = "queues:{$queueName}:reserved";
            $reservedJobs = $redis->zrangebyscore($reservedKey, 0, Carbon::now()->subMinutes(30)->timestamp);
            
            foreach ($reservedJobs as $job) {
                $jobData = json_decode($job, true);
                $stuckJobs[] = [
                    'queue' => $queueName,
                    'key' => $reservedKey,
                    'data' => $jobData,
                    'type' => 'reserved'
                ];
            }
        }
        
        return $stuckJobs;
    }

    /**
     * Get old stuck jobs from Redis
     */
    private function getOldStuckJobsFromRedis(Carbon $cutoffTime): array
    {
        $redis = Redis::connection();
        $stuckJobs = [];
        $queueNames = $this->option('queue') ? [$this->option('queue')] : ['default', 'sync'];
        
        foreach ($queueNames as $queueName) {
            $reservedKey = "queues:{$queueName}:reserved";
            $reservedJobs = $redis->zrangebyscore($reservedKey, 0, $cutoffTime->timestamp);
            
            foreach ($reservedJobs as $job) {
                $jobData = json_decode($job, true);
                $stuckJobs[] = [
                    'queue' => $queueName,
                    'key' => $reservedKey,
                    'data' => $jobData,
                    'type' => 'reserved'
                ];
            }
        }
        
        return $stuckJobs;
    }

    /**
     * Display database jobs in table format
     */
    private function displayDatabaseJobs(array $jobs): void
    {
        $tableData = [];
        foreach ($jobs as $job) {
            $payload = json_decode($job->payload, true);
            $tableData[] = [
                $job->id,
                $job->queue,
                $payload['displayName'] ?? 'Unknown',
                $job->attempts,
                Carbon::createFromTimestamp($job->reserved_at)->format('Y-m-d H:i:s'),
            ];
        }
        
        $this->table(
            ['Job ID', 'Queue', 'Job Class', 'Attempts', 'Reserved At'],
            $tableData
        );
    }

    /**
     * Display Redis jobs in table format
     */
    private function displayRedisJobs(array $jobs): void
    {
        $tableData = [];
        foreach ($jobs as $job) {
            $tableData[] = [
                $job['data']['id'] ?? 'Unknown',
                $job['queue'],
                $job['data']['displayName'] ?? 'Unknown',
                $job['type'],
            ];
        }
        
        $this->table(
            ['Job ID', 'Queue', 'Job Class', 'Type'],
            $tableData
        );
    }

    /**
     * Delete job from database
     */
    private function deleteJobFromDatabase(int $jobId): bool
    {
        return DB::table('jobs')->where('id', $jobId)->delete() > 0;
    }

    /**
     * Delete job from Redis
     */
    private function deleteJobFromRedis(array $job): bool
    {
        $redis = Redis::connection();
        $jobJson = json_encode($job['data']);
        
        return $redis->zrem($job['key'], $jobJson) > 0;
    }

    /**
     * Cleanup progress tracking data
     */
    private function cleanupProgressData(string $jobId): void
    {
        $redis = Redis::connection();
        $progressKey = "sync_progress:{$jobId}";
        
        if ($redis->exists($progressKey)) {
            $redis->del($progressKey);
        }
    }
}
