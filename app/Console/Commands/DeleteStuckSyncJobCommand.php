<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use App\Services\SyncProgressService;
use Carbon\Carbon;

final class DeleteStuckSyncJobCommand extends Command
{
    private const CACHE_PREFIX = 'sync_progress:';
    private const ACTIVE_JOBS_KEY = 'sync:active_jobs';
    private const BATCH_INDEX_KEY = 'sync:batch_index';

    /**
     * The name and signature of the console command.
     */
    protected $signature = 'sync:delete-stuck-job
                            {job_id : The progress tracking job ID (UUID from API)}
                            {--force : Force deletion without confirmation}
                            {--cleanup-all : Also cleanup all related data}';

    /**
     * The console command description.
     */
    protected $description = 'Delete a stuck sync job using the progress tracking job ID from API';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $jobId = $this->argument('job_id');
        $force = $this->option('force');
        $cleanupAll = $this->option('cleanup-all');

        $this->info("Searching for sync job with progress ID: {$jobId}");

        // Get progress data to find batch_id
        $progressData = $this->getProgressData($jobId);
        
        if (!$progressData) {
            $this->error("No progress data found for job ID: {$jobId}");
            return 1;
        }

        $batchId = $progressData['batch_id'] ?? null;
        
        if (!$batchId) {
            $this->error("No batch_id found in progress data for job ID: {$jobId}");
            return 1;
        }

        $this->info("Found batch_id: {$batchId}");

        // Find related queue jobs and sync logs
        $queueJobs = $this->findRelatedQueueJobs($batchId);
        $syncLogs = $this->findRelatedSyncLogs($batchId);

        // Display information
        $this->displayJobInfo($progressData, $queueJobs, $syncLogs);

        // Confirm deletion
        if (!$force && !$this->confirm('Do you want to delete this stuck sync job and all related data?')) {
            $this->info('Operation cancelled.');
            return 0;
        }

        // Perform deletion
        $deletedItems = $this->performDeletion($jobId, $batchId, $queueJobs, $syncLogs, $cleanupAll);

        // Report results
        $this->reportResults($deletedItems);

        Log::info('Stuck sync job deleted', [
            'progress_job_id' => $jobId,
            'batch_id' => $batchId,
            'deleted_items' => $deletedItems,
            'cleanup_all' => $cleanupAll,
            'deleted_by' => 'console_command',
        ]);

        return 0;
    }

    /**
     * Get progress data from Redis
     */
    private function getProgressData(string $jobId): ?array
    {
        return Cache::get(self::CACHE_PREFIX . $jobId);
    }

    /**
     * Find related queue jobs by batch_id
     */
    private function findRelatedQueueJobs(string $batchId): array
    {
        $jobs = [];
        
        // Search in database queue
        $databaseJobs = DB::table('jobs')->get();
        foreach ($databaseJobs as $job) {
            $payload = json_decode($job->payload, true);
            if (isset($payload['data']['batchId']) && $payload['data']['batchId'] === $batchId) {
                $jobs[] = [
                    'type' => 'database',
                    'id' => $job->id,
                    'data' => $job,
                ];
            }
        }

        // Search in Redis queue
        $redis = Redis::connection();
        $queueNames = ['default', 'sync'];
        
        foreach ($queueNames as $queueName) {
            $keys = [
                "queues:{$queueName}",
                "queues:{$queueName}:delayed",
                "queues:{$queueName}:reserved"
            ];
            
            foreach ($keys as $key) {
                $queueJobs = $redis->exists($key) ? 
                    ($redis->type($key) === Redis::REDIS_LIST ? 
                        $redis->lrange($key, 0, -1) : 
                        $redis->zrange($key, 0, -1)) : [];
                
                foreach ($queueJobs as $jobJson) {
                    $jobData = json_decode($jobJson, true);
                    if (isset($jobData['data']['batchId']) && $jobData['data']['batchId'] === $batchId) {
                        $jobs[] = [
                            'type' => 'redis',
                            'queue' => $queueName,
                            'key' => $key,
                            'data' => $jobData,
                            'json' => $jobJson,
                        ];
                    }
                }
            }
        }

        return $jobs;
    }

    /**
     * Find related sync logs by batch_id
     */
    private function findRelatedSyncLogs(string $batchId): array
    {
        return DB::table('sync_logs')
            ->where('batch_id', $batchId)
            ->get()
            ->toArray();
    }

    /**
     * Display job information
     */
    private function displayJobInfo(array $progressData, array $queueJobs, array $syncLogs): void
    {
        $this->info('=== Stuck Sync Job Information ===');
        
        // Progress data
        $this->info("\nProgress Data:");
        $this->table(
            ['Field', 'Value'],
            [
                ['Job ID', $progressData['job_id'] ?? 'N/A'],
                ['Batch ID', $progressData['batch_id'] ?? 'N/A'],
                ['Status', $progressData['status'] ?? 'N/A'],
                ['Percentage', ($progressData['percentage'] ?? 0) . '%'],
                ['Started At', $progressData['started_at'] ?? 'N/A'],
                ['Updated At', $progressData['updated_at'] ?? 'N/A'],
                ['Elapsed Time', ($progressData['elapsed_time'] ?? 0) . ' seconds'],
            ]
        );

        // Queue jobs
        if (!empty($queueJobs)) {
            $this->info("\nRelated Queue Jobs:");
            $tableData = [];
            foreach ($queueJobs as $job) {
                $tableData[] = [
                    $job['type'],
                    $job['type'] === 'database' ? $job['id'] : $job['queue'],
                    $job['type'] === 'database' ? 
                        ($job['data']->queue ?? 'N/A') : 
                        basename($job['key']),
                    $job['type'] === 'database' ? 
                        ($job['data']->attempts ?? 'N/A') : 
                        'N/A',
                ];
            }
            $this->table(['Type', 'ID/Queue', 'Queue/Key', 'Attempts'], $tableData);
        }

        // Sync logs
        if (!empty($syncLogs)) {
            $this->info("\nRelated Sync Logs:");
            $tableData = [];
            foreach ($syncLogs as $log) {
                $tableData[] = [
                    $log->id,
                    $log->sync_type,
                    $log->status,
                    $log->started_at ?? 'N/A',
                    $log->total_records ?? 0,
                    $log->success_records ?? 0,
                ];
            }
            $this->table(['ID', 'Type', 'Status', 'Started', 'Total', 'Success'], $tableData);
        }
    }

    /**
     * Perform deletion of all related data
     */
    private function performDeletion(string $jobId, string $batchId, array $queueJobs, array $syncLogs, bool $cleanupAll): array
    {
        $deleted = [
            'progress_data' => 0,
            'queue_jobs' => 0,
            'sync_logs' => 0,
            'sync_records' => 0,
            'active_jobs' => 0,
        ];

        // Delete progress data
        if (Cache::forget(self::CACHE_PREFIX . $jobId)) {
            $deleted['progress_data'] = 1;
            $this->info("✓ Deleted progress data");
        }

        // Remove from active jobs list
        $activeJobs = Cache::get(self::ACTIVE_JOBS_KEY, []);
        if (isset($activeJobs[$jobId])) {
            unset($activeJobs[$jobId]);
            Cache::put(self::ACTIVE_JOBS_KEY, $activeJobs, 3600);
            $deleted['active_jobs'] = 1;
            $this->info("✓ Removed from active jobs list");
        }

        // Delete queue jobs
        foreach ($queueJobs as $job) {
            if ($job['type'] === 'database') {
                if (DB::table('jobs')->where('id', $job['id'])->delete()) {
                    $deleted['queue_jobs']++;
                    $this->info("✓ Deleted database queue job ID: {$job['id']}");
                }
            } else {
                $redis = Redis::connection();
                if ($redis->type($job['key']) === Redis::REDIS_LIST) {
                    $removed = $redis->lrem($job['key'], 0, $job['json']);
                } else {
                    $removed = $redis->zrem($job['key'], $job['json']);
                }
                if ($removed > 0) {
                    $deleted['queue_jobs']++;
                    $this->info("✓ Deleted Redis queue job from: {$job['key']}");
                }
            }
        }

        // Delete sync logs and records if requested
        if ($cleanupAll) {
            foreach ($syncLogs as $log) {
                // Delete related sync records first
                $recordsDeleted = DB::table('sync_records')->where('sync_log_id', $log->id)->delete();
                $deleted['sync_records'] += $recordsDeleted;
                
                // Delete sync log
                if (DB::table('sync_logs')->where('id', $log->id)->delete()) {
                    $deleted['sync_logs']++;
                    $this->info("✓ Deleted sync log ID: {$log->id} and {$recordsDeleted} related records");
                }
            }
        } else {
            // Just mark sync logs as failed
            foreach ($syncLogs as $log) {
                if ($log->status === 'processing') {
                    DB::table('sync_logs')
                        ->where('id', $log->id)
                        ->update([
                            'status' => 'failed',
                            'completed_at' => now(),
                            'error_message' => 'Manually cancelled due to stuck job',
                        ]);
                    $this->info("✓ Marked sync log ID: {$log->id} as failed");
                }
            }
        }

        return $deleted;
    }

    /**
     * Report deletion results
     */
    private function reportResults(array $deleted): void
    {
        $this->info("\n=== Deletion Summary ===");
        $this->info("Progress data deleted: {$deleted['progress_data']}");
        $this->info("Queue jobs deleted: {$deleted['queue_jobs']}");
        $this->info("Active jobs removed: {$deleted['active_jobs']}");
        $this->info("Sync logs deleted: {$deleted['sync_logs']}");
        $this->info("Sync records deleted: {$deleted['sync_records']}");
        
        $total = array_sum($deleted);
        if ($total > 0) {
            $this->info("\n✓ Successfully cleaned up stuck sync job!");
        } else {
            $this->warn("\n⚠ No items were deleted. Job may have already been cleaned up.");
        }
    }
}
