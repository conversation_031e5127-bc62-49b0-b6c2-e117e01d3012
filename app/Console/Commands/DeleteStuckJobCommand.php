<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

final class DeleteStuckJobCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'queue:delete-stuck-job 
                            {job_id : The job ID to delete}
                            {--force : Force deletion without confirmation}
                            {--cleanup-progress : Also cleanup progress tracking data}';

    /**
     * The console command description.
     */
    protected $description = 'Delete a stuck job from the queue by job ID';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $jobId = $this->argument('job_id');
        $force = $this->option('force');
        $cleanupProgress = $this->option('cleanup-progress');

        $this->info("Searching for job ID: {$jobId}");

        // Search in database queue (jobs table)
        $databaseJob = $this->findJobInDatabase($jobId);
        
        // Search in Redis queue
        $redisJob = $this->findJobInRedis($jobId);

        if (!$databaseJob && !$redisJob) {
            $this->error("Job with ID {$jobId} not found in any queue.");
            return 1;
        }

        // Display job information
        $this->displayJobInfo($databaseJob, $redisJob);

        // Confirm deletion
        if (!$force && !$this->confirm('Do you want to delete this job?')) {
            $this->info('Operation cancelled.');
            return 0;
        }

        // Delete the job
        $deleted = false;

        if ($databaseJob) {
            $deleted = $this->deleteFromDatabase($jobId) || $deleted;
        }

        if ($redisJob) {
            $deleted = $this->deleteFromRedis($jobId) || $deleted;
        }

        // Cleanup progress tracking data if requested
        if ($cleanupProgress) {
            $this->cleanupProgressData($jobId);
        }

        if ($deleted) {
            $this->info("✓ Job {$jobId} has been successfully deleted.");
            
            Log::info('Stuck job deleted manually', [
                'job_id' => $jobId,
                'deleted_by' => 'console_command',
                'cleanup_progress' => $cleanupProgress,
            ]);
            
            return 0;
        } else {
            $this->error("✗ Failed to delete job {$jobId}.");
            return 1;
        }
    }

    /**
     * Find job in database queue
     */
    private function findJobInDatabase(string $jobId): ?object
    {
        // First try to find by primary key ID
        $job = DB::table('jobs')->where('id', $jobId)->first();

        if ($job) {
            return $job;
        }

        // If not found, try to find by UUID in payload (for Laravel job IDs)
        $jobs = DB::table('jobs')->get();
        foreach ($jobs as $job) {
            $payload = json_decode($job->payload, true);
            if (isset($payload['uuid']) && $payload['uuid'] === $jobId) {
                return $job;
            }
            // Also check if the job ID matches any internal job identifier
            if (isset($payload['id']) && $payload['id'] === $jobId) {
                return $job;
            }
        }

        return null;
    }

    /**
     * Find job in Redis queue
     */
    private function findJobInRedis(string $jobId): ?array
    {
        $redis = Redis::connection();
        $queueNames = ['default', 'sync']; // Based on your horizon config
        
        foreach ($queueNames as $queueName) {
            $queueKey = "queues:{$queueName}";
            $delayedKey = "queues:{$queueName}:delayed";
            $reservedKey = "queues:{$queueName}:reserved";
            
            // Check regular queue
            $jobs = $redis->lrange($queueKey, 0, -1);
            foreach ($jobs as $job) {
                $jobData = json_decode($job, true);
                if (isset($jobData['id']) && $jobData['id'] == $jobId) {
                    return [
                        'queue' => $queueName,
                        'key' => $queueKey,
                        'data' => $jobData,
                        'type' => 'regular'
                    ];
                }
            }
            
            // Check delayed queue
            $delayedJobs = $redis->zrange($delayedKey, 0, -1);
            foreach ($delayedJobs as $job) {
                $jobData = json_decode($job, true);
                if (isset($jobData['id']) && $jobData['id'] == $jobId) {
                    return [
                        'queue' => $queueName,
                        'key' => $delayedKey,
                        'data' => $jobData,
                        'type' => 'delayed'
                    ];
                }
            }
            
            // Check reserved queue
            $reservedJobs = $redis->zrange($reservedKey, 0, -1);
            foreach ($reservedJobs as $job) {
                $jobData = json_decode($job, true);
                if (isset($jobData['id']) && $jobData['id'] == $jobId) {
                    return [
                        'queue' => $queueName,
                        'key' => $reservedKey,
                        'data' => $jobData,
                        'type' => 'reserved'
                    ];
                }
            }
        }
        
        return null;
    }

    /**
     * Display job information
     */
    private function displayJobInfo(?object $databaseJob, ?array $redisJob): void
    {
        $this->info('=== Job Information ===');
        
        if ($databaseJob) {
            $this->info('Found in Database Queue:');
            $this->table(
                ['Field', 'Value'],
                [
                    ['ID', $databaseJob->id],
                    ['Queue', $databaseJob->queue],
                    ['Attempts', $databaseJob->attempts],
                    ['Created At', Carbon::createFromTimestamp($databaseJob->created_at)->format('Y-m-d H:i:s')],
                    ['Available At', Carbon::createFromTimestamp($databaseJob->available_at)->format('Y-m-d H:i:s')],
                    ['Reserved At', $databaseJob->reserved_at ? Carbon::createFromTimestamp($databaseJob->reserved_at)->format('Y-m-d H:i:s') : 'Not reserved'],
                ]
            );
            
            // Try to decode payload to show job class
            $payload = json_decode($databaseJob->payload, true);
            if (isset($payload['displayName'])) {
                $this->info("Job Class: {$payload['displayName']}");
            }
        }
        
        if ($redisJob) {
            $this->info('Found in Redis Queue:');
            $this->table(
                ['Field', 'Value'],
                [
                    ['Queue', $redisJob['queue']],
                    ['Type', $redisJob['type']],
                    ['Key', $redisJob['key']],
                ]
            );
            
            if (isset($redisJob['data']['displayName'])) {
                $this->info("Job Class: {$redisJob['data']['displayName']}");
            }
        }
    }

    /**
     * Delete job from database
     */
    private function deleteFromDatabase(string $jobId): bool
    {
        $deleted = DB::table('jobs')->where('id', $jobId)->delete();
        
        if ($deleted > 0) {
            $this->info("✓ Deleted job from database queue.");
            return true;
        }
        
        return false;
    }

    /**
     * Delete job from Redis
     */
    private function deleteFromRedis(string $jobId): bool
    {
        $redis = Redis::connection();
        $redisJob = $this->findJobInRedis($jobId);
        
        if (!$redisJob) {
            return false;
        }
        
        $jobJson = json_encode($redisJob['data']);
        
        switch ($redisJob['type']) {
            case 'regular':
                $removed = $redis->lrem($redisJob['key'], 0, $jobJson);
                break;
            case 'delayed':
            case 'reserved':
                $removed = $redis->zrem($redisJob['key'], $jobJson);
                break;
            default:
                $removed = 0;
        }
        
        if ($removed > 0) {
            $this->info("✓ Deleted job from Redis queue ({$redisJob['type']}).");
            return true;
        }
        
        return false;
    }

    /**
     * Cleanup progress tracking data
     */
    private function cleanupProgressData(string $jobId): void
    {
        $redis = Redis::connection();
        $progressKey = "sync_progress:{$jobId}";
        
        if ($redis->exists($progressKey)) {
            $redis->del($progressKey);
            $this->info("✓ Cleaned up progress tracking data.");
        }
    }
}
