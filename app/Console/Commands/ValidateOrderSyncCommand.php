<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Models\SyncLog;
use App\Services\OrderSyncValidationService;
use Illuminate\Console\Command;

final class ValidateOrderSyncCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:validate-orders 
                            {sync_log_id? : The ID of the sync log to validate}
                            {--latest : Validate the latest completed order sync}
                            {--batch_id= : Validate sync by batch ID}
                            {--json : Output results in JSON format}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Validate order sync data integrity against remote database';

    /**
     * Execute the console command.
     */
    public function handle(OrderSyncValidationService $validationService): int
    {
        try {
            $syncLog = $this->getSyncLog();
            
            if (!$syncLog) {
                $this->error('No sync log found matching the criteria.');
                return 1;
            }

            $this->info("Validating sync log ID: {$syncLog->id}");
            $this->info("Batch ID: {$syncLog->batch_id}");
            $this->info("Sync Type: {$syncLog->sync_type}");
            $this->info("Status: {$syncLog->status}");
            $this->newLine();

            $startTime = microtime(true);
            $results = $validationService->validateSync($syncLog);
            $endTime = microtime(true);

            $executionTime = round($endTime - $startTime, 2);

            if ($this->option('json')) {
                $this->line(json_encode($results, JSON_PRETTY_PRINT));
                return 0;
            }

            $this->displayResults($results, $executionTime);

            return $results['overall_status'] === 'passed' ? 0 : 1;

        } catch (\Exception $e) {
            $this->error("Validation failed: {$e->getMessage()}");
            return 1;
        }
    }

    /**
     * Get the sync log to validate based on command options.
     */
    private function getSyncLog(): ?SyncLog
    {
        if ($syncLogId = $this->argument('sync_log_id')) {
            return SyncLog::find($syncLogId);
        }

        if ($batchId = $this->option('batch_id')) {
            return SyncLog::where('batch_id', $batchId)->first();
        }

        if ($this->option('latest')) {
            return SyncLog::where('sync_type', 'order_sync')
                ->where('status', 'completed')
                ->latest('completed_at')
                ->first();
        }

        // If no specific criteria, ask user to choose
        $recentSyncs = SyncLog::where('sync_type', 'order_sync')
            ->where('status', 'completed')
            ->latest('completed_at')
            ->limit(10)
            ->get();

        if ($recentSyncs->isEmpty()) {
            $this->error('No completed order sync logs found.');
            return null;
        }

        $choices = $recentSyncs->map(function ($sync) {
            return "ID: {$sync->id} | Batch: {$sync->batch_id} | Completed: {$sync->completed_at}";
        })->toArray();

        $choice = $this->choice('Select a sync log to validate:', $choices);
        $selectedIndex = array_search($choice, $choices);
        
        return $recentSyncs->get($selectedIndex);
    }

    /**
     * Display validation results in a formatted way.
     */
    private function displayResults(array $results, float $executionTime): void
    {
        $this->info("Validation completed in {$executionTime} seconds");
        $this->newLine();

        // Overall status
        $status = $results['overall_status'];
        $statusColor = match($status) {
            'passed' => 'info',
            'failed' => 'error',
            'error' => 'error',
            default => 'comment'
        };

        $this->line("<{$statusColor}>Overall Status: " . strtoupper($status) . "</{$statusColor}>");
        $this->newLine();

        // Summary table
        $summaryData = [
            ['Data Integrity', $results['data_integrity']['status'] ?? 'N/A'],
            ['Sampling Validation', $results['sampling_validation']['status'] ?? 'N/A'],
            ['Business Logic', $results['business_logic']['status'] ?? 'N/A'],
            ['Relationship Consistency', $results['relationship_consistency']['status'] ?? 'N/A'],
        ];

        $this->table(['Validation Type', 'Status'], $summaryData);

        // Data integrity details
        if (isset($results['data_integrity']['record_counts'])) {
            $this->newLine();
            $this->line('<comment>Data Integrity Details:</comment>');
            
            $counts = $results['data_integrity']['record_counts'];
            $this->line("Orders - Remote: {$counts['remote']['orders']}, Local: {$counts['local']['orders']}");
            $this->line("Order Items - Remote: {$counts['remote']['order_items']}, Local: {$counts['local']['order_items']}");
        }

        // Sampling details
        if (isset($results['sampling_validation']['sample_size'])) {
            $this->newLine();
            $this->line('<comment>Sampling Validation Details:</comment>');
            
            $sampling = $results['sampling_validation'];
            $this->line("Sample Size: {$sampling['sample_size']}");
            $this->line("Matched Records: {$sampling['matched_records']}");
            $this->line("Mismatched Records: {$sampling['mismatched_records']}");
            
            if ($sampling['sample_size'] > 0) {
                $accuracy = round(($sampling['matched_records'] / $sampling['sample_size']) * 100, 2);
                $this->line("Accuracy: {$accuracy}%");
            }
        }

        // Issues
        if (!empty($results['issues'])) {
            $this->newLine();
            $this->error('Issues Found:');
            foreach ($results['issues'] as $issue) {
                $this->line("  • {$issue}");
            }
        }

        // Individual section issues
        foreach (['data_integrity', 'sampling_validation', 'business_logic', 'relationship_consistency'] as $section) {
            if (!empty($results[$section]['issues'])) {
                $this->newLine();
                $this->error(ucfirst(str_replace('_', ' ', $section)) . ' Issues:');
                foreach ($results[$section]['issues'] as $issue) {
                    $this->line("  • {$issue}");
                }
            }
        }

        // Mismatched orders in relationship consistency
        if (!empty($results['relationship_consistency']['order_item_counts']['mismatched_orders'])) {
            $this->newLine();
            $this->error('Order Item Count Mismatches:');
            $mismatches = $results['relationship_consistency']['order_item_counts']['mismatched_orders'];
            
            foreach (array_slice($mismatches, 0, 10) as $mismatch) {
                $this->line("  • Order {$mismatch['store_order_id']}: Remote={$mismatch['remote_count']}, Local={$mismatch['local_count']}");
            }
            
            if (count($mismatches) > 10) {
                $remaining = count($mismatches) - 10;
                $this->line("  ... and {$remaining} more mismatches");
            }
        }

        // Sampling mismatches
        if (!empty($results['sampling_validation']['mismatches'])) {
            $this->newLine();
            $this->error('Sample Record Mismatches:');
            $mismatches = $results['sampling_validation']['mismatches'];

            foreach (array_slice($mismatches, 0, 5) as $mismatch) {
                if (isset($mismatch['error'])) {
                    $this->line("  • Order {$mismatch['remote_order_id']}: {$mismatch['error']}");
                } elseif (isset($mismatch['mismatched_fields']) && !empty($mismatch['mismatched_fields'])) {
                    $this->line("  • Order {$mismatch['remote_order_id']}:");
                    foreach ($mismatch['mismatched_fields'] as $field => $values) {
                        $remoteVal = is_null($values['remote']) ? 'NULL' : $values['remote'];
                        $localVal = is_null($values['local']) ? 'NULL' : $values['local'];
                        $this->line("    - {$field}: remote='{$remoteVal}', local='{$localVal}'");
                    }
                } else {
                    $this->line("  • Order {$mismatch['remote_order_id']}: Field mismatches found");
                }
            }

            if (count($mismatches) > 5) {
                $remaining = count($mismatches) - 5;
                $this->line("  ... and {$remaining} more mismatches");
            }
        }

        $this->newLine();
        
        if ($status === 'passed') {
            $this->info('✓ Validation passed - Data synchronization appears to be consistent');
        } elseif ($status === 'failed') {
            $this->error('✗ Validation failed - Data inconsistencies detected');
        } else {
            $this->error('✗ Validation error - Unable to complete validation');
        }
    }
}
