<?php

declare(strict_types=1);

namespace App\Contracts;

use App\Models\SyncLog;

interface ProductSyncServiceInterface
{
    /**
     * Execute product synchronization.
     */
    public function sync(array $config = [], ?callable $progressCallback = null): SyncLog;

    /**
     * Re-execute sync for specified batch.
     * This method re-syncs the exact same data range as the original batch.
     */
    public function reSync(string $batchId): SyncLog;

    /**
     * Get sync statistics.
     */
    public function getSyncStatistics(): array;

    /**
     * Clean up old sync logs and records.
     */
    public function cleanup(int $days = null): array;

    /**
     * Execute product synchronization in queue with predefined batch ID.
     */
    public function syncInQueue(array $config = [], string $batchId = null, ?callable $progressCallback = null): SyncLog;

    /**
     * Re-sync a failed batch in queue.
     */
    public function reSyncInQueue(string $originalBatchId, array $config = [], ?callable $progressCallback = null): SyncLog;
}
