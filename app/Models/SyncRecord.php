<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

final class SyncRecord extends Model
{
    protected $fillable = [
        'batch_id', 'source_table', 'source_id', 'target_table', 'target_id',
        'status', 'source_data', 'transformed_data', 'error_message'
    ];

    protected $casts = [
        'source_data' => 'array',
        'transformed_data' => 'array',
    ];

    public function syncLog(): BelongsTo
    {
        return $this->belongsTo(SyncLog::class, 'batch_id', 'batch_id');
    }
}
