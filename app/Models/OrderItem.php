<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

final class OrderItem extends Model
{
    use HasFactory;
    protected $fillable = [
        'order_id',
        'store_order_item_id',
        'store_variant_id',
        'product_name',
        'variant_name',
        'quantity',
        'unit_price',
        'units_total',
        'adjustments_total',
        'total',
        'quantity_refunded',
    ];

    protected $casts = [
        'order_id' => 'integer',
        'store_order_item_id' => 'integer',
        'store_variant_id' => 'integer',
        'quantity' => 'integer',
        'unit_price' => 'integer',
        'units_total' => 'integer',
        'adjustments_total' => 'integer',
        'total' => 'integer',
        'quantity_refunded' => 'integer',
    ];

    /**
     * Get the order that owns this item.
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the unit price in the original currency format.
     */
    public function getUnitPriceInCurrencyAttribute(): float
    {
        return $this->unit_price / 100;
    }

    /**
     * Get the units total in the original currency format.
     */
    public function getUnitsTotalInCurrencyAttribute(): float
    {
        return $this->units_total / 100;
    }

    /**
     * Get the adjustments total in the original currency format.
     */
    public function getAdjustmentsTotalInCurrencyAttribute(): float
    {
        return $this->adjustments_total / 100;
    }

    /**
     * Get the total in the original currency format.
     */
    public function getTotalInCurrencyAttribute(): float
    {
        return $this->total / 100;
    }

    /**
     * Check if this item has been partially or fully refunded.
     */
    public function hasRefund(): bool
    {
        return $this->quantity_refunded > 0;
    }

    /**
     * Check if this item has been fully refunded.
     */
    public function isFullyRefunded(): bool
    {
        return $this->quantity_refunded >= $this->quantity;
    }

    /**
     * Get the remaining (non-refunded) quantity.
     */
    public function getRemainingQuantity(): int
    {
        return $this->quantity - $this->quantity_refunded;
    }

    /**
     * Get the refund percentage for this item.
     */
    public function getRefundPercentage(): float
    {
        if ($this->quantity === 0) {
            return 0.0;
        }

        return ($this->quantity_refunded / $this->quantity) * 100;
    }

    /**
     * Calculate the effective total after refunds.
     */
    public function getEffectiveTotal(): int
    {
        if ($this->quantity === 0) {
            return 0;
        }

        $remainingQuantity = $this->getRemainingQuantity();
        return (int) round(($this->total * $remainingQuantity) / $this->quantity);
    }

    /**
     * Get the effective total in currency format.
     */
    public function getEffectiveTotalInCurrencyAttribute(): float
    {
        return $this->getEffectiveTotal() / 100;
    }

    /**
     * Scope to filter items by product variant.
     */
    public function scopeByVariant($query, int $variantId)
    {
        return $query->where('store_variant_id', $variantId);
    }

    /**
     * Scope to get items with refunds.
     */
    public function scopeWithRefunds($query)
    {
        return $query->where('quantity_refunded', '>', 0);
    }

    /**
     * Scope to get fully refunded items.
     */
    public function scopeFullyRefunded($query)
    {
        return $query->whereRaw('quantity_refunded >= quantity');
    }

    /**
     * Scope to get partially refunded items.
     */
    public function scopePartiallyRefunded($query)
    {
        return $query->where('quantity_refunded', '>', 0)
                    ->whereRaw('quantity_refunded < quantity');
    }
}
