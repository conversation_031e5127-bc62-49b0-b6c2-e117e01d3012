<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Carbon;

final class Invitation extends Model
{
    use HasFactory, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'model_type',
        'model_id',
        'role',
        'created_by_user_id',
        'expires_at',
        'max_uses',
        'uses',
        'email_restriction',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'expires_at' => 'datetime',
            'max_uses' => 'integer',
            'uses' => 'integer',
        ];
    }

    /**
     * Get the model that the invitation is for.
     */
    public function model(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the user who created the invitation.
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by_user_id');
    }

    /**
     * Check if the invitation is expired.
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * Check if the invitation has reached its usage limit.
     */
    public function hasReachedUsageLimit(): bool
    {
        return $this->uses >= $this->max_uses;
    }

    /**
     * Check if the invitation is valid (not expired and not reached usage limit).
     */
    public function isValid(): bool
    {
        return !$this->isExpired() && !$this->hasReachedUsageLimit();
    }

    /**
     * Check if the email is allowed to use this invitation.
     */
    public function isEmailAllowed(?string $email): bool
    {
        if (!$this->email_restriction) {
            return true;
        }

        return $email === $this->email_restriction;
    }

    /**
     * Increment the usage count.
     */
    public function incrementUsage(): void
    {
        $this->increment('uses');
    }

    /**
     * Check if the role is a system role (guard_name = 'system').
     */
    public function isSystemRole(): bool
    {
        return in_array($this->role, ['root', 'admin']);
    }

    /**
     * Scope to get valid invitations.
     */
    public function scopeValid($query)
    {
        return $query->where('expires_at', '>', now())
                    ->whereColumn('uses', '<', 'max_uses');
    }

    /**
     * Boot the model.
     */
    protected static function boot(): void
    {
        parent::boot();

        static::creating(function (Invitation $invitation) {
            // Set default expiration to one week from now if not set
            if (!$invitation->expires_at) {
                $invitation->expires_at = Carbon::now()->addWeek();
            }
        });
    }
}
