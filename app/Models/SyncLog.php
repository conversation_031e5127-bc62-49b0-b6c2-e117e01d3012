<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

final class SyncLog extends Model
{
    use HasFactory;
    protected $fillable = [
        'sync_type', 'batch_id', 'status', 'started_at', 'completed_at',
        'total_records', 'processed_records', 'success_records', 'failed_records',
        'summary', 'error_message', 'sync_config', 'validation_results'
    ];

    protected $casts = [
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'summary' => 'array',
        'sync_config' => 'array',
        'validation_results' => 'array',
    ];

    public function records(): HasMany
    {
        return $this->hasMany(SyncRecord::class, 'batch_id', 'batch_id');
    }

    public function getProgressPercentageAttribute(): float
    {
        if ($this->total_records === 0) {
            return 0;
        }
        return round(($this->processed_records / $this->total_records) * 100, 2);
    }
}
