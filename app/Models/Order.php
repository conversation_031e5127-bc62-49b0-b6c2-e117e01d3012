<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

final class Order extends Model
{
    use HasFactory;
    protected $fillable = [
        'store_order_id',
        'order_number',
        'state',
        'completed_at',
        'items_total',
        'adjustments_total',
        'total_amount',
        'currency_code',
        'payment_state',
        'shipping_country',
        'customer_id',
        'refund_total',
        'refund_comment',
        'refund_status',
        'refunded_at',
        'store_updated_at',
    ];

    protected $casts = [
        'completed_at' => 'datetime',
        'refunded_at' => 'datetime',
        'store_updated_at' => 'datetime',
        'items_total' => 'integer',
        'adjustments_total' => 'integer',
        'total_amount' => 'integer',
        'refund_total' => 'integer',
        'store_order_id' => 'integer',
        'customer_id' => 'integer',
    ];

    /**
     * Get the order items for this order.
     */
    public function orderItems(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * Get the total amount in the original currency format (e.g., dollars).
     */
    public function getTotalAmountInCurrencyAttribute(): float
    {
        return $this->total_amount / 100;
    }

    /**
     * Get the items total in the original currency format.
     */
    public function getItemsTotalInCurrencyAttribute(): float
    {
        return $this->items_total / 100;
    }

    /**
     * Get the adjustments total in the original currency format.
     */
    public function getAdjustmentsTotalInCurrencyAttribute(): float
    {
        return $this->adjustments_total / 100;
    }

    /**
     * Get the refund total in the original currency format.
     */
    public function getRefundTotalInCurrencyAttribute(): ?float
    {
        return $this->refund_total ? $this->refund_total / 100 : null;
    }

    /**
     * Check if the order has been refunded.
     */
    public function hasRefund(): bool
    {
        return $this->refund_status === 'success';
    }

    /**
     * Check if the order is completed.
     */
    public function isCompleted(): bool
    {
        return $this->state === 'completed';
    }

    /**
     * Check if the order is cancelled.
     */
    public function isCancelled(): bool
    {
        return $this->state === 'cancelled';
    }

    /**
     * Check if the payment is completed.
     */
    public function isPaymentCompleted(): bool
    {
        return $this->payment_state === 'completed';
    }

    /**
     * Get the total quantity of items in this order.
     */
    public function getTotalQuantity(): int
    {
        return $this->orderItems()->sum('quantity');
    }

    /**
     * Get the total refunded quantity for this order.
     */
    public function getTotalRefundedQuantity(): int
    {
        return $this->orderItems()->sum('quantity_refunded');
    }

    /**
     * Scope to filter orders by date range.
     */
    public function scopeCompletedBetween($query, $startDate, $endDate)
    {
        return $query->whereBetween('completed_at', [$startDate, $endDate]);
    }

    /**
     * Scope to filter orders by country.
     */
    public function scopeByCountry($query, string $countryCode)
    {
        return $query->where('shipping_country', $countryCode);
    }

    /**
     * Scope to filter orders by state.
     */
    public function scopeByState($query, string $state)
    {
        return $query->where('state', $state);
    }

    /**
     * Scope to filter orders by payment state.
     */
    public function scopeByPaymentState($query, string $paymentState)
    {
        return $query->where('payment_state', $paymentState);
    }

    /**
     * Scope to get orders with refunds.
     */
    public function scopeWithRefunds($query)
    {
        return $query->where('refund_status', 'success');
    }
}
