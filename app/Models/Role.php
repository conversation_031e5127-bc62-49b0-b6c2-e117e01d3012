<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\Permission\Models\Role as SpatieRole;

final class Role extends SpatieRole
{
    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'guard_name',
        'organisation_id',
    ];

    /**
     * Get the organisation that owns the role.
     */
    public function organisation(): BelongsTo
    {
        return $this->belongsTo(Organisation::class);
    }

    /**
     * Scope a query to only include roles for a specific organisation.
     */
    public function scopeForOrganisation($query, int $organisationId)
    {
        return $query->where('organisation_id', $organisationId);
    }

    /**
     * Check if the role belongs to a specific organisation.
     */
    public function belongsToOrganisation(int $organisationId): bool
    {
        return $this->organisation_id === $organisationId;
    }
}
