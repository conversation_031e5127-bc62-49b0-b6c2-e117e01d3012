<?php

namespace App\Providers;

use App\Contracts\ProductSyncServiceInterface;
use App\Contracts\OrderSyncServiceInterface;
use App\Services\ProductSyncService;
use App\Services\OrderSyncService;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->bind(ProductSyncServiceInterface::class, ProductSyncService::class);
        $this->app->bind(OrderSyncServiceInterface::class, OrderSyncService::class);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
    }
}
