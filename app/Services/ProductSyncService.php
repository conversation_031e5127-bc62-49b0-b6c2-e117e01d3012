<?php

declare(strict_types=1);

namespace App\Services;

use App\Contracts\ProductSyncServiceInterface;
use App\Models\SyncLog;
use App\Models\SyncRecord;
use App\Models\Product;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Exception;

final class ProductSyncService implements ProductSyncServiceInterface
{
    private $remoteConnection;
    private $currentBatchId;
    private $syncLog;
    private $config;
    private $progressCallback;

    public function __construct()
    {
        $this->remoteConnection = DB::connection('store');
        $this->config = config('sync');
    }

    /**
     * Execute product synchronization.
     */
    public function sync(array $config = [], ?callable $progressCallback = null): SyncLog
    {
        $this->currentBatchId = $this->generateBatchId();
        $this->progressCallback = $progressCallback;

        Log::info(__('sync.product_sync_started'), [
            'batch_id' => $this->currentBatchId,
            'config' => $config,
        ]);

        // Create sync log
        $this->syncLog = SyncLog::create([
            'sync_type' => 'product_sync',
            'batch_id' => $this->currentBatchId,
            'status' => 'pending',
            'started_at' => now(),
            'sync_config' => $config,
        ]);

        try {
            $this->syncLog->update(['status' => 'processing']);

            // Get remote data
            $remoteVariants = $this->getRemoteProducts($config);

            // Update sync log with total records and actual sync range info
            $syncRangeInfo = $this->getSyncRangeInfo($config);
            $this->syncLog->update([
                'total_records' => count($remoteVariants),
                'sync_config' => array_merge($config, $syncRangeInfo),
            ]);

            Log::info(__('sync.records_processed', ['count' => count($remoteVariants)]), [
                'batch_id' => $this->currentBatchId,
            ]);

            // Process each variant (batch processing to avoid memory issues)
            $batchSize = (int) ($config['batch_size'] ?? $this->config['batch_size'] ?? 100);
            $chunks = array_chunk($remoteVariants, $batchSize);
            $totalChunks = count($chunks);
            $processedChunks = 0;
            $startTime = microtime(true);

            foreach ($chunks as $chunkIndex => $chunk) {
                foreach ($chunk as $remoteVariant) {
                    $this->processProduct($remoteVariant);
                }

                $processedChunks++;

                // Report progress after each batch
                if ($this->progressCallback) {
                    $elapsedTime = microtime(true) - $startTime;
                    call_user_func($this->progressCallback, $processedChunks, $totalChunks, $elapsedTime);
                }

                // Clean up memory
                if (function_exists('gc_collect_cycles')) {
                    gc_collect_cycles();
                }
            }

            // Complete sync
            $this->completeSyncLog();

        } catch (Exception $e) {
            $this->handleSyncError($e);
        }

        return $this->syncLog->fresh();
    }

    /**
     * Get remote product dataimage
     */
    private function getRemoteProducts(array $config): array
    {
        $query = $this->remoteConnection->table('sylius_product_variant as pv')
            ->leftJoin('sylius_product as p', 'pv.product_id', '=', 'p.id')
            ->leftJoin('sylius_product_variant_translation as pvt', function($join) {
                $join->on('pv.id', '=', 'pvt.translatable_id')
                     ->where('pvt.locale', '=', 'en_US');
            })
            ->leftJoin('sylius_product_translation as pt', function($join) {
                $join->on('p.id', '=', 'pt.translatable_id')
                     ->where('pt.locale', '=', 'en_US');
            })
            ->select([
                'pv.id as variant_id',
                'pv.code as variant_code',
                'pv.enabled as variant_enabled',
                'pv.updated_at as variant_updated_at',
                'pvt.name as variant_name',
                'p.id as product_id',
                'p.owner_id',
                'p.sku',
                'p.release_date',
                'p.updated_at as product_updated_at',
                'pt.name as product_name',
                'pt.slug as product_slug'
            ]);

        // Handle time-based filtering
        if (isset($config['force_time_range']) && $config['force_time_range']) {
            // For re-sync: use exact time range from original sync
            $startTime = $config['start_time'];
            $endTime = $config['end_time'];
            $query->where(function($q) use ($startTime, $endTime) {
                $q->whereBetween('pv.updated_at', [$startTime, $endTime])
                  ->orWhereBetween('p.updated_at', [$startTime, $endTime]);
            });
        } elseif (isset($config['incremental']) && $config['incremental']) {
            // Incremental sync: only get updated data since last successful sync
            $lastSyncTime = $this->getLastSuccessfulSyncTime();
            if ($lastSyncTime) {
                $query->where(function($q) use ($lastSyncTime) {
                    $q->where('pv.updated_at', '>', $lastSyncTime)
                      ->orWhere('p.updated_at', '>', $lastSyncTime);
                });
            }
        }

        $variants = $query->get()->toArray();

        // Get additional data for each variant
        foreach ($variants as &$variant) {
            // Convert object to array if needed (for testing)
            if (is_object($variant)) {
                $variant = (array) $variant;
            }

            // Get pricing data
            $variant['pricing'] = $this->getVariantPricing($variant['variant_id']);

            // Get price history
            $variant['price_history'] = $this->getVariantPriceHistory($variant['variant_id']);

            // Get package image
            $variant['package'] = $this->getProductPackageImage($variant['product_id']);
        }

        return $variants;
    }

    /**
     * Get variant pricing information from sylius_channel_pricing.
     */
    private function getVariantPricing(int $variantId): array
    {
        $pricing = $this->remoteConnection->table('sylius_channel_pricing')
            ->where('product_variant_id', $variantId)
            ->select([
                'price',
                'original_price',
                'minimum_price',
                'lowest_price_before_discount'
            ])
            ->first();

        if (!$pricing) {
            return [
                'price' => null,
                'original_price' => null,
                'minimum_price' => null,
                'lowest_price_before_discount' => null
            ];
        }

        // Convert object to array if needed (for testing)
        return is_object($pricing) ? (array) $pricing : $pricing;
    }

    /**
     * Get variant price history from sylius_channel_pricing_log_entry.
     */
    private function getVariantPriceHistory(int $variantId): array
    {
        $history = $this->remoteConnection->table('sylius_channel_pricing_log_entry as log')
            ->leftJoin('sylius_channel_pricing as cp', 'log.channel_pricing_id', '=', 'cp.id')
            ->where('cp.product_variant_id', $variantId)
            ->select([
                'log.price',
                'log.original_price',
                'log.logged_at'
            ])
            ->orderBy('log.logged_at', 'desc')
            ->get()
            ->toArray();

        // Convert objects to arrays if needed (for testing)
        return array_map(function($entry) {
            return is_object($entry) ? (array) $entry : $entry;
        }, $history);
    }

    /**
     * Get product package image with type TAIL_PACKAGE_THUMBNAIL_PRODUCT or TAIL_PACKAGE_THUMBNAIL_PRODUCT_en_US.
     */
    private function getProductPackageImage(int $productId): ?string
    {
        $image = $this->remoteConnection->table('sylius_product_image')
            ->where('owner_id', $productId)
            ->where(function($query) {
                $query->where('type', 'TAIL_PACKAGE_THUMBNAIL_PRODUCT')
                      ->orWhere('type', 'TAIL_PACKAGE_THUMBNAIL_PRODUCT_en_US');
            })
            ->select('path')
            ->first();

        if (!$image) {
            return null;
        }

        return $image->path;
    }

    /**
     * Process single product variant.
     */
    private function processProduct(array $remoteVariant): void
    {
        $syncRecord = SyncRecord::create([
            'batch_id' => $this->currentBatchId,
            'source_table' => 'sylius_product_variant',
            'source_id' => (string) $remoteVariant['variant_id'],
            'target_table' => 'products',
            'status' => 'pending',
            'source_data' => $remoteVariant,
        ]);

        try {
            // Data transformation
            $transformedData = $this->transformProductData($remoteVariant);

            // Check if update is needed (based on variant_updated_at or product_updated_at)
            $existingProduct = Product::where('store_variant_id', $remoteVariant['variant_id'])->first();

            if ($existingProduct) {
                $latestRemoteUpdate = max(
                    $remoteVariant['variant_updated_at'] ?? '1970-01-01 00:00:00',
                    $remoteVariant['product_updated_at'] ?? '1970-01-01 00:00:00'
                );

                $latestLocalUpdate = max(
                    $existingProduct->store_variant_updated_at?->format('Y-m-d H:i:s') ?? '1970-01-01 00:00:00',
                    $existingProduct->store_product_updated_at?->format('Y-m-d H:i:s') ?? '1970-01-01 00:00:00'
                );

                if ($latestLocalUpdate >= $latestRemoteUpdate) {
                    // Data not updated, skip
                    $syncRecord->update([
                        'status' => 'skipped',
                        'transformed_data' => $transformedData,
                    ]);
                    return;
                }
            }

            // Save or update product
            $product = Product::updateOrCreate(
                ['store_variant_id' => $remoteVariant['variant_id']],
                $transformedData
            );

            $syncRecord->update([
                'status' => 'success',
                'target_id' => (string) $product->id,
                'transformed_data' => $transformedData,
            ]);

        } catch (Exception $e) {
            $syncRecord->update([
                'status' => 'failed',
                'error_message' => $e->getMessage(),
            ]);

            Log::error('Product sync failed', [
                'batch_id' => $this->currentBatchId,
                'variant_id' => $remoteVariant['variant_id'],
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Data transformation.
     */
    private function transformProductData(array $remoteVariant): array
    {
        return [
            'store_variant_id' => $remoteVariant['variant_id'],
            'owner_id' => $remoteVariant['owner_id'], // Use remote owner_id as is (can be null)
            'sku' => $remoteVariant['sku'],
            'code' => $remoteVariant['variant_code'],
            'name' => $remoteVariant['product_name'] ?? $remoteVariant['variant_name'] ?? $remoteVariant['variant_code'],
            'slug' => $remoteVariant['product_slug'] ?? Str::slug($remoteVariant['product_name'] ?? $remoteVariant['variant_name'] ?? $remoteVariant['variant_code']),
            'enabled' => (bool) $remoteVariant['variant_enabled'],
            'release_date' => $remoteVariant['release_date'] ? now()->parse($remoteVariant['release_date']) : null,
            'package' => $remoteVariant['package'],
            'current_price' => $remoteVariant['pricing']['price'] ?? null,
            'original_price' => $remoteVariant['pricing']['original_price'] ?? null,
            'minimum_price' => $remoteVariant['pricing']['minimum_price'] ?? null,
            'lowest_price_before_discount' => $remoteVariant['pricing']['lowest_price_before_discount'] ?? null,
            'price_history' => $remoteVariant['price_history'],
            'store_product_updated_at' => $remoteVariant['product_updated_at'] ? now()->parse($remoteVariant['product_updated_at']) : null,
            'store_variant_updated_at' => $remoteVariant['variant_updated_at'] ? now()->parse($remoteVariant['variant_updated_at']) : null,
        ];
    }



    /**
     * Complete sync log.
     */
    private function completeSyncLog(): void
    {
        $records = SyncRecord::where('batch_id', $this->currentBatchId)->get();

        $summary = [
            'total' => $records->count(),
            'success' => $records->where('status', 'success')->count(),
            'failed' => $records->where('status', 'failed')->count(),
            'skipped' => $records->where('status', 'skipped')->count(),
        ];

        $this->syncLog->update([
            'status' => 'completed',
            'completed_at' => now(),
            'processed_records' => $summary['total'],
            'success_records' => $summary['success'],
            'failed_records' => $summary['failed'],
            'summary' => $summary,
        ]);

        Log::info(__('sync.product_sync_completed'), [
            'batch_id' => $this->currentBatchId,
            'summary' => $summary,
        ]);
    }

    /**
     * Handle sync error.
     */
    private function handleSyncError(Exception $e): void
    {
        $this->syncLog->update([
            'status' => 'failed',
            'completed_at' => now(),
            'error_message' => $e->getMessage(),
        ]);

        Log::error(__('sync.product_sync_failed'), [
            'batch_id' => $this->currentBatchId,
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString(),
        ]);
    }

    /**
     * Generate batch ID.
     */
    private function generateBatchId(): string
    {
        return 'product_sync_' . date('YmdHis') . '_' . Str::random(8);
    }

    /**
     * Get last successful sync time.
     */
    private function getLastSuccessfulSyncTime(): ?string
    {
        $lastSync = SyncLog::where('sync_type', 'product_sync')
            ->where('status', 'completed')
            ->orderBy('completed_at', 'desc')
            ->first();

        return $lastSync ? $lastSync->completed_at->format('Y-m-d H:i:s') : null;
    }

    /**
     * Check if a full sync has ever been completed successfully.
     */
    public function hasCompletedFullSync(): bool
    {
        return SyncLog::where('sync_type', 'product_sync')
            ->where('status', 'completed')
            ->whereJsonContains('sync_config->sync_type_detail', 'full')
            ->exists();
    }

    /**
     * Check if incremental sync can be performed.
     * Requires at least one successful full sync to have been completed.
     */
    public function canPerformIncrementalSync(): bool
    {
        return $this->hasCompletedFullSync();
    }

    /**
     * Get sync range information for storing in sync_config.
     * This allows us to reproduce the exact same sync later.
     */
    private function getSyncRangeInfo(array $config): array
    {
        $rangeInfo = [];

        if (isset($config['incremental']) && $config['incremental']) {
            // For incremental sync, record the time range being synced
            $lastSyncTime = $this->getLastSuccessfulSyncTime();
            $currentTime = now()->format('Y-m-d H:i:s');

            $rangeInfo['sync_start_time'] = $lastSyncTime ?: '1970-01-01 00:00:00';
            $rangeInfo['sync_end_time'] = $currentTime;
            $rangeInfo['sync_type_detail'] = 'incremental';
        } else {
            // For full sync, record that it was a full sync at this time
            $rangeInfo['sync_start_time'] = '1970-01-01 00:00:00';
            $rangeInfo['sync_end_time'] = now()->format('Y-m-d H:i:s');
            $rangeInfo['sync_type_detail'] = 'full';
        }

        return $rangeInfo;
    }

    /**
     * Re-execute sync for specified batch.
     * This method re-syncs the exact same data range as the original batch.
     */
    public function reSync(string $batchId): SyncLog
    {
        $originalLog = SyncLog::where('batch_id', $batchId)->firstOrFail();
        $originalConfig = $originalLog->sync_config ?? [];

        // For re-sync, we need to use the exact same data range as the original sync
        // If the original sync was incremental, we need to use the same time range
        if (isset($originalConfig['sync_start_time']) && isset($originalConfig['sync_end_time'])) {
            // Use the original sync's time range for exact re-sync
            $reSyncConfig = array_merge($originalConfig, [
                'force_time_range' => true,
                'start_time' => $originalConfig['sync_start_time'],
                'end_time' => $originalConfig['sync_end_time'],
            ]);
        } else {
            // For non-incremental syncs, we can't exactly reproduce the same range
            // So we'll do a full sync with the same configuration
            $reSyncConfig = $originalConfig;
        }

        return $this->sync($reSyncConfig);
    }

    /**
     * Get sync statistics.
     */
    public function getSyncStatistics(): array
    {
        $totalSyncs = SyncLog::where('sync_type', 'product_sync')->count();
        $successfulSyncs = SyncLog::where('sync_type', 'product_sync')
            ->where('status', 'completed')
            ->count();
        $failedSyncs = SyncLog::where('sync_type', 'product_sync')
            ->where('status', 'failed')
            ->count();

        $lastSync = SyncLog::where('sync_type', 'product_sync')
            ->orderBy('created_at', 'desc')
            ->first();

        return [
            'total_syncs' => $totalSyncs,
            'successful_syncs' => $successfulSyncs,
            'failed_syncs' => $failedSyncs,
            'success_rate' => $totalSyncs > 0 ? round(($successfulSyncs / $totalSyncs) * 100, 2) : 0,
            'last_sync' => $lastSync ? [
                'batch_id' => $lastSync->batch_id,
                'status' => $lastSync->status,
                'created_at' => $lastSync->created_at->toISOString(),
                'completed_at' => $lastSync->completed_at?->toISOString(),
            ] : null,
        ];
    }

    /**
     * Clean up old sync logs and records.
     */
    public function cleanup(int $days = null): array
    {
        $days = $days ?? $this->config['log_retention_days'] ?? 30;
        $cutoffDate = now()->subDays($days);

        // Delete old sync records first (foreign key constraint)
        $recordsDeleted = SyncRecord::whereHas('syncLog', function ($query) use ($cutoffDate) {
            $query->where('created_at', '<', $cutoffDate);
        })->delete();

        // Delete old sync logs
        $logsDeleted = SyncLog::where('sync_type', 'product_sync')
            ->where('created_at', '<', $cutoffDate)
            ->delete();

        Log::info('Sync cleanup completed', [
            'days' => $days,
            'records_deleted' => $recordsDeleted,
            'logs_deleted' => $logsDeleted,
        ]);

        return [
            'records_deleted' => $recordsDeleted,
            'logs_deleted' => $logsDeleted,
            'cutoff_date' => $cutoffDate->toISOString(),
        ];
    }

    /**
     * Execute product synchronization in queue with predefined batch ID.
     */
    public function syncInQueue(array $config = [], string $batchId = null, ?callable $progressCallback = null): SyncLog
    {
        $this->currentBatchId = $batchId ?? $this->generateBatchId();
        $this->progressCallback = $progressCallback;

        Log::info(__('sync.product_sync_started'), [
            'batch_id' => $this->currentBatchId,
            'config' => $config,
            'queue_mode' => true,
        ]);

        // Create sync log
        $this->syncLog = SyncLog::create([
            'sync_type' => 'product_sync',
            'batch_id' => $this->currentBatchId,
            'status' => 'pending',
            'started_at' => now(),
            'sync_config' => $config,
        ]);

        try {
            $this->syncLog->update(['status' => 'processing']);

            // Get remote data
            $remoteVariants = $this->getRemoteProducts($config);

            // Update sync log with total records and actual sync range info
            $syncRangeInfo = $this->getSyncRangeInfo($config);
            $this->syncLog->update([
                'total_records' => count($remoteVariants),
                'sync_config' => array_merge($config, $syncRangeInfo),
            ]);

            Log::info(__('sync.records_processed', ['count' => count($remoteVariants)]), [
                'batch_id' => $this->currentBatchId,
            ]);

            // Process each variant (batch processing to avoid memory issues)
            $batchSize = (int) ($config['batch_size'] ?? $this->config['batch_size'] ?? 100);
            $chunks = array_chunk($remoteVariants, $batchSize);
            $totalChunks = count($chunks);
            $processedChunks = 0;
            $startTime = microtime(true);

            foreach ($chunks as $chunkIndex => $chunk) {
                foreach ($chunk as $remoteVariant) {
                    $this->processProduct($remoteVariant);
                }

                $processedChunks++;

                // Report progress after each batch
                if ($this->progressCallback) {
                    $elapsedTime = microtime(true) - $startTime;
                    call_user_func($this->progressCallback, $processedChunks, $totalChunks, $elapsedTime);
                }

                // Clean up memory
                if (function_exists('gc_collect_cycles')) {
                    gc_collect_cycles();
                }
            }

            // Complete sync
            $this->completeSyncLog();

        } catch (Exception $e) {
            $this->handleSyncError($e);
        }

        return $this->syncLog->fresh();
    }

    /**
     * Re-sync a failed batch in queue.
     */
    public function reSyncInQueue(string $originalBatchId, array $config = [], ?callable $progressCallback = null): SyncLog
    {
        $originalLog = SyncLog::where('batch_id', $originalBatchId)->firstOrFail();
        $originalConfig = $originalLog->sync_config ?? [];

        // For re-sync, we need to use the exact same data range as the original sync
        // If the original sync was incremental, we need to use the same time range
        if (isset($originalConfig['sync_start_time']) && isset($originalConfig['sync_end_time'])) {
            // Use the original sync's time range for exact re-sync
            $reSyncConfig = array_merge($originalConfig, $config, [
                'force_time_range' => true,
                'start_time' => $originalConfig['sync_start_time'],
                'end_time' => $originalConfig['sync_end_time'],
            ]);
        } else {
            // For non-incremental syncs, we can't exactly reproduce the same range
            // So we'll do a full sync with the same configuration
            $reSyncConfig = array_merge($originalConfig, $config);
        }

        return $this->syncInQueue($reSyncConfig, null, $progressCallback);
    }
}
