<?php

declare(strict_types=1);

namespace App\Services;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

/**
 * Order Status Report Service
 * 
 * Handles generation of order status distribution reports including
 * status breakdowns, completion rates, and status transition analysis.
 */
final class OrderStatusReportService
{
    /**
     * Generate order status report
     */
    public function generateReport(Builder $query, array $filters): array
    {
        $groupBy = $filters['group_by'] ?? 'day';

        // Get status summary data
        $summary = $this->getStatusSummaryData(clone $query);

        // Get grouped status data
        $chartData = $this->getGroupedStatusData(clone $query, $groupBy, $filters);

        // Get status distribution data
        $statusDistribution = $this->getStatusDistributionData(clone $query);

        // Get payment status distribution
        $paymentStatusDistribution = $this->getPaymentStatusDistributionData(clone $query);

        // Get region status data
        $regionData = $this->getRegionStatusData(clone $query);

        return [
            'summary' => $summary,
            'chart_data' => $chartData->toArray(),
            'status_distribution' => $statusDistribution->toArray(),
            'payment_status_distribution' => $paymentStatusDistribution->toArray(),
            'region_data' => $regionData->toArray(),
            'meta' => [
                'period' => $filters['start_date'] . ' to ' . $filters['end_date'],
                'group_by' => $groupBy,
                'total_records' => $chartData->count(),
                'timezone' => $filters['timezone'] ?? config('app.timezone'),
            ]
        ];
    }

    /**
     * Get status summary data
     */
    private function getStatusSummaryData(Builder $query): array
    {
        $summary = $query->selectRaw('
            COUNT(*) as total_orders,
            SUM(CASE WHEN state = "completed" THEN 1 ELSE 0 END) as completed_orders,
            SUM(CASE WHEN state = "cancelled" THEN 1 ELSE 0 END) as cancelled_orders,
            SUM(CASE WHEN state = "processing" THEN 1 ELSE 0 END) as processing_orders,
            SUM(CASE WHEN state = "pending" THEN 1 ELSE 0 END) as pending_orders,
            SUM(CASE WHEN payment_state = "completed" THEN 1 ELSE 0 END) as paid_orders,
            SUM(CASE WHEN payment_state = "pending" THEN 1 ELSE 0 END) as pending_payment_orders,
            SUM(CASE WHEN payment_state = "failed" THEN 1 ELSE 0 END) as failed_payment_orders,
            SUM(CASE WHEN payment_state = "cancelled" THEN 1 ELSE 0 END) as cancelled_payment_orders
        ')->first();

        $completionRate = ($summary->total_orders ?? 0) > 0
            ? round(((float) ($summary->completed_orders ?? 0) / (float) ($summary->total_orders ?? 0)) * 100, 2)
            : 0;

        $cancellationRate = ($summary->total_orders ?? 0) > 0
            ? round(((float) ($summary->cancelled_orders ?? 0) / (float) ($summary->total_orders ?? 0)) * 100, 2)
            : 0;

        $paymentSuccessRate = ($summary->total_orders ?? 0) > 0
            ? round(((float) ($summary->paid_orders ?? 0) / (float) ($summary->total_orders ?? 0)) * 100, 2)
            : 0;

        return [
            'total_orders' => (int) ($summary->total_orders ?? 0),
            'completed_orders' => (int) ($summary->completed_orders ?? 0),
            'cancelled_orders' => (int) ($summary->cancelled_orders ?? 0),
            'processing_orders' => (int) ($summary->processing_orders ?? 0),
            'pending_orders' => (int) ($summary->pending_orders ?? 0),
            'paid_orders' => (int) ($summary->paid_orders ?? 0),
            'pending_payment_orders' => (int) ($summary->pending_payment_orders ?? 0),
            'failed_payment_orders' => (int) ($summary->failed_payment_orders ?? 0),
            'cancelled_payment_orders' => (int) ($summary->cancelled_payment_orders ?? 0),
            'completion_rate' => $completionRate,
            'cancellation_rate' => $cancellationRate,
            'payment_success_rate' => $paymentSuccessRate,
        ];
    }

    /**
     * Get grouped status data by time period
     */
    private function getGroupedStatusData(Builder $query, string $groupBy, array $filters): \Illuminate\Support\Collection
    {
        $dateFormat = $this->getDateFormat($groupBy);
        $timezone = $filters['timezone'] ?? config('app.timezone');

        return $query->selectRaw("
            DATE_FORMAT(CONVERT_TZ(completed_at, '+00:00', ?), ?) as period,
            COUNT(*) as total_orders,
            SUM(CASE WHEN state = 'completed' THEN 1 ELSE 0 END) as completed_orders,
            SUM(CASE WHEN state = 'cancelled' THEN 1 ELSE 0 END) as cancelled_orders,
            SUM(CASE WHEN state = 'processing' THEN 1 ELSE 0 END) as processing_orders,
            SUM(CASE WHEN state = 'pending' THEN 1 ELSE 0 END) as pending_orders,
            SUM(CASE WHEN payment_state = 'completed' THEN 1 ELSE 0 END) as paid_orders,
            shipping_country
        ", [$timezone, $dateFormat])
        ->groupBy(['period', 'shipping_country'])
        ->orderBy('period')
        ->get()
        ->map(function ($item) {
            $completionRate = ($item->total_orders ?? 0) > 0
                ? round(((float) ($item->completed_orders ?? 0) / (float) ($item->total_orders ?? 0)) * 100, 2)
                : 0;

            $cancellationRate = ($item->total_orders ?? 0) > 0
                ? round(((float) ($item->cancelled_orders ?? 0) / (float) ($item->total_orders ?? 0)) * 100, 2)
                : 0;

            return [
                'period' => $item->period,
                'total_orders' => (int) ($item->total_orders ?? 0),
                'completed_orders' => (int) ($item->completed_orders ?? 0),
                'cancelled_orders' => (int) ($item->cancelled_orders ?? 0),
                'processing_orders' => (int) ($item->processing_orders ?? 0),
                'pending_orders' => (int) ($item->pending_orders ?? 0),
                'paid_orders' => (int) ($item->paid_orders ?? 0),
                'completion_rate' => $completionRate,
                'cancellation_rate' => $cancellationRate,
                'country' => $item->shipping_country,
            ];
        });
    }

    /**
     * Get status distribution data
     */
    private function getStatusDistributionData(Builder $query): \Illuminate\Support\Collection
    {
        return $query->selectRaw('
            state,
            COUNT(*) as order_count,
            SUM(total_amount) as total_amount
        ')
        ->groupBy('state')
        ->orderByDesc('order_count')
        ->get()
        ->map(function ($item) {
            return [
                'status' => $item->state,
                'order_count' => (int) ($item->order_count ?? 0),
                'total_amount' => (int) ($item->total_amount ?? 0),
                'total_amount_formatted' => number_format(($item->total_amount ?? 0) / 100, 2),
            ];
        });
    }

    /**
     * Get payment status distribution data
     */
    private function getPaymentStatusDistributionData(Builder $query): \Illuminate\Support\Collection
    {
        return $query->selectRaw('
            payment_state,
            COUNT(*) as order_count,
            SUM(total_amount) as total_amount
        ')
        ->whereNotNull('payment_state')
        ->groupBy('payment_state')
        ->orderByDesc('order_count')
        ->get()
        ->map(function ($item) {
            return [
                'payment_status' => $item->payment_state,
                'order_count' => (int) ($item->order_count ?? 0),
                'total_amount' => (int) ($item->total_amount ?? 0),
                'total_amount_formatted' => number_format(($item->total_amount ?? 0) / 100, 2),
            ];
        });
    }

    /**
     * Get region status distribution data
     */
    private function getRegionStatusData(Builder $query): \Illuminate\Support\Collection
    {
        return $query->selectRaw('
            shipping_country,
            COUNT(*) as total_orders,
            SUM(CASE WHEN state = "completed" THEN 1 ELSE 0 END) as completed_orders,
            SUM(CASE WHEN state = "cancelled" THEN 1 ELSE 0 END) as cancelled_orders,
            SUM(CASE WHEN state = "processing" THEN 1 ELSE 0 END) as processing_orders,
            SUM(CASE WHEN state = "pending" THEN 1 ELSE 0 END) as pending_orders,
            SUM(CASE WHEN payment_state = "completed" THEN 1 ELSE 0 END) as paid_orders,
            SUM(total_amount) as total_amount
        ')
        ->whereNotNull('shipping_country')
        ->groupBy('shipping_country')
        ->orderByDesc('total_orders')
        ->get()
        ->map(function ($item) {
            $completionRate = ($item->total_orders ?? 0) > 0
                ? round((($item->completed_orders ?? 0) / ($item->total_orders ?? 0)) * 100, 2)
                : 0;

            $cancellationRate = ($item->total_orders ?? 0) > 0
                ? round((($item->cancelled_orders ?? 0) / ($item->total_orders ?? 0)) * 100, 2)
                : 0;

            $paymentSuccessRate = ($item->total_orders ?? 0) > 0
                ? round((($item->paid_orders ?? 0) / ($item->total_orders ?? 0)) * 100, 2)
                : 0;

            return [
                'country' => $item->shipping_country,
                'total_orders' => (int) ($item->total_orders ?? 0),
                'completed_orders' => (int) ($item->completed_orders ?? 0),
                'cancelled_orders' => (int) ($item->cancelled_orders ?? 0),
                'processing_orders' => (int) ($item->processing_orders ?? 0),
                'pending_orders' => (int) ($item->pending_orders ?? 0),
                'paid_orders' => (int) ($item->paid_orders ?? 0),
                'total_amount' => (int) ($item->total_amount ?? 0),
                'total_amount_formatted' => number_format(($item->total_amount ?? 0) / 100, 2),
                'completion_rate' => $completionRate,
                'cancellation_rate' => $cancellationRate,
                'payment_success_rate' => $paymentSuccessRate,
            ];
        });
    }

    /**
     * Get date format for MySQL based on grouping type
     */
    private function getDateFormat(string $groupBy): string
    {
        return match ($groupBy) {
            'day' => '%Y-%m-%d',
            'week' => '%Y-%u',
            'month' => '%Y-%m',
            'quarter' => '%Y-Q%q',
            'year' => '%Y',
            default => '%Y-%m-%d',
        };
    }
}
