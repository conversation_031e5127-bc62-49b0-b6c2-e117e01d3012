<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\SyncLog;
use App\Models\Order;
use App\Models\OrderItem;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Collection;
use Exception;

class OrderSyncValidationService
{
    private $remoteConnection;
    private $config;

    public function __construct()
    {
        $this->remoteConnection = DB::connection('store');
        $this->config = config('sync');
    }

    /**
     * Validate sync data integrity for a specific sync log.
     */
    public function validateSync(SyncLog $syncLog): array
    {
        Log::info('Starting sync validation', [
            'sync_log_id' => $syncLog->id,
            'batch_id' => $syncLog->batch_id,
        ]);

        $validationResults = [
            'sync_log_id' => $syncLog->id,
            'batch_id' => $syncLog->batch_id,
            'validation_started_at' => now(),
            'data_integrity' => [],
            'sampling_validation' => [],
            'business_logic' => [],
            'relationship_consistency' => [],
            'overall_status' => 'pending',
            'issues' => [],
        ];

        try {
            // Get sync configuration and query conditions
            $syncConfig = $syncLog->sync_config ?? [];

            // 1. Data Integrity Validation
            $validationResults['data_integrity'] = $this->validateDataIntegrity($syncConfig);

            // 2. Sampling Validation (0.1% random sample)
            $validationResults['sampling_validation'] = $this->validateSampling($syncConfig);

            // 3. Business Logic Validation
            $validationResults['business_logic'] = $this->validateBusinessLogic($syncConfig);

            // 4. Relationship Consistency Validation
            $validationResults['relationship_consistency'] = $this->validateRelationshipConsistency($syncConfig);

            // Determine overall status
            $validationResults['overall_status'] = $this->determineOverallStatus($validationResults);
            $validationResults['validation_completed_at'] = now();

            Log::info('Sync validation completed', [
                'sync_log_id' => $syncLog->id,
                'overall_status' => $validationResults['overall_status'],
            ]);

        } catch (Exception $e) {
            $validationResults['overall_status'] = 'error';
            $validationResults['error_message'] = $e->getMessage();
            $validationResults['validation_completed_at'] = now();

            Log::error('Sync validation failed', [
                'sync_log_id' => $syncLog->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }

        return $validationResults;
    }

    /**
     * Validate data integrity by comparing record counts and key field checksums.
     */
    protected function validateDataIntegrity(array $syncConfig): array
    {
        $results = [
            'record_counts' => [],
            'field_checksums' => [],
            'status' => 'passed',
            'issues' => [],
        ];

        try {
            // Build query conditions based on sync config
            $queryConditions = $this->buildQueryConditions($syncConfig);

            // 1. Record count comparison
            $remoteCounts = $this->getRemoteRecordCounts($queryConditions);
            $localCounts = $this->getLocalRecordCounts($queryConditions);

            $results['record_counts'] = [
                'remote' => $remoteCounts,
                'local' => $localCounts,
                'matches' => [
                    'orders' => $remoteCounts['orders'] === $localCounts['orders'],
                    'order_items' => $remoteCounts['order_items'] === $localCounts['order_items'],
                ],
            ];

            // 2. Field checksum comparison
            $remoteChecksums = $this->getRemoteFieldChecksums($queryConditions);
            $localChecksums = $this->getLocalFieldChecksums($queryConditions);

            $results['field_checksums'] = [
                'remote' => $remoteChecksums,
                'local' => $localChecksums,
                'matches' => [
                    'items_total_sum' => abs($remoteChecksums['items_total_sum'] - $localChecksums['items_total_sum']) < 0.01,
                    'adjustments_total_sum' => abs($remoteChecksums['adjustments_total_sum'] - $localChecksums['adjustments_total_sum']) < 0.01,
                    'total_amount_sum' => abs($remoteChecksums['total_amount_sum'] - $localChecksums['total_amount_sum']) < 0.01,
                    'customer_count' => $remoteChecksums['customer_count'] === $localChecksums['customer_count'],
                ],
            ];

            // Check for issues
            if (!$results['record_counts']['matches']['orders']) {
                $results['issues'][] = 'Order count mismatch';
                $results['status'] = 'failed';
            }

            if (!$results['record_counts']['matches']['order_items']) {
                $results['issues'][] = 'Order item count mismatch';
                $results['status'] = 'failed';
            }

            foreach ($results['field_checksums']['matches'] as $field => $matches) {
                if (!$matches) {
                    $results['issues'][] = "Field checksum mismatch: {$field}";
                    $results['status'] = 'failed';
                }
            }

        } catch (Exception $e) {
            $results['status'] = 'error';
            $results['error'] = $e->getMessage();
        }

        return $results;
    }

    /**
     * Validate data by sampling 0.1% of records for detailed comparison.
     */
    private function validateSampling(array $syncConfig): array
    {
        $results = [
            'sample_size' => 0,
            'matched_records' => 0,
            'mismatched_records' => 0,
            'sample_percentage' => 0.1,
            'status' => 'passed',
            'issues' => [],
            'mismatches' => [],
        ];

        try {
            $queryConditions = $this->buildQueryConditions($syncConfig);

            // Get total count for sampling
            $totalOrders = $this->getRemoteRecordCounts($queryConditions)['orders'];
            $sampleSize = max(1, (int) ceil($totalOrders * 0.001)); // 0.1% sample

            $results['sample_size'] = $sampleSize;

            // Get random sample of order IDs
            $sampleOrderIds = $this->getRandomSampleOrderIds($queryConditions, $sampleSize);

            foreach ($sampleOrderIds as $orderId) {
                $comparison = $this->compareOrderRecord($orderId);

                if ($comparison['matches']) {
                    $results['matched_records']++;
                } else {
                    $results['mismatched_records']++;
                    $results['mismatches'][] = $comparison;
                }
            }

            // Determine status
            $mismatchRate = $results['sample_size'] > 0 ?
                ($results['mismatched_records'] / $results['sample_size']) : 0;

            if ($mismatchRate > 0.05) { // More than 5% mismatch rate
                $results['status'] = 'failed';
                $results['issues'][] = "High mismatch rate: {$mismatchRate}%";
            }

        } catch (Exception $e) {
            $results['status'] = 'error';
            $results['error'] = $e->getMessage();
        }

        return $results;
    }

    /**
     * Validate business logic by checking state and refund status distributions.
     */
    private function validateBusinessLogic(array $syncConfig): array
    {
        $results = [
            'state_distribution' => [],
            'refund_status_distribution' => [],
            'status' => 'passed',
            'issues' => [],
        ];

        try {
            $queryConditions = $this->buildQueryConditions($syncConfig);

            // Compare state distributions
            $remoteStates = $this->getRemoteStateDistribution($queryConditions);
            $localStates = $this->getLocalStateDistribution($queryConditions);

            $results['state_distribution'] = [
                'remote' => $remoteStates,
                'local' => $localStates,
                'matches' => [],
            ];

            foreach ($remoteStates as $state => $count) {
                $localCount = $localStates[$state] ?? 0;
                $matches = $count === $localCount;
                $results['state_distribution']['matches'][$state] = $matches;

                if (!$matches) {
                    $results['issues'][] = "State count mismatch for '{$state}': remote={$count}, local={$localCount}";
                    $results['status'] = 'failed';
                }
            }

            // Compare refund status distributions
            $remoteRefundStatuses = $this->getRemoteRefundStatusDistribution($queryConditions);
            $localRefundStatuses = $this->getLocalRefundStatusDistribution($queryConditions);

            $results['refund_status_distribution'] = [
                'remote' => $remoteRefundStatuses,
                'local' => $localRefundStatuses,
                'matches' => [],
            ];

            foreach ($remoteRefundStatuses as $status => $count) {
                $localCount = $localRefundStatuses[$status] ?? 0;
                $matches = $count === $localCount;
                $results['refund_status_distribution']['matches'][$status] = $matches;

                if (!$matches) {
                    $results['issues'][] = "Refund status count mismatch for '{$status}': remote={$count}, local={$localCount}";
                    $results['status'] = 'failed';
                }
            }

        } catch (Exception $e) {
            $results['status'] = 'error';
            $results['error'] = $e->getMessage();
        }

        return $results;
    }

    /**
     * Validate relationship consistency by checking order item counts per order.
     */
    private function validateRelationshipConsistency(array $syncConfig): array
    {
        $results = [
            'order_item_counts' => [],
            'status' => 'passed',
            'issues' => [],
        ];

        try {
            $queryConditions = $this->buildQueryConditions($syncConfig);

            // Get order item counts grouped by order
            $remoteItemCounts = $this->getRemoteOrderItemCounts($queryConditions);
            $localItemCounts = $this->getLocalOrderItemCounts($queryConditions);

            $results['order_item_counts'] = [
                'remote_orders_checked' => count($remoteItemCounts),
                'local_orders_checked' => count($localItemCounts),
                'mismatched_orders' => [],
            ];

            // Compare item counts for each order
            foreach ($remoteItemCounts as $storeOrderId => $remoteCount) {
                $localCount = $localItemCounts[$storeOrderId] ?? 0;

                if ($remoteCount !== $localCount) {
                    $results['order_item_counts']['mismatched_orders'][] = [
                        'store_order_id' => $storeOrderId,
                        'remote_count' => $remoteCount,
                        'local_count' => $localCount,
                    ];
                }
            }

            if (!empty($results['order_item_counts']['mismatched_orders'])) {
                $mismatchCount = count($results['order_item_counts']['mismatched_orders']);
                $results['issues'][] = "Order item count mismatches found: {$mismatchCount} orders";
                $results['status'] = 'failed';
            }

        } catch (Exception $e) {
            $results['status'] = 'error';
            $results['error'] = $e->getMessage();
        }

        return $results;
    }
    /**
     * Build query conditions based on sync configuration.
     */
    private function buildQueryConditions(array $syncConfig): array
    {
        $conditions = [];

        // Handle time-based filtering
        if (isset($syncConfig['force_time_range']) && $syncConfig['force_time_range']) {
            $conditions['time_range'] = [
                'start_time' => $syncConfig['start_time'],
                'end_time' => $syncConfig['end_time'],
                'type' => 'force_range',
            ];
        } elseif (isset($syncConfig['incremental']) && $syncConfig['incremental']) {
            $conditions['time_range'] = [
                'type' => 'incremental',
                'last_sync_time' => $this->getLastSuccessfulSyncTime(),
            ];
        } else {
            $conditions['time_range'] = [
                'type' => 'full',
            ];
        }

        return $conditions;
    }

    /**
     * Get remote record counts.
     */
    private function getRemoteRecordCounts(array $queryConditions): array
    {
        try {
            $orderQuery = $this->remoteConnection->table('sylius_order as o')
                ->whereNotNull('o.checkout_completed_at');

            $orderItemQuery = $this->remoteConnection->table('sylius_order_item as oi')
                ->join('sylius_order as o', 'oi.order_id', '=', 'o.id')
                ->whereNotNull('o.checkout_completed_at');

            $this->applyTimeConditions($orderQuery, $queryConditions, 'o');
            $this->applyTimeConditions($orderItemQuery, $queryConditions, 'o');

            return [
                'orders' => $orderQuery->count(),
                'order_items' => $orderItemQuery->count(),
            ];
        } catch (Exception $e) {
            // Return zero counts if remote connection fails
            return [
                'orders' => 0,
                'order_items' => 0,
            ];
        }
    }

    /**
     * Get local record counts.
     */
    private function getLocalRecordCounts(array $queryConditions): array
    {
        $orderQuery = Order::query();
        $orderItemQuery = OrderItem::query()->join('orders', 'order_items.order_id', '=', 'orders.id');

        $this->applyLocalTimeConditions($orderQuery, $queryConditions);
        $this->applyLocalTimeConditions($orderItemQuery, $queryConditions, 'orders');

        return [
            'orders' => $orderQuery->count(),
            'order_items' => $orderItemQuery->count(),
        ];
    }

    /**
     * Get remote field checksums.
     */
    private function getRemoteFieldChecksums(array $queryConditions): array
    {
        try {
            $query = $this->remoteConnection->table('sylius_order as o')
                ->whereNotNull('o.checkout_completed_at');

            $this->applyTimeConditions($query, $queryConditions, 'o');

            $result = $query->selectRaw('
                SUM(o.items_total) as items_total_sum,
                SUM(o.adjustments_total) as adjustments_total_sum,
                SUM(o.total) as total_amount_sum,
                COUNT(DISTINCT o.customer_id) as customer_count
            ')->first();

            return [
                'items_total_sum' => (float) ($result->items_total_sum ?? 0),
                'adjustments_total_sum' => (float) ($result->adjustments_total_sum ?? 0),
                'total_amount_sum' => (float) ($result->total_amount_sum ?? 0),
                'customer_count' => (int) ($result->customer_count ?? 0),
            ];
        } catch (Exception $e) {
            // Return zero checksums if remote connection fails
            return [
                'items_total_sum' => 0.0,
                'adjustments_total_sum' => 0.0,
                'total_amount_sum' => 0.0,
                'customer_count' => 0,
            ];
        }
    }

    /**
     * Get local field checksums.
     */
    private function getLocalFieldChecksums(array $queryConditions): array
    {
        $query = Order::query();
        $this->applyLocalTimeConditions($query, $queryConditions);

        $result = $query->selectRaw('
            SUM(items_total) as items_total_sum,
            SUM(adjustments_total) as adjustments_total_sum,
            SUM(total_amount) as total_amount_sum,
            COUNT(DISTINCT customer_id) as customer_count
        ')->first();

        return [
            'items_total_sum' => (float) $result->items_total_sum,
            'adjustments_total_sum' => (float) $result->adjustments_total_sum,
            'total_amount_sum' => (float) $result->total_amount_sum,
            'customer_count' => (int) $result->customer_count,
        ];
    }

    /**
     * Get random sample of order IDs.
     */
    private function getRandomSampleOrderIds(array $queryConditions, int $sampleSize): array
    {
        try {
            $query = $this->remoteConnection->table('sylius_order as o')
                ->whereNotNull('o.checkout_completed_at')
                ->select('o.id');

            $this->applyTimeConditions($query, $queryConditions, 'o');

            return $query->inRandomOrder()
                ->limit($sampleSize)
                ->pluck('id')
                ->toArray();
        } catch (Exception $e) {
            // Return empty array if remote connection fails
            return [];
        }
    }

    /**
     * Compare a single order record between remote and local.
     */
    private function compareOrderRecord(int $remoteOrderId): array
    {
        try {
            $remoteOrder = $this->remoteConnection->table('sylius_order as o')
                ->leftJoin('sylius_address as sa', 'o.shipping_address_id', '=', 'sa.id')
                ->leftJoin('bitbag_refund_credit_memo as rcm', function($join) {
                    $join->on('o.id', '=', 'rcm.order_id')
                         ->where('rcm.status', '=', 'success');
                })
                ->where('o.id', $remoteOrderId)
                ->select([
                    'o.id as order_id',
                    'o.number as order_number',
                    'o.state',
                    'o.items_total',
                    'o.adjustments_total',
                    'o.total',
                    'o.currency_code',
                    'o.payment_state',
                    'o.customer_id',
                    'sa.country_code as shipping_country',
                    'rcm.total as refund_total',
                    'rcm.status as refund_status',
                ])
                ->first();

            if (!$remoteOrder) {
                return [
                    'matches' => false,
                    'error' => 'Remote order not found',
                    'remote_order_id' => $remoteOrderId,
                ];
            }

            $localOrder = Order::where('store_order_id', $remoteOrderId)->first();

            if (!$localOrder) {
                return [
                    'matches' => false,
                    'error' => 'Local order not found',
                    'remote_order_id' => $remoteOrderId,
                ];
            }

            // Map remote state to local state for comparison
            $stateMapping = $this->getOrderStateMapping();
            $mappedRemoteState = $stateMapping[$remoteOrder->state] ?? $remoteOrder->state;

            // Compare key fields
            $fieldComparisons = [
                'order_number' => $remoteOrder->order_number === $localOrder->order_number,
                'state' => $mappedRemoteState === $localOrder->state,
                'items_total' => $remoteOrder->items_total === $localOrder->items_total,
                'adjustments_total' => $remoteOrder->adjustments_total === $localOrder->adjustments_total,
                'total' => $remoteOrder->total === $localOrder->total_amount,
                'currency_code' => $remoteOrder->currency_code === $localOrder->currency_code,
                'payment_state' => $remoteOrder->payment_state === $localOrder->payment_state,
                'customer_id' => $remoteOrder->customer_id === $localOrder->customer_id,
                'shipping_country' => $remoteOrder->shipping_country === $localOrder->shipping_country,
                'refund_total' => $remoteOrder->refund_total === $localOrder->refund_total,
                'refund_status' => $remoteOrder->refund_status === $localOrder->refund_status,
            ];

            $allMatch = !in_array(false, $fieldComparisons, true);

            // Collect mismatched fields with their values for detailed reporting
            $mismatchedFields = [];
            if (!$allMatch) {
                foreach ($fieldComparisons as $field => $matches) {
                    if (!$matches) {
                        $remoteValue = $field === 'state' ? $mappedRemoteState : $remoteOrder->{$field === 'total' ? 'total' : $field};
                        $localValue = $localOrder->{$field === 'total' ? 'total_amount' : $field};

                        $mismatchedFields[$field] = [
                            'remote' => $remoteValue,
                            'local' => $localValue,
                        ];
                    }
                }
            }

            return [
                'matches' => $allMatch,
                'remote_order_id' => $remoteOrderId,
                'local_order_id' => $localOrder->id,
                'field_comparisons' => $fieldComparisons,
                'mismatched_fields' => $mismatchedFields,
                'remote_data' => (array) $remoteOrder,
                'local_data' => $localOrder->toArray(),
            ];
        } catch (Exception $e) {
            return [
                'matches' => false,
                'error' => 'Database connection failed: ' . $e->getMessage(),
                'remote_order_id' => $remoteOrderId,
            ];
        }
    }
    /**
     * Get remote state distribution.
     */
    private function getRemoteStateDistribution(array $queryConditions): array
    {
        try {
            $query = $this->remoteConnection->table('sylius_order as o')
                ->whereNotNull('o.checkout_completed_at')
                ->select('o.state', DB::raw('COUNT(*) as count'));

            $this->applyTimeConditions($query, $queryConditions, 'o');

            return $query->groupBy('o.state')
                ->pluck('count', 'state')
                ->toArray();
        } catch (Exception $e) {
            // Return empty array if remote connection fails
            return [];
        }
    }

    /**
     * Get local state distribution.
     */
    private function getLocalStateDistribution(array $queryConditions): array
    {
        $query = Order::query()->select('state', DB::raw('COUNT(*) as count'));
        $this->applyLocalTimeConditions($query, $queryConditions);

        $localDistribution = $query->groupBy('state')
            ->pluck('count', 'state')
            ->toArray();

        // Map local states back to remote states for comparison
        // This reverses the mapping done in OrderSyncService::mapOrderState
        $reverseMappedDistribution = [];
        $stateMapping = $this->getOrderStateMapping();

        foreach ($localDistribution as $localState => $count) {
            // Find the remote state that maps to this local state
            $remoteState = array_search($localState, $stateMapping);
            if ($remoteState !== false) {
                $reverseMappedDistribution[$remoteState] = ($reverseMappedDistribution[$remoteState] ?? 0) + $count;
            } else {
                // If no mapping found, use the local state as is
                $reverseMappedDistribution[$localState] = ($reverseMappedDistribution[$localState] ?? 0) + $count;
            }
        }

        return $reverseMappedDistribution;
    }

    /**
     * Get order state mapping from remote to local states.
     * This should match the mapping in OrderSyncService::mapOrderState.
     */
    private function getOrderStateMapping(): array
    {
        return [
            'cart' => 'cart',
            'new' => 'new',
            'fulfilled' => 'completed',
            'cancelled' => 'cancelled',
            // Additional states that may exist locally but don't have remote equivalents
            'pending' => 'pending',
            'processing' => 'processing',
        ];
    }

    /**
     * Get remote refund status distribution.
     */
    private function getRemoteRefundStatusDistribution(array $queryConditions): array
    {
        try {
            // First, get distinct orders with their refund status (deduplicated)
            $subQuery = $this->remoteConnection->table('sylius_order as o')
                ->leftJoin('bitbag_refund_credit_memo as rcm', function($join) {
                    $join->on('o.id', '=', 'rcm.order_id')
                         ->where('rcm.status', '=', 'success');
                })
                ->whereNotNull('o.checkout_completed_at')
                ->select([
                    'o.id',
                    DB::raw('CASE WHEN rcm.status IS NOT NULL THEN "success" ELSE "none" END as refund_status')
                ])
                ->distinct();

            $this->applyTimeConditions($subQuery, $queryConditions, 'o');

            // Then count the distribution from the deduplicated results
            $query = $this->remoteConnection->table(DB::raw("({$subQuery->toSql()}) as deduplicated_orders"))
                ->mergeBindings($subQuery)
                ->select('refund_status', DB::raw('COUNT(*) as count'))
                ->groupBy('refund_status');

            return $query->pluck('count', 'refund_status')->toArray();
        } catch (Exception $e) {
            // Return empty array if remote connection fails
            return [];
        }
    }

    /**
     * Get local refund status distribution.
     */
    private function getLocalRefundStatusDistribution(array $queryConditions): array
    {
        $query = Order::query()->select(DB::raw('COALESCE(refund_status, "none") as refund_status'), DB::raw('COUNT(*) as count'));
        $this->applyLocalTimeConditions($query, $queryConditions);

        return $query->groupBy('refund_status')
            ->pluck('count', 'refund_status')
            ->toArray();
    }

    /**
     * Get remote order item counts grouped by order.
     */
    private function getRemoteOrderItemCounts(array $queryConditions): array
    {
        try {
            $query = $this->remoteConnection->table('sylius_order_item as oi')
                ->join('sylius_order as o', 'oi.order_id', '=', 'o.id')
                ->whereNotNull('o.checkout_completed_at')
                ->select('o.id as store_order_id', DB::raw('COUNT(*) as item_count'));

            $this->applyTimeConditions($query, $queryConditions, 'o');

            return $query->groupBy('o.id')
                ->pluck('item_count', 'store_order_id')
                ->toArray();
        } catch (Exception $e) {
            // Return empty array if remote connection fails
            return [];
        }
    }

    /**
     * Get local order item counts grouped by order.
     */
    private function getLocalOrderItemCounts(array $queryConditions): array
    {
        $query = OrderItem::query()
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->select('orders.store_order_id', DB::raw('COUNT(*) as item_count'));

        $this->applyLocalTimeConditions($query, $queryConditions, 'orders');

        return $query->groupBy('orders.store_order_id')
            ->pluck('item_count', 'store_order_id')
            ->toArray();
    }

    /**
     * Apply time conditions to remote query.
     */
    private function applyTimeConditions($query, array $queryConditions, string $tableAlias = 'o'): void
    {
        if (!isset($queryConditions['time_range'])) {
            return;
        }

        $timeRange = $queryConditions['time_range'];

        if ($timeRange['type'] === 'force_range') {
            $query->whereBetween("{$tableAlias}.checkout_completed_at", [
                $timeRange['start_time'],
                $timeRange['end_time']
            ]);
        } elseif ($timeRange['type'] === 'incremental' && isset($timeRange['last_sync_time'])) {
            $query->where(function($q) use ($tableAlias, $timeRange) {
                $q->where("{$tableAlias}.updated_at", '>', $timeRange['last_sync_time'])
                  ->orWhere("{$tableAlias}.checkout_completed_at", '>', $timeRange['last_sync_time']);
            });
        }
    }

    /**
     * Apply time conditions to local query.
     */
    private function applyLocalTimeConditions($query, array $queryConditions, string $tableAlias = 'orders'): void
    {
        if (!isset($queryConditions['time_range'])) {
            return;
        }

        $timeRange = $queryConditions['time_range'];

        if ($timeRange['type'] === 'force_range') {
            $query->whereBetween("{$tableAlias}.completed_at", [
                $timeRange['start_time'],
                $timeRange['end_time']
            ]);
        } elseif ($timeRange['type'] === 'incremental' && isset($timeRange['last_sync_time'])) {
            $query->where(function($q) use ($tableAlias, $timeRange) {
                $q->where("{$tableAlias}.updated_at", '>', $timeRange['last_sync_time'])
                  ->orWhere("{$tableAlias}.completed_at", '>', $timeRange['last_sync_time']);
            });
        }
    }

    /**
     * Get the last successful sync time.
     */
    private function getLastSuccessfulSyncTime(): ?string
    {
        $lastSync = SyncLog::where('sync_type', 'order_sync')
            ->where('status', 'completed')
            ->orderBy('completed_at', 'desc')
            ->first();

        return $lastSync?->completed_at?->toDateTimeString();
    }

    /**
     * Determine overall validation status based on individual results.
     */
    private function determineOverallStatus(array $validationResults): string
    {
        $sections = ['data_integrity', 'sampling_validation', 'business_logic', 'relationship_consistency'];

        foreach ($sections as $section) {
            if (isset($validationResults[$section]['status'])) {
                if ($validationResults[$section]['status'] === 'error') {
                    return 'error';
                }
                if ($validationResults[$section]['status'] === 'failed') {
                    return 'failed';
                }
            }
        }

        return 'passed';
    }
}
