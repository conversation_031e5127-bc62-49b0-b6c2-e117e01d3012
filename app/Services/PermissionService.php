<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Role;
use App\Models\User;
use App\Traits\HasPermissionHelpers;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Validation\ValidationException;

final class PermissionService
{
    use HasPermissionHelpers;

    /**
     * Default organization roles that are created for each organization.
     */
    public const DEFAULT_ORGANIZATION_ROLES = [
        'owner',          // Organization owner
        'member',         // Member
        'visitor',        // Visitor
    ];

    /**
     * Create a new role.
     */
    public function createRole(string $name, string $guardName = 'api', ?int $organisationId = null): Role
    {
        $role = Role::firstOrCreate([
            'name' => $name,
            'guard_name' => $guardName,
            'organisation_id' => $organisationId,
        ]);

        return $role;
    }

    /**
     * Assign role to user within organisation context.
     */
    public function assignRoleToUser(User $user, Role $role): User
    {
        // Set the team context to the role's organisation
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($role->organisation_id);

        // For system roles with different guard, we need to handle this specially
        if ($role->guard_name === 'system') {
            // Directly insert into model_has_roles table to bypass guard validation
            $user->roles()->attach($role->id, [
                'model_type' => get_class($user),
                'organisation_id' => $role->organisation_id
            ]);
        } else {
            $user->assignRole($role);
        }

        // Clear the cache to ensure role is recognized
        app(\Spatie\Permission\PermissionRegistrar::class)->forgetCachedPermissions();

        return $user;
    }

    /**
     * Get user role names considering both system and organisation contexts.
     */
    public function getUserRoleNames(User $user): \Illuminate\Support\Collection
    {
        // Use direct database query to bypass Spatie Permission's team context filtering
        // This joins model_has_roles with roles table to get all role names for the user
        return \DB::table('model_has_roles')
            ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
            ->where('model_has_roles.model_type', get_class($user))
            ->where('model_has_roles.model_id', $user->id)
            ->pluck('roles.name')
            ->unique();
    }

    /**
     * Assign role to user with owner uniqueness check.
     */
    public function assignRoleToUserSafely(User $assigner, User $targetUser, Role $role): User
    {
        // Check if user already has this role to avoid duplicate assignment
        $existingAssignment = \DB::table('model_has_roles')
            ->where('model_type', get_class($targetUser))
            ->where('model_id', $targetUser->id)
            ->where('role_id', $role->id)
            ->where('organisation_id', $role->organisation_id)
            ->first();

        if ($existingAssignment) {
            throw ValidationException::withMessages([
                'role' => [__('errors.permission_user_already_has_role')],
            ]);
        }

        // Special validation for owner role - only one owner per organisation
        if ($role->name === 'owner' && $role->organisation_id) {
            // Set team context for the query
            app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($role->organisation_id);

            $existingOwner = User::whereHas('roles', function ($query) use ($role) {
                $query->where('roles.name', 'owner')
                    ->where('roles.guard_name', 'api')
                    ->where('roles.organisation_id', $role->organisation_id);
            })->where('id', '!=', $targetUser->id)->first();

            if ($existingOwner) {
                throw ValidationException::withMessages([
                    'role' => [__('errors.permission_organization_already_has_owner')],
                ]);
            }
        }

        // Set appropriate team context
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($role->organisation_id);

        // For system roles with different guard, we need to handle this specially
        if ($role->guard_name === 'system') {
            // Directly insert into model_has_roles table to bypass guard validation
            $targetUser->roles()->attach($role->id, [
                'model_type' => get_class($targetUser),
                'organisation_id' => $role->organisation_id
            ]);
        } else {
            $targetUser->assignRole($role);
        }

        // Clear the cache to ensure role is recognized
        app(\Spatie\Permission\PermissionRegistrar::class)->forgetCachedPermissions();

        return $targetUser;
    }

    /**
     * Transfer owner role from one user to another.
     */
    public function transferOwnerRole(User $currentOwner, User $newOwner, int $organisationId): bool
    {
        // Validate that current user is actually the owner
        $ownerRole = Role::where('name', 'owner')
            ->where('guard_name', 'api')
            ->where('organisation_id', $organisationId)
            ->first();

        if (!$ownerRole) {
            throw ValidationException::withMessages([
                'role' => [__('errors.permission_owner_role_not_exist')],
            ]);
        }

        // Set team context
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($organisationId);

        // Check if current owner has the role (considering they might be system admin)
        $currentOwnerHasRole = false;

        // Check if current owner is system admin (can transfer any owner role)
        $isSystemAdmin = $currentOwner->hasSystemAdminAccess();

        if ($isSystemAdmin) {
            $currentOwnerHasRole = true;
        } else {
            // Check if current owner actually has the owner role for this organisation
            $currentOwnerHasRole = $currentOwner->hasOwnerRoleForOrganisation($organisationId);
        }

        if (!$currentOwnerHasRole) {
            throw ValidationException::withMessages([
                'role' => [__('errors.permission_current_user_not_owner_or_admin')],
            ]);
        }

        // Verify new owner belongs to the organisation
        if (!$newOwner->belongsToOrganisation($organisationId)) {
            throw ValidationException::withMessages([
                'role' => [__('errors.permission_new_owner_must_belong_to_organization')],
            ]);
        }

        // For current owner, they can be system admin transferring ownership
        if (!$currentOwner->hasSystemAdminAccess() && !$currentOwner->belongsToOrganisation($organisationId)) {
            throw ValidationException::withMessages([
                'role' => [__('errors.permission_current_user_must_belong_to_organization')],
            ]);
        }

        // Remove role from current owner only if they actually have it
        // For non-system admins, check if they have the owner role
        if (!$isSystemAdmin && $currentOwner->hasOwnerRoleForOrganisation($organisationId)) {
            $currentOwner->removeRole($ownerRole);
        }

        // Assign role to new owner
        // Owner role uses 'api' guard, so no special handling needed
        $newOwner->assignRole($ownerRole);

        return true;
    }

    /**
     * Get available role names that a user can assign.
     */
    public function getAssignableRoleNames(User $user): array
    {
        // Get user's highest role level
        $userHighestLevel = $this->getUserHighestRoleLevel($user);

        if ($userHighestLevel === 0) {
            // User has no roles, cannot assign anything
            return [];
        }

        // Get all role names with level lower than user's highest level
        $assignableRoleNames = collect(self::ROLE_HIERARCHY)
            ->filter(fn($level) => $level < $userHighestLevel)
            ->keys()
            ->toArray();

        return $assignableRoleNames;
    }

    /**
     * Get available roles with detailed information that a user can assign.
     * This method returns full role objects instead of just names.
     */
    public function getAssignableRoleDetails(User $user): array
    {
        // Get assignable role names
        $assignableRoleNames = $this->getAssignableRoleNames($user);

        if (empty($assignableRoleNames)) {
            return [];
        }

        $assignableRoles = collect();

        // Get system roles (guard_name: 'system')
        $systemRoles = Role::whereIn('name', $assignableRoleNames)
            ->where('guard_name', 'system')
            ->whereNull('organisation_id')
            ->get(['id', 'name', 'guard_name', 'organisation_id', 'created_at', 'updated_at']);

        $assignableRoles = $assignableRoles->merge($systemRoles);

        // Get organization roles (guard_name: 'api') that the user can manage
        if ($user->hasSystemAdminAccess()) {
            // System admin can assign roles in any organization
            $orgRoles = Role::whereIn('name', $assignableRoleNames)
                ->where('guard_name', 'api')
                ->whereNotNull('organisation_id')
                ->get(['id', 'name', 'guard_name', 'organisation_id', 'created_at', 'updated_at']);
        } else {
            // Organization owner can only assign roles in their own organizations
            $userOrganisationIds = $user->getOrganisationIds();
            $orgRoles = Role::whereIn('name', $assignableRoleNames)
                ->where('guard_name', 'api')
                ->whereIn('organisation_id', $userOrganisationIds)
                ->get(['id', 'name', 'guard_name', 'organisation_id', 'created_at', 'updated_at']);
        }

        $assignableRoles = $assignableRoles->merge($orgRoles);

        return $assignableRoles->toArray();
    }

    /**
     * Get available roles with detailed information that a user can assign.
     * Returns structured data with roles grouped by guard_name and detailed information.
     * For root/admin users, details array is empty; for others, it contains full role details.
     */
    public function getAssignableRoles(User $user): array
    {
        // Get assignable role names
        $assignableRoleNames = $this->getAssignableRoleNames($user);

        if (empty($assignableRoleNames)) {
            return [
                'roles' => [],
                'details' => []
            ];
        }

        // Check if user is root or admin (system level)
        $isSystemAdmin = $user->hasSystemAdminAccess();
        $userHighestLevel = $this->getUserHighestRoleLevel($user);
        $isRootOrAdmin = in_array($userHighestLevel, [4, 3]); // root=4, admin=3

        // Get user's organizations for organization-specific roles
        $userOrganisationIds = $user->getOrganisationIds()->toArray();

        // Build a single optimized query to get all assignable roles
        $roles = Role::select(['id', 'name', 'guard_name', 'organisation_id', 'created_at', 'updated_at'])
            ->where(function ($query) use ($assignableRoleNames, $userOrganisationIds, $user) {
                // System roles - use guard_name to identify
                $query->orWhere(function ($subQuery) use ($assignableRoleNames) {
                    $subQuery->whereIn('name', $assignableRoleNames)
                        ->where('guard_name', 'system');
                });

                // Organization roles - use guard_name to identify and filter by user's organizations
                if ($user->hasSystemAdminAccess()) {
                    // System admin can see roles in any organization
                    $query->orWhere(function ($subQuery) use ($assignableRoleNames) {
                        $subQuery->whereIn('name', $assignableRoleNames)
                            ->where('guard_name', 'api')
                            ->whereNotNull('organisation_id');
                    });
                } elseif (!empty($userOrganisationIds)) {
                    // Organization owner can only see roles in their own organizations
                    $query->orWhere(function ($subQuery) use ($assignableRoleNames, $userOrganisationIds) {
                        $subQuery->whereIn('name', $assignableRoleNames)
                            ->where('guard_name', 'api')
                            ->whereIn('organisation_id', $userOrganisationIds);
                    });
                }
            })
            ->get();

        // Group roles by guard_name for the roles section
        $rolesByGuard = [];
        foreach ($roles as $role) {
            $guardName = $role->guard_name;
            if (!isset($rolesByGuard[$guardName])) {
                $rolesByGuard[$guardName] = [];
            }
            if (!in_array($role->name, $rolesByGuard[$guardName])) {
                $rolesByGuard[$guardName][] = $role->name;
            }
        }

        // For root/admin users, ensure api roles are properly populated from assignable role names
        if ($isRootOrAdmin && !empty($assignableRoleNames)) {
            $apiRoleNames = array_intersect($assignableRoleNames, self::DEFAULT_ORGANIZATION_ROLES);
            if (!empty($apiRoleNames)) {
                if (!isset($rolesByGuard['api'])) {
                    $rolesByGuard['api'] = [];
                }
                // Merge and deduplicate
                $rolesByGuard['api'] = array_unique(array_merge($rolesByGuard['api'], $apiRoleNames));
            }
        }

        // Prepare details array - empty for root/admin, full details for others
        $details = [];
        if (!$isRootOrAdmin) {
            $details = $roles->map(function ($role) {
                return [
                    'id' => $role->id,
                    'name' => $role->name,
                    'guard_name' => $role->guard_name,
                    'organisation_id' => $role->organisation_id,
                    'created_at' => $role->created_at,
                    'updated_at' => $role->updated_at,
                    'type' => $role->guard_name === 'system' ? 'system' : 'organisation'
                ];
            })->toArray();
        }

        return [
            'roles' => $rolesByGuard,
            'details' => $details
        ];
    }

    /**
     * Remove role from user within organisation context.
     */
    public function removeRoleFromUser(User $user, Role $role): User
    {
        // Set the team context to the role's organisation
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($role->organisation_id);
        $user->removeRole($role);
        return $user;
    }

    /**
     * Get user's complete role information including both system and organisation roles.
     */
    public function getUserCompleteRoleInfo(User $user): array
    {
        // Get system-wide roles (clear team context first)
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $systemRoles = $user->roles()
            ->where('roles.guard_name', 'system')
            ->whereNull('roles.organisation_id')
            ->get(['roles.id', 'roles.name', 'roles.organisation_id']);

        // Get ALL organisation roles for the user, not just from organisations they belong to
        // This is important because users can have roles in organisations without being members
        // Use direct database query to bypass Spatie Permission's team context filtering
        $organisationRolesData = \DB::table('model_has_roles')
            ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
            ->where('model_has_roles.model_type', get_class($user))
            ->where('model_has_roles.model_id', $user->id)
            ->where('roles.guard_name', 'api')
            ->whereNotNull('roles.organisation_id')
            ->get(['roles.id', 'roles.name', 'roles.organisation_id']);

        // Convert to collection of arrays that match the expected structure
        $organisationRoles = collect($organisationRolesData)->map(function ($role) {
            return [
                'id' => $role->id,
                'name' => $role->name,
                'organisation_id' => $role->organisation_id,
            ];
        });

        return [
            'system_roles' => $systemRoles,
            'organisation_roles' => $organisationRoles,
            'all_role_names' => $systemRoles->pluck('name')->merge($organisationRoles->pluck('name'))->unique()->values(),
        ];
    }

    /**
     * Get all roles for a user within organisation context.
     */
    public function getUserRoles(User $user, ?int $organisationId = null): \Illuminate\Support\Collection
    {
        if ($organisationId) {
            app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($organisationId);
            return $user->getRoleNames();
        }

        // Get roles from all user's organisations
        $allRoles = collect();
        $userOrganisationIds = $user->getOrganisationIds();

        foreach ($userOrganisationIds as $orgId) {
            app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($orgId);
            $orgRoles = $user->getRoleNames();
            $allRoles = $allRoles->merge($orgRoles);
        }

        return $allRoles->unique();
    }

    /**
     * Get all roles for organisation.
     */
    public function getOrganisationRoles(int $organisationId): Collection
    {
        return Role::where('organisation_id', $organisationId)->get();
    }

    /**
     * Sync user roles within organisation context.
     */
    public function syncUserRoles(User $user, array $roleNames, int $organisationId): User
    {
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($organisationId);
        $user->syncRoles($roleNames);
        return $user;
    }

    /**
     * Create default roles for an organisation.
     * Note: Only creates organisation-specific roles (owner, member, visitor).
     * Global admin roles (root, admin) are created via createSystemRoles().
     */
    public function createDefaultRoles(int $organisationId): array
    {
        $createdRoles = [];
        foreach (self::DEFAULT_ORGANIZATION_ROLES as $roleName) {
            $role = Role::firstOrCreate([
                'name' => $roleName,
                'guard_name' => 'api',
                'organisation_id' => $organisationId,
            ]);

            $createdRoles[] = $role;
        }

        return $createdRoles;
    }

    /**
     * Create global system roles (not tied to any organisation).
     */
    public function createSystemRoles(): array
    {
        $systemRoles = [
            'root',           // Root - First admin account set during system initialization
            'admin',          // Administrator
        ];

        $createdRoles = [];
        foreach ($systemRoles as $roleName) {
            $role = Role::firstOrCreate([
                'name' => $roleName,
                'guard_name' => 'system', // Use 'system' guard for global admin roles
                'organisation_id' => null, // System-wide roles
            ]);

            $createdRoles[] = $role;
        }

        return $createdRoles;
    }
}
