<?php

declare(strict_types=1);

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

final class EmailVerificationService
{
    private const VERIFICATION_CODE_LENGTH = 6;
    private const CACHE_TTL = 900; // 15 minutes in seconds - verification code expiry time
    private const RETRY_INTERVAL = 180; // 3 minutes in seconds - minimum interval between resend attempts
    private const CACHE_PREFIX = 'email_verification:';
    private const RETRY_PREFIX = 'email_verification_retry:';

    /**
     * Check if a new verification code can be sent (respects retry interval).
     */
    public function canResendCode(string $email): bool
    {
        $retryKey = self::RETRY_PREFIX . $email;
        return !Cache::has($retryKey);
    }

    /**
     * Generate and send verification code to email address.
     * Will replace existing code if retry interval has passed.
     */
    public function sendVerificationCode(string $email): string
    {
        // Check if we can send a new code
        if (!$this->canResendCode($email)) {
            Log::warning('Verification code resend blocked due to retry interval', [
                'email' => $email,
            ]);
            throw new \Exception('Please wait before requesting a new verification code.');
        }

        $code = $this->generateVerificationCode();

        // Store code in cache with email as key (this will replace any existing code)
        $cacheKey = self::CACHE_PREFIX . $email;
        Cache::put($cacheKey, $code, self::CACHE_TTL);

        // Set retry interval lock
        $retryKey = self::RETRY_PREFIX . $email;
        Cache::put($retryKey, true, self::RETRY_INTERVAL);

        // Mock email sending (replace with actual email service later)
        $this->mockSendEmail($email, $code);

        Log::info('Verification code sent', [
            'email' => $email,
            'code' => $code, // Remove this in production
            'expires_at' => now()->addSeconds(self::CACHE_TTL)->toISOString(),
        ]);

        return $code;
    }

    /**
     * Verify the provided code against cached value.
     */
    public function verifyCode(string $email, string $code): bool
    {
        $cacheKey = self::CACHE_PREFIX . $email;
        $cachedCode = Cache::get($cacheKey);

        if ($cachedCode === null) {
            Log::warning('Verification code not found or expired', ['email' => $email]);
            return false;
        }

        $isValid = $cachedCode === $code;

        if ($isValid) {
            // Remove both verification code and retry lock after successful verification
            Cache::forget($cacheKey);
            Cache::forget(self::RETRY_PREFIX . $email);
            Log::info('Verification code verified successfully', ['email' => $email]);
        } else {
            Log::warning('Invalid verification code provided', ['email' => $email]);
        }

        return $isValid;
    }

    /**
     * Check if verification code exists for email.
     */
    public function hasValidCode(string $email): bool
    {
        $cacheKey = self::CACHE_PREFIX . $email;
        return Cache::has($cacheKey);
    }

    /**
     * Generate a random 6-digit verification code.
     */
    private function generateVerificationCode(): string
    {
        return str_pad((string) random_int(0, 999999), self::VERIFICATION_CODE_LENGTH, '0', STR_PAD_LEFT);
    }

    /**
     * Mock email sending implementation.
     * Replace this with actual email service integration.
     */
    private function mockSendEmail(string $email, string $code): void
    {
        // Mock implementation - log the email content
        Log::info('Mock email sent', [
            'to' => $email,
            'subject' => 'Email Verification Code',
            'body' => "Your verification code is: {$code}. This code will expire in 15 minutes.",
        ]);

        // In a real implementation, you would integrate with:
        // - Laravel Mail facade
        // - Third-party email services (SendGrid, Mailgun, etc.)
        // - Queue jobs for async email sending
    }
}
