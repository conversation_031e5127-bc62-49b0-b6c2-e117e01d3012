<?php

declare(strict_types=1);

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class SyncProgressService
{
    private const CACHE_PREFIX = 'sync_progress:';
    private const ACTIVE_JOBS_KEY = 'sync:active_jobs';
    private const BATCH_INDEX_KEY = 'sync:batch_index';
    private const CACHE_TTL = 3600; // 1 hour

    /**
     * Initialize progress tracking for a sync job.
     */
    public function initializeProgress(string $jobId, string $batchId): void
    {
        $progressData = [
            'job_id' => $jobId,
            'batch_id' => $batchId,
            'status' => 'pending',
            'percentage' => 0,
            'processed_chunks' => 0,
            'total_chunks' => 0,
            'elapsed_time' => 0,
            'started_at' => now()->toISOString(),
            'updated_at' => now()->toISOString(),
        ];

        Cache::put(self::CACHE_PREFIX . $jobId, $progressData, self::CACHE_TTL);

        // Add to active jobs list
        $activeJobs = Cache::get(self::ACTIVE_JOBS_KEY, []);
        $activeJobs[$jobId] = [
            'batch_id' => $batchId,
            'started_at' => now()->toISOString(),
        ];
        Cache::put(self::ACTIVE_JOBS_KEY, $activeJobs, self::CACHE_TTL);

        // Add to batch index for efficient batch_id -> job_id lookup
        $this->addToBatchIndex($batchId, $jobId);
    }

    /**
     * Update progress for a sync job.
     */
    public function updateProgress(string $jobId, array $progressData): void
    {
        $existingData = Cache::get(self::CACHE_PREFIX . $jobId, []);
        $updatedData = array_merge($existingData, $progressData, [
            'updated_at' => now()->toISOString(),
        ]);

        Cache::put(self::CACHE_PREFIX . $jobId, $updatedData, self::CACHE_TTL);
    }

    /**
     * Mark a sync job as completed.
     */
    public function markCompleted(string $jobId, array $completionData): void
    {
        $existingData = Cache::get(self::CACHE_PREFIX . $jobId, []);
        $completedData = array_merge($existingData, $completionData, [
            'status' => 'completed',
            'percentage' => 100,
            'completed_at' => now()->toISOString(),
            'updated_at' => now()->toISOString(),
        ]);

        Cache::put(self::CACHE_PREFIX . $jobId, $completedData, self::CACHE_TTL);

        // Remove from active jobs list
        $this->removeFromActiveJobs($jobId);
    }

    /**
     * Mark a sync job as sync completed but validation pending.
     */
    public function markSyncCompleted(string $jobId, array $completionData): void
    {
        $existingData = Cache::get(self::CACHE_PREFIX . $jobId, []);
        $completedData = array_merge($existingData, $completionData, [
            'status' => 'validating',
            'percentage' => 90, // Sync is done but validation is pending
            'sync_completed_at' => now()->toISOString(),
            'updated_at' => now()->toISOString(),
        ]);

        Cache::put(self::CACHE_PREFIX . $jobId, $completedData, self::CACHE_TTL);

        // Keep in active jobs list since validation is still pending
    }

    /**
     * Mark validation as started.
     */
    public function markValidationStarted(string $jobId, array $validationData): void
    {
        $existingData = Cache::get(self::CACHE_PREFIX . $jobId, []);
        $updatedData = array_merge($existingData, $validationData, [
            'status' => 'validating',
            'percentage' => 95, // Validation in progress
            'updated_at' => now()->toISOString(),
        ]);

        Cache::put(self::CACHE_PREFIX . $jobId, $updatedData, self::CACHE_TTL);
    }

    /**
     * Mark validation as completed successfully.
     */
    public function markValidationCompleted(string $jobId, array $validationData): void
    {
        $existingData = Cache::get(self::CACHE_PREFIX . $jobId, []);
        $completedData = array_merge($existingData, $validationData, [
            'status' => 'completed',
            'percentage' => 100,
            'completed_at' => now()->toISOString(),
            'updated_at' => now()->toISOString(),
        ]);

        Cache::put(self::CACHE_PREFIX . $jobId, $completedData, self::CACHE_TTL);

        // Remove from active jobs list
        $this->removeFromActiveJobs($jobId);
    }

    /**
     * Mark validation as failed.
     */
    public function markValidationFailed(string $jobId, array $errorData): void
    {
        $existingData = Cache::get(self::CACHE_PREFIX . $jobId, []);
        $failedData = array_merge($existingData, $errorData, [
            'status' => 'failed',
            'failed_at' => now()->toISOString(),
            'updated_at' => now()->toISOString(),
        ]);

        Cache::put(self::CACHE_PREFIX . $jobId, $failedData, self::CACHE_TTL);

        // Remove from active jobs list
        $this->removeFromActiveJobs($jobId);
    }

    /**
     * Mark a sync job as failed.
     */
    public function markFailed(string $jobId, array $errorData): void
    {
        $existingData = Cache::get(self::CACHE_PREFIX . $jobId, []);
        $failedData = array_merge($existingData, $errorData, [
            'status' => 'failed',
            'failed_at' => now()->toISOString(),
            'updated_at' => now()->toISOString(),
        ]);

        Cache::put(self::CACHE_PREFIX . $jobId, $failedData, self::CACHE_TTL);
        
        // Remove from active jobs list
        $this->removeFromActiveJobs($jobId);
    }

    /**
     * Get progress for a specific job.
     */
    public function getProgress(string $jobId): ?array
    {
        return Cache::get(self::CACHE_PREFIX . $jobId);
    }

    /**
     * Get progress by batch ID.
     */
    public function getProgressByBatchId(string $batchId): ?array
    {
        // Use batch index to find job_id for the given batch_id
        $jobId = $this->getJobIdFromBatchIndex($batchId);

        if ($jobId) {
            return $this->getProgress($jobId);
        }

        return null;
    }

    /**
     * Check if there are any active sync jobs.
     */
    public function hasActiveSyncJobs(): bool
    {
        $activeJobs = Cache::get(self::ACTIVE_JOBS_KEY, []);
        
        // Clean up stale entries
        $cleanedJobs = [];
        foreach ($activeJobs as $jobId => $jobInfo) {
            $jobIdString = (string) $jobId;
            if (Cache::has(self::CACHE_PREFIX . $jobIdString)) {
                $progressData = Cache::get(self::CACHE_PREFIX . $jobIdString);
                if (in_array($progressData['status'] ?? '', ['pending', 'processing', 'validating'])) {
                    $cleanedJobs[$jobId] = $jobInfo;
                }
            }
        }

        if (count($cleanedJobs) !== count($activeJobs)) {
            Cache::put(self::ACTIVE_JOBS_KEY, $cleanedJobs, self::CACHE_TTL);
        }

        return !empty($cleanedJobs);
    }

    /**
     * Get all active sync jobs.
     */
    public function getActiveSyncJobs(): array
    {
        $activeJobs = Cache::get(self::ACTIVE_JOBS_KEY, []);
        $result = [];

        foreach ($activeJobs as $jobId => $jobInfo) {
            $progressData = $this->getProgress((string) $jobId);
            if ($progressData && in_array($progressData['status'] ?? '', ['pending', 'processing', 'validating'])) {
                $result[] = $progressData;
            }
        }

        return $result;
    }

    /**
     * Check if a specific batch is currently being processed.
     */
    public function isBatchActive(string $batchId): bool
    {
        $activeJobs = Cache::get(self::ACTIVE_JOBS_KEY, []);
        
        foreach ($activeJobs as $jobId => $jobInfo) {
            if ($jobInfo['batch_id'] === $batchId) {
                $progressData = $this->getProgress((string) $jobId);
                if ($progressData && in_array($progressData['status'] ?? '', ['pending', 'processing', 'validating'])) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Clean up old progress data.
     */
    public function cleanup(int $hours = 24): int
    {
        $cutoffTime = now()->subHours($hours);
        $cleaned = 0;

        // Clean up active jobs list and individual progress entries
        $activeJobs = Cache::get(self::ACTIVE_JOBS_KEY, []);
        $cleanedActiveJobs = [];
        $batchIndex = Cache::get(self::BATCH_INDEX_KEY, []);
        $cleanedBatchIndex = [];

        foreach ($activeJobs as $jobId => $jobInfo) {
            $jobIdString = (string) $jobId;
            $progressData = Cache::get(self::CACHE_PREFIX . $jobIdString);

            if ($progressData) {
                $updatedAt = \Carbon\Carbon::parse($progressData['updated_at'] ?? $progressData['started_at']);

                if ($updatedAt->lt($cutoffTime)) {
                    // Remove old progress data
                    Cache::forget(self::CACHE_PREFIX . $jobIdString);
                    $cleaned++;
                } else {
                    // Keep active job
                    $cleanedActiveJobs[$jobId] = $jobInfo;
                    // Keep batch index entry if job is still valid
                    if (isset($jobInfo['batch_id'])) {
                        $cleanedBatchIndex[$jobInfo['batch_id']] = $jobIdString;
                    }
                }
            } else {
                // Remove job from active list if progress data doesn't exist
                $cleaned++;
            }
        }

        // Also clean up batch index entries for jobs that no longer exist
        foreach ($batchIndex as $batchId => $jobId) {
            if (!Cache::has(self::CACHE_PREFIX . $jobId)) {
                $cleaned++;
            } else {
                $cleanedBatchIndex[$batchId] = $jobId;
            }
        }

        Cache::put(self::ACTIVE_JOBS_KEY, $cleanedActiveJobs, self::CACHE_TTL);
        Cache::put(self::BATCH_INDEX_KEY, $cleanedBatchIndex, self::CACHE_TTL);

        return $cleaned;
    }

    /**
     * Remove a job from the active jobs list.
     */
    private function removeFromActiveJobs(string $jobId): void
    {
        $activeJobs = Cache::get(self::ACTIVE_JOBS_KEY, []);
        unset($activeJobs[$jobId]);
        Cache::put(self::ACTIVE_JOBS_KEY, $activeJobs, self::CACHE_TTL);
    }

    /**
     * Add batch_id -> job_id mapping to the batch index.
     */
    private function addToBatchIndex(string $batchId, string $jobId): void
    {
        $batchIndex = Cache::get(self::BATCH_INDEX_KEY, []);
        $batchIndex[$batchId] = $jobId;
        Cache::put(self::BATCH_INDEX_KEY, $batchIndex, self::CACHE_TTL);
    }

    /**
     * Get job_id from batch index by batch_id.
     */
    private function getJobIdFromBatchIndex(string $batchId): ?string
    {
        $batchIndex = Cache::get(self::BATCH_INDEX_KEY, []);
        return $batchIndex[$batchId] ?? null;
    }

    /**
     * Remove batch_id from the batch index.
     */
    private function removeFromBatchIndex(string $batchId): void
    {
        $batchIndex = Cache::get(self::BATCH_INDEX_KEY, []);
        unset($batchIndex[$batchId]);
        Cache::put(self::BATCH_INDEX_KEY, $batchIndex, self::CACHE_TTL);
    }
}
