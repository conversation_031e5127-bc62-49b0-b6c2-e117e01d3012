<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\User;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

final class UserService
{
    public function __construct(
        private readonly PermissionService $permissionService
    ) {}
    /**
     * Get all users with optional filtering.
     */
    public function getUsers(int $perPage = 15, ?string $status = null, ?User $currentUser = null, ?array $organisationIds = null, bool $onlyOwner = false): LengthAwarePaginator
    {
        $query = User::with('organisations');

        // System admins can access all users
        if ($currentUser && $currentUser->hasSystemAdminAccess()) {
            if ($onlyOwner) {
                // If only_owner is true, filter to users from organisations where current user is owner
                $ownedOrganisationIds = $currentUser->getOwnedOrganisationIds();
                if ($ownedOrganisationIds->isEmpty()) {
                    // No owned organisations, return empty result
                    return new \Illuminate\Pagination\LengthAwarePaginator([], 0, $perPage, 1);
                }
                $query->whereHas('organisations', function ($q) use ($ownedOrganisationIds) {
                    $q->whereIn('organisations.id', $ownedOrganisationIds);
                });
            } elseif ($organisationIds && !empty($organisationIds)) {
                // Handle organisation IDs filter
                $query->whereHas('organisations', function ($q) use ($organisationIds) {
                    $q->whereIn('organisations.id', $organisationIds);
                });
            }
        } else {
            // Non-system admins can only access users from their organisations
            if ($onlyOwner) {
                // Filter to users from organisations where current user is owner
                $ownedOrganisationIds = $currentUser ? $currentUser->getOwnedOrganisationIds() : collect();
                if ($ownedOrganisationIds->isEmpty()) {
                    // User owns no organisations, return empty result
                    return new \Illuminate\Pagination\LengthAwarePaginator([], 0, $perPage, 1);
                }
                $query->whereHas('organisations', function ($q) use ($ownedOrganisationIds) {
                    $q->whereIn('organisations.id', $ownedOrganisationIds);
                });
            } else {
                // Original logic for non-owner filtering
                $userOrganisationIds = $currentUser ? $currentUser->getOrganisationIds() : collect();

                // Handle organisation IDs filter
                if ($organisationIds && !empty($organisationIds)) {
                    // Validate that user has access to all requested organisation IDs
                    $unauthorizedIds = array_diff($organisationIds, $userOrganisationIds->toArray());
                    if (!empty($unauthorizedIds)) {
                        throw ValidationException::withMessages([
                            'authorization' => [__('errors.user_no_access_organization') . ' IDs: ' . implode(', ', $unauthorizedIds)]
                        ]);
                    }
                    $query->whereHas('organisations', function ($q) use ($organisationIds) {
                        $q->whereIn('organisations.id', $organisationIds);
                    });
                } else {
                    // Filter to only show users from organisations the current user belongs to
                    $query->whereHas('organisations', function ($q) use ($userOrganisationIds) {
                        $q->whereIn('organisations.id', $userOrganisationIds);
                    });
                }
            }
        }

        // Add status filtering if needed (this would require adding a status column to users table)
        // For now, we'll skip status filtering as the current User model doesn't have a status field
        return $query->orderBy('created_at', 'desc')->paginate($perPage);
    }

    /**
     * Get users by organisation.
     */
    public function getUsersByOrganisation(int $organisationId, int $perPage = 15): LengthAwarePaginator
    {
        return User::with('organisations')
            ->whereHas('organisations', function ($query) use ($organisationId) {
                $query->where('organisations.id', $organisationId);
            })
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    /**
     * Get user by ID.
     */
    public function getById(int $id): ?User
    {
        return User::find($id);
    }



    /**
     * Create a new user.
     */
    public function create(array $data, ?User $currentUser = null): User
    {
        // Extract organisation_ids before validation
        $organisationIds = $data['organisation_ids'] ?? [];

        // Handle guest user registration (when currentUser is null)
        if ($currentUser === null) {
            // For guest registration, organisation_ids should be empty or null
            $organisationIds = [];
        } else {
            // Authorization check for organisation access (authenticated users only)
            if (!$currentUser->hasSystemAdminAccess()) {
                $userOrganisationIds = $currentUser->getOrganisationIds();

                // If organisation_ids are specified, check if user has access to all of them
                if (!empty($organisationIds)) {
                    $invalidOrganisationIds = array_diff($organisationIds, $userOrganisationIds->toArray());
                    if (!empty($invalidOrganisationIds)) {
                        throw ValidationException::withMessages([
                            'authorization' => [__('errors.user_no_access_some_organizations')]
                        ]);
                    }
                } else {
                    // If no organisations specified, default to user's first organisation
                    if (!$userOrganisationIds->isEmpty()) {
                        $organisationIds = [$userOrganisationIds->first()];
                    }
                }
            }
        }

        // Hash password if provided
        if (isset($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        }

        unset($data['organisation_ids']);
        $user = User::create($data);

        // Attach organisations if provided (only for authenticated user creation)
        if (!empty($organisationIds)) {
            $user->organisations()->attach($organisationIds);
        }

        return $user;
    }

    /**
     * Update user.
     */
    public function update(User $user, array $data, ?User $currentUser = null): User
    {
        // Hash password if provided
        if (isset($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        }

        // Handle organisation_ids if provided
        if (isset($data['organisation_ids'])) {
            $this->syncOrganisations($user, $data['organisation_ids'], $currentUser);
            unset($data['organisation_ids']);
        }

        $user->update($data);
        return $user;
    }

    /**
     * Suspend user (revoke all tokens).
     */
    public function suspend(User $user, ?User $currentUser = null): User
    {
        // Revoke all tokens for the user
        $user->tokens()->delete();

        return $user;
    }

    /**
     * Activate user.
     */
    public function activate(User $user, ?User $currentUser = null): User
    {
        // For now, this is a placeholder
        // In the future, you might want to add a status field to users table
        return $user;
    }

    /**
     * Get users count by organisation.
     */
    public function getUsersCountByOrganisation(int $organisationId): int
    {
        return User::whereHas('organisations', function ($query) use ($organisationId) {
            $query->where('organisations.id', $organisationId);
        })->count();
    }

    /**
     * Search users by name or email.
     */
    public function searchUsers(string $search, int $perPage = 15, ?int $organisationId = null): LengthAwarePaginator
    {
        $query = User::with('organisations')
            ->where(function (Builder $query) use ($search) {
                $query->where('name', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%");
            });

        if ($organisationId) {
            $query->whereHas('organisations', function ($query) use ($organisationId) {
                $query->where('organisations.id', $organisationId);
            });
        }

        return $query->orderBy('created_at', 'desc')->paginate($perPage);
    }

    /**
     * Add user to organisation.
     */
    public function addToOrganisation(User $user, int $organisationId, ?User $currentUser = null): User
    {
        if (!$user->belongsToOrganisation($organisationId)) {
            $user->organisations()->attach($organisationId);
        }

        return $user;
    }

    /**
     * Remove user from organisation.
     */
    public function removeFromOrganisation(User $user, int $organisationId, ?User $currentUser = null): User
    {
        // Check if user is the owner of this organisation
        if ($user->hasOwnerRoleForOrganisation($organisationId)) {
            throw new \InvalidArgumentException(__('errors.user_cannot_remove_owner'));
        }

        // Remove all organisation-specific roles before removing from organisation
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($organisationId);
        $user->syncRoles([]);

        // Remove user from organisation
        $user->organisations()->detach($organisationId);

        return $user;
    }

    /**
     * Sync user organisations.
     */
    public function syncOrganisations(User $user, array $organisationIds, ?User $currentUser = null): User
    {
        // Get current organisation IDs
        $currentOrganisationIds = $user->getOrganisationIds()->toArray();

        // Find organisations being removed
        $removedOrganisationIds = array_diff($currentOrganisationIds, $organisationIds);

        // Check if user is owner of any organisation being removed
        foreach ($removedOrganisationIds as $organisationId) {
            if ($user->hasOwnerRoleForOrganisation($organisationId)) {
                throw new \InvalidArgumentException(__('errors.user_cannot_remove_owner_with_id', ['id' => $organisationId]));
            }
        }

        // Remove roles from organisations being removed
        foreach ($removedOrganisationIds as $organisationId) {
            app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($organisationId);
            $user->syncRoles([]);
        }

        // Sync organisations
        $user->organisations()->sync($organisationIds);

        return $user;
    }
}
