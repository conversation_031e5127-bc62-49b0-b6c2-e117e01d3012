<?php

declare(strict_types=1);

namespace App\Policies;

use App\Models\Invitation;
use App\Models\User;
use App\Traits\HasPermissionHelpers;

final class InvitationPolicy
{
    use HasPermissionHelpers;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        // System admins can view all invitations
        if ($user->hasSystemAdminAccess()) {
            return true;
        }

        // Organization owners can view invitations for their organizations
        $userOrganisationIds = $user->getOrganisationIds();
        return !$userOrganisationIds->isEmpty();
    }



    /**
     * Determine whether the user can create models.
     * This method is called by authorizeResource for store() method.
     */
    public function create(User $user): bool
    {
        // System admins can create invitations
        if ($user->hasSystemAdminAccess()) {
            return true;
        }

        // Organization owners can create invitations for their organizations
        $userOrganisationIds = $user->getOrganisationIds();
        return !$userOrganisationIds->isEmpty();
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Invitation $invitation): bool
    {
        // System admins can update any invitation
        if ($user->hasSystemAdminAccess()) {
            return true;
        }

        // Organization owners can update invitations for their organizations
        if ($invitation->model_type === 'App\Models\Organisation') {
            return $user->hasOrganisationAdminAccess($invitation->model_id);
        }

        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Invitation $invitation): bool
    {
        // System admins can delete any invitation
        if ($user->hasSystemAdminAccess()) {
            return true;
        }

        // Organization owners can delete invitations for their organizations
        if ($invitation->model_type === 'App\Models\Organisation') {
            return $user->hasOrganisationAdminAccess($invitation->model_id);
        }

        return false;
    }

    /**
     * Determine whether the user can view the invitation.
     * This method is called by authorizeResource for show() method.
     * Since show method is now unrestricted, this policy allows all authenticated users to view invitations.
     */
    public function view(User $user, Invitation $invitation): bool
    {
        // Security check: prevent system role assignment through invitations
        if ($invitation->isSystemRole()) {
            return false;
        }

        // Any authenticated user can view invitations (email restriction is checked in accept method)
        return true;
    }

    /**
     * Determine whether the user can accept the invitation.
     */
    public function accept(User $user, Invitation $invitation): bool
    {
        // Check email restriction if set
        /*
        if (!$invitation->isEmailAllowed($user->email)) {
            return false;
        }
        */

        // Security check: prevent system role assignment through invitations
        if ($invitation->isSystemRole()) {
            return false;
        }

        return true;
    }

    /**
     * Determine whether the user can create an invitation for a specific organization.
     */
    public function createForOrganisation(User $user, int $organisationId): bool
    {
        // System admins can create invitations for any organization
        if ($user->hasSystemAdminAccess()) {
            return true;
        }

        // Organization owners can create invitations for their organization
        return $user->hasOrganisationAdminAccess($organisationId);
    }
}
