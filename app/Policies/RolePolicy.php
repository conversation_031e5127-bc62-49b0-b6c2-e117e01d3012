<?php

declare(strict_types=1);

namespace App\Policies;

use App\Models\Role;
use App\Models\User;
use Illuminate\Auth\Access\Response;

final class RolePolicy
{
    /**
     * Determine whether the user can view any models.
     * Based on RoleController middleware (AdminAccessMiddleware).
     */
    public function viewAny(User $user): bool
    {
        // Only system admins can view roles
        return $user->hasSystemAdminAccess();
    }

    /**
     * Determine whether the user can view the model.
     * System admins can view any role, organisation owners can view roles in their organisations.
     */
    public function view(User $user, Role $role): bool
    {
        // System admins can view any role
        if ($user->hasSystemAdminAccess()) {
            return true;
        }

        // For organisation roles, check if user has admin access to that organisation
        if ($role->organisation_id !== null) {
            return $user->hasOrganisationAdminAccess($role->organisation_id);
        }

        // System roles can only be viewed by system admins
        return false;
    }

    /**
     * Determine whether the user can create models.
     * Based on RoleController middleware (AdminAccessMiddleware).
     */
    public function create(User $user): bool
    {
        // Only system admins can create roles
        return $user->hasSystemAdminAccess();
    }

    /**
     * Determine whether the user can create a role for a specific organisation.
     * Based on RoleController::store() authorization logic.
     */
    public function createForOrganisation(User $user, int $organisationId): bool
    {
        // Check if user belongs to the organisation
        if (!$user->belongsToOrganisation($organisationId)) {
            return false;
        }

        // System admins can create roles for any organisation
        if ($user->hasSystemAdminAccess()) {
            return true;
        }

        // Organisation owners can create roles for their organisation
        return $user->hasOrganisationAdminAccess($organisationId);
    }

    /**
     * Determine whether the user can create system-wide roles.
     * Based on RoleController::store() authorization logic.
     */
    public function createSystemRole(User $user): bool
    {
        // Only system administrators can create system-wide roles
        return $user->hasSystemAdminAccess();
    }

    /**
     * Check if guard name is valid for organisation roles.
     * Organisation roles cannot use system guard.
     */
    public function isValidOrganisationRoleGuard(string $guardName): bool
    {
        return $guardName !== 'system';
    }

    /**
     * Check if guard name is valid for system roles.
     * System roles should use system guard.
     */
    public function isValidSystemRoleGuard(string $guardName): bool
    {
        return $guardName === 'system';
    }

    /**
     * Determine whether the user can update the model.
     * Based on RoleController::update() authorization logic.
     */
    public function update(User $user, Role $role): bool
    {
        // System roles can only be updated by system admins
        if ($role->organisation_id === null) {
            return $user->hasSystemAdminAccess();
        }

        // Organisation roles can be updated by organisation admins or system admins
        $hasSystemAccess = $user->hasSystemAdminAccess();
        $hasOrgAccess = $user->hasOrganisationAdminAccess($role->organisation_id);

        return $hasSystemAccess || $hasOrgAccess;
    }

    /**
     * Determine whether the role name can be updated.
     * Core roles cannot be renamed.
     */
    public function updateName(User $user, Role $role): bool
    {
        // First check if user can update the role
        if (!$this->update($user, $role)) {
            return false;
        }

        // Prevent updating core role names
        $coreRoles = ['root', 'admin', 'owner', 'member'];
        return !in_array($role->name, $coreRoles);
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Role $role): bool
    {
        // System roles can only be deleted by system admins
        if ($role->organisation_id === null) {
            return $user->hasSystemAdminAccess();
        }

        // Organisation roles can be deleted by organisation admins or system admins
        $hasSystemAccess = $user->hasSystemAdminAccess();
        $hasOrgAccess = $user->hasOrganisationAdminAccess($role->organisation_id);

        return $hasSystemAccess || $hasOrgAccess;
    }

    /**
     * Determine whether the role can be safely deleted.
     * Core roles cannot be deleted and roles assigned to users cannot be deleted.
     */
    public function safeDelete(User $user, Role $role): bool
    {
        // First check if user can delete the role
        if (!$this->delete($user, $role)) {
            return false;
        }

        // Prevent deletion of core roles
        $coreRoles = ['root', 'admin', 'owner', 'member'];
        if (in_array($role->name, $coreRoles)) {
            return false;
        }

        // Check if role is currently assigned to any users
        $usersWithRole = $role->users()->count();
        return $usersWithRole === 0;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Role $role): bool
    {
        // Same logic as delete
        return $this->delete($user, $role);
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Role $role): bool
    {
        // Only system admins can permanently delete roles
        return $user->hasSystemAdminAccess();
    }
}
