<?php

declare(strict_types=1);

namespace App\Policies;

use App\Models\User;
use App\Traits\HasPermissionHelpers;
use Illuminate\Auth\Access\Response;

final class UserPolicy
{
    use HasPermissionHelpers;
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        // System admins can view all users
        if ($user->hasSystemAdminAccess()) {
            return true;
        }

        // Non-system admins can view users if they belong to at least one organisation
        $userOrganisationIds = $user->getOrganisationIds();
        return !$userOrganisationIds->isEmpty();
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, User $model): bool
    {
        return $this->canAccessUserByOrganisation($user, $model);
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        // System admins can create users in any organisation
        if ($user->hasSystemAdminAccess()) {
            return true;
        }

        // Non-system admins can create users if they belong to at least one organisation
        $userOrganisationIds = $user->getOrganisationIds();
        return !$userOrganisationIds->isEmpty();
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, User $model): bool
    {
        return $this->canAccessUserByOrganisation($user, $model);
    }

    /**
     * Determine whether the user can suspend the model.
     */
    public function suspend(User $user, User $model): bool
    {
        return $this->canAccessUserByOrganisation($user, $model);
    }

    /**
     * Determine whether the user can activate the model.
     */
    public function activate(User $user, User $model): bool
    {
        return $this->canAccessUserByOrganisation($user, $model);
    }

    /**
     * Determine whether the user can add another user to an organisation.
     */
    public function addToOrganisation(User $user, User $model, int $organisationId): bool
    {
        // System admins can add users to any organisation
        if ($user->hasSystemAdminAccess()) {
            return true;
        }

        // Only organisation owners can add users to their organisation
        return $user->hasOwnerRoleForOrganisation($organisationId);
    }

    /**
     * Determine whether the user can remove another user from an organisation.
     */
    public function removeFromOrganisation(User $user, User $model, int $organisationId): bool
    {
        // System admins can remove users from any organisation
        if ($user->hasSystemAdminAccess()) {
            return true;
        }

        // Only organisation owners can remove users from their organisation
        return $user->hasOwnerRoleForOrganisation($organisationId);
    }

    /**
     * Determine whether the user can sync another user's organisations.
     */
    public function syncOrganisations(User $user, User $model, array $organisationIds): bool
    {
        // System admins can sync users to any organisations
        if ($user->hasSystemAdminAccess()) {
            return true;
        }

        // Non-system admins can only manage users in organisations they belong to
        return $this->hasAccessToAllOrganisations($user, $organisationIds);
    }



    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, User $model): bool
    {
        // Only system admins can delete users
        return $user->hasSystemAdminAccess();
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, User $model): bool
    {
        // Only system admins can restore users
        return $user->hasSystemAdminAccess();
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, User $model): bool
    {
        // Only system admins can permanently delete users
        return $user->hasSystemAdminAccess();
    }
}
