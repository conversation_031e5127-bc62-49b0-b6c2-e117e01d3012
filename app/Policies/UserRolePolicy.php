<?php

declare(strict_types=1);

namespace App\Policies;

use App\Models\Role;
use App\Models\User;
use App\Traits\HasPermissionHelpers;

final class UserRolePolicy
{
    use HasPermissionHelpers;

    /**
     * Determine whether the user can assign a role to another user.
     */
    public function assignRole(User $user, User $targetUser, Role $role): bool
    {
        // Get assigner's highest role level
        $assignerLevel = $this->getUserHighestRoleLevel($user);
        $targetRoleLevel = self::ROLE_HIERARCHY[$role->name] ?? 0;

        // Check basic hierarchy permission
        if ($assignerLevel <= $targetRoleLevel) {
            return false;
        }

        // Additional business rules
        return $this->canAssignRole($user, $targetUser, $role);
    }

    /**
     * Determine whether the user can remove a role from another user.
     */
    public function removeRole(User $user, User $targetUser, Role $role): bool
    {
        // Users cannot remove roles from themselves
        if ($user->id === $targetUser->id) {
            return false;
        }

        // Owner role cannot be directly removed
        if ($role->name === 'owner') {
            return false;
        }

        // Get remover's highest role level
        $removerLevel = $this->getUserHighestRoleLevel($user);
        $targetRoleLevel = self::ROLE_HIERARCHY[$role->name] ?? 0;

        // Can only remove roles of lower level
        return $removerLevel > $targetRoleLevel;
    }

    /**
     * Determine whether the user can transfer ownership.
     */
    public function transferOwnerRole(User $user, User $newOwner, int $organisationId): bool
    {
        // Check if current user is system admin (can transfer any owner role)
        if ($user->hasSystemAdminAccess()) {
            return true;
        }

        // Check if current user has owner role for this organisation
        return $user->hasOwnerRoleForOrganisation($organisationId);
    }

    /**
     * Determine whether the user can view assignable roles.
     */
    public function getAssignableRoles(User $user): bool
    {
        $userRoles = $user->getAllRoleNames();

        // Root, Admin, and Owner can view assignable roles
        return $userRoles->intersect(['root', 'admin', 'owner'])->isNotEmpty();
    }

    /**
     * Determine whether the user can view another user's roles.
     */
    public function getUserRoles(User $user, User $targetUser): bool
    {
        // Users can always view their own roles
        if ($user->id === $targetUser->id) {
            return true;
        }

        return $this->canAccessUserByOrganisation($user, $targetUser);
    }

}
