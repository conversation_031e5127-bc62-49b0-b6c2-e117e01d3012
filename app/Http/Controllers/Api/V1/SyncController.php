<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1;

use App\Http\Resources\Api\V1\SyncLogCollection;
use App\Http\Resources\Api\V1\SyncLogResource;
use App\Jobs\ProductSyncJob;
use App\Jobs\OrderSyncJob;
use App\Models\SyncLog;
use App\Services\ProductSyncService;
use App\Contracts\OrderSyncServiceInterface;
use App\Services\SyncProgressService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

/**
 * Sync Controller
 * 
 * Handles synchronization operations including viewing sync logs,
 * triggering manual syncs, and retrying failed syncs.
 * Only accessible by system administrators.
 */
final class SyncController extends ApiController
{
    public function __construct(
        private readonly ProductSyncService $productSyncService,
        private readonly OrderSyncServiceInterface $orderSyncService,
        private readonly SyncProgressService $progressService
    ) {
        // Set up automatic authorization for sync operations
        $this->authorizeResource(SyncLog::class, 'syncLog');
    }

    /**
     * Display a listing of sync logs.
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = min((int) $request->get('per_page', 15), 100);
        
        $logs = SyncLog::with(['records' => function($query) {
                // Only load failed records for overview
                $query->where('status', 'failed')->limit(5);
            }])
            ->when($request->sync_type, function($query, $type) {
                return $query->where('sync_type', $type);
            })
            ->when($request->status, function($query, $status) {
                return $query->where('status', $status);
            })
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);

        return $this->successResponse(
            new SyncLogCollection($logs),
            'sync.logs_retrieved'
        );
    }

    /**
     * Display the specified sync log with detailed records.
     */
    public function show(SyncLog $syncLog): JsonResponse
    {
        $syncLog->load(['records' => function($query) {
            $query->orderBy('created_at', 'desc');
        }]);

        return $this->successResponse(
            new SyncLogResource($syncLog),
            'sync.log_details_retrieved'
        );
    }

    /**
     * Manually trigger a new synchronization.
     */
    public function trigger(Request $request): JsonResponse
    {
        // Check permission for triggering sync
        $this->checkPermission('trigger', SyncLog::class);

        // Check if there are any active sync jobs
        if ($this->progressService->hasActiveSyncJobs()) {
            return $this->errorResponse(
                'sync.sync_already_running',
                ['active_jobs' => $this->progressService->getActiveSyncJobs()],
                409
            );
        }

        $config = $request->validate([
            'sync_type' => 'required|string|in:product_sync,order_sync',
            'incremental' => 'boolean',
            'batch_size' => 'integer|min:1|max:1000',
            'timeout' => 'integer|min:60|max:3600',
        ]);

        try {
            // Generate batch ID and dispatch appropriate job
            $batchId = Str::uuid()->toString();

            if ($config['sync_type'] === 'order_sync') {
                OrderSyncJob::dispatch($config, $batchId);
            } else {
                ProductSyncJob::dispatch($config, $batchId);
            }

            return $this->successResponse([
                'batch_id' => $batchId,
                'sync_type' => $config['sync_type'],
                'status' => 'queued',
                'message' => __('sync.sync_queued'),
            ], 'sync.sync_queued');
        } catch (\Exception $e) {
            return $this->errorResponse(
                'sync.sync_trigger_failed',
                ['error' => $e->getMessage()],
                500
            );
        }
    }

    /**
     * Retry a failed synchronization.
     */
    public function retry(SyncLog $syncLog): JsonResponse
    {
        // Check permission for retrying sync
        $this->checkPermission('retry', $syncLog);

        if ($syncLog->status !== 'failed') {
            return $this->errorResponse(
                'sync.retry_invalid_status',
                ['current_status' => $syncLog->status],
                422
            );
        }

        // Check if there are any active sync jobs
        if ($this->progressService->hasActiveSyncJobs()) {
            return $this->errorResponse(
                'sync.sync_already_running',
                ['active_jobs' => $this->progressService->getActiveSyncJobs()],
                409
            );
        }

        try {
            // Generate new batch ID and dispatch appropriate retry job
            $newBatchId = Str::uuid()->toString();
            $config = $syncLog->sync_config ?? [];

            if ($syncLog->sync_type === 'order_sync') {
                OrderSyncJob::dispatch(
                    $config,
                    $newBatchId,
                    true, // isRetry
                    $syncLog->batch_id // originalBatchId
                );
            } else {
                ProductSyncJob::dispatch(
                    $config,
                    $newBatchId,
                    true, // isRetry
                    $syncLog->batch_id // originalBatchId
                );
            }

            return $this->successResponse([
                'new_batch_id' => $newBatchId,
                'original_batch_id' => $syncLog->batch_id,
                'sync_type' => $syncLog->sync_type,
                'status' => 'queued',
                'message' => __('sync.retry_queued'),
            ], 'sync.retry_queued');
        } catch (\Exception $e) {
            return $this->errorResponse(
                'sync.retry_failed',
                ['error' => $e->getMessage()],
                500
            );
        }
    }

    /**
     * Get sync progress by job ID.
     */
    public function progress(Request $request): JsonResponse
    {
        // Check permission for viewing sync progress
        $this->authorize('viewAny', SyncLog::class);

        $request->validate([
            'job_id' => 'required_without:batch_id|string',
            'batch_id' => 'required_without:job_id|string',
        ]);

        try {
            $progressData = null;

            if ($request->job_id) {
                $progressData = $this->progressService->getProgress($request->job_id);
            } elseif ($request->batch_id) {
                $progressData = $this->progressService->getProgressByBatchId($request->batch_id);
            }

            if (!$progressData) {
                return $this->errorResponse(
                    'sync.progress_not_found',
                    [],
                    404
                );
            }

            return $this->successResponse($progressData, 'sync.progress_retrieved');
        } catch (\Exception $e) {
            return $this->errorResponse(
                'sync.progress_retrieval_failed',
                ['error' => $e->getMessage()],
                500
            );
        }
    }

    /**
     * Get all active sync jobs.
     */
    public function activeJobs(): JsonResponse
    {
        // Check permission for viewing sync jobs
        $this->authorize('viewAny', SyncLog::class);

        try {
            $activeJobs = $this->progressService->getActiveSyncJobs();

            return $this->successResponse([
                'active_jobs' => $activeJobs,
                'count' => count($activeJobs),
            ], 'sync.active_jobs_retrieved');
        } catch (\Exception $e) {
            return $this->errorResponse(
                'sync.active_jobs_retrieval_failed',
                ['error' => $e->getMessage()],
                500
            );
        }
    }
}
