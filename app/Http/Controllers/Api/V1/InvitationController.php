<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1;

use App\Constants\ErrorCodes;
use App\Http\Requests\Api\V1\InvitationRequest;
use App\Http\Resources\Api\V1\InvitationResource;
use App\Http\Resources\Api\V1\InvitationCollection;
use App\Models\Invitation;
use App\Services\InvitationService;
use App\Services\PermissionService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

/**
 * Invitation Controller
 *
 * Handles invitation link functionality for users to join organizations with specific roles
 */
final class InvitationController extends ApiController
{
    public function __construct(
        private readonly PermissionService $permissionService,
        private readonly InvitationService $invitationService
    ) {
        // Set up automatic authorization for Invitation resource, but exclude show method
        $this->authorizeResource(Invitation::class, 'invitation', [
            'except' => ['show']
        ]);
    }

    /**
     * Display a listing of invitations
     * System admins can view all invitations, organization owners can view their own created invitations
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = min((int) $request->get('per_page', 15), 100);

        $invitations = $this->invitationService->getInvitations(
            $request->user(),
            $perPage,
            $request->get('model_type'),
            $request->get('model_id') ? (int) $request->get('model_id') : null,
            $request->get('role'),
            $request->get('status')
        );

        return $this->successResponse(
            new InvitationCollection($invitations),
            'api.invitation.list_retrieved'
        );
    }

    /**
     * Create a new invitation
     */
    public function store(InvitationRequest $request): JsonResponse
    {
        try {
            $invitation = $this->invitationService->createInvitation(
                $request->validated(),
                $request->user()
            );

            return $this->successResponse(
                new InvitationResource($invitation->load(['model', 'createdBy'])),
                'api.invitation.created',
                201
            );
        } catch (ValidationException $e) {
            return $this->validationErrorResponse($e->errors(), $e->getMessage());
        }
    }

    /**
     * Display the specified invitation.
     * This method allows unauthenticated access to view invitation details, but requires authentication for accepting
     */
    public function show(Request $request, Invitation $invitation): JsonResponse
    {
        // Check if invitation is expired or reached usage limit - return 410 Gone
        if ($invitation->isExpired()) {
            return $this->errorResponse('api.invitation.expired', null, 410, ErrorCodes::INVITATION_EXPIRED);
        }

        if ($invitation->hasReachedUsageLimit()) {
            return $this->errorResponse('api.invitation.usage_limit_reached', null, 410, ErrorCodes::INVITATION_USAGE_LIMIT_REACHED);
        }

        // Return full invitation information using resource
        return $this->successResponse(
            new InvitationResource($invitation->load(['model', 'createdBy'])),
            'api.invitation.details_retrieved'
        );
    }

    /**
     * Accept invitation and assign role to user
     * This is a custom action, so we need explicit authorization
     */
    public function accept(Request $request, Invitation $invitation): JsonResponse
    {
        $user = $request->user();

        if (!$invitation->isEmailAllowed($user->email)) {
            return $this->validationErrorResponse(
                ['email' => [__('api.invitation.email_not_allowed')]],
                'api.invitation.email_not_allowed'
            );
        }

        $this->checkPermission('accept', $invitation);

        // Validate invitation before processing
        try {
            $this->invitationService->validateInvitationForAcceptance($invitation);
        } catch (ValidationException $e) {
            return $this->validationErrorResponse($e->errors(), $e->getMessage());
        }

        // Check email restriction before policy authorization
        /*

        // Check system role restriction
        if ($invitation->isSystemRole()) {
            return $this->errorResponse('System role invitations cannot be accepted through this method', null, 403, ErrorCodes::BUSINESS_LOGIC_ERROR);
        }
        */

        try {
            $result = $this->invitationService->acceptInvitation($invitation, $user);

            return $this->successResponse(
                $result,
                'api.invitation.accepted'
            );
        } catch (ValidationException $e) {
            return $this->validationErrorResponse($e->errors(), $e->getMessage());
        } catch (\Exception $e) {
            return $this->errorResponse(__('api.invitation.processing_error') . ': ' . $e->getMessage(), null, 500, ErrorCodes::INVITATION_PROCESSING_ERROR);
        }
    }

}
