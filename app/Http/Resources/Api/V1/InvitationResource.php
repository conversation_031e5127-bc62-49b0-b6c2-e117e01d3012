<?php

declare(strict_types=1);

namespace App\Http\Resources\Api\V1;

use App\Http\Resources\ApiResource;
use Illuminate\Http\Request;

final class InvitationResource extends ApiResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'model_type' => $this->model_type,
            'model_id' => $this->model_id,
            'role' => $this->role,
            'expires_at' => $this->expires_at,
            'max_uses' => $this->max_uses,
            'uses' => $this->uses,
            'email_restriction' => $this->email_restriction,
            'is_expired' => $this->isExpired(),
            'is_valid' => $this->isValid(),
            'has_reached_usage_limit' => $this->hasReachedUsageLimit(),
            'created_by' => [
                'id' => $this->createdBy?->id,
                'name' => $this->createdBy?->name,
                'email' => $this->createdBy?->email,
            ],
            'model_name' => $this->getModelName(),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }

    /**
     * Get model name for display purposes
     */
    private function getModelName(): string
    {
        $model = $this->model;

        if (!$model) {
            return 'Unknown';
        }

        // For Organization model, return the organization name
        if ($this->model_type === 'App\Models\Organisation') {
            return $model->name ?? 'Unknown Organization';
        }

        return 'Unknown Model';
    }
}
