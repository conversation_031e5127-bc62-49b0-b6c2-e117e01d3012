<?php

declare(strict_types=1);

namespace App\Http\Resources\Api\V1;

use App\Http\Resources\ApiResource;
use Illuminate\Http\Request;

/**
 * Order Status Report Resource
 * 
 * Transforms order status report data for API responses with chart-compatible format
 */
final class OrderStatusReportResource extends ApiResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        $data = $this->resource;
        
        $transformed = [];

        // Transform status_distribution for pie chart
        if (isset($data['status_distribution']) && is_array($data['status_distribution'])) {
            $transformed['order_status_chart'] = $this->transformOrderStatusChart($data['status_distribution']);
        }

        // Transform payment_status_distribution for pie chart
        if (isset($data['payment_status_distribution']) && is_array($data['payment_status_distribution'])) {
            $transformed['payment_status_chart'] = $this->transformPaymentStatusChart($data['payment_status_distribution']);
        }

        return $transformed;
    }

    /**
     * Transform order status data for pie chart
     */
    private function transformOrderStatusChart(array $statusData): array
    {
        $data = [];
        $statusColors = [
            'completed' => '#52c41a',
            'processing' => '#fa8c16',
            'cancelled' => '#ff4d4f',
            'pending' => '#722ed1'
        ];

        foreach ($statusData as $item) {
            $statusName = $this->getStatusDisplayName($item['status']);
            $data[] = [
                'name' => $statusName,
                'value' => (int) $item['order_count'],
                'color' => $statusColors[$item['status']] ?? '#8c8c8c'
            ];
        }

        return [
            'data' => $data
        ];
    }

    /**
     * Transform payment status data for pie chart
     */
    private function transformPaymentStatusChart(array $paymentStatusData): array
    {
        $data = [];
        $statusColors = [
            'completed' => '#52c41a',
            'pending' => '#fa8c16',
            'failed' => '#ff4d4f',
            'cancelled' => '#722ed1'
        ];

        foreach ($paymentStatusData as $item) {
            $statusName = $this->getPaymentStatusDisplayName($item['payment_status']);
            $data[] = [
                'name' => $statusName,
                'value' => (int) $item['order_count'],
                'color' => $statusColors[$item['payment_status']] ?? '#8c8c8c'
            ];
        }

        return [
            'data' => $data
        ];
    }

    /**
     * Get display name for order status using translation
     */
    private function getStatusDisplayName(string $status): string
    {
        $translationKey = "api.reports.order_status.{$status}";
        $translated = __($translationKey);
        
        // If translation key not found, return the original status
        if ($translated === $translationKey) {
            return $status;
        }
        
        return $translated;
    }

    /**
     * Get display name for payment status using translation
     */
    private function getPaymentStatusDisplayName(string $paymentStatus): string
    {
        $translationKey = "api.reports.payment_status.{$paymentStatus}";
        $translated = __($translationKey);
        
        // If translation key not found, return the original status
        if ($translated === $translationKey) {
            return $paymentStatus;
        }
        
        return $translated;
    }
}
