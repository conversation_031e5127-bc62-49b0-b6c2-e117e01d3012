<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\File;
use Symfony\Component\HttpFoundation\Response;

/**
 * Locale Middleware
 * 
 * Handles language setting based on request headers
 * Sets the application locale for internationalization
 */
final class LocaleMiddleware
{
    /**
     * Supported locales
     */
    private const SUPPORTED_LOCALES = ['en', 'zh'];

    /**
     * Default locale
     */
    private const DEFAULT_LOCALE = 'en';

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $locale = $this->determineLocale($request);
        
        // Set the application locale
        App::setLocale($locale);
        
        return $next($request);
    }

    /**
     * Determine the locale from request headers
     */
    private function determineLocale(Request $request): string
    {
        // Check for custom X-Locale header first (highest priority)
        $customLocale = $request->header('X-Locale');
        if ($customLocale && $this->isValidLocale($customLocale)) {
            return $customLocale;
        }

        // Check for Accept-Language header
        $acceptLanguage = $request->header('Accept-Language');

        if ($acceptLanguage) {
            $locale = $this->parseAcceptLanguageHeader($acceptLanguage);
            if ($locale && $this->isValidLocale($locale)) {
                return $locale;
            }
        }

        // Return default locale
        return self::DEFAULT_LOCALE;
    }

    /**
     * Parse Accept-Language header to extract preferred locale
     */
    private function parseAcceptLanguageHeader(string $acceptLanguage): ?string
    {
        // Parse Accept-Language header (e.g., "zh-CN,zh;q=0.9,en;q=0.8")
        $languages = explode(',', $acceptLanguage);
        
        foreach ($languages as $language) {
            $lang = trim(explode(';', $language)[0]);
            
            // Extract primary language code (e.g., "zh" from "zh-CN")
            $primaryLang = explode('-', $lang)[0];
            
            if (in_array($primaryLang, self::SUPPORTED_LOCALES, true)) {
                return $primaryLang;
            }
        }

        return null;
    }

    /**
     * Check if the locale is valid and has corresponding language files
     */
    private function isValidLocale(string $locale): bool
    {
        // Check if locale is in supported list
        if (!in_array($locale, self::SUPPORTED_LOCALES, true)) {
            return false;
        }

        // Check if language files exist
        $langPath = lang_path($locale);
        
        if (!File::isDirectory($langPath)) {
            return false;
        }

        // Check if required language files exist
        $requiredFiles = ['api.php', 'errors.php'];
        
        foreach ($requiredFiles as $file) {
            if (!File::exists($langPath . DIRECTORY_SEPARATOR . $file)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get supported locales
     */
    public static function getSupportedLocales(): array
    {
        return self::SUPPORTED_LOCALES;
    }

    /**
     * Get default locale
     */
    public static function getDefaultLocale(): string
    {
        return self::DEFAULT_LOCALE;
    }
}
