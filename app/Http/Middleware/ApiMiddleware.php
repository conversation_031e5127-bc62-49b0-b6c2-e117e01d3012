<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * API Middleware
 * 
 * Handles API-specific concerns like CORS, content type, and response headers
 */
final class ApiMiddleware
{
    /**
     * Handle an incoming request
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Log incoming request
        $startTime = microtime(true);
        $this->logRequest($request);

        // Ensure JSON content type for API requests
        if (!$request->isJson() && $request->isMethod('POST', 'PUT', 'PATCH')) {
            $request->headers->set('Content-Type', 'application/json');
        }

        // Process the request
        $response = $next($request);

        // Log response
        $endTime = microtime(true);
        $this->logResponse($request, $response, $endTime - $startTime);

        // Add API-specific headers
        $response->headers->set('Content-Type', 'application/json');
        $response->headers->set('X-API-Version', '1.0');
        $response->headers->set('X-Powered-By', config('app.name'));

        // Handle CORS
        $response->headers->set('Access-Control-Allow-Origin', '*');
        $response->headers->set('Access-Control-Allow-Methods', 'GET, POST, PUT, PATCH, DELETE, OPTIONS');
        $response->headers->set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept, Origin');
        $response->headers->set('Access-Control-Max-Age', '86400');

        // Handle preflight OPTIONS requests
        if ($request->isMethod('OPTIONS')) {
            $response->setStatusCode(200);
            $response->setContent('');
        }

        return $response;
    }

    /**
     * Log incoming request details
     */
    private function logRequest(Request $request): void
    {
        try {
            \Log::channel('requests')->info('API Request', [
                'method' => $request->getMethod(),
                'url' => $request->fullUrl(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'headers' => $this->getFilteredHeaders($request),
                'body' => $this->getFilteredBody($request),
                'timestamp' => now()->toISOString(),
            ]);
        } catch (\Exception $e) {
            // Fallback to default logger if requests channel fails
            \Log::info('API Request (fallback)', [
                'method' => $request->getMethod(),
                'url' => $request->fullUrl(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'timestamp' => now()->toISOString(),
                'log_error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Log response details
     */
    private function logResponse(Request $request, Response $response, float $duration): void
    {
        try {
            \Log::channel('requests')->info('API Response', [
                'method' => $request->getMethod(),
                'url' => $request->fullUrl(),
                'status_code' => $response->getStatusCode(),
                'duration_ms' => round($duration * 1000, 2),
                'response_size' => strlen($response->getContent()),
                'timestamp' => now()->toISOString(),
            ]);
        } catch (\Exception $e) {
            // Fallback to default logger if requests channel fails
            \Log::info('API Response (fallback)', [
                'method' => $request->getMethod(),
                'url' => $request->fullUrl(),
                'status_code' => $response->getStatusCode(),
                'duration_ms' => round($duration * 1000, 2),
                'timestamp' => now()->toISOString(),
                'log_error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Get filtered headers (exclude sensitive information)
     */
    private function getFilteredHeaders(Request $request): array
    {
        $headers = $request->headers->all();

        // Remove sensitive headers
        $sensitiveHeaders = ['authorization', 'cookie', 'x-api-key'];
        foreach ($sensitiveHeaders as $header) {
            if (isset($headers[$header])) {
                $headers[$header] = ['***FILTERED***'];
            }
        }

        return $headers;
    }

    /**
     * Get filtered request body (exclude sensitive information)
     */
    private function getFilteredBody(Request $request): mixed
    {
        if (!$request->isJson()) {
            return null;
        }

        $body = $request->all();

        // Remove sensitive fields
        $sensitiveFields = ['password', 'password_confirmation', 'token', 'secret'];
        foreach ($sensitiveFields as $field) {
            if (isset($body[$field])) {
                $body[$field] = '***FILTERED***';
            }
        }

        return $body;
    }
}
