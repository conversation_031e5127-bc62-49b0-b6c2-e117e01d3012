<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Spatie\Permission\Exceptions\UnauthorizedException;
use Symfony\Component\HttpFoundation\Response;

/**
 * Admin Access Middleware
 * 
 * Restricts access to users with Root or Admin roles.
 * This middleware is reusable across different controllers that require admin access.
 */
final class AdminAccessMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();

        if (!$user) {
            throw UnauthorizedException::notLoggedIn();
        }

        // Clear team context for system role checks
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);

        // Check if user has system admin access (root or admin with system guard)
        $hasSystemAccess = $user->hasSystemAdminAccess();

        if (!$hasSystemAccess) {
            throw UnauthorizedException::forRoles(['root', 'admin']);
        }

        return $next($request);
    }
}
