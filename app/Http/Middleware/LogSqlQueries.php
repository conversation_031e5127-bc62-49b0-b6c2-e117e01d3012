<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class LogSqlQueries
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Only enable in local environment
        if (!app()->environment('local')) {
            return $next($request);
        }

        // Show info message once about log location
        static $infoShown = false;
        if (!$infoShown && php_sapi_name() === 'cli-server') {
            $infoShown = true;
            $logFile = storage_path('logs/sql_debug_' . date('Y-m-d') . '.log');
            // Show startup message in console
            file_put_contents('php://stderr', "[" . date('H:i:s') . "] SQL_DEBUG: Middleware enabled - logging to {$logFile}" . PHP_EOL);
            file_put_contents('php://stderr', "[" . date('H:i:s') . "] SQL_DEBUG: Real-time output enabled in console" . PHP_EOL);
        }

        // Record request start time
        $requestStartTime = microtime(true);

        // Log request information
        $this->logRequestInfo($request);

        // Start SQL query logging
        $queries = [];
        DB::listen(function ($query) use (&$queries) {
            $sql = $query->sql;
            $bindings = $query->bindings;
            $time = $query->time;

            // Replace placeholders with actual values
            foreach ($bindings as $binding) {
                if (is_string($binding)) {
                    $binding = "'{$binding}'";
                } elseif (is_null($binding)) {
                    $binding = 'NULL';
                } elseif (is_bool($binding)) {
                    $binding = $binding ? 'TRUE' : 'FALSE';
                }
                $sql = preg_replace('/\?/', $binding, $sql, 1);
            }

            $queries[] = [
                'sql' => $sql,
                'time' => $time,
                'bindings' => $query->bindings
            ];

            // Output each query immediately to terminal
            $output = "\033[36m[SQL]\033[0m \033[33m[{$time}ms]\033[0m {$sql}";
            $this->writeToConsole($output);
        });

        // Record time before processing request
        $processingStartTime = microtime(true);

        // Process the request
        $response = $next($request);

        // Record time after processing request
        $processingEndTime = microtime(true);

        // Calculate timing metrics
        $totalRequestTime = ($processingEndTime - $requestStartTime) * 1000; // Convert to milliseconds
        $processingTime = ($processingEndTime - $processingStartTime) * 1000;
        $middlewareOverhead = $totalRequestTime - $processingTime;

        // Log summary after request completion
        $this->logRequestSummary($request, $queries, [
            'total_time' => $totalRequestTime,
            'processing_time' => $processingTime,
            'middleware_overhead' => $middlewareOverhead,
            'response_size' => strlen($response->getContent()),
            'status_code' => $response->getStatusCode()
        ]);

        return $response;
    }

    /**
     * Log request information
     */
    private function logRequestInfo(Request $request): void
    {
        $method = $request->getMethod();
        $url = $request->fullUrl();
        $ip = $request->ip();

        // Log request start
        $output = "\033[32m[REQUEST START]\033[0m \033[1m{$method}\033[0m {$url} \033[90m(IP: {$ip})\033[0m";
        $this->writeToConsole($output);

        // Log route information (controller and method)
        $this->logRouteInfo($request);

        // Log request headers (only important ones)
        $importantHeaders = ['content-type', 'authorization', 'accept', 'user-agent'];
        foreach ($importantHeaders as $header) {
            if ($request->hasHeader($header)) {
                $value = $request->header($header);
                // Mask authorization header for security
                if ($header === 'authorization' && is_string($value)) {
                    $value = 'Bearer ' . substr($value, -10);
                }
                $output = "\033[94m[HEADER]\033[0m {$header}: {$value}";
                $this->writeToConsole($output);
            }
        }

        // Log request parameters
        $this->logRequestParameters($request);
    }

    /**
     * Log route information (controller and method)
     */
    private function logRouteInfo(Request $request): void
    {
        $route = $request->route();

        if ($route) {
            $routeName = $route->getName() ?: 'N/A';
            $routeUri = $route->uri();
            $action = $route->getAction();

            // Extract controller and method information
            $controller = 'N/A';
            $method = 'N/A';

            if (isset($action['controller'])) {
                $controllerAction = $action['controller'];
                if (is_string($controllerAction) && strpos($controllerAction, '@') !== false) {
                    // Format: ControllerClass@method
                    [$controllerClass, $methodName] = explode('@', $controllerAction);
                    $controller = class_basename($controllerClass);
                    $method = $methodName;
                } elseif (is_string($controllerAction)) {
                    // Format: ControllerClass::class (invokable controller)
                    $controller = class_basename($controllerAction);
                    $method = '__invoke';
                } elseif (is_array($controllerAction) && count($controllerAction) === 2) {
                    // Format: [ControllerClass::class, 'method']
                    [$controllerClass, $methodName] = $controllerAction;
                    $controller = class_basename($controllerClass);
                    $method = $methodName;
                }
            } elseif (isset($action['uses']) && is_callable($action['uses'])) {
                // Closure route
                $controller = 'Closure';
                $method = 'anonymous';
            }

            // Log route information

            $controllerOutput = "\033[92m[CONTROLLER]\033[0m " .
                "Class: \033[33m{$controller}\033[0m | " .
                "Method: \033[33m{$method}\033[0m";
            $this->writeToConsole($controllerOutput);

            // Log middleware applied to this route
            $middleware = $route->gatherMiddleware();
            if (!empty($middleware)) {
                $middlewareList = implode(', ', array_map(function($m) {
                    return is_string($m) ? $m : (is_object($m) ? get_class($m) : 'Unknown');
                }, $middleware));
                $middlewareOutput = "\033[96m[MIDDLEWARE]\033[0m {$middlewareList}";
                $this->writeToConsole($middlewareOutput);
            }
        } else {
            // No route found (might be a 404 or other special case)
            $output = "\033[91m[ROUTE]\033[0m No route matched for this request";
            $this->writeToConsole($output);
        }
    }

    /**
     * Log request parameters
     */
    private function logRequestParameters(Request $request): void
    {
        // Log query parameters
        if ($request->query->count() > 0) {
            $queryParams = $request->query->all();
            $output = "\033[95m[QUERY PARAMS]\033[0m " . json_encode($queryParams, JSON_UNESCAPED_UNICODE);
            $this->writeToConsole($output);
        }

        // Log request body (for POST, PUT, PATCH requests)
        if (in_array($request->getMethod(), ['POST', 'PUT', 'PATCH', 'DELETE'])) {
            $body = $request->all();

            // Mask sensitive fields
            $sensitiveFields = ['password', 'password_confirmation', 'token', 'secret'];
            foreach ($sensitiveFields as $field) {
                if (isset($body[$field])) {
                    $body[$field] = '***MASKED***';
                }
            }

            if (!empty($body)) {
                $output = "\033[93m[REQUEST BODY]\033[0m " . json_encode($body, JSON_UNESCAPED_UNICODE);
                $this->writeToConsole($output);
            }
        }

        // Log uploaded files
        $allFiles = $request->allFiles();
        if (!empty($allFiles)) {
            $files = [];
            foreach ($allFiles as $key => $file) {
                if (is_array($file)) {
                    foreach ($file as $index => $f) {
                        $files["{$key}[{$index}]"] = [
                            'name' => $f->getClientOriginalName(),
                            'size' => $f->getSize(),
                            'mime' => $f->getMimeType()
                        ];
                    }
                } else {
                    $files[$key] = [
                        'name' => $file->getClientOriginalName(),
                        'size' => $file->getSize(),
                        'mime' => $file->getMimeType()
                    ];
                }
            }
            $output = "\033[96m[FILES]\033[0m " . json_encode($files, JSON_UNESCAPED_UNICODE);
            $this->writeToConsole($output);
        }
    }

    /**
     * Log request summary
     */
    private function logRequestSummary(Request $request, array $queries, array $timing = []): void
    {
        $method = $request->getMethod();
        $url = $request->fullUrl();
        $queryCount = count($queries);
        $sqlTotalTime = array_sum(array_column($queries, 'time'));

        // Basic request info
        $output = "\033[32m[REQUEST END]\033[0m \033[1m{$method}\033[0m {$url}";
        $this->writeToConsole($output);

        // Log controller and method in summary
        $this->logControllerSummary($request);

        // SQL statistics
        $sqlOutput = "\033[36m[SQL STATS]\033[0m \033[33m{$queryCount} queries\033[0m \033[33m({$sqlTotalTime}ms SQL time)\033[0m";
        $this->writeToConsole($sqlOutput);

        // Performance timing (if available)
        if (!empty($timing)) {
            $totalTime = round($timing['total_time'], 2);
            $processingTime = round($timing['processing_time'], 2);
            $middlewareOverhead = round($timing['middleware_overhead'], 2);
            $responseSize = $this->formatBytes($timing['response_size']);
            $statusCode = $timing['status_code'];

            $timingOutput = "\033[35m[PERFORMANCE]\033[0m " .
                "Total: \033[33m{$totalTime}ms\033[0m | " .
                "Processing: \033[33m{$processingTime}ms\033[0m | " .
                "Middleware: \033[33m{$middlewareOverhead}ms\033[0m";
            $this->writeToConsole($timingOutput);

            $responseOutput = "\033[94m[RESPONSE]\033[0m " .
                "Status: \033[33m{$statusCode}\033[0m | " .
                "Size: \033[33m{$responseSize}\033[0m";
            $this->writeToConsole($responseOutput);

            // Performance analysis
            $this->logPerformanceAnalysis($timing, $sqlTotalTime, $queryCount);
        }

        // Add separator line
        $this->writeToConsole("\033[90m" . str_repeat('-', 80) . "\033[0m");
    }

    /**
     * Log controller summary in request end
     */
    private function logControllerSummary(Request $request): void
    {
        $route = $request->route();

        if ($route) {
            $action = $route->getAction();
            $controller = 'N/A';
            $method = 'N/A';

            // Extract controller and method information (same logic as logRouteInfo)
            if (isset($action['controller'])) {
                $controllerAction = $action['controller'];
                if (is_string($controllerAction) && strpos($controllerAction, '@') !== false) {
                    [$controllerClass, $methodName] = explode('@', $controllerAction);
                    $controller = class_basename($controllerClass);
                    $method = $methodName;
                } elseif (is_string($controllerAction)) {
                    $controller = class_basename($controllerAction);
                    $method = '__invoke';
                } elseif (is_array($controllerAction) && count($controllerAction) === 2) {
                    [$controllerClass, $methodName] = $controllerAction;
                    $controller = class_basename($controllerClass);
                    $method = $methodName;
                }
            } elseif (isset($action['uses']) && is_callable($action['uses'])) {
                $controller = 'Closure';
                $method = 'anonymous';
            }

            $controllerSummary = "\033[92m[EXECUTED]\033[0m " .
                "Controller: \033[33m{$controller}\033[0m | " .
                "Method: \033[33m{$method}\033[0m";
            $this->writeToConsole($controllerSummary);
        }
    }

    /**
     * Log performance analysis
     */
    private function logPerformanceAnalysis(array $timing, float $sqlTime, int $queryCount): void
    {
        $totalTime = $timing['total_time'];
        $processingTime = $timing['processing_time'];

        $analysis = [];

        // SQL performance analysis
        if ($queryCount > 0) {
            $sqlPercentage = round(($sqlTime / $totalTime) * 100, 1);
            $avgQueryTime = round($sqlTime / $queryCount, 2);

            if ($sqlPercentage > 50) {
                $analysis[] = "⚠️  SQL takes {$sqlPercentage}% of total time, consider query optimization";
            }

            if ($avgQueryTime > 10) {
                $analysis[] = "⚠️  Average query time {$avgQueryTime}ms is slow";
            }

            if ($queryCount > 20) {
                $analysis[] = "⚠️  Too many queries ({$queryCount}), consider reducing N+1 problems";
            }
        }

        // Overall performance analysis
        if ($totalTime > 1000) {
            $analysis[] = "🐌 Request time exceeds 1 second, optimization needed";
        } elseif ($totalTime > 500) {
            $analysis[] = "⚠️  Request time is long, optimization recommended";
        } elseif ($totalTime < 100) {
            $analysis[] = "🚀 Response speed is good";
        }

        // Output analysis
        if (!empty($analysis)) {
            foreach ($analysis as $note) {
                $this->writeToConsole("\033[91m[ANALYSIS]\033[0m {$note}");
            }
        }
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes(int $bytes): string
    {
        if ($bytes >= 1048576) {
            return round($bytes / 1048576, 2) . 'MB';
        } elseif ($bytes >= 1024) {
            return round($bytes / 1024, 2) . 'KB';
        } else {
            return $bytes . 'B';
        }
    }

    /**
     * Check if we're running under Laravel's development server
     */
    private function isLaravelServe(): bool
    {
        return isset($_SERVER['SERVER_SOFTWARE']) &&
               strpos($_SERVER['SERVER_SOFTWARE'], 'PHP') !== false &&
               isset($_SERVER['SERVER_NAME']) &&
               $_SERVER['SERVER_NAME'] === 'localhost';
    }

    /**
     * Write output to both console and log file
     */
    private function writeToConsole(string $message): void
    {
        // Always write to log file with daily rotation
        $logFile = storage_path('logs/sql_debug_' . date('Y-m-d') . '.log');
        $timestamp = date('Y-m-d H:i:s');
        $cleanMessage = preg_replace('/\033\[[0-9;]*m/', '', $message);
        file_put_contents($logFile, "[{$timestamp}] {$cleanMessage}" . PHP_EOL, FILE_APPEND | LOCK_EX);

        // Output to console using a safe method that avoids Laravel serve conflicts
        $this->outputToConsole($message);
    }

    /**
     * Output directly to console with colors preserved
     */
    private function outputToConsole(string $message): void
    {
        // Use error_log to output with colors preserved
        error_log($message);
    }
}
