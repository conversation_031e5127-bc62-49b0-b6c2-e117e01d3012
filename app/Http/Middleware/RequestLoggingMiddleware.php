<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

/**
 * Request Logging Middleware
 * 
 * Logs all HTTP requests and responses for monitoring and debugging purposes
 */
final class RequestLoggingMiddleware
{
    /**
     * Handle an incoming request
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Skip logging if disabled
        if (!config('app.log_requests', true)) {
            return $next($request);
        }

        $startTime = microtime(true);

        // Log the incoming request
        $this->logIncomingRequest($request);

        // Process the request
        $response = $next($request);

        // Log the response
        $duration = microtime(true) - $startTime;
        $this->logResponse($request, $response, $duration);

        return $response;
    }

    /**
     * Log incoming request details
     */
    private function logIncomingRequest(Request $request): void
    {
        $logData = [
            'type' => 'request',
            'method' => $request->getMethod(),
            'url' => $request->fullUrl(),
            'path' => $request->getPathInfo(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'referer' => $request->header('referer'),
            'content_type' => $request->header('content-type'),
            'accept' => $request->header('accept'),
            'user_id' => $request->user()?->id,
            'session_id' => $this->getSessionId($request),
            'headers' => $this->getFilteredHeaders($request),
            'query_params' => $request->query(),
            'body' => $this->getFilteredBody($request),
            'timestamp' => now()->toISOString(),
        ];

        try {
            Log::channel('requests')->info('Incoming Request', $logData);
        } catch (\Exception $e) {
            // Fallback to default logger if requests channel fails
            Log::info('Incoming Request (fallback)', array_merge($logData, ['log_error' => $e->getMessage()]));
        }
    }

    /**
     * Log response details
     */
    private function logResponse(Request $request, Response $response, float $duration): void
    {
        $logData = [
            'type' => 'response',
            'method' => $request->getMethod(),
            'url' => $request->fullUrl(),
            'path' => $request->getPathInfo(),
            'status_code' => $response->getStatusCode(),
            'status_text' => $this->getStatusText($response->getStatusCode()),
            'duration_ms' => round($duration * 1000, 2),
            'memory_usage_mb' => round(memory_get_peak_usage(true) / 1024 / 1024, 2),
            'response_size_bytes' => strlen($response->getContent()),
            'content_type' => $response->headers->get('content-type'),
            'user_id' => $request->user()?->id,
            'timestamp' => now()->toISOString(),
        ];

        // Add response body for non-successful responses (for debugging)
        if ($response->getStatusCode() >= 400) {
            $logData['response_body'] = $this->getFilteredResponseBody($response);
        }

        try {
            Log::channel('requests')->info('Response Sent', $logData);
        } catch (\Exception $e) {
            // Fallback to default logger if requests channel fails
            Log::info('Response Sent (fallback)', array_merge($logData, ['log_error' => $e->getMessage()]));
        }
    }

    /**
     * Get filtered headers (exclude sensitive information)
     */
    private function getFilteredHeaders(Request $request): array
    {
        $headers = $request->headers->all();
        
        // Remove sensitive headers
        $sensitiveHeaders = [
            'authorization',
            'cookie',
            'x-api-key',
            'x-auth-token',
            'x-access-token',
            'bearer',
        ];
        
        foreach ($sensitiveHeaders as $header) {
            if (isset($headers[$header])) {
                $headers[$header] = ['***FILTERED***'];
            }
        }
        
        return $headers;
    }

    /**
     * Get filtered request body (exclude sensitive information)
     */
    private function getFilteredBody(Request $request): mixed
    {
        // Don't log file uploads
        if ($request->hasFile('*')) {
            return ['***FILE_UPLOAD***'];
        }

        // Don't log non-JSON requests with large bodies
        if (!$request->isJson() && strlen($request->getContent()) > 1024) {
            return ['***LARGE_NON_JSON_BODY***'];
        }

        $body = $request->all();
        
        // Remove sensitive fields
        $sensitiveFields = [
            'password',
            'password_confirmation',
            'token',
            'secret',
            'api_key',
            'access_token',
            'refresh_token',
            'current_password',
            'new_password',
        ];
        
        foreach ($sensitiveFields as $field) {
            if (isset($body[$field])) {
                $body[$field] = '***FILTERED***';
            }
        }
        
        return $body;
    }

    /**
     * Get filtered response body for error responses
     */
    private function getFilteredResponseBody(Response $response): mixed
    {
        $content = $response->getContent();
        
        // Only log JSON responses
        if (!$this->isJson($content)) {
            return ['***NON_JSON_RESPONSE***'];
        }

        // Limit response body size
        if (strlen($content) > 2048) {
            return ['***LARGE_RESPONSE_BODY***', 'size' => strlen($content)];
        }

        $decoded = json_decode($content, true);
        
        // Remove sensitive data from response
        if (is_array($decoded)) {
            $sensitiveFields = ['token', 'secret', 'password', 'api_key'];
            foreach ($sensitiveFields as $field) {
                if (isset($decoded[$field])) {
                    $decoded[$field] = '***FILTERED***';
                }
            }
        }

        return $decoded;
    }

    /**
     * Get session ID safely (handles API-only applications without sessions)
     */
    private function getSessionId(Request $request): ?string
    {
        try {
            // Check if session is available before accessing it
            if ($request->hasSession()) {
                return $request->session()->getId();
            }
        } catch (\Exception $e) {
            // Session not available (e.g., API-only application)
        }

        return null;
    }

    /**
     * Get HTTP status text from status code
     */
    private function getStatusText(int $statusCode): string
    {
        $statusTexts = [
            200 => 'OK',
            201 => 'Created',
            202 => 'Accepted',
            204 => 'No Content',
            301 => 'Moved Permanently',
            302 => 'Found',
            304 => 'Not Modified',
            400 => 'Bad Request',
            401 => 'Unauthorized',
            403 => 'Forbidden',
            404 => 'Not Found',
            405 => 'Method Not Allowed',
            422 => 'Unprocessable Entity',
            429 => 'Too Many Requests',
            500 => 'Internal Server Error',
            502 => 'Bad Gateway',
            503 => 'Service Unavailable',
        ];

        return $statusTexts[$statusCode] ?? 'Unknown';
    }

    /**
     * Check if content is JSON
     */
    private function isJson(string $content): bool
    {
        json_decode($content);
        return json_last_error() === JSON_ERROR_NONE;
    }
}
