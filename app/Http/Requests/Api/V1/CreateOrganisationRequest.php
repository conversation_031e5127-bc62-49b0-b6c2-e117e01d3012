<?php

declare(strict_types=1);

namespace App\Http\Requests\Api\V1;

use Illuminate\Foundation\Http\FormRequest;

final class CreateOrganisationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'code' => ['required', 'string', 'max:50', 'unique:organisations,code'],
            'details' => ['nullable', 'array'],
            'remarks' => ['nullable', 'string', 'max:1000'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => __('api.validation.organisation.name_required'),
            'name.string' => __('api.validation.organisation.name_string'),
            'name.max' => __('api.validation.organisation.name_max'),
            'code.required' => __('api.validation.organisation.code_required'),
            'code.string' => __('api.validation.organisation.code_string'),
            'code.max' => __('api.validation.organisation.code_max'),
            'code.unique' => __('api.validation.organisation.code_unique'),
            'details.array' => __('api.validation.organisation.details_array'),
            'remarks.string' => __('api.validation.organisation.remarks_string'),
            'remarks.max' => __('api.validation.organisation.remarks_max'),
            'status.string' => __('api.validation.organisation.status_string'),
            'status.in' => __('api.validation.organisation.status_in'),
        ];
    }
}
