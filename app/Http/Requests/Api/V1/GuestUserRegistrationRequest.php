<?php

declare(strict_types=1);

namespace App\Http\Requests\Api\V1;

use App\Services\EmailVerificationService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\ValidationException;

final class GuestUserRegistrationRequest extends FormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Allow unauthenticated access
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email|max:255',
            'password' => 'required|string|min:8|max:255',
            'verification_code' => 'required|string|size:6',
            // organisation_ids is not allowed for guest registration
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Verify verification code
            $email = $this->input('email');
            $verificationCode = $this->input('verification_code');

            if ($email && $verificationCode) {
                $emailVerificationService = app(EmailVerificationService::class);
                if (!$emailVerificationService->verifyCode($email, $verificationCode)) {
                    $validator->errors()->add('verification_code', __('api.validation.user.verification_code_invalid'));
                }
            }

            // Ensure organisation_ids is not provided for guest registration
            if ($this->has('organisation_ids')) {
                $validator->errors()->add('organisation_ids', __('api.validation.user.organisation_ids_not_allowed'));
            }
        });
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => __('api.validation.user.name_required'),
            'name.string' => __('api.validation.user.name_string'),
            'name.max' => __('api.validation.user.name_max'),
            'email.required' => __('api.validation.user.email_required'),
            'email.email' => __('api.validation.user.email_format'),
            'email.unique' => __('api.validation.user.email_unique'),
            'email.max' => __('api.validation.user.email_max'),
            'password.required' => __('api.validation.user.password_required'),
            'password.string' => __('api.validation.user.password_string'),
            'password.min' => __('api.validation.user.password_min'),
            'password.max' => __('api.validation.user.password_max'),
            'verification_code.required' => __('api.validation.user.verification_code_required'),
            'verification_code.string' => __('api.validation.user.verification_code_string'),
            'verification_code.size' => __('api.validation.user.verification_code_size'),
        ];
    }
}
