<?php

declare(strict_types=1);

namespace App\Http\Requests\Api\V1;

use Illuminate\Foundation\Http\FormRequest;

final class InvitationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Authorization is handled in the controller
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'model_type' => 'required|string|in:App\Models\Organisation',
            'model_id' => 'required|integer|exists:organisations,id',
            'role' => 'required|string|in:owner,member',
            'expires_at' => 'nullable|date|after:now',
            'max_uses' => 'nullable|integer|min:1|max:100',
            'email_restriction' => 'nullable|email',
        ];
    }

    /**
     * Get custom error messages for validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'model_type.required' => __('api.validation.invitation.model_type_required'),
            'model_type.in' => __('api.validation.invitation.model_type_in'),
            'model_id.required' => __('api.validation.invitation.model_id_required'),
            'model_id.exists' => __('api.validation.invitation.model_id_exists'),
            'role.required' => __('api.validation.invitation.role_required'),
            'role.in' => __('api.validation.invitation.role_in'),
            'expires_at.date' => __('api.validation.invitation.expires_at_date'),
            'expires_at.after' => __('api.validation.invitation.expires_at_after'),
            'max_uses.integer' => __('api.validation.invitation.max_uses_integer'),
            'max_uses.min' => __('api.validation.invitation.max_uses_min'),
            'max_uses.max' => __('api.validation.invitation.max_uses_max'),
            'email_restriction.email' => __('api.validation.invitation.email_restriction_email'),
        ];
    }

    /**
     * Get custom attribute names for validation errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'model_type' => __('api.validation.invitation_attributes.model_type'),
            'model_id' => __('api.validation.invitation_attributes.model_id'),
            'role' => __('api.validation.invitation_attributes.role'),
            'expires_at' => __('api.validation.invitation_attributes.expires_at'),
            'max_uses' => __('api.validation.invitation_attributes.max_uses'),
            'email_restriction' => __('api.validation.invitation_attributes.email_restriction'),
        ];
    }
}
