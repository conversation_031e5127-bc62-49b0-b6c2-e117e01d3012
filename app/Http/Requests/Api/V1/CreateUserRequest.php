<?php

declare(strict_types=1);

namespace App\Http\Requests\Api\V1;

use Illuminate\Foundation\Http\FormRequest;

final class CreateUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization will be handled by middleware
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email|max:255',
            'password' => 'required|string|min:8|max:255',
            'organisation_ids' => 'nullable|array',
            'organisation_ids.*' => 'integer|exists:organisations,id',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => __('api.validation.user.name_required'),
            'name.string' => __('api.validation.user.name_string'),
            'name.max' => __('api.validation.user.name_max'),
            'email.required' => __('api.validation.user.email_required'),
            'email.email' => __('api.validation.user.email_format'),
            'email.unique' => __('api.validation.user.email_unique'),
            'email.max' => __('api.validation.user.email_max'),
            'password.required' => __('api.validation.user.password_required'),
            'password.string' => __('api.validation.user.password_string'),
            'password.min' => __('api.validation.user.password_min'),
            'password.max' => __('api.validation.user.password_max'),
            'organisation_ids.array' => __('api.validation.user.organisation_ids_array'),
            'organisation_ids.*.integer' => __('api.validation.user.organisation_ids_integer'),
            'organisation_ids.*.exists' => __('api.validation.user.organisation_ids_exists'),
        ];
    }
}
