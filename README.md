# jast_partner

## Installation

1. `make init ENV=development`
2. `make up ENV=development`
3. `make artisan cmd="migrate:fresh --seed" ENV=development`
4. `make artisan cmd="db:seed" ENV=development`
4. `make down ENV=development`


## Installation without makefile

1. `php artisan migrate:fresh --seed --env=development`
2. `php artisan db:seed --env=development`

## start web ui dev server
`cd webui && pnpm run dev`
