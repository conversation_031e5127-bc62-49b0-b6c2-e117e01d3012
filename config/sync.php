<?php

return [
    // sync config
    'batch_size' => env('SYNC_BATCH_SIZE', 100),

    // scheduled sync control
    'scheduled_incremental_enabled' => env('SYNC_SCHEDULED_INCREMENTAL_ENABLED', true),
    'scheduled_full_enabled' => env('SYNC_SCHEDULED_FULL_ENABLED', true),
    'scheduled_cleanup_enabled' => env('SYNC_SCHEDULED_CLEANUP_ENABLED', true),

    // notification config
    'notifications' => [
        'email' => env('SYNC_NOTIFICATION_EMAIL', '<EMAIL>'),
        'slack_webhook' => env('SYNC_SLACK_WEBHOOK'),
    ],

    // performance config
    'memory_limit' => env('SYNC_MEMORY_LIMIT', '512M'),
    'time_limit' => env('SYNC_TIME_LIMIT', 0),

    // performance optimization config (balanced efficiency and memory)
    'performance' => [
        'batch_insert_size' => env('SYNC_BATCH_INSERT_SIZE', 300),
    ],
];
