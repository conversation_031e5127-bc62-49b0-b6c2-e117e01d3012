# Makefile for JAST Partner Project

# 获取当前用户的UID和GID
HOST_UID := $(shell id -u)
HOST_GID := $(shell id -g)

# 检查是否为root用户，如果是，使用默认值1000
ifeq ($(HOST_UID),0)
    $(warning 警告: 您正在以root用户运行。将使用默认UID/GID (1000)。)
    HOST_UID := 1000
    HOST_GID := 1000
endif

# 默认环境
ENV ?= development

# Docker Compose 命令辅助函数
define docker_compose_cmd
	$(if $(shell test -f docker-compose.$(ENV).yml && echo "exists"), \
		docker compose -f docker-compose.yml -f docker-compose.$(ENV).yml, \
		docker compose)
endef

# 默认目标
.PHONY: help
help:
	@echo "使用说明:"
	@echo "  make init [ENV=环境名]   - 初始化项目（首次克隆后运行）*"
	@echo "  make build [ENV=环境名]  - 构建Docker容器*"
	@echo "  make rebuild [ENV=环境名] - 清理并重新构建Docker容器*"
	@echo "  make up [ENV=环境名]     - 启动所有容器 (默认: development)^"
	@echo "  make down               - 停止所有容器"
	@echo "  make restart [ENV=环境名] - 重启所有容器"
	@echo "  make composer-install [ENV=环境名] - 安装Composer依赖"
	@echo "  make composer cmd=命令 [ENV=环境名] - 运行任意Composer命令"
	@echo "  make artisan cmd=命令 [ENV=环境名]  - 运行任意Artisan命令"
	@echo "  make migrate [ENV=环境名] - 运行数据库迁移"
	@echo "  make fresh [ENV=环境名]   - 重置数据库并运行所有迁移"
	@echo "  make seed [ENV=环境名]    - 运行数据库填充"
	@echo "  make test               - 运行测试（默认: testing环境）"
	@echo "  make test-with-env ENV=环境名 - 在指定环境运行测试（需要确认）"
	@echo "  make shell [ENV=环境名]   - 进入应用容器的Shell"
	@echo "  make queue-work [ENV=环境名] - 启动队列工作进程"
	@echo "  make queue-status [ENV=环境名] - 查看队列状态"
	@echo "  make horizon-start [ENV=环境名] - 启动 Horizon"
	@echo "  make horizon-stop [ENV=环境名] - 停止 Horizon"
	@echo "  make horizon-restart [ENV=环境名] - 重启 Horizon"
	@echo "  make horizon-status [ENV=环境名] - 查看 Horizon 状态"
	@echo "  make horizon-pause [ENV=环境名] - 暂停 Horizon"
	@echo "  make horizon-continue [ENV=环境名] - 继续 Horizon"
	@echo "  make supervisor-status - 查看 Supervisor 状态"
	@echo "  make supervisor-restart - 重启 Supervisor"
	@echo "  make fix-permissions     - 修复项目文件权限"
	@echo "  make verify-docker-config - 验证 Docker 配置"
	@echo ""
	@echo "环境选项:"
	@echo "  ENV=development (默认)"
	@echo "  ENV=production"
	@echo "  ENV=testing"
	@echo ""
	@echo "注意:"
	@echo "  * 这些命令会永久覆盖 .env 文件"
	@echo "  ^ 这个命令会临时覆盖 .env 文件，执行后自动恢复"
	@echo "  其他命令不会修改 .env 文件，安全使用"
	@echo ""
	@echo "环境特定配置:"
	@echo "  项目支持环境特定的 docker-compose 配置文件"
	@echo "  - docker-compose.development.yml: 开发环境（包含测试数据库初始化）"
	@echo "  - docker-compose.testing.yml: 测试环境（包含测试数据库初始化）"
	@echo "  - docker-compose.production.yml: 生产环境（不包含测试数据库初始化）"

# 初始化项目
.PHONY: init
init:
	@echo "使用环境: $(ENV)"
	@if [ ! -f ".env.$(ENV)" ]; then \
		echo "错误: .env.$(ENV) 文件不存在"; \
		exit 1; \
	fi
	@echo "复制 .env.$(ENV) 到 .env..."
	@cp -f .env.$(ENV) .env
	@$(MAKE) build ENV=$(ENV)
	@$(MAKE) create-project ENV=$(ENV)
	@$(MAKE) composer-install ENV=$(ENV)
	@$(MAKE) fix-permissions

# 创建Laravel项目
.PHONY: create-project
create-project:
	@echo "使用环境: $(ENV)"
	@if [ ! -f ".env.$(ENV)" ]; then \
		echo "错误: .env.$(ENV) 文件不存在"; \
		exit 1; \
	fi
	@echo "复制 .env.$(ENV) 到 .env..."
	@cp -f .env.$(ENV) .env
	@echo "在已有目录中初始化Laravel项目..."
	@if [ -f "composer.json" ]; then \
		echo "composer.json已存在，跳过创建项目步骤"; \
	else \
		echo "创建临时目录..."; \
		HOST_UID=$(HOST_UID) HOST_GID=$(HOST_GID) docker compose run --rm app bash -c "mkdir -p /tmp/laravel-temp && composer create-project laravel/laravel /tmp/laravel-temp && cp -r /tmp/laravel-temp/. /var/www/ && rm -rf /tmp/laravel-temp"; \
	fi

# 构建容器
.PHONY: build
build:
	@echo "使用环境: $(ENV)"
	@if [ ! -f ".env.$(ENV)" ]; then \
		echo "错误: .env.$(ENV) 文件不存在"; \
		exit 1; \
	fi
	@echo "复制 .env.$(ENV) 到 .env..."
	@cp -f .env.$(ENV) .env
	@if [ -f "docker-compose.$(ENV).yml" ]; then \
		echo "使用环境特定的 docker-compose 配置: docker-compose.$(ENV).yml"; \
	else \
		echo "使用默认的 docker-compose 配置"; \
	fi
	HOST_UID=$(HOST_UID) HOST_GID=$(HOST_GID) $(call docker_compose_cmd) build

# 清理并重新构建容器
.PHONY: rebuild
rebuild:
	@echo "使用环境: $(ENV)"
	@if [ ! -f ".env.$(ENV)" ]; then \
		echo "错误: .env.$(ENV) 文件不存在"; \
		exit 1; \
	fi
	@echo "复制 .env.$(ENV) 到 .env..."
	@cp -f .env.$(ENV) .env
	@if [ -f "docker-compose.$(ENV).yml" ]; then \
		echo "使用环境特定的 docker-compose 配置: docker-compose.$(ENV).yml"; \
		docker compose -f docker-compose.yml -f docker-compose.$(ENV).yml down --rmi local; \
		docker compose -f docker-compose.yml -f docker-compose.$(ENV).yml rm -f; \
		HOST_UID=$(HOST_UID) HOST_GID=$(HOST_GID) docker compose -f docker-compose.yml -f docker-compose.$(ENV).yml build --no-cache; \
	else \
		echo "使用默认的 docker-compose 配置"; \
		docker compose down --rmi local; \
		docker compose rm -f; \
		HOST_UID=$(HOST_UID) HOST_GID=$(HOST_GID) docker compose build --no-cache; \
	fi

# 启动容器
.PHONY: up
up:
	@echo "使用环境: $(ENV)"
	@if [ ! -f ".env.$(ENV)" ]; then \
		echo "错误: .env.$(ENV) 文件不存在"; \
		exit 1; \
	fi
	@echo "备份当前 .env 文件..."
	@if [ -f ".env" ]; then cp .env .env.backup; fi
	@echo "临时使用 .env.$(ENV) 启动容器..."
	@cp -f .env.$(ENV) .env
	@if [ -f "docker-compose.$(ENV).yml" ]; then \
		echo "使用环境特定的 docker-compose 配置: docker-compose.$(ENV).yml"; \
		HOST_UID=$(HOST_UID) HOST_GID=$(HOST_GID) docker compose -f docker-compose.yml -f docker-compose.$(ENV).yml up -d; \
	else \
		echo "使用默认的 docker-compose 配置"; \
		HOST_UID=$(HOST_UID) HOST_GID=$(HOST_GID) docker compose up -d; \
	fi
	@echo "恢复原始 .env 文件..."
	@if [ -f ".env.backup" ]; then mv .env.backup .env; else rm -f .env; fi
	@echo "确保 Composer 依赖已安装..."
	@$(MAKE) composer-install ENV=$(ENV)

# 停止容器
.PHONY: down
down:
	@# 尝试使用所有可能的 docker-compose 配置文件来停止容器
	@if [ -f "docker-compose.development.yml" ]; then \
		docker compose -f docker-compose.yml -f docker-compose.development.yml down 2>/dev/null || true; \
	fi
	@if [ -f "docker-compose.testing.yml" ]; then \
		docker compose -f docker-compose.yml -f docker-compose.testing.yml down 2>/dev/null || true; \
	fi
	@if [ -f "docker-compose.production.yml" ]; then \
		docker compose -f docker-compose.yml -f docker-compose.production.yml down 2>/dev/null || true; \
	fi
	@# 最后使用默认配置确保所有容器都被停止
	@docker compose down 2>/dev/null || true

# 重启容器
.PHONY: restart
restart: down up

# 安装Composer依赖
.PHONY: composer-install
composer-install:
	@echo "使用环境: $(ENV)"
	@if [ ! -f ".env.$(ENV)" ]; then \
		echo "错误: .env.$(ENV) 文件不存在"; \
		exit 1; \
	fi
	@echo "使用环境配置文件: .env.$(ENV)"
	@if [ -f "docker-compose.$(ENV).yml" ]; then \
		HOST_UID=$(HOST_UID) HOST_GID=$(HOST_GID) docker compose -f docker-compose.yml -f docker-compose.$(ENV).yml run --rm app bash -c "cp .env.$(ENV) .env.temp && composer install && rm -f .env.temp"; \
	else \
		HOST_UID=$(HOST_UID) HOST_GID=$(HOST_GID) docker compose run --rm app bash -c "cp .env.$(ENV) .env.temp && composer install && rm -f .env.temp"; \
	fi

# 运行任意Composer命令
.PHONY: composer
composer:
	@echo "使用环境: $(ENV)"
	@if [ ! -f ".env.$(ENV)" ]; then \
		echo "错误: .env.$(ENV) 文件不存在"; \
		exit 1; \
	fi
	@echo "使用环境配置文件: .env.$(ENV)"
	HOST_UID=$(HOST_UID) HOST_GID=$(HOST_GID) docker compose run --rm app bash -c "cp .env.$(ENV) .env.temp && composer $(cmd) && rm -f .env.temp"

# 运行任意Artisan命令
.PHONY: artisan
artisan:
	@echo "使用环境: $(ENV)"
	@if [ ! -f ".env.$(ENV)" ]; then \
		echo "错误: .env.$(ENV) 文件不存在"; \
		exit 1; \
	fi
	@echo "使用环境配置文件: .env.$(ENV)"
	@# 检查是否是测试命令，测试命令不支持 --env 参数
	@if echo "$(cmd)" | grep -q "^test"; then \
		echo "运行测试命令，使用 phpunit.xml 配置"; \
		if [ "$(ENV)" != "testing" ]; then \
			echo "警告: 您正在尝试在 $(ENV) 环境中运行测试！"; \
			echo "测试应该在 testing 环境中运行以避免影响其他环境的数据。"; \
			echo "是否继续？这可能会清空 $(ENV) 环境的数据库！"; \
			read -p "输入 'yes' 确认继续: " confirm; \
			if [ "$$confirm" != "yes" ]; then \
				echo "测试已取消"; \
				exit 1; \
			fi; \
		fi; \
		HOST_UID=$(HOST_UID) HOST_GID=$(HOST_GID) docker compose run --rm app bash -c "export APP_ENV=$(ENV) && php artisan $(cmd) --env=.env.$(ENV)"; \
	else \
		HOST_UID=$(HOST_UID) HOST_GID=$(HOST_GID) docker compose run --rm app bash -c "export APP_ENV=$(ENV) && php artisan $(cmd) --env=.env.$(ENV)"; \
	fi

# 运行数据库迁移
.PHONY: migrate
migrate:
	@echo "使用环境: $(ENV)"
	@if [ ! -f ".env.$(ENV)" ]; then \
		echo "错误: .env.$(ENV) 文件不存在"; \
		exit 1; \
	fi
	@echo "使用环境配置文件: .env.$(ENV)"
	HOST_UID=$(HOST_UID) HOST_GID=$(HOST_GID) docker compose run --rm app bash -c "export APP_ENV=$(ENV) && php artisan migrate --env=.env.$(ENV)"

# 重置数据库并运行所有迁移
.PHONY: fresh
fresh:
	@echo "使用环境: $(ENV)"
	@if [ ! -f ".env.$(ENV)" ]; then \
		echo "错误: .env.$(ENV) 文件不存在"; \
		exit 1; \
	fi
	@echo "使用环境配置文件: .env.$(ENV)"
	HOST_UID=$(HOST_UID) HOST_GID=$(HOST_GID) docker compose run --rm app bash -c "export APP_ENV=$(ENV) && php artisan migrate:fresh --env=.env.$(ENV)"

# 运行数据库填充
.PHONY: seed
seed:
	@echo "使用环境: $(ENV)"
	@if [ ! -f ".env.$(ENV)" ]; then \
		echo "错误: .env.$(ENV) 文件不存在"; \
		exit 1; \
	fi
	@echo "使用环境配置文件: .env.$(ENV)"
	HOST_UID=$(HOST_UID) HOST_GID=$(HOST_GID) docker compose run --rm app bash -c "export APP_ENV=$(ENV) && php artisan db:seed --env=.env.$(ENV)"

# 运行测试（默认使用testing环境）
.PHONY: test
test:
	@echo "使用环境: testing"
	@if [ ! -f ".env.testing" ]; then \
		echo "错误: .env.testing 文件不存在"; \
		exit 1; \
	fi
	@echo "使用环境配置文件: .env.testing"
	@HOST_UID=$(HOST_UID) HOST_GID=$(HOST_GID) docker compose run --rm app bash -c "export APP_ENV=testing && php artisan test --env=.env.testing"

# 在指定环境运行测试（需要确认）
.PHONY: test-with-env
test-with-env:
	@echo "使用环境: $(ENV)"
	@if [ ! -f ".env.$(ENV)" ]; then \
		echo "错误: .env.$(ENV) 文件不存在"; \
		exit 1; \
	fi
	@echo "使用环境配置文件: .env.$(ENV)"
	@if [ "$(ENV)" != "testing" ]; then \
		echo ""; \
		echo "⚠️  警告: 您正在尝试在 $(ENV) 环境中运行测试！"; \
		echo ""; \
		echo "测试通常应该在 testing 环境中运行，因为："; \
		echo "1. 测试会使用 RefreshDatabase trait 清空数据库"; \
		echo "2. 这可能会删除 $(ENV) 环境中的所有数据"; \
		echo "3. phpunit.xml 配置了专门的测试环境设置"; \
		echo ""; \
		echo "建议使用: make test（默认使用testing环境）"; \
		echo ""; \
		echo "如果您确实需要在 $(ENV) 环境中运行测试，请确认："; \
		echo "- 您了解这可能会清空 $(ENV) 环境的数据库"; \
		echo "- 您已经备份了重要数据"; \
		echo ""; \
		read -p "输入 'yes' 确认在 $(ENV) 环境中运行测试: " confirm; \
		if [ "$$confirm" != "yes" ]; then \
			echo "测试已取消。建议使用: make test"; \
			exit 1; \
		fi; \
		echo ""; \
		echo "正在 $(ENV) 环境中运行测试..."; \
	fi
	@HOST_UID=$(HOST_UID) HOST_GID=$(HOST_GID) docker compose run --rm app bash -c "export APP_ENV=$(ENV) && php artisan test --env=.env.$(ENV)"

# 进入应用容器的Shell
.PHONY: shell
shell:
	@echo "使用环境: $(ENV)"
	@if [ ! -f ".env.$(ENV)" ]; then \
		echo "错误: .env.$(ENV) 文件不存在"; \
		exit 1; \
	fi
	@echo "使用环境配置文件: .env.$(ENV)"
	HOST_UID=$(HOST_UID) HOST_GID=$(HOST_GID) docker compose run --rm app bash -c "cp .env.$(ENV) .env.temp && export APP_ENV=$(ENV) && bash; rm -f .env.temp"

# 启动队列工作进程
.PHONY: queue-work
queue-work:
	@echo "使用环境: $(ENV)"
	@if [ ! -f ".env.$(ENV)" ]; then \
		echo "错误: .env.$(ENV) 文件不存在"; \
		exit 1; \
	fi
	@echo "使用环境配置文件: .env.$(ENV)"
	@echo "启动队列工作进程..."
	HOST_UID=$(HOST_UID) HOST_GID=$(HOST_GID) docker compose run --rm app bash -c "export APP_ENV=$(ENV) && php artisan queue:work --queue=sync --sleep=3 --tries=1 --timeout=3600 --env=.env.$(ENV)"

# 查看队列状态
.PHONY: queue-status
queue-status:
	@echo "使用环境: $(ENV)"
	@if [ ! -f ".env.$(ENV)" ]; then \
		echo "错误: .env.$(ENV) 文件不存在"; \
		exit 1; \
	fi
	@echo "使用环境配置文件: .env.$(ENV)"
	@echo "查看队列状态..."
	HOST_UID=$(HOST_UID) HOST_GID=$(HOST_GID) docker compose run --rm app bash -c "export APP_ENV=$(ENV) && php artisan tinker --execute=\"echo 'Jobs in queue: ' . DB::table('jobs')->count(); echo PHP_EOL; echo 'Failed jobs: ' . DB::table('failed_jobs')->count(); echo PHP_EOL;\" --env=.env.$(ENV)"

# 管理卡住的队列任务
.PHONY: queue-stuck-list
queue-stuck-list:
	@./scripts/manage_stuck_jobs.sh list --env=$(ENV)

.PHONY: queue-stuck-delete
queue-stuck-delete:
	@if [ -z "$(JOB_ID)" ]; then \
		echo "错误: 请提供 JOB_ID 参数"; \
		echo "用法: make queue-stuck-delete JOB_ID=123 ENV=development"; \
		exit 1; \
	fi
	@./scripts/manage_stuck_jobs.sh delete $(JOB_ID) --env=$(ENV)

.PHONY: queue-stuck-delete-sync
queue-stuck-delete-sync:
	@if [ -z "$(JOB_ID)" ]; then \
		echo "错误: 请提供 JOB_ID 参数 (来自API的进度跟踪ID)"; \
		echo "用法: make queue-stuck-delete-sync JOB_ID=4d75e64e-2993-4f6b-b8fa-3b2835d46e24 ENV=development"; \
		exit 1; \
	fi
	@./scripts/manage_stuck_jobs.sh delete-sync $(JOB_ID) --env=$(ENV)

.PHONY: queue-stuck-delete-all
queue-stuck-delete-all:
	@./scripts/manage_stuck_jobs.sh delete-all --env=$(ENV)

.PHONY: queue-stuck-delete-old
queue-stuck-delete-old:
	@./scripts/manage_stuck_jobs.sh delete-old $(if $(HOURS),$(HOURS),2) --env=$(ENV)

.PHONY: queue-stuck-status
queue-stuck-status:
	@./scripts/manage_stuck_jobs.sh status --env=$(ENV)

# 启动 Horizon
.PHONY: horizon-start
horizon-start:
	@echo "使用环境: $(ENV)"
	@if [ ! -f ".env.$(ENV)" ]; then \
		echo "错误: .env.$(ENV) 文件不存在"; \
		exit 1; \
	fi
	@echo "使用环境配置文件: .env.$(ENV)"
	@echo "启动 Horizon..."
	HOST_UID=$(HOST_UID) HOST_GID=$(HOST_GID) docker compose exec app bash -c "export APP_ENV=$(ENV) && php artisan horizon"

# 停止 Horizon
.PHONY: horizon-stop
horizon-stop:
	@echo "使用环境: $(ENV)"
	@if [ ! -f ".env.$(ENV)" ]; then \
		echo "错误: .env.$(ENV) 文件不存在"; \
		exit 1; \
	fi
	@echo "使用环境配置文件: .env.$(ENV)"
	@echo "停止 Horizon..."
	HOST_UID=$(HOST_UID) HOST_GID=$(HOST_GID) docker compose exec app bash -c "export APP_ENV=$(ENV) && php artisan horizon:terminate"

# 重启 Horizon
.PHONY: horizon-restart
horizon-restart: horizon-stop
	@sleep 2
	@$(MAKE) horizon-start ENV=$(ENV)

# 查看 Horizon 状态
.PHONY: horizon-status
horizon-status:
	@echo "使用环境: $(ENV)"
	@if [ ! -f ".env.$(ENV)" ]; then \
		echo "错误: .env.$(ENV) 文件不存在"; \
		exit 1; \
	fi
	@echo "使用环境配置文件: .env.$(ENV)"
	@echo "查看 Horizon 状态..."
	HOST_UID=$(HOST_UID) HOST_GID=$(HOST_GID) docker compose exec app bash -c "export APP_ENV=$(ENV) && php artisan horizon:status"

# 暂停 Horizon
.PHONY: horizon-pause
horizon-pause:
	@echo "使用环境: $(ENV)"
	@if [ ! -f ".env.$(ENV)" ]; then \
		echo "错误: .env.$(ENV) 文件不存在"; \
		exit 1; \
	fi
	@echo "使用环境配置文件: .env.$(ENV)"
	@echo "暂停 Horizon..."
	HOST_UID=$(HOST_UID) HOST_GID=$(HOST_GID) docker compose exec app bash -c "export APP_ENV=$(ENV) && php artisan horizon:pause"

# 继续 Horizon
.PHONY: horizon-continue
horizon-continue:
	@echo "使用环境: $(ENV)"
	@if [ ! -f ".env.$(ENV)" ]; then \
		echo "错误: .env.$(ENV) 文件不存在"; \
		exit 1; \
	fi
	@echo "使用环境配置文件: .env.$(ENV)"
	@echo "继续 Horizon..."
	HOST_UID=$(HOST_UID) HOST_GID=$(HOST_GID) docker compose exec app bash -c "export APP_ENV=$(ENV) && php artisan horizon:continue"

# 查看 Supervisor 状态
.PHONY: supervisor-status
supervisor-status:
	@echo "查看 Supervisor 状态..."
	docker compose exec app supervisorctl -c /etc/supervisor/conf.d/supervisord.conf status

# 重启 Supervisor
.PHONY: supervisor-restart
supervisor-restart:
	@echo "重启 Supervisor..."
	docker compose exec app supervisorctl -c /etc/supervisor/conf.d/supervisord.conf restart all

# 修复项目文件权限
.PHONY: fix-permissions
fix-permissions:
	@echo "修复项目文件权限..."
	@if [ -d "storage" ]; then \
		echo "修复Laravel目录权限..."; \
		sudo chown -R $(HOST_UID):$(HOST_GID) .; \
		sudo chmod -R 755 .; \
		sudo chmod -R 775 storage bootstrap/cache; \
		echo "权限已修复"; \
	else \
		echo "storage目录不存在，只修复基本权限"; \
		sudo chown -R $(HOST_UID):$(HOST_GID) .; \
		sudo chmod -R 755 .; \
	fi

# 验证 Docker 配置
.PHONY: verify-docker-config
verify-docker-config:
	@echo "验证 Docker 配置..."
	@./scripts/verify_docker_config.sh
