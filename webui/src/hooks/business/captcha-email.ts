import { computed, ref } from 'vue';
import { useCountDown, useLoading } from '@sa/hooks';
import { REG_EMAIL } from '@/constants/reg';
import { fetchSendVerificationCode } from '@/service/api/user';
import { $t } from '@/locales';

export function useCaptchaEmail() {
  const { loading, startLoading, endLoading } = useLoading();
  const { count, start, stop, isCounting } = useCountDown(180); // 3 minutes = 180 seconds

  const label = computed(() => {
    let text = $t('invitation.getVerificationCode');

    const countingLabel = $t('invitation.resendCode', { time: count.value });

    if (loading.value) {
      text = '';
    }

    if (isCounting.value) {
      text = countingLabel;
    }

    return text;
  });

  function isEmailValid(email: string) {
    if (email.trim() === '') {
      window.$message?.error?.($t('invitation.enterEmail'));
      return false;
    }

    if (!REG_EMAIL.test(email)) {
      window.$message?.error?.($t('invitation.enterValidEmail'));
      return false;
    }

    return true;
  }

  async function getCaptchaEmail(email: string) {
    const valid = isEmailValid(email);

    if (!valid || loading.value) {
      return;
    }

    startLoading();

    try {
      const { data } = await fetchSendVerificationCode(email);

      if (data && data.success) {
        window.$message?.success?.($t('invitation.verificationCodeSent'));
        start(); // Start countdown
      } else {
        window.$message?.error?.(data?.message || $t('invitation.sendCodeFailed'));
      }
    } catch (err: any) {
      const errorMsg = err?.response?.data?.message || err?.message || $t('invitation.sendCodeFailed');
      window.$message?.error?.(errorMsg);
    } finally {
      endLoading();
    }
  }

  return {
    label,
    start,
    stop,
    isCounting,
    loading,
    getCaptchaEmail,
    isEmailValid
  };
}
