<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import type { SelectOption } from 'naive-ui';
import { NSelect, useMessage } from 'naive-ui';
import { fetchRolesList } from '@/service/api';
import { handleApiError } from '@/utils/error-handler';
import { $t } from '@/locales';

interface Props {
  modelValue?: string | string[] | null;
  multiple?: boolean;
  placeholder?: string;
  clearable?: boolean;
  filterable?: boolean;
  disabled?: boolean;
  size?: 'small' | 'medium' | 'large';
  roleType?: 'system' | 'organization' | 'all'; // Filter roles by type
  organizationId?: number; // Filter roles by organization
}

interface Emits {
  (e: 'update:modelValue', value: string | string[] | null): void;
  (e: 'change', value: string | string[] | null): void;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: null,
  multiple: false,
  placeholder: '',
  clearable: true,
  filterable: true,
  disabled: false,
  size: 'medium',
  roleType: 'all'
});

const emit = defineEmits<Emits>();

const message = useMessage();
const isLoading = ref(false);
const roles = ref<Api.Auth.Role[]>([]);

// Computed placeholder
const computedPlaceholder = computed(() => {
  if (props.placeholder) return props.placeholder;
  return $t('page.user.selectRole');
});

// Compute available role options
const roleOptions = computed<SelectOption[]>(() => {
  let filteredRoles = roles.value;

  // Filter by role type
  if (props.roleType === 'system') {
    filteredRoles = roles.value.filter(role =>
      role.guard_name === 'system' || role.organisation_id === null
    );
  } else if (props.roleType === 'organization') {
    filteredRoles = roles.value.filter(role =>
      role.guard_name === 'api' && role.organisation_id !== null
    );
  }

  // Filter by organization ID if specified
  if (props.organizationId) {
    filteredRoles = filteredRoles.filter(role =>
      role.organisation_id === props.organizationId
    );
  }

  return filteredRoles.map((role: Api.Auth.Role) => ({
    label: role.name,
    value: role.name,
    disabled: false
  }));
});

// Load roles list
async function loadRoles() {
  try {
    isLoading.value = true;
    const { data } = await fetchRolesList();
    if (data?.success) {
      roles.value = data.data || [];
    } else {
      handleApiError({ response: { data } }, message, $t('page.user.loadRolesFailed' as any));
    }
  } catch (error) {
    console.error('Load roles error:', error);
    handleApiError(error, message, $t('page.user.loadRolesFailed' as any));
  } finally {
    isLoading.value = false;
  }
}

// Handle value change
function handleValueChange(value: string | string[] | null) {
  emit('update:modelValue', value);
  emit('change', value);
}

// Load roles list when component is mounted
onMounted(() => {
  loadRoles();
});

// Expose refresh method
defineExpose({
  refresh: loadRoles
});
</script>

<template>
  <NSelect
    :value="modelValue"
    :options="roleOptions"
    :placeholder="computedPlaceholder"
    :multiple="multiple"
    :clearable="clearable"
    :filterable="filterable"
    :disabled="disabled"
    :size="size"
    :loading="isLoading"
    @update:value="handleValueChange"
  />
</template>
