<script setup lang="ts">
import { computed } from 'vue';
import { useAuthStore } from '@/store/modules/auth';
import { $t } from '@/locales';
import SvgIcon from './svg-icon.vue';

defineOptions({
  name: 'UserInfoCard'
});

const authStore = useAuthStore();

// 计算用户信息显示数据
const userInfo = computed(() => {
  const complete = authStore.completeUserInfo;
  if (!complete) return null;

  return {
    id: complete.id,
    name: complete.name,
    email: complete.email,
    emailVerified: complete.email_verified_at !== null,
    createdAt: new Date(complete.created_at).toLocaleDateString(),
    updatedAt: new Date(complete.updated_at).toLocaleDateString(),
    organisations: complete.organisations,
    systemRoles: complete.roles.system_roles,
    organisationRoles: complete.roles.organisation_roles,
    allRoles: complete.roles.all_role_names
  };
});

// 角色显示颜色
const getRoleColor = (roleName: string) => {
  const roleColors: Record<string, string> = {
    root: 'error',
    admin: 'warning', 
    owner: 'primary',
    manager: 'info',
    user: 'default'
  };
  return roleColors[roleName] || 'default';
};

// 组织状态颜色
const getOrgStatusColor = (status: string) => {
  const statusColors: Record<string, string> = {
    active: 'success',
    pending: 'warning',
    suspended: 'error'
  };
  return statusColors[status] || 'default';
};
</script>

<template>
  <NCard v-if="userInfo" :bordered="false" class="user-info-card">
    <template #header>
      <div class="flex items-center gap-3">
        <NAvatar :size="48" round>
          <SvgIcon icon="ph:user-circle" class="text-24px" />
        </NAvatar>
        <div>
          <h3 class="text-18px font-semibold">{{ userInfo.name }}</h3>
          <div class="flex items-center gap-2 text-14px text-gray-500">
            <SvgIcon icon="ph:envelope" class="text-12px" />
            <span>{{ userInfo.email }}</span>
            <NTag v-if="userInfo.emailVerified" type="success" size="small">
              {{ $t('common.verified') }}
            </NTag>
            <NTag v-else type="warning" size="small">
              {{ $t('common.unverified') }}
            </NTag>
          </div>
        </div>
      </div>
    </template>

    <NSpace vertical :size="16">
      <!-- 基本信息 -->
      <div>
        <h4 class="text-16px font-medium mb-2 flex items-center gap-2">
          <SvgIcon icon="ph:info" class="text-16px" />
          {{ $t('page.user.basicInfo') }}
        </h4>
        <NDescriptions :column="2" size="small">
          <NDescriptionsItem :label="$t('page.user.userId')">
            {{ userInfo.id }}
          </NDescriptionsItem>
          <NDescriptionsItem :label="$t('page.user.createdAt')">
            {{ userInfo.createdAt }}
          </NDescriptionsItem>
          <NDescriptionsItem :label="$t('page.user.updatedAt')">
            {{ userInfo.updatedAt }}
          </NDescriptionsItem>
        </NDescriptions>
      </div>

      <!-- 角色信息 -->
      <div>
        <h4 class="text-16px font-medium mb-2 flex items-center gap-2">
          <SvgIcon icon="ph:shield-check" class="text-16px" />
          {{ $t('page.user.roles') }}
        </h4>
        <div class="space-y-2">
          <!-- 系统角色 -->
          <div v-if="userInfo.systemRoles.length > 0">
            <span class="text-14px text-gray-600 dark:text-gray-400">{{ $t('page.user.systemRoles') }}:</span>
            <div class="flex flex-wrap gap-1 mt-1">
              <NTag
                v-for="role in userInfo.systemRoles"
                :key="role.id"
                :type="getRoleColor(role.name)"
                size="small"
              >
                {{ role.name }}
              </NTag>
            </div>
          </div>
          
          <!-- 组织角色 -->
          <div v-if="userInfo.organisationRoles.length > 0">
            <span class="text-14px text-gray-600 dark:text-gray-400">{{ $t('page.user.organisationRoles') }}:</span>
            <div class="flex flex-wrap gap-1 mt-1">
              <NTag
                v-for="role in userInfo.organisationRoles"
                :key="`${role.id}-${role.organisation_id}`"
                :type="getRoleColor(role.name)"
                size="small"
              >
                {{ role.name }}
                <span v-if="role.organisation_name" class="text-12px opacity-75">
                  ({{ role.organisation_name }})
                </span>
              </NTag>
            </div>
          </div>
        </div>
      </div>

      <!-- 组织信息 -->
      <div v-if="userInfo.organisations.length > 0">
        <h4 class="text-16px font-medium mb-2 flex items-center gap-2">
          <SvgIcon icon="ph:buildings" class="text-16px" />
          {{ $t('page.user.organisations') }}
        </h4>
        <div class="space-y-2">
          <div
            v-for="org in userInfo.organisations"
            :key="org.id"
            class="flex items-center justify-between p-2 rounded border border-gray-200 dark:border-gray-700"
          >
            <div>
              <span class="font-medium">{{ org.name }}</span>
              <span class="text-12px text-gray-500 ml-2">({{ org.code }})</span>
            </div>
            <NTag :type="getOrgStatusColor(org.status)" size="small">
              {{ org.status }}
            </NTag>
          </div>
        </div>
      </div>
    </NSpace>
  </NCard>
  
  <NCard v-else :bordered="false" class="user-info-card">
    <NEmpty :description="$t('page.user.noUserInfo')" />
  </NCard>
</template>

<style scoped>
.user-info-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
