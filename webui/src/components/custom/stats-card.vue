<script setup lang="tsx">
import SvgIcon from '@/components/custom/svg-icon.vue';
import CountTo from '@/components/custom/count-to.vue';

defineOptions({
  name: 'StatsCard'
});

interface Props {
  /** 标题 */
  title: string;
  /** 数值 */
  value: number;
  /** 单位 */
  unit: string;
  /** 渐变色配置 */
  color: {
    start: string;
    end: string;
  };
  /** 图标名称 */
  icon: string;
  /** 是否将单位作为前缀显示（如货币符号） */
  unitAsPrefix?: boolean;
  /** 动画持续时间 */
  duration?: number;
  /** 是否使用缓动动画 */
  useEasing?: boolean;
  /** 动画过渡效果 */
  transition?: string;
}

const props = withDefaults(defineProps<Props>(), {
  unitAsPrefix: false,
  duration: 2000,
  useEasing: true,
  transition: 'easeOutExpo'
});
</script>

<template>
  <div
    class="relative overflow-hidden rounded-lg p-6 text-white"
    :style="{
      background: `linear-gradient(135deg, ${color.start} 0%, ${color.end} 100%)`
    }"
  >
    <div class="flex items-center justify-between">
      <div>
        <div class="mb-1 text-sm opacity-90">{{ title }}</div>
        <div class="text-2xl font-bold">
          <CountTo
            :prefix="unitAsPrefix ? unit : ''"
            :suffix="unitAsPrefix ? '' : ` ${unit}`"
            :end-value="value"
            :duration="duration"
            :use-easing="useEasing"
            :transition="transition"
            class="text-white"
          />
        </div>
      </div>
      <div class="text-3xl opacity-80">
        <SvgIcon :icon="icon" />
      </div>
    </div>

    <!-- 装饰性背景图案 -->
    <div class="absolute text-6xl opacity-20 -right-4 -top-4">
      <SvgIcon :icon="icon" />
    </div>
  </div>
</template>


