<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue';
import { getPaletteColorByNumber } from '@sa/color';

defineOptions({ name: 'GameBg' });

interface Props {
  /** Theme color */
  themeColor: string;
}

const props = defineProps<Props>();

// 使用更鲜艳的颜色方案
const lightColor = computed(() => getPaletteColorByNumber(props.themeColor, 200));
const mediumColor = computed(() => getPaletteColorByNumber(props.themeColor, 400));
const darkColor = computed(() => getPaletteColorByNumber(props.themeColor, 600));
const deepColor = computed(() => getPaletteColorByNumber(props.themeColor, 800));

// 额外的鲜艳色彩
const accentColor = computed(() => getPaletteColorByNumber(props.themeColor, 500));
const brightColor = computed(() => getPaletteColorByNumber(props.themeColor, 300));

// 动画控制
const animationStarted = ref(false);

// 黑客帝国风格的二进制数据流
const matrixColumns = ref<Array<{
  id: number;
  left: string;
  delay: number;
  duration: number;
  chars: string[];
  animationKey: number; // 用于强制重新渲染动画
  visible: boolean; // 控制可见性
  startDelay: number; // 启动延迟
}>>([]);

// 生成随机二进制字符 - 每行一位
const generateBinaryChars = (count: number) => {
  const chars = [];
  for (let i = 0; i < count; i++) {
    // 每行都是一位二进制数
    chars.push(Math.random() > 0.5 ? '1' : '0');
  }
  return chars;
};

// 初始化矩阵列 - 减少到3条以提升性能
const initMatrixColumns = () => {
  const columns = [];
  const positions = ['20%', '50%', '80%']; // 固定三个位置：左、中、右

  for (let i = 0; i < 3; i++) {
    columns.push({
      id: i,
      left: positions[i], // 使用预设位置
      delay: 0, // 初始延迟为0，由启动逻辑控制
      duration: 10 + Math.random() * 4, // 10-14秒的持续时间，稍微长一些
      chars: generateBinaryChars(25 + Math.floor(Math.random() * 10)), // 25-35个字符，稍微多一些
      animationKey: Date.now() + i, // 唯一的动画键
      visible: false, // 初始隐藏
      startDelay: i * 800 + Math.random() * 600 // 错开启动时间：每列间隔800ms + 随机600ms
    });
  }
  matrixColumns.value = columns;
};

onMounted(() => {
  setTimeout(() => {
    animationStarted.value = true;
  }, 100);

  // 延迟初始化矩阵列，让其他动画先开始
  setTimeout(() => {
    initMatrixColumns();

    // 错开启动每一列
    matrixColumns.value.forEach((column) => {
      setTimeout(() => {
        column.visible = true;
        // 启动后立即开始动画，但有小的随机延迟
        column.delay = Math.random() * 1;
      }, column.startDelay);
    });
  }, 800);

  // 定期更新二进制字符，让内容保持变化 - 降低频率
  setInterval(() => {
    matrixColumns.value.forEach(column => {
      if (Math.random() > 0.6) {
        // 40% 概率更新字符内容
        column.chars = generateBinaryChars(25 + Math.floor(Math.random() * 10));
      }
    });
  }, 3000); // 增加到3秒

  // 定期重新启动列的动画，避免循环时的突兀感 - 降低频率
  setInterval(() => {
    matrixColumns.value.forEach((column, index) => {
      if (column.visible && Math.random() > 0.7) {
        // 30% 概率重新启动这一列（只对可见的列）
        column.delay = Math.random() * 2; // 短延迟重新开始
        column.duration = 10 + Math.random() * 4;
        column.chars = generateBinaryChars(25 + Math.floor(Math.random() * 10));
        column.animationKey = Date.now() + index; // 更新动画键强制重新渲染
      }
    });
  }, 6000); // 增加到6秒
});
</script>

<template>
  <div class="absolute-lt z-1 size-full overflow-hidden">
    <!-- 主背景渐变 -->
    <div
      class="absolute inset-0"
      :style="{
        background: `radial-gradient(ellipse at top left, ${lightColor}40 0%, ${brightColor}20 30%, transparent 70%),
                     radial-gradient(ellipse at bottom right, ${accentColor}35 0%, ${mediumColor}25 40%, transparent 70%),
                     radial-gradient(circle at center, ${darkColor}15 0%, transparent 60%),
                     linear-gradient(135deg, ${brightColor}08 0%, ${accentColor}12 50%, ${deepColor}18 100%)`
      }"
    />

    <!-- 网格背景 -->
    <div class="absolute inset-0 opacity-30">
      <svg class="size-full">
        <defs>
          <pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse">
            <path
              :d="`M 50 0 L 0 0 0 50`"
              fill="none"
              :stroke="accentColor"
              stroke-width="1"
              opacity="0.6"
            />
            <circle cx="25" cy="25" r="1" :fill="brightColor" opacity="0.4" />
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#grid)" />
      </svg>
    </div>

    <!-- 装饰性几何图形 - 左上角 -->
    <div
      class="absolute -top-40 -left-40 transition-all duration-2000 ease-out lt-sm:(-top-20 -left-20)"
      :class="[
        animationStarted ? 'translate-x-0 translate-y-0 opacity-60' : '-translate-x-20 -translate-y-20 opacity-0',
        animationStarted ? 'animate-drift' : ''
      ]"
    >
      <svg class="w-400px h-400px lt-sm:(w-250px h-250px)" viewBox="0 0 400 400">
        <defs>
          <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" :stop-color="brightColor" stop-opacity="0.9" />
            <stop offset="50%" :stop-color="accentColor" stop-opacity="0.7" />
            <stop offset="100%" :stop-color="mediumColor" stop-opacity="0.5" />
          </linearGradient>
        </defs>
        <!-- 将所有元素包装在一个旋转的组中 -->
        <g class="animate-rotate-slow" transform-origin="200 200">
          <!-- 六边形 -->
          <polygon
            points="200,50 350,125 350,275 200,350 50,275 50,125"
            fill="url(#gradient1)"
            stroke="none"
            class="animate-pulse-scale"
          />
          <!-- 内部装饰线条 -->
          <g :stroke="darkColor" stroke-width="2" fill="none" opacity="0.6" class="animate-glow">
            <line x1="200" y1="50" x2="200" y2="350" />
            <line x1="50" y1="125" x2="350" y2="275" />
            <line x1="50" y1="275" x2="350" y2="125" />
          </g>
        </g>
      </svg>
    </div>

    <!-- 装饰性几何图形 - 右下角 -->
    <div
      class="absolute -bottom-32 -right-32 transition-all duration-2000 ease-out delay-300 lt-sm:(-bottom-16 -right-16)"
      :class="[
        animationStarted ? 'translate-x-0 translate-y-0 opacity-50' : 'translate-x-20 translate-y-20 opacity-0',
        animationStarted ? 'animate-float' : ''
      ]"
    >
      <svg class="w-300px h-300px lt-sm:(w-200px h-200px)" viewBox="0 0 300 300">
        <defs>
          <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" :stop-color="accentColor" stop-opacity="0.8" />
            <stop offset="50%" :stop-color="mediumColor" stop-opacity="0.6" />
            <stop offset="100%" :stop-color="darkColor" stop-opacity="0.4" />
          </linearGradient>
        </defs>
        <!-- 圆形 -->
        <circle cx="150" cy="150" r="120" fill="url(#gradient2)" class="animate-pulse-scale" />
        <!-- 内部方形 -->
        <rect x="75" y="75" width="150" height="150" fill="none" :stroke="brightColor" stroke-width="2" opacity="0.8" class="animate-glow" />
        <!-- 对角线 -->
        <g :stroke="accentColor" stroke-width="1" opacity="0.7">
          <line x1="75" y1="75" x2="225" y2="225" />
          <line x1="225" y1="75" x2="75" y2="225" />
        </g>
      </svg>
    </div>

    <!-- 浮动粒子效果 - 减少数量 -->
    <div class="absolute inset-0 pointer-events-none">
      <div
        v-for="i in 6"
        :key="i"
        class="absolute rounded-full animate-pulse"
        :class="i % 3 === 0 ? 'w-1 h-1' : i % 3 === 1 ? 'w-2 h-2' : 'w-1.5 h-1.5'"
        :style="{
          backgroundColor: i % 3 === 0 ? brightColor : i % 3 === 1 ? accentColor : mediumColor,
          left: `${10 + (i * 15)}%`,
          top: `${20 + (i * 12)}%`,
          animationDelay: `${i * 0.8}s`,
          animationDuration: `${3 + (i % 3)}s`,
          opacity: 0.7
        }"
      />
    </div>

    <!-- 游戏控制器图标装饰 -->
    <div
      class="absolute top-1/3 right-1/4 transition-all duration-2500 ease-out delay-700 lt-sm:(right-1/6 top-1/4)"
      :class="[
        animationStarted ? 'opacity-20 rotate-12' : 'opacity-0 rotate-0',
        animationStarted ? 'animate-float' : ''
      ]"
    >
      <svg width="80" height="80" viewBox="0 0 80 80" :fill="accentColor" class="animate-glow lt-sm:(w-60px h-60px)">
        <path d="M60 25H20c-8.284 0-15 6.716-15 15v10c0 8.284 6.716 15 15 15h40c8.284 0 15-6.716 15-15V40c0-8.284-6.716-15-15-15zM25 45c-2.761 0-5-2.239-5-5s2.239-5 5-5 5 2.239 5 5-2.239 5-5 5zm30-10c-1.657 0-3-1.343-3-3s1.343-3 3-3 3 1.343 3 3-1.343 3-3 3zm5 10c-1.657 0-3-1.343-3-3s1.343-3 3-3 3 1.343 3 3-1.343 3-3 3z"/>
      </svg>
    </div>

    <!-- 像素风格装饰 -->
    <div
      class="absolute bottom-1/4 left-1/6 transition-all duration-2000 ease-out delay-1000"
      :class="animationStarted ? 'opacity-25' : 'opacity-0'"
    >
      <svg width="60" height="60" viewBox="0 0 60 60">
        <g :fill="brightColor">
          <rect x="0" y="0" width="10" height="10"/>
          <rect x="20" y="0" width="10" height="10"/>
          <rect x="40" y="0" width="10" height="10"/>
          <rect x="10" y="10" width="10" height="10"/>
          <rect x="30" y="10" width="10" height="10"/>
          <rect x="50" y="10" width="10" height="10"/>
          <rect x="0" y="20" width="10" height="10"/>
          <rect x="20" y="20" width="10" height="10"/>
          <rect x="40" y="20" width="10" height="10"/>
        </g>
      </svg>
    </div>

    <!-- 科技感线条 -->
    <div
      class="absolute top-1/4 left-1/4 transition-all duration-3000 ease-out delay-500"
      :class="animationStarted ? 'opacity-30' : 'opacity-0'"
    >
      <svg width="200" height="100" viewBox="0 0 200 100">
        <defs>
          <linearGradient id="lineGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" :stop-color="accentColor" stop-opacity="0" />
            <stop offset="50%" :stop-color="brightColor" stop-opacity="1" />
            <stop offset="100%" :stop-color="accentColor" stop-opacity="0" />
          </linearGradient>
        </defs>
        <path
          d="M0,50 Q50,20 100,50 T200,50"
          stroke="url(#lineGradient)"
          stroke-width="2"
          fill="none"
        />
      </svg>
    </div>

    <!-- 黑客帝国风格的二进制数据流 -->
    <div class="absolute inset-0 pointer-events-none overflow-hidden">
      <!-- 添加一个微妙的绿色滤镜效果 -->
      <div
        class="absolute inset-0 opacity-5"
        :style="{
          background: `linear-gradient(180deg, transparent 0%, ${accentColor}20 50%, transparent 100%)`
        }"
      />

      <div
        v-for="column in matrixColumns"
        v-show="column.visible"
        :key="`matrix-${column.id}-${column.animationKey}`"
        class="absolute top-0 h-full flex flex-col justify-start items-center animate-matrix-appear"
        :style="{
          left: column.left,
          animationDelay: `${column.delay}s`
        }"
      >
        <!-- 垂直的二进制字符流 -->
        <div
          class="flex flex-col font-mono leading-tight animate-matrix-fall"
          :style="{
            animationDuration: `${column.duration}s`,
            animationDelay: `${column.delay}s`,
            width: '16px', // 调整宽度适应一位数字
            textAlign: 'center'
          }"
        >
          <span
            v-for="(char, index) in column.chars"
            :key="`char-${column.id}-${index}-${char}`"
            class="block transition-all duration-500 select-none"
            :class="{
              'animate-matrix-glow': index < 3,
              'animate-pulse': index >= 3 && index < 8
            }"
            :style="{
              color: index < 3 ? brightColor : index < 8 ? accentColor : index < 15 ? mediumColor : darkColor,
              opacity: index < 3 ? 1 : index < 8 ? 0.85 : index < 15 ? 0.6 : 0.25,
              fontSize: index < 3 ? '14px' : index < 8 ? '13px' : index < 15 ? '12px' : '11px',
              textShadow: index < 3 ? `0 0 6px ${brightColor}` : index < 8 ? `0 0 3px ${accentColor}` : 'none',
              lineHeight: index < 3 ? '18px' : index < 8 ? '16px' : '14px',
              fontWeight: index < 3 ? '600' : index < 8 ? '500' : '400'
            }"
          >
            {{ char }}
          </span>
        </div>
      </div>
    </div>

    <!-- 额外的数字雨效果（散点） - 减少数量 -->
    <div class="absolute inset-0 pointer-events-none overflow-hidden">
      <div
        v-for="i in 6"
        :key="`digital-${i}`"
        class="absolute text-xs font-mono opacity-20 animate-pulse select-none"
        :style="{
          color: i % 2 === 0 ? accentColor : brightColor,
          left: `${10 + (i * 15)}%`,
          top: `${20 + (i * 12)}%`,
          animationDelay: `${i * 2}s`,
          animationDuration: `${5 + (i % 2)}s`,
          width: '12px',
          textAlign: 'center'
        }"
      >
        {{ ['0', '1', '1', '0', '1', '0'][i % 6] }}
      </div>
    </div>
  </div>
</template>

<style scoped>
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes glow {
  0%, 100% { filter: brightness(1) drop-shadow(0 0 5px currentColor); }
  50% { filter: brightness(1.2) drop-shadow(0 0 15px currentColor); }
}

@keyframes rotate-slow {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes pulse-scale {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

/* SVG 组元素的旋转动画 */
:deep(.animate-rotate-slow) {
  animation: rotate-slow 20s linear infinite;
  transform-origin: center;
}

/* SVG 元素的脉冲缩放动画 */
:deep(.animate-pulse-scale) {
  animation: pulse-scale 3s ease-in-out infinite;
  transform-origin: center;
}

@keyframes drift {
  0% { transform: translateX(0px) translateY(0px); }
  25% { transform: translateX(10px) translateY(-5px); }
  50% { transform: translateX(-5px) translateY(-10px); }
  75% { transform: translateX(-10px) translateY(5px); }
  100% { transform: translateX(0px) translateY(0px); }
}

@keyframes matrix-fall {
  0% {
    transform: translateY(-150vh);
    opacity: 0;
  }
  3% {
    opacity: 0.1;
  }
  8% {
    opacity: 0.6;
  }
  12% {
    opacity: 1;
  }
  88% {
    opacity: 1;
  }
  92% {
    opacity: 0.6;
  }
  97% {
    opacity: 0.1;
  }
  100% {
    transform: translateY(150vh);
    opacity: 0;
  }
}

@keyframes matrix-glow {
  0%, 100% {
    text-shadow: 0 0 5px currentColor;
    filter: brightness(1);
  }
  50% {
    text-shadow: 0 0 10px currentColor, 0 0 20px currentColor;
    filter: brightness(1.3);
  }
}

@keyframes matrix-appear {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 4s ease-in-out infinite;
}

.animate-drift {
  animation: drift 8s ease-in-out infinite;
}

.animate-matrix-fall {
  animation: matrix-fall linear infinite;
  /* 添加硬件加速 */
  transform: translateZ(0);
  will-change: transform, opacity;
}

.animate-matrix-glow {
  animation: matrix-glow 2s ease-in-out infinite;
}

.animate-matrix-appear {
  animation: matrix-appear 0.8s ease-out forwards;
}
</style>
