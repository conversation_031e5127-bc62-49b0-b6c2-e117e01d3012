<script setup lang="tsx">
import { computed } from 'vue';

defineOptions({
  name: 'StatsGrid'
});

interface Props {
  /** 网格列数配置 */
  columns?: {
    default?: number;
    sm?: number;
    lg?: number;
  };
  /** 网格间距 */
  gap?: number;
}

const props = withDefaults(defineProps<Props>(), {
  columns: () => ({
    default: 1,
    sm: 2,
    lg: 4
  }),
  gap: 4
});

// 计算网格类名
const gridClasses = computed(() => {
  const { columns } = props;
  const classes = [`grid-cols-${columns.default || 1}`];
  
  if (columns.sm) {
    classes.push(`sm:grid-cols-${columns.sm}`);
  }
  
  if (columns.lg) {
    classes.push(`lg:grid-cols-${columns.lg}`);
  }
  
  return classes.join(' ');
});

// 计算间距类名
const gapClass = computed(() => `gap-${props.gap}`);
</script>

<template>
  <div class="grid" :class="[gridClasses, gapClass]">
    <slot />
  </div>
</template>

<style scoped>
.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

@media (min-width: 640px) {
  .sm\:grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  
  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  
  .sm\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  
  .sm\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  
  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  
  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  
  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

.gap-1 {
  gap: 0.25rem;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-3 {
  gap: 0.75rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-5 {
  gap: 1.25rem;
}

.gap-6 {
  gap: 1.5rem;
}

.gap-8 {
  gap: 2rem;
}
</style>
