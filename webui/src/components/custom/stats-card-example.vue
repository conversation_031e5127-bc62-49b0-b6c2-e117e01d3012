<script setup lang="tsx">
import StatsCard from '@/components/custom/stats-card.vue';
import StatsGrid from '@/components/custom/stats-grid.vue';

defineOptions({
  name: 'StatsCardExample'
});
</script>

<template>
  <div class="space-y-8">
    <!-- 基础用法 -->
    <div>
      <h3 class="mb-4 text-lg font-semibold">基础用法</h3>
      <StatsGrid>
        <StatsCard
          title="总用户数"
          :value="1234"
          unit="人"
          :color="{ start: '#007aff', end: '#0056cc' }"
          icon="mdi:account-group"
        />
        <StatsCard
          title="订单数量"
          :value="567"
          unit="单"
          :color="{ start: '#52c41a', end: '#389e0d' }"
          icon="mdi:receipt"
        />
      </StatsGrid>
    </div>

    <!-- 货币前缀示例 -->
    <div>
      <h3 class="mb-4 text-lg font-semibold">货币前缀示例</h3>
      <StatsGrid>
        <StatsCard
          title="总收入"
          :value="123456"
          unit="$"
          :unit-as-prefix="true"
          :color="{ start: '#722ed1', end: '#531dab' }"
          icon="mdi:currency-usd"
        />
        <StatsCard
          title="净利润"
          :value="45678"
          unit="$"
          :unit-as-prefix="true"
          :color="{ start: '#fa8c16', end: '#d46b08' }"
          icon="mdi:trending-up"
        />
      </StatsGrid>
    </div>

    <!-- 自定义列数 -->
    <div>
      <h3 class="mb-4 text-lg font-semibold">自定义列数（3列）</h3>
      <StatsGrid :columns="{ default: 1, sm: 2, lg: 3 }">
        <StatsCard
          title="项目1"
          :value="100"
          unit="个"
          :color="{ start: '#ec4786', end: '#b955a4' }"
          icon="mdi:cube"
        />
        <StatsCard
          title="项目2"
          :value="200"
          unit="个"
          :color="{ start: '#56cdf3', end: '#719de3' }"
          icon="mdi:cube-outline"
        />
        <StatsCard
          title="项目3"
          :value="300"
          unit="个"
          :color="{ start: '#fcbc25', end: '#f68057' }"
          icon="mdi:cube-send"
        />
      </StatsGrid>
    </div>

    <!-- 单个卡片示例 -->
    <div>
      <h3 class="mb-4 text-lg font-semibold">单个卡片</h3>
      <div class="w-64">
        <StatsCard
          title="独立卡片"
          :value="999"
          unit="次"
          :color="{ start: '#9c27b0', end: '#673ab7' }"
          icon="mdi:star"
          :duration="1000"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.space-y-8 > * + * {
  margin-top: 2rem;
}
</style>
