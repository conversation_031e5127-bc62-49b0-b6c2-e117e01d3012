/**
 * Namespace Api
 *
 * All backend api type
 */
declare namespace Api {
  /** Generic API response structure */
  interface ApiResponse<T = any> {
    success: boolean;
    message: string;
    data?: T;
    errors?: any;
    timestamp: string;
  }

  namespace Common {
    /** common params of paginating */
    interface PaginatingCommonParams {
      /** current page number */
      current: number;
      /** page size */
      size: number;
      /** total count */
      total: number;
    }

    /** common params of paginating query list data */
    interface PaginatingQueryRecord<T = any> extends PaginatingCommonParams {
      records: T[];
    }

    /** common search params of table */
    type CommonSearchParams = Pick<Common.PaginatingCommonParams, 'current' | 'size'>;

    /**
     * enable status
     *
     * - "1": enabled
     * - "2": disabled
     */
    type EnableStatus = '1' | '2';

    /** common record */
    type CommonRecord<T = any> = {
      /** record id */
      id: number;
      /** record creator */
      createBy: string;
      /** record create time */
      createTime: string;
      /** record updater */
      updateBy: string;
      /** record update time */
      updateTime: string;
      /** record status */
      status: EnableStatus | null;
    } & T;
  }

  /**
   * namespace Auth
   *
   * backend api module: "auth"
   */
  namespace Auth {
    /** Login request */
    interface LoginRequest {
      email: string;
      password: string;
    }

    /** Login response data */
    interface LoginData {
      user: {
        id: number;
        name: string;
        email: string;
        email_verified_at: string | null;
      };
      token: string;
      token_type: string;
    }

    /** Login token (for compatibility) */
    interface LoginToken {
      token: string;
      refreshToken?: string; // Optional for backward compatibility
    }

    /** Organization info */
    interface Organisation {
      id: number;
      name: string;
      code: string;
      status: string;
    }

    /** Role info */
    interface Role {
      id: number;
      name: string;
      guard_name: string;
      organisation_id: number | null;
      organisation_name?: string;
    }

    /** User roles structure */
    interface UserRoles {
      system_roles: Role[];
      organisation_roles: Role[];
      all_role_names: string[];
    }

    /** Complete user info from API */
    interface ApiUserInfo {
      id: number;
      name: string;
      email: string;
      email_verified_at: string | null;
      organisations: Organisation[];
      roles: UserRoles;
      created_at: string;
      updated_at: string;
    }

    /** User info for frontend (compatible with existing code) */
    interface UserInfo {
      userId: string;
      userName: string;
      roles: string[];
      buttons: string[];
    }
  }

  /**
   * namespace Route
   *
   * backend api module: "route"
   */
  namespace Route {
    type ElegantConstRoute = import('@elegant-router/types').ElegantConstRoute;

    interface MenuRoute extends ElegantConstRoute {
      id: string;
    }

    interface UserRoute {
      routes: MenuRoute[];
      home: import('@elegant-router/types').LastLevelRouteKey;
    }
  }

  /**
   * namespace Organization
   *
   * backend api module: "organization"
   */
  namespace Organization {
    /** Organization status */
    type OrganizationStatus = 'pending' | 'active' | 'suspended';

    /** Organization basic info */
    interface Organization {
      id: number;
      name: string;
      code: string;
      details?: Record<string, any>;
      remarks?: string;
      status: OrganizationStatus;
      is_active: boolean;
      is_pending: boolean;
      is_suspended: boolean;
      users_count?: number;
      created_at: string;
      updated_at: string;
    }

    /** Pagination meta info */
    interface PaginationMeta {
      total: number;
      per_page: number;
      current_page: number;
      last_page: number;
      from: number;
      to: number;
    }

    /** Organization list response */
    interface OrganizationListResponse {
      data: Organization[];
      meta: PaginationMeta;
    }

    /** Create organization request */
    interface CreateOrganizationRequest {
      name: string;
      code: string;
      details?: Record<string, any>;
      remarks?: string;
      status?: OrganizationStatus;
    }

    /** Update organization request */
    interface UpdateOrganizationRequest {
      name?: string;
      code?: string;
      details?: Record<string, any>;
      remarks?: string;
      status?: OrganizationStatus;
    }

    /** User in organization context */
    interface OrganizationUser {
      id: number;
      name: string;
      email: string;
      email_verified_at: string | null;
      organisations: Auth.Organisation[];
      created_at: string;
      updated_at: string;
    }

    /** Organization users response */
    interface OrganizationUsersResponse {
      data: OrganizationUser[];
      meta: PaginationMeta;
    }

    /** User list response */
    interface UserListResponse {
      data: OrganizationUser[];
      meta: PaginationMeta;
    }

    /** Create user request */
    interface CreateUserRequest {
      name: string;
      email: string;
      password: string;
      organisation_ids?: number[];
    }

    /** Update user request */
    interface UpdateUserRequest {
      name?: string;
      email?: string;
      password?: string;
      organisation_ids?: number[];
    }

    /** Invitation info */
    interface Invitation {
      id: string;
      model_type: string;
      model_id: number;
      role: string;
      created_by_user_id: number;
      expires_at: string | null;
      max_uses: number;
      uses: number;
      email_restriction: string | null;
      created_at: string;
      updated_at: string;
      model: Organization;
      created_by: {
        id: number;
        name: string;
        email: string;
      };
    }

    /** Invitation list response */
    interface InvitationListResponse {
      data: Invitation[];
      meta: PaginationMeta;
    }

    /** Create invitation request */
    interface CreateInvitationRequest {
      model_type: string;
      model_id: number;
      role: string;
      expires_at?: string;
      max_uses?: number;
      email_restriction?: string;
    }

    /** Accept invitation response */
    interface AcceptInvitationResponse {
      user: {
        id: number;
        name: string;
        email: string;
        email_verified_at: string;
      };
      organisation: Organization;
      role: string;
    }

    /** Invitation info response (for join page) */
    interface InvitationInfo {
      id: string;
      model_type: string;
      model_id: number;
      role: string;
      expires_at: string | null;
      max_uses: number;
      uses: number;
      email_restriction: string | null;
      is_expired: boolean;
      is_valid: boolean;
      has_reached_usage_limit: boolean;
      created_by: {
        id: number;
        name: string;
        email: string;
      };
      model_name: string;
      created_at: string;
      updated_at: string;
    }

    /** Send verification code response */
    interface SendVerificationCodeResponse {
      message: string;
      expires_in_seconds: number;
    }

    /** User status type */
    type UserStatus = 'active' | 'inactive' | 'pending' | 'suspended';

    /** User search parameters */
    interface UserSearchParams {
      per_page?: number;
      organisation_id?: number;
      organisation_ids?: string; // Comma-separated organisation IDs
      status?: UserStatus;
      name?: string;
      email?: string;
    }

    /** User statistics */
    interface UserStats {
      total: number;
      active: number;
      pending: number;
      suspended: number;
      new_this_month: number;
    }

    /** Role assignment request */
    interface AssignRoleRequest {
      role_name: string;
      organisation_id?: number;
    }

    /** Role assignment response */
    interface AssignRoleResponse {
      user_id: number;
      role_name: string;
      organisation_id?: number;
    }

    /** Remove role response */
    interface RemoveRoleResponse {
      user_id: number;
      role_id: number;
      role_name: string;
    }

    /** User roles detail response */
    interface UserRolesResponse {
      user_id: number;
      user_name: string;
      user_email: string;
      organisations: Auth.Organisation[];
      roles: Auth.UserRoles;
    }

    /** Transfer owner response */
    interface TransferOwnerResponse {
      new_owner: {
        id: number;
        name: string;
        email: string;
      };
      organisation: {
        id: number;
        name: string;
        code: string;
      };
      previous_owner: {
        id: number;
        name: string;
        email: string;
      };
    }

    /** Sync organizations request */
    interface SyncOrganizationsRequest {
      organisation_ids: number[];
    }

    /** Send verification code response */
    interface SendVerificationCodeResponse {
      message: string;
      expires_in_seconds: number;
    }

    /** Guest registration request */
    interface GuestRegistrationRequest {
      name: string;
      email: string;
      password: string;
      password_confirmation: string;
      verification_code: string;
      organisation_ids?: number[];
    }
  }
}
