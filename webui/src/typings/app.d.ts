/** The global namespace for the app */
declare namespace App {
  /** Theme namespace */
  namespace Theme {
    type ColorPaletteNumber = import('@sa/color').ColorPaletteNumber;

    /** Theme setting */
    interface ThemeSetting {
      /** Theme scheme */
      themeScheme: UnionKey.ThemeScheme;
      /** grayscale mode */
      grayscale: boolean;
      /** colour weakness mode */
      colourWeakness: boolean;
      /** Whether to recommend color */
      recommendColor: boolean;
      /** Theme color */
      themeColor: string;
      /** Other color */
      otherColor: OtherColor;
      /** Whether info color is followed by the primary color */
      isInfoFollowPrimary: boolean;
      /** Reset cache strategy */
      resetCacheStrategy: UnionKey.ResetCacheStrategy;
      /** Layout */
      layout: {
        /** Layout mode */
        mode: UnionKey.ThemeLayoutMode;
        /** Scroll mode */
        scrollMode: UnionKey.ThemeScrollMode;
        /**
         * Whether to reverse the horizontal mix
         *
         * if true, the vertical child level menus in left and horizontal first level menus in top
         */
        reverseHorizontalMix: boolean;
      };
      /** Page */
      page: {
        /** Whether to show the page transition */
        animate: boolean;
        /** Page animate mode */
        animateMode: UnionKey.ThemePageAnimateMode;
      };
      /** Header */
      header: {
        /** Header height */
        height: number;
        /** Header breadcrumb */
        breadcrumb: {
          /** Whether to show the breadcrumb */
          visible: boolean;
          /** Whether to show the breadcrumb icon */
          showIcon: boolean;
        };
        /** Multilingual */
        multilingual: {
          /** Whether to show the multilingual */
          visible: boolean;
        };
        globalSearch: {
          /** Whether to show the GlobalSearch */
          visible: boolean;
        };
      };
      /** Tab */
      tab: {
        /** Whether to show the tab */
        visible: boolean;
        /**
         * Whether to cache the tab
         *
         * If cache, the tabs will get from the local storage when the page is refreshed
         */
        cache: boolean;
        /** Tab height */
        height: number;
        /** Tab mode */
        mode: UnionKey.ThemeTabMode;
      };
      /** Fixed header and tab */
      fixedHeaderAndTab: boolean;
      /** Sider */
      sider: {
        /** Inverted sider */
        inverted: boolean;
        /** Sider width */
        width: number;
        /** Collapsed sider width */
        collapsedWidth: number;
        /** Sider width when the layout is 'vertical-mix' or 'horizontal-mix' */
        mixWidth: number;
        /** Collapsed sider width when the layout is 'vertical-mix' or 'horizontal-mix' */
        mixCollapsedWidth: number;
        /** Child menu width when the layout is 'vertical-mix' or 'horizontal-mix' */
        mixChildMenuWidth: number;
      };
      /** Footer */
      footer: {
        /** Whether to show the footer */
        visible: boolean;
        /** Whether fixed the footer */
        fixed: boolean;
        /** Footer height */
        height: number;
        /** Whether float the footer to the right when the layout is 'horizontal-mix' */
        right: boolean;
      };
      /** Watermark */
      watermark: {
        /** Whether to show the watermark */
        visible: boolean;
        /** Watermark text */
        text: string;
      };
      /** define some theme settings tokens, will transform to css variables */
      tokens: {
        light: ThemeSettingToken;
        dark?: {
          [K in keyof ThemeSettingToken]?: Partial<ThemeSettingToken[K]>;
        };
      };
    }

    interface OtherColor {
      info: string;
      success: string;
      warning: string;
      error: string;
    }

    interface ThemeColor extends OtherColor {
      primary: string;
    }

    type ThemeColorKey = keyof ThemeColor;

    type ThemePaletteColor = {
      [key in ThemeColorKey | `${ThemeColorKey}-${ColorPaletteNumber}`]: string;
    };

    type BaseToken = Record<string, Record<string, string>>;

    interface ThemeSettingTokenColor {
      /** the progress bar color, if not set, will use the primary color */
      nprogress?: string;
      container: string;
      layout: string;
      inverted: string;
      'base-text': string;
    }

    interface ThemeSettingTokenBoxShadow {
      header: string;
      sider: string;
      tab: string;
    }

    interface ThemeSettingToken {
      colors: ThemeSettingTokenColor;
      boxShadow: ThemeSettingTokenBoxShadow;
    }

    type ThemeTokenColor = ThemePaletteColor & ThemeSettingTokenColor;

    /** Theme token CSS variables */
    type ThemeTokenCSSVars = {
      colors: ThemeTokenColor & { [key: string]: string };
      boxShadow: ThemeSettingTokenBoxShadow & { [key: string]: string };
    };
  }

  /** Global namespace */
  namespace Global {
    type VNode = import('vue').VNode;
    type RouteLocationNormalizedLoaded = import('vue-router').RouteLocationNormalizedLoaded;
    type RouteKey = import('@elegant-router/types').RouteKey;
    type RouteMap = import('@elegant-router/types').RouteMap;
    type RoutePath = import('@elegant-router/types').RoutePath;
    type LastLevelRouteKey = import('@elegant-router/types').LastLevelRouteKey;

    /** The router push options */
    type RouterPushOptions = {
      query?: Record<string, string>;
      params?: Record<string, string>;
    };

    /** The global header props */
    interface HeaderProps {
      /** Whether to show the logo */
      showLogo?: boolean;
      /** Whether to show the menu toggler */
      showMenuToggler?: boolean;
      /** Whether to show the menu */
      showMenu?: boolean;
    }

    /** The global menu */
    type Menu = {
      /**
       * The menu key
       *
       * Equal to the route key
       */
      key: string;
      /** The menu label */
      label: string;
      /** The menu i18n key */
      i18nKey?: I18n.I18nKey | null;
      /** The route key */
      routeKey: RouteKey;
      /** The route path */
      routePath: RoutePath;
      /** The menu icon */
      icon?: () => VNode;
      /** The menu children */
      children?: Menu[];
    };

    type Breadcrumb = Omit<Menu, 'children'> & {
      options?: Breadcrumb[];
    };

    /** Tab route */
    type TabRoute = Pick<RouteLocationNormalizedLoaded, 'name' | 'path' | 'meta'> &
      Partial<Pick<RouteLocationNormalizedLoaded, 'fullPath' | 'query' | 'matched'>>;

    /** The global tab */
    type Tab = {
      /** The tab id */
      id: string;
      /** The tab label */
      label: string;
      /**
       * The new tab label
       *
       * If set, the tab label will be replaced by this value
       */
      newLabel?: string;
      /**
       * The old tab label
       *
       * when reset the tab label, the tab label will be replaced by this value
       */
      oldLabel?: string;
      /** The tab route key */
      routeKey: LastLevelRouteKey;
      /** The tab route path */
      routePath: RouteMap[LastLevelRouteKey];
      /** The tab route full path */
      fullPath: string;
      /** The tab fixed index */
      fixedIndex?: number | null;
      /**
       * Tab icon
       *
       * Iconify icon
       */
      icon?: string;
      /**
       * Tab local icon
       *
       * Local icon
       */
      localIcon?: string;
      /** I18n key */
      i18nKey?: I18n.I18nKey | null;
    };

    /** Form rule */
    type FormRule = import('naive-ui').FormItemRule;

    /** The global dropdown key */
    type DropdownKey = 'closeCurrent' | 'closeOther' | 'closeLeft' | 'closeRight' | 'closeAll';
  }

  /**
   * I18n namespace
   *
   * Locales type
   */
  namespace I18n {
    type RouteKey = import('@elegant-router/types').RouteKey;

    type LangType = 'en-US' | 'zh-CN';

    type LangOption = {
      label: string;
      key: LangType;
    };

    type I18nRouteKey = Exclude<RouteKey, 'root' | 'not-found'>;

    type FormMsg = {
      required: string;
      invalid: string;
    };

    type Schema = {
      system: {
        title: string;
        updateTitle: string;
        updateContent: string;
        updateConfirm: string;
        updateCancel: string;
      };
      common: {
        action: string;
        add: string;
        addSuccess: string;
        backToHome: string;
        batchDelete: string;
        cancel: string;
        close: string;
        check: string;
        expandColumn: string;
        columnSetting: string;
        config: string;
        confirm: string;
        delete: string;
        deleteSuccess: string;
        confirmDelete: string;
        edit: string;
        warning: string;
        error: string;
        index: string;
        keywordSearch: string;
        logout: string;
        logoutConfirm: string;
        lookForward: string;
        modify: string;
        modifySuccess: string;
        noData: string;
        operate: string;
        pleaseCheckValue: string;
        refresh: string;
        reset: string;
        search: string;
        status: string;
        switch: string;
        tip: string;
        trigger: string;
        update: string;
        updateSuccess: string;
        view: string;
        userCenter: string;
        unit: {
          reports: string;
          orders: string;
          organizations: string;
          people: string;
          games: string;
          copies: string;
        };
        yesOrNo: {
          yes: string;
          no: string;
        };
      };
      request: {
        logout: string;
        logoutMsg: string;
        logoutWithModal: string;
        logoutWithModalMsg: string;
        refreshToken: string;
        tokenExpired: string;
      };
      theme: {
        themeSchema: { title: string } & Record<UnionKey.ThemeScheme, string>;
        grayscale: string;
        colourWeakness: string;
        layoutMode: { title: string; reverseHorizontalMix: string } & Record<UnionKey.ThemeLayoutMode, string>;
        recommendColor: string;
        recommendColorDesc: string;
        themeColor: {
          title: string;
          followPrimary: string;
        } & Theme.ThemeColor;
        scrollMode: { title: string } & Record<UnionKey.ThemeScrollMode, string>;
        page: {
          animate: string;
          mode: { title: string } & Record<UnionKey.ThemePageAnimateMode, string>;
        };
        fixedHeaderAndTab: string;
        header: {
          height: string;
          breadcrumb: {
            visible: string;
            showIcon: string;
          };
          multilingual: {
            visible: string;
          };
          globalSearch: {
            visible: string;
          };
        };
        tab: {
          visible: string;
          cache: string;
          height: string;
          mode: { title: string } & Record<UnionKey.ThemeTabMode, string>;
        };
        sider: {
          inverted: string;
          width: string;
          collapsedWidth: string;
          mixWidth: string;
          mixCollapsedWidth: string;
          mixChildMenuWidth: string;
        };
        footer: {
          visible: string;
          fixed: string;
          height: string;
          right: string;
        };
        watermark: {
          visible: string;
          text: string;
        };
        themeDrawerTitle: string;
        pageFunTitle: string;
        resetCacheStrategy: { title: string } & Record<UnionKey.ResetCacheStrategy, string>;
        configOperation: {
          copyConfig: string;
          copySuccessMsg: string;
          resetConfig: string;
          resetSuccessMsg: string;
        };
      };
      route: Record<I18nRouteKey, string> & {
        organization: string;
        user: string;
        game: string;
        sales: string;
        finance: string;
        admin: string;
      };
      invitation: {
        title: string;
        loading: string;
        invalid: string;
        expired: string;
        usageLimitReached: string;
        invalidLink: string;
        expiredLink: string;
        usageLimitLink: string;
        invitedToJoin: string;
        organizationInfo: string;
        inviterRole: string;
        inviter: string;
        expirationTime: string;
        restrictedEmail: string;
        cannotAccept: string;
        acceptInvitation: string;
        loginToAccept: string;
        backToHome: string;
        joinSuccess: string;
        joinFailed: string;
        emailRestriction: string;
        getVerificationCode: string;
        resendCode: string;
        verificationCodeSent: string;
        sendCodeFailed: string;
        enterEmail: string;
        enterValidEmail: string;
        enterVerificationCode: string;
        enterPassword: string;
        enterPasswordAgain: string;
        emailPlaceholder: string;
        codePlaceholder: string;
        passwordPlaceholder: string;
        confirmPasswordPlaceholder: string;
        register: string;
        codeLogin: string;
        roles: {
          owner: string;
          member: string;
        };
      };
      page: {
        login: {
          common: {
            loginOrRegister: string;
            userNamePlaceholder: string;
            phonePlaceholder: string;
            codePlaceholder: string;
            passwordPlaceholder: string;
            confirmPasswordPlaceholder: string;
            codeLogin: string;
            confirm: string;
            back: string;
            validateSuccess: string;
            loginSuccess: string;
            welcomeBack: string;
          };
          pwdLogin: {
            title: string;
            rememberMe: string;
            forgetPassword: string;
            register: string;
            otherAccountLogin: string;
            otherLoginMode: string;
            superAdmin: string;
            admin: string;
            user: string;
          };
          codeLogin: {
            title: string;
            getCode: string;
            reGetCode: string;
            sendCodeSuccess: string;
            imageCodePlaceholder: string;
          };
          register: {
            title: string;
            agreement: string;
            protocol: string;
            policy: string;
          };
          resetPwd: {
            title: string;
          };
          bindWeChat: {
            title: string;
          };
        };
        home: {
          branchDesc: string;
          greeting: string;
          weatherDesc: string;
          projectCount: string;
          todo: string;
          message: string;
          downloadCount: string;
          registerCount: string;
          schedule: string;
          study: string;
          work: string;
          rest: string;
          entertainment: string;
          userCount: string;
          gameCount: string;
          orderCount: string;
          reportCount: string;
          dailyOrderSales: string;
          salesByRegion: string;
          orderAmount: string;
          orderQuantity: string;
          projectNews: {
            title: string;
            moreNews: string;
            desc1: string;
            desc2: string;
            desc3: string;
            desc4: string;
            desc5: string;
          };
          creativity: string;
        };
        organization: {
          title: string;
          list: string;
          stats: string;
          totalOrganizations: string;
          activeOrganizations: string;
          totalUsers: string;
          newOrganizations: string;
          searchPlaceholder: string;
          addOrganization: string;
          organizationName: string;
          createTime: string;
          userCount: string;
          status: string;
          actions: string;
          edit: string;
          viewDetails: string;
          manageUsers: string;
          active: string;
          inactive: string;
        };
        user: {
          title: string;
          list: string;
          stats: string;
          totalUsers: string;
          activeUsers: string;
          pendingActivation: string;
          newUsers: string;
          searchConditions: string;
          username: string;
          email: string;
          status: string;
          organization: string;
          avatar: string;
          role: string;
          createTime: string;
          actions: string;
          addUser: string;
          batchOperations: string;
          batchEnable: string;
          batchDisable: string;
          batchDelete: string;
          usernamePlaceholder: string;
          emailPlaceholder: string;
          statusPlaceholder: string;
          organizationPlaceholder: string;
          active: string;
          inactive: string;
          admin: string;
          normalUser: string;
        };
        game: {
          title: string;
          list: string;
          stats: string;
          totalGames: string;
          enabledGames: string;
          totalInventory: string;
          newGames: string;
          filterConditions: string;
          gameName: string;
          gameCode: string;
          priceRange: string;
          package: string;
          coverImage: string;
          name: string;
          code: string;
          multiLangName: string;
          onHand: string;
          enabled: string;
          supportedLanguages: string;
          sales: string;
          price: string;
          actions: string;
          importGame: string;
          batchOperations: string;
          batchEnable: string;
          batchDisable: string;
          batchDelete: string;
          gameNamePlaceholder: string;
          gameCodePlaceholder: string;
          statusPlaceholder: string;
          enable: string;
          disable: string;
          details: string;
          variants: string;
          languages: string;
          import: {
            title: string;
            searchPlaceholder: string;
            codeSearchPlaceholder: string;
            selectCategory: string;
            selectPlatform: string;
            importing: string;
            importSelected: string;
            selectAll: string;
            deselectAll: string;
            selectedCount: string;
            availableGames: string;
            category: string;
            platform: string;
            description: string;
            available: string;
            unavailable: string;
            importSuccess: string;
            importFailed: string;
            importing_: string;
            noGamesSelected: string;
            expandAll: string;
            collapseAll: string;
          };
          statistics: {
            title: string;
            gameBasicInfo: string;
            filterConditions: string;
            timeRange: string;
            region: string;
            currency: string;
            orderStatus: string;
            allRegions: string;
            northAmerica: string;
            europe: string;
            asia: string;
            others: string;
            allCurrencies: string;
            allStatuses: string;
            completed: string;
            processing: string;
            cancelled: string;
            refunded: string;
            totalSales: string;
            price: string;
            regionDistribution: string;
            currencyDistribution: string;
            statusDistribution: string;
            dailySales: string;
            hourlySales: string;
            salesVolume: string;
            salesAmount: string;
            orderCount: string;
            selectGame: string;
            noDataAvailable: string;
            date: string;
            hour: string;
            quantity: string;
            amount: string;
            count: string;
          };
        };
        sales: {
          title: string;
          stats: string;
          charts: string;
          totalSales: string;
          totalOrders: string;
          todaySales: string;
          monthSales: string;
          regionDistribution: string;
          currencyDistribution: string;
          orderStatus: string;
          dailySales: string;
          hourlyTrend: string;
          exportData: string;
          selectRegion: string;
          selectCurrency: string;
          allRegions: string;
          allCurrencies: string;
          northAmerica: string;
          europe: string;
          asia: string;
          others: string;
          completed: string;
          processing: string;
          cancelled: string;
          refunded: string;
          timeRange: string;
          region: string;
          currency: string;
          tabs: {
            overview: string;
            orders: string;
            refunds: string;
            regional: string;
            customers: string;
            topRanking: string;
          };
          topSalesRanking: string;
          topRefundRanking: string;
          ordersUnit: string;
        };
        finance: {
          title: string;
          reports: string;
          stats: string;
          totalRevenue: string;
          monthlyRevenue: string;
          pendingReports: string;
          publishedReports: string;
          filterConditions: string;
          reportId: string;
          organization: string;
          period: string;
          status: string;
          createTime: string;
          auditTime: string;
          publishTime: string;
          totalAmount: string;
          actions: string;
          generateReport: string;
          batchAudit: string;
          view: string;
          audit: string;
          draft: string;
          pending: string;
          published: string;
          organizationPlaceholder: string;
          statusPlaceholder: string;
          selectMonth: string;
        };
        admin: {
          title: string;
          settings: string;
          systemLogs: string;
          emailSettings: string;
          reportSettings: string;
          notificationSettings: string;
          smtpConfig: string;
          smtpHost: string;
          smtpPort: string;
          smtpUser: string;
          smtpPassword: string;
          enableTLS: string;
          emailTemplates: string;
          inviteTemplate: string;
          activationTemplate: string;
          testConnection: string;
          saveConfig: string;
          autoGenerate: string;
          generateDay: string;
          generateTime: string;
          autoAudit: string;
          autoPublish: string;
          notifyUsers: string;
          notificationChannels: string;
          emailNotification: string;
          smsNotification: string;
          pushNotification: string;
          notificationTypes: string;
          orderNotification: string;
          reportNotification: string;
          systemNotification: string;
          operationTime: string;
          operationUser: string;
          operationType: string;
          operationContent: string;
          ipAddress: string;
          operationResult: string;
          success: string;
          failed: string;
          exportLogs: string;
          clearLogs: string;
          userManagement: string;
          organizationManagement: string;
          gameManagement: string;
          login: string;
          systemSettings: string;
          timeRange: string;
          userPlaceholder: string;
          typePlaceholder: string;
          resultPlaceholder: string;
          monthlyDay: string;
        };
      };
      form: {
        required: string;
        userName: FormMsg;
        phone: FormMsg;
        pwd: FormMsg;
        confirmPwd: FormMsg;
        code: FormMsg;
        email: FormMsg;
      };
      dropdown: Record<Global.DropdownKey, string>;
      icon: {
        themeConfig: string;
        themeSchema: string;
        lang: string;
        fullscreen: string;
        fullscreenExit: string;
        reload: string;
        collapse: string;
        expand: string;
        pin: string;
        unpin: string;
      };
      datatable: {
        itemCount: string;
      };
      errorHandler: {
        operationFailed: string;
        networkError: string;
        validationFailed: string;
        fieldNames: {
          name: string;
          code: string;
          details: string;
          remarks: string;
          status: string;
          email: string;
          password: string;
          password_confirmation: string;
          username: string;
          user_name: string;
          organisation_ids: string;
          role_name: string;
          organisation_id: string;
          verification_code: string;
          role: string;
          model_type: string;
          model_id: string;
          expires_at: string;
          max_uses: string;
          email_restriction: string;
          id: string;
          created_at: string;
          updated_at: string;
        };
      };
    };

    type GetI18nKey<T extends Record<string, unknown>, K extends keyof T = keyof T> = K extends string
      ? T[K] extends Record<string, unknown>
        ? `${K}.${GetI18nKey<T[K]>}`
        : K
      : never;

    type I18nKey = GetI18nKey<Schema>;

    type TranslateOptions<Locales extends string> = import('vue-i18n').TranslateOptions<Locales>;

    interface $T {
      (key: I18nKey): string;
      (key: I18nKey, plural: number, options?: TranslateOptions<LangType>): string;
      (key: I18nKey, defaultMsg: string, options?: TranslateOptions<I18nKey>): string;
      (key: I18nKey, list: unknown[], options?: TranslateOptions<I18nKey>): string;
      (key: I18nKey, list: unknown[], plural: number): string;
      (key: I18nKey, list: unknown[], defaultMsg: string): string;
      (key: I18nKey, named: Record<string, unknown>, options?: TranslateOptions<LangType>): string;
      (key: I18nKey, named: Record<string, unknown>, plural: number): string;
      (key: I18nKey, named: Record<string, unknown>, defaultMsg: string): string;
    }
  }

  /** Service namespace */
  namespace Service {
    /** Other baseURL key */
    type OtherBaseURLKey = 'demo';

    interface ServiceConfigItem {
      /** The backend service base url */
      baseURL: string;
      /** The proxy pattern of the backend service base url */
      proxyPattern: string;
    }

    interface OtherServiceConfigItem extends ServiceConfigItem {
      key: OtherBaseURLKey;
    }

    /** The backend service config */
    interface ServiceConfig extends ServiceConfigItem {
      /** Other backend service config */
      other: OtherServiceConfigItem[];
    }

    interface SimpleServiceConfig extends Pick<ServiceConfigItem, 'baseURL'> {
      other: Record<OtherBaseURLKey, string>;
    }

    /** The backend service response data */
    type Response<T = unknown> = {
      /** The backend service response code */
      code: string;
      /** The backend service response message */
      msg: string;
      /** The backend service response data */
      data: T;
    };

    /** The demo backend service response data */
    type DemoResponse<T = unknown> = {
      /** The backend service response code */
      status: string;
      /** The backend service response message */
      message: string;
      /** The backend service response data */
      result: T;
    };
  }
}
