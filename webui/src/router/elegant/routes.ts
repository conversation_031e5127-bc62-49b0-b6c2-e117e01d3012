/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { GeneratedRoute } from '@elegant-router/types';

export const generatedRoutes: GeneratedRoute[] = [
  {
    name: '403',
    path: '/403',
    component: 'layout.blank$view.403',
    meta: {
      title: '403',
      i18nKey: 'route.403',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '404',
    path: '/404',
    component: 'layout.blank$view.404',
    meta: {
      title: '404',
      i18nKey: 'route.404',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '500',
    path: '/500',
    component: 'layout.blank$view.500',
    meta: {
      title: '500',
      i18nKey: 'route.500',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'admin',
    path: '/admin',
    component: 'layout.base$view.admin',
    meta: {
      title: 'admin',
      i18nKey: 'route.admin',
      icon: 'mdi:cog',
      order: 70,
      roles: ['root']
    }
  },
  {
    name: 'finance',
    path: '/finance',
    component: 'layout.base$view.finance',
    meta: {
      title: 'finance',
      i18nKey: 'route.finance',
      icon: 'mdi:cash-multiple',
      order: 22,
      roles: ['root', 'admin']
    }
  },
  {
    name: 'game',
    path: '/game',
    component: 'layout.base$view.game',
    meta: {
      title: 'game',
      i18nKey: 'route.game',
      icon: 'mdi:gamepad-variant',
      order: 10,
      roles: ['root', 'admin', 'owner', 'member']
    }
  },
  {
    name: 'home',
    path: '/home',
    component: 'layout.base$view.home',
    meta: {
      title: 'home',
      i18nKey: 'route.home',
      icon: 'mdi:monitor-dashboard',
      order: 1
    }
  },
  {
    name: 'iframe-page',
    path: '/iframe-page/:url',
    component: 'layout.base$view.iframe-page',
    props: true,
    meta: {
      title: 'iframe-page',
      i18nKey: 'route.iframe-page',
      constant: true,
      hideInMenu: true,
      keepAlive: true
    }
  },
  {
    name: 'join',
    path: '/join',
    component: 'layout.blank$view.join',
    meta: {
      title: 'join',
      i18nKey: 'route.join',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'login',
    path: '/login/:module(pwd-login|code-login|register|reset-pwd|bind-wechat)?',
    component: 'layout.blank$view.login',
    props: true,
    meta: {
      title: 'login',
      i18nKey: 'route.login',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'organization',
    path: '/organization',
    component: 'layout.base$view.organization',
    meta: {
      title: 'organization',
      i18nKey: 'route.organization',
      icon: 'mdi:office-building',
      order: 50,
      roles: ['root', 'admin']
    }
  },
  {
    name: 'sales',
    path: '/sales',
    component: 'layout.base$view.sales',
    meta: {
      title: 'sales',
      i18nKey: 'route.sales',
      icon: 'mdi:chart-line',
      order: 20,
      roles: ['root', 'admin', 'owner', 'member']
    }
  },
  {
    name: 'user',
    path: '/user',
    component: 'layout.base$view.user',
    meta: {
      title: 'user',
      i18nKey: 'route.user',
      icon: 'mdi:account-group',
      order: 30,
      roles: ['root', 'admin', 'owner', 'member']
    }
  }
];
