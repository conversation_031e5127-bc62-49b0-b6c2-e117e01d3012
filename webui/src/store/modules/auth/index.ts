import { computed, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
import { defineStore } from 'pinia';
import { useLoading } from '@sa/hooks';
import { fetchGetUserInfo, fetchLogin, fetchLogout } from '@/service/api';
import { useRouterPush } from '@/hooks/common/router';
import { localStg } from '@/utils/storage';
import { SetupStoreId } from '@/enum';
import { $t } from '@/locales';
import { useRouteStore } from '../route';
import { useTabStore } from '../tab';
import { clearAuthStorage, getToken } from './shared';
import { clearUserCaches } from '@/utils/cache-manager';

export const useAuthStore = defineStore(SetupStoreId.Auth, () => {
  const route = useRoute();
  const routeStore = useRouteStore();
  const tabStore = useTabStore();
  const { toLogin, redirectFromLogin } = useRouterPush(false);
  const { loading: loginLoading, startLoading, endLoading } = useLoading();

  const token = ref(getToken());

  const userInfo: Api.Auth.UserInfo = reactive({
    userId: '',
    userName: '',
    roles: [],
    buttons: []
  });

  // Store complete user info from API for additional features
  const completeUserInfo = ref<Api.Auth.ApiUserInfo | null>(null);

  /** is super role in static route */
  const isStaticSuper = computed(() => {
    const { VITE_AUTH_ROUTE_MODE, VITE_STATIC_SUPER_ROLE } = import.meta.env;

    return VITE_AUTH_ROUTE_MODE === 'static' && userInfo.roles.includes(VITE_STATIC_SUPER_ROLE);
  });

  /** Is login */
  const isLogin = computed(() => Boolean(token.value));

  /** User email from complete user info */
  const userEmail = computed(() => completeUserInfo.value?.email || '');

  /** User organisations from complete user info */
  const userOrganisations = computed(() => completeUserInfo.value?.organisations || []);

  /** User system roles from complete user info */
  const userSystemRoles = computed(() => completeUserInfo.value?.roles.system_roles || []);

  /** User organisation roles from complete user info */
  const userOrganisationRoles = computed(() => completeUserInfo.value?.roles.organisation_roles || []);

  /** Reset auth store */
  async function resetStore() {
    const authStore = useAuthStore();

    recordUserId();

    // Get current user ID for cache cleanup
    const currentUserId = userInfo.userId;

    // Call backend logout API if user is logged in
    if (token.value) {
      try {
        await fetchLogout();
      } catch {
        // Ignore logout API errors, still proceed with local cleanup
        // This ensures user can always logout even if API is unavailable
      }
    }

    clearAuthStorage();

    // Clear user-specific caches
    clearUserCaches(currentUserId);

    // Clear complete user info
    completeUserInfo.value = null;

    authStore.$reset();

    if (!route.meta.constant) {
      await toLogin();
    }

    tabStore.cacheTabs();
    routeStore.resetStore();
  }

  /** Record the user ID of the previous login session Used to compare with the current user ID on next login */
  function recordUserId() {
    if (!userInfo.userId) {
      return;
    }

    // Store current user ID locally for next login comparison
    localStg.set('lastLoginUserId', userInfo.userId);
  }

  /**
   * Check if current login user is different from previous login user If different, clear all tabs
   *
   * @returns {boolean} Whether to clear all tabs
   */
  function checkTabClear(): boolean {
    if (!userInfo.userId) {
      return false;
    }

    const lastLoginUserId = localStg.get('lastLoginUserId');

    // Clear all tabs if current user is different from previous user
    if (!lastLoginUserId || lastLoginUserId !== userInfo.userId) {
      localStg.remove('globalTabs');
      tabStore.clearTabs();

      localStg.remove('lastLoginUserId');
      return true;
    }

    localStg.remove('lastLoginUserId');
    return false;
  }

  /**
   * Login
   *
   * @param email User email
   * @param password Password
   * @param [redirect=true] Whether to redirect after login. Default is `true`
   */
  async function login(email: string, password: string, redirect = true) {
    startLoading();

    const { data: loginResponse, error } = await fetchLogin(email, password);

    if (!error && loginResponse?.data) {
      const loginToken = {
        token: loginResponse.data.token,
        refreshToken: '' // Not used in new API
      };
      const pass = await loginByToken(loginToken);

      if (pass) {
        // Check if the tab needs to be cleared
        const isClear = checkTabClear();
        let needRedirect = redirect;

        if (isClear) {
          // If the tab needs to be cleared,it means we don't need to redirect.
          needRedirect = false;
        }

        // Only redirect if redirect parameter is true
        if (redirect) {
          await redirectFromLogin(needRedirect);
        }

        window.$notification?.success({
          title: $t('page.login.common.loginSuccess'),
          content: $t('page.login.common.welcomeBack', { userName: userInfo.userName }),
          duration: 4500
        });
      }
    } else {
      resetStore();
    }

    endLoading();
  }

  async function loginByToken(loginToken: Api.Auth.LoginToken) {
    // 1. stored in the localStorage, the later requests need it in headers
    localStg.set('token', loginToken.token);
    if (loginToken.refreshToken) {
      localStg.set('refreshToken', loginToken.refreshToken);
    }

    // 2. get user info
    const pass = await getUserInfo();

    if (pass) {
      token.value = loginToken.token;

      return true;
    }

    return false;
  }

  async function getUserInfo() {
    const { data: response, error } = await fetchGetUserInfo();

    if (!error && response?.data) {
      const apiUserInfo = response.data;

      // Store complete user info for additional features
      completeUserInfo.value = apiUserInfo;

      // Transform API user info to frontend format for compatibility
      const transformedUserInfo: Api.Auth.UserInfo = {
        userId: String(apiUserInfo.id),
        userName: apiUserInfo.name,
        roles: apiUserInfo.roles.all_role_names,
        buttons: [] // API doesn't provide buttons, keeping empty for compatibility
      };

      // update store
      Object.assign(userInfo, transformedUserInfo);

      return true;
    }

    return false;
  }

  async function initUserInfo() {
    const hasToken = getToken();

    if (hasToken) {
      const pass = await getUserInfo();

      if (!pass) {
        resetStore();
      }
    }
  }

  return {
    token,
    userInfo,
    completeUserInfo,
    isStaticSuper,
    isLogin,
    loginLoading,
    userEmail,
    userOrganisations,
    userSystemRoles,
    userOrganisationRoles,
    resetStore,
    login,
    initUserInfo
  };
});
