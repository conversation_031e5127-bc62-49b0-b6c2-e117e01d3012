<script setup lang="tsx">
import { $t } from '@/locales';
import StatsCard from '@/components/custom/stats-card.vue';
import StatsGrid from '@/components/custom/stats-grid.vue';

defineOptions({
  name: 'GameStats'
});
</script>

<template>
  <StatsGrid>
    <StatsCard
      :title="$t('page.game.totalGames')"
      :value="1256"
      :unit="$t('common.unit.games')"
      :color="{ start: '#007aff', end: '#0056cc' }"
      icon="mdi:gamepad-variant"
    />
    <StatsCard
      :title="$t('page.game.enabledGames')"
      :value="1089"
      :unit="$t('common.unit.games')"
      :color="{ start: '#52c41a', end: '#389e0d' }"
      icon="mdi:check-circle"
    />
    <StatsCard
      :title="$t('page.game.totalInventory')"
      :value="156789"
      :unit="$t('common.unit.copies')"
      :color="{ start: '#722ed1', end: '#531dab' }"
      icon="mdi:package-variant"
    />
    <StatsCard
      :title="$t('page.game.newGames')"
      :value="45"
      :unit="$t('common.unit.games')"
      :color="{ start: '#fa8c16', end: '#d46b08' }"
      icon="mdi:plus-circle"
    />
  </StatsGrid>
</template>
