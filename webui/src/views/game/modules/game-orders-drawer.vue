<script setup lang="tsx">
import { computed, h, reactive, ref, watch } from 'vue';
import type { DataTableColumns } from 'naive-ui';
import {
  N<PERSON>utton,
  NCard,
  NDataTable,
  NDrawer,
  NDrawerContent,
  NGrid,
  NGridItem,
  NSelect,
  NSpace,
  NStatistic,
  NTag
} from 'naive-ui';
import { useAppStore } from '@/store/modules/app';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';
import ButtonIcon from '@/components/custom/button-icon.vue';
import OrderDetailDrawer from '@/views/finance/modules/order-detail-drawer.vue';

defineOptions({
  name: 'GameOrdersDrawer'
});

interface GameData {
  id: string;
  coverImage: string;
  name: string;
  code: string;
  multiLangName: Record<string, string>;
  languages: string[];
  onHand: number;
  enabled: boolean;
  price: number;
  sales: number;
}

interface GameVariant {
  id: string;
  name: string;
  code: string;
  onHand: number;
  price: number;
  enabled: boolean;
  type: 'default' | 'special' | 'collector' | 'limited';
  description?: string;
}

interface OrderData {
  id: string;
  orderDate: string;
  buyerName: string;
  distributor: string;
  region: string;
  quantity: number;
  unitPrice: number;
  totalAmount: number;
  orderStatus: 'completed' | 'processing' | 'cancelled' | 'refunded';
}

interface Props {
  visible: boolean;
  game: GameData | null;
  variant?: GameVariant | null;
}

interface Emits {
  (e: 'update:visible', visible: boolean): void;
  (e: 'viewOrderDetail', orderId: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const appStore = useAppStore();
const isMobile = computed(() => appStore.isMobile);

const show = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value)
});

// 订单详情抽屉状态
const orderDetailVisible = ref(false);
const selectedOrderId = ref('');

// 加载状态
const loading = ref(false);

// 筛选条件
const filterOptions = reactive({
  region: 'all',
  dateRange: null as [number, number] | null
});

// 地区选项
const regionOptions = computed(() => [
  { label: $t('page.game.orders.allRegions'), value: 'all' },
  { label: $t('page.game.statistics.northAmerica'), value: 'northAmerica' },
  { label: $t('page.game.statistics.europe'), value: 'europe' },
  { label: $t('page.game.statistics.asia'), value: 'asia' },
  { label: $t('page.game.statistics.others'), value: 'others' }
]);

// 模拟订单数据
const mockOrderData = ref<OrderData[]>([
  {
    id: 'ORD001',
    orderDate: '2024-01-15',
    buyerName: 'John Smith',
    distributor: 'TechCorp Solutions',
    region: 'northAmerica',
    quantity: 1,
    unitPrice: 29.99,
    totalAmount: 29.99,
    orderStatus: 'completed'
  },
  {
    id: 'ORD002',
    orderDate: '2024-01-16',
    buyerName: 'Emily Johnson',
    distributor: 'Digital Innovations Ltd',
    region: 'northAmerica',
    quantity: 2,
    unitPrice: 29.99,
    totalAmount: 59.98,
    orderStatus: 'completed'
  },
  {
    id: 'ORD003',
    orderDate: '2024-01-17',
    buyerName: 'Hans Mueller',
    distributor: 'GameHub Distribution',
    region: 'europe',
    quantity: 1,
    unitPrice: 29.99,
    totalAmount: 29.99,
    orderStatus: 'processing'
  },
  {
    id: 'ORD004',
    orderDate: '2024-01-18',
    buyerName: 'Yuki Tanaka',
    distributor: 'Global Gaming Partners',
    region: 'asia',
    quantity: 3,
    unitPrice: 29.99,
    totalAmount: 89.97,
    orderStatus: 'completed'
  },
  {
    id: 'ORD005',
    orderDate: '2024-01-19',
    buyerName: 'Sarah Wilson',
    distributor: 'TechCorp Solutions',
    region: 'northAmerica',
    quantity: 1,
    unitPrice: 29.99,
    totalAmount: 29.99,
    orderStatus: 'cancelled'
  }
]);

// 筛选后的订单数据
const filteredOrderData = computed(() => {
  return mockOrderData.value.filter(order => {
    const regionMatch = filterOptions.region === 'all' || order.region === filterOptions.region;

    // 时间区间筛选
    let dateMatch = true;
    if (filterOptions.dateRange && filterOptions.dateRange.length === 2) {
      const orderDate = new Date(order.orderDate).getTime();
      const [startDate, endDate] = filterOptions.dateRange;
      dateMatch = orderDate >= startDate && orderDate <= endDate;
    }

    return regionMatch && dateMatch;
  });
});

// 统计数据
const orderStats = computed(() => {
  const orders = filteredOrderData.value;
  return {
    totalOrders: orders.length,
    totalQuantity: orders.reduce((sum, order) => sum + order.quantity, 0),
    totalRevenue: orders.reduce((sum, order) => sum + order.totalAmount, 0)
  };
});

// 获取地区显示名称
function getRegionName(region: string) {
  const regionMap: Record<string, string> = {
    northAmerica: $t('page.game.statistics.northAmerica'),
    europe: $t('page.game.statistics.europe'),
    asia: $t('page.game.statistics.asia'),
    others: $t('page.game.statistics.others')
  };
  return regionMap[region] || region;
}

// 获取订单状态颜色
function getOrderStatusColor(status: OrderData['orderStatus']) {
  const colorMap = {
    completed: 'success',
    processing: 'warning',
    cancelled: 'error',
    refunded: 'info'
  } as const;
  return colorMap[status] || 'default';
}

// 获取订单状态文本
function getOrderStatusText(status: OrderData['orderStatus']) {
  const textMap = {
    completed: $t('page.game.orders.completed' as any),
    processing: $t('page.game.orders.processing' as any),
    cancelled: $t('page.game.orders.cancelled' as any),
    refunded: $t('page.game.orders.refunded' as any)
  };
  return textMap[status] || status;
}

// 处理筛选条件变化
function handleFilterChange() {
  // 筛选条件变化时可以在这里处理数据刷新
}

// 查看订单详情
function handleViewOrderDetail(orderId: string) {
  selectedOrderId.value = orderId;
  orderDetailVisible.value = true;
}

// 刷新订单数据
function handleRefresh() {
  loading.value = true;
  // 模拟刷新数据
  setTimeout(() => {
    loading.value = false;
    // 这里可以调用实际的数据刷新API
    window.$message?.success('订单数据已刷新');
  }, 1000);
}

// 表格列定义
const columns = computed<DataTableColumns<OrderData>>(() => [
  {
    title: $t('page.game.orders.orderId' as any),
    key: 'id',
    width: 100,
    render: row => h('span', { class: 'font-mono text-xs' }, row.id)
  },
  {
    title: $t('page.game.orders.orderDate' as any),
    key: 'orderDate',
    width: 100,
    sorter: true
  },

  {
    title: $t('page.game.orders.distributor' as any),
    key: 'distributor',
    width: 140,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: $t('page.game.orders.region' as any),
    key: 'region',
    width: 100,
    render: row => getRegionName(row.region)
  },
  {
    title: $t('page.game.orders.quantity' as any),
    key: 'quantity',
    width: 80,
    render: row => h(NTag, { type: 'info', size: 'small' }, { default: () => row.quantity })
  },
  {
    title: $t('page.game.orders.unitPrice' as any),
    key: 'unitPrice',
    width: 100,
    render: row => h('span', { class: 'font-medium' }, `$${row.unitPrice}`)
  },
  {
    title: $t('page.game.orders.totalAmount' as any),
    key: 'totalAmount',
    width: 120,
    render: row => h('span', { class: 'font-medium text-primary' }, `$${row.totalAmount.toFixed(2)}`)
  },
  {
    title: $t('page.game.orders.orderStatus' as any),
    key: 'orderStatus',
    width: 100,
    render: row =>
      h(
        NTag,
        {
          type: getOrderStatusColor(row.orderStatus),
          size: 'small'
        },
        { default: () => getOrderStatusText(row.orderStatus) }
      )
  },
  {
    title: $t('page.game.orders.actions' as any),
    key: 'actions',
    width: 80,
    render: row =>
      h(ButtonIcon, {
        icon: 'mdi:eye',
        tooltipContent: $t('page.game.orders.viewDetails' as any),
        class: 'text-primary',
        size: 'small',
        onClick: () => handleViewOrderDetail(row.id)
      })
  }
]);

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  onChange: (page: number) => {
    pagination.page = page;
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
  }
});

// 监听游戏或变体变化，重置筛选条件
watch([() => props.game, () => props.variant], () => {
  filterOptions.region = 'all';
  filterOptions.dateRange = null;
  pagination.page = 1;
});
</script>

<template>
  <NDrawer
    v-model:show="show"
    :width="isMobile ? '100%' : '66.67%'"
    placement="left"
    :mask-closable="true"
    :close-on-esc="true"
  >
    <NDrawerContent
      :title="
        variant
          ? `${variant.name} - ${$t('page.game.orders.title')}`
          : `${game?.name || ''} - ${$t('page.game.orders.title')}`
      "
      closable
    >
      <div v-if="game" class="space-y-6">
        <!-- 游戏/变体基本信息 -->
        <NCard :bordered="false" class="from-blue-50 to-indigo-50 bg-gradient-to-r dark:from-gray-800 dark:to-gray-700">
          <div class="flex items-center gap-4">
            <img
              v-if="!variant"
              :src="game.coverImage"
              :alt="game.name"
              class="h-20 w-16 rounded-lg object-cover shadow-md"
            />
            <div class="flex-1">
              <h3 class="text-lg text-gray-900 font-bold dark:text-white">
                {{ variant ? variant.name : game.name }}
              </h3>
              <p class="text-sm text-gray-600 font-mono dark:text-gray-300">
                {{ variant ? variant.code : game.code }}
              </p>
              <div class="mt-2 flex items-center gap-4">
                <span class="text-sm text-gray-500">
                  {{ $t('page.game.orders.totalOrders') }}:
                  <strong class="text-primary">{{ orderStats.totalOrders }}</strong>
                </span>
                <span class="text-sm text-gray-500">
                  {{ $t('page.game.orders.totalQuantity') }}:
                  <strong class="text-success">{{ orderStats.totalQuantity }}</strong>
                </span>
                <span class="text-sm text-gray-500">
                  {{ $t('page.game.orders.totalRevenue') }}:
                  <strong class="text-warning">${{ orderStats.totalRevenue.toFixed(2) }}</strong>
                </span>
              </div>
            </div>
          </div>
        </NCard>

        <!-- 筛选条件 -->
        <NCard :bordered="false" class="bg-gray-50 dark:bg-gray-800">
          <div class="space-y-4">
            <h4 class="text-md flex items-center gap-2 text-gray-900 font-semibold dark:text-white">
              <SvgIcon icon="mdi:filter" class="text-primary" />
              {{ $t('page.game.orders.filterConditions') }}
            </h4>

            <div :class="isMobile ? 'space-y-3' : 'grid grid-cols-2 gap-4'">
              <div class="flex flex-col gap-1">
                <span class="text-sm text-gray-600 font-medium dark:text-gray-300">
                  {{ $t('page.game.orders.region') }}
                </span>
                <NSelect
                  v-model:value="filterOptions.region"
                  :options="regionOptions"
                  size="small"
                  @update:value="handleFilterChange"
                />
              </div>

              <div class="flex flex-col gap-1">
                <span class="text-sm text-gray-600 font-medium dark:text-gray-300">
                  {{ $t('page.game.orders.dateRange' as any) }}
                </span>
                <NDatePicker
                  v-model:value="filterOptions.dateRange"
                  type="daterange"
                  size="small"
                  clearable
                  @update:value="handleFilterChange"
                />
              </div>
            </div>
          </div>
        </NCard>

        <!-- 订单列表 -->
        <NCard :bordered="false">
          <template #header>
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-2">
                <SvgIcon icon="mdi:format-list-bulleted" class="text-primary" />
                {{ $t('page.game.orders.orderList') }}
              </div>
              <NButton size="small" :loading="loading" @click="handleRefresh">
                <template #icon>
                  <SvgIcon icon="mdi:refresh" />
                </template>
                {{ $t('common.refresh') }}
              </NButton>
            </div>
          </template>

          <NDataTable
            :columns="columns"
            :data="filteredOrderData"
            :pagination="pagination"
            :loading="loading"
            :bordered="false"
            size="small"
            flex-height
            class="h-400px"
            scroll-x="800px"
          />
        </NCard>
      </div>

      <div v-else class="h-64 flex items-center justify-center">
        <div class="text-center text-gray-500">
          <SvgIcon icon="mdi:receipt-text-outline" class="mb-4 text-6xl" />
          <p>{{ $t('page.game.orders.selectGameOrVariant') }}</p>
        </div>
      </div>
    </NDrawerContent>

    <!-- 订单详情抽屉 -->
    <NDrawer
      v-model:show="orderDetailVisible"
      :width="isMobile ? '100%' : '66.67%'"
      placement="left"
      :mask-closable="true"
      :close-on-esc="true"
    >
      <NDrawerContent :title="$t('page.finance.orderDetail')" closable>
        <OrderDetailDrawer v-if="orderDetailVisible" :order-id="selectedOrderId" @close="orderDetailVisible = false" />
      </NDrawerContent>
    </NDrawer>
  </NDrawer>
</template>

<style scoped>
/* 自定义样式 */
</style>
