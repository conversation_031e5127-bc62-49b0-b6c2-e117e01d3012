<script setup lang="ts">
import { computed, nextTick, reactive, ref, watch } from 'vue';
import { <PERSON>ard, NDate<PERSON>icker, NDrawer, NDrawerContent, NGrid, NGridItem, NSelect } from 'naive-ui';
import dayjs from 'dayjs';
import { useAppStore } from '@/store/modules/app';
import { useEcharts } from '@/hooks/common/echarts';
import type { ECOption } from '@/hooks/common/echarts';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';

defineOptions({
  name: 'GameStatsDrawer'
});

interface GameData {
  id: string;
  coverImage: string;
  name: string;
  code: string;
  multiLangName: Record<string, string>;
  languages: string[];
  onHand: number;
  enabled: boolean;
  price: number;
  sales: number;
}

interface Props {
  visible: boolean;
  game: GameData | null;
}

interface Emits {
  (e: 'update:visible', visible: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const appStore = useAppStore();
const isMobile = computed(() => appStore.isMobile);

const show = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value)
});

// 时间范围选择，默认最近30天
const dateRange = ref<[number, number]>([Date.now() - 30 * 24 * 60 * 60 * 1000, Date.now()]);

// 筛选选项
const filterOptions = reactive({
  region: 'all',
  currency: 'all'
});

// 地区选项
const regionOptions = computed(() => [
  { label: $t('page.game.statistics.allRegions'), value: 'all' },
  { label: $t('page.game.statistics.northAmerica'), value: 'north_america' },
  { label: $t('page.game.statistics.europe'), value: 'europe' },
  { label: $t('page.game.statistics.asia'), value: 'asia' },
  { label: $t('page.game.statistics.others'), value: 'others' }
]);

// 币种选项
const currencyOptions = computed(() => [
  { label: $t('page.game.statistics.allCurrencies'), value: 'all' },
  { label: 'USD', value: 'usd' },
  { label: 'EUR', value: 'eur' },
  { label: 'JPY', value: 'jpy' },
  { label: 'CNY', value: 'cny' }
]);

// 模拟数据生成函数
function generateMockData() {
  const regions = [
    $t('page.game.statistics.northAmerica'),
    $t('page.game.statistics.europe'),
    $t('page.game.statistics.asia'),
    $t('page.game.statistics.others')
  ];
  const currencies = ['USD', 'EUR', 'JPY', 'CNY'];

  // 生成统计数据
  const totalSalesAmount = Math.floor(Math.random() * 100000) + 50000;
  const totalSalesVolume = Math.floor(Math.random() * 5000) + 2000;
  const refundCount = Math.floor(Math.random() * 200) + 50;
  const refundRate = ((refundCount / totalSalesVolume) * 100).toFixed(2);

  return {
    // 核心统计数据
    statistics: {
      totalSalesAmount,
      totalSalesVolume,
      refundCount,
      refundRate: Number.parseFloat(refundRate)
    },
    // 按地区的订单量分布
    regionVolumeData: regions.map(region => ({
      name: region,
      value: Math.floor(Math.random() * 1000) + 100
    })),
    // 按币种的订单量分布
    currencyVolumeData: currencies.map(currency => ({
      name: currency,
      value: Math.floor(Math.random() * 1000) + 100
    })),
    // 按地区的销售金额分布
    regionAmountData: regions.map(region => ({
      name: region,
      value: Math.floor(Math.random() * 50000) + 10000
    })),
    // 按币种的销售金额分布
    currencyAmountData: currencies.map(currency => ({
      name: currency,
      value: Math.floor(Math.random() * 50000) + 10000
    })),
    dailySales: Array.from({ length: 30 }, (_, i) => ({
      date: dayjs(new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000)).format('MM/DD'),
      sales: Math.floor(Math.random() * 200) + 50
    })),
    hourlySales: Array.from({ length: 24 }, (_, i) => ({
      hour: `${i.toString().padStart(2, '0')}:00`,
      sales: Math.floor(Math.random() * 50) + 10
    }))
  };
}

// 监听游戏变化，重新生成数据
const mockData = ref(generateMockData());

// 图表重新渲染的key，用于强制重新渲染图表
const chartRenderKey = ref(0);
watch(
  () => props.game,
  () => {
    if (props.game) {
      mockData.value = generateMockData();
    }
  }
);

// 监听语言变化，重新生成数据以更新图表中的文本
watch(
  () => appStore.locale,
  () => {
    updateChartsLocale();
  }
);

// 监听抽屉显示状态，当抽屉重新打开时确保图表使用最新的语言配置
watch(
  () => show.value,
  newShow => {
    if (newShow) {
      // 抽屉打开时，延迟一下确保DOM已渲染，然后更新图表
      nextTick(() => {
        updateChartsLocale();
      });
    }
  }
);

// 地区订单量饼图配置
const regionVolumePieOption = computed(
  (): ECOption => ({
    title: {
      text: $t('page.game.statistics.regionVolumeDistribution'),
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        return `${params.seriesName} <br/>${params.name}: ${params.value} (${params.percent}%)`;
      }
    },
    legend: {
      orient: 'horizontal',
      left: 'center',
      bottom: '10%'
    },
    series: [
      {
        name: $t('page.game.statistics.salesVolume'),
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '45%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: mockData.value.regionVolumeData
      }
    ]
  })
);

// 币种订单量饼图配置
const currencyVolumePieOption = computed(
  (): ECOption => ({
    title: {
      text: $t('page.game.statistics.currencyVolumeDistribution'),
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        return `${params.seriesName} <br/>${params.name}: ${params.value} (${params.percent}%)`;
      }
    },
    legend: {
      orient: 'horizontal',
      left: 'center',
      bottom: '10%'
    },
    series: [
      {
        name: $t('page.game.statistics.salesVolume'),
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '45%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: mockData.value.currencyVolumeData
      }
    ]
  })
);

// 地区销售金额饼图配置
const regionAmountPieOption = computed(
  (): ECOption => ({
    title: {
      text: $t('page.game.statistics.regionAmountDistribution'),
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        return `${params.seriesName} <br/>${params.name}: $${params.value.toLocaleString()} (${params.percent}%)`;
      }
    },
    legend: {
      orient: 'horizontal',
      left: 'center',
      bottom: '10%'
    },
    series: [
      {
        name: $t('page.game.statistics.salesAmount'),
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '45%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: mockData.value.regionAmountData
      }
    ]
  })
);

// 币种销售金额饼图配置
const currencyAmountPieOption = computed(
  (): ECOption => ({
    title: {
      text: $t('page.game.statistics.currencyAmountDistribution'),
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        return `${params.seriesName} <br/>${params.name}: $${params.value.toLocaleString()} (${params.percent}%)`;
      }
    },
    legend: {
      orient: 'horizontal',
      left: 'center',
      bottom: '10%'
    },
    series: [
      {
        name: $t('page.game.statistics.salesAmount'),
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '45%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: mockData.value.currencyAmountData
      }
    ]
  })
);

// 日销售柱状图配置
const dailyBarOption = computed(
  (): ECOption => ({
    title: {
      text: $t('page.game.statistics.dailySales'),
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params: any) => {
        const param = params[0];
        return `${param.name}<br/>${param.seriesName}: ${param.value}`;
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: mockData.value.dailySales.map(item => item.date),
        axisTick: {
          alignWithLabel: true
        },
        name: $t('page.game.statistics.date'),
        nameLocation: 'middle',
        nameGap: 30
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: $t('page.game.statistics.quantity'),
        nameLocation: 'middle',
        nameGap: 50
      }
    ],
    series: [
      {
        name: $t('page.game.statistics.salesVolume'),
        type: 'bar',
        barWidth: '60%',
        itemStyle: {
          borderRadius: [4, 4, 0, 0]
        },
        data: mockData.value.dailySales.map(item => item.sales)
      }
    ]
  })
);

// 分时曲线图配置
const hourlyLineOption = computed(
  (): ECOption => ({
    title: {
      text: $t('page.game.statistics.hourlySales'),
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const param = params[0];
        return `${param.name}<br/>${param.seriesName}: ${param.value}`;
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: mockData.value.hourlySales.map(item => item.hour),
      name: $t('page.game.statistics.hour'),
      nameLocation: 'middle',
      nameGap: 30
    },
    yAxis: {
      type: 'value',
      name: $t('page.game.statistics.quantity'),
      nameLocation: 'middle',
      nameGap: 50
    },
    series: [
      {
        name: $t('page.game.statistics.salesVolume'),
        type: 'line',
        stack: 'Total',
        smooth: true,
        lineStyle: {
          width: 3
        },
        areaStyle: {
          opacity: 0.3
        },
        data: mockData.value.hourlySales.map(item => item.sales)
      }
    ]
  })
);

// 图表hooks
const { domRef: regionVolumeChartRef, updateOptions: updateRegionVolumeChart } = useEcharts(
  () => regionVolumePieOption.value
);
const { domRef: currencyVolumeChartRef, updateOptions: updateCurrencyVolumeChart } = useEcharts(
  () => currencyVolumePieOption.value
);
const { domRef: regionAmountChartRef, updateOptions: updateRegionAmountChart } = useEcharts(
  () => regionAmountPieOption.value
);
const { domRef: currencyAmountChartRef, updateOptions: updateCurrencyAmountChart } = useEcharts(
  () => currencyAmountPieOption.value
);
const { domRef: dailyChartRef, updateOptions: updateDailyChart } = useEcharts(() => dailyBarOption.value);
const { domRef: hourlyChartRef, updateOptions: updateHourlyChart } = useEcharts(() => hourlyLineOption.value);

// 更新图表语言配置
function updateChartsLocale() {
  // 重新生成数据以更新图表中的文本
  mockData.value = generateMockData();

  // 强制重新渲染图表
  chartRenderKey.value += 1;

  // 更新所有图表配置
  updateRegionVolumeChart((_, factory) => factory());
  updateCurrencyVolumeChart((_, factory) => factory());
  updateRegionAmountChart((_, factory) => factory());
  updateCurrencyAmountChart((_, factory) => factory());
  updateDailyChart((_, factory) => factory());
  updateHourlyChart((_, factory) => factory());
}

// 处理日期范围变化
function handleDateRangeChange() {
  // 重新生成数据
  mockData.value = generateMockData();
}

// 处理筛选条件变化
function handleFilterChange() {
  // 重新生成数据
  mockData.value = generateMockData();
}
</script>

<template>
  <NDrawer
    v-model:show="show"
    :width="isMobile ? '100%' : '66.67%'"
    placement="left"
    :mask-closable="true"
    :close-on-esc="true"
  >
    <NDrawerContent :title="`${game?.name || ''} - ${$t('page.game.statistics.title')}`" closable>
      <div v-if="game" class="space-y-6">
        <!-- 游戏基本信息 -->
        <NCard :bordered="false" class="from-blue-50 to-indigo-50 bg-gradient-to-r dark:from-gray-800 dark:to-gray-700">
          <div class="flex items-center gap-4">
            <img :src="game.coverImage" :alt="game.name" class="h-20 w-16 rounded-lg object-cover shadow-md" />
            <div class="flex-1">
              <h3 class="text-lg text-gray-900 font-bold dark:text-white">{{ game.name }}</h3>
              <p class="text-sm text-gray-600 font-mono dark:text-gray-300">{{ game.code }}</p>
              <div class="mt-2 flex items-center gap-4">
                <span class="text-sm text-gray-500">
                  {{ $t('page.game.statistics.totalSales') }}:
                  <strong class="text-primary">{{ game.sales.toLocaleString() }}</strong>
                </span>
                <span class="text-sm text-gray-500">
                  {{ $t('page.game.statistics.price') }}:
                  <strong class="text-success">${{ game.price }}</strong>
                </span>
              </div>
            </div>
          </div>
        </NCard>

        <!-- 筛选条件 -->
        <NCard :bordered="false" class="bg-gray-50 dark:bg-gray-800">
          <div class="space-y-4">
            <h4 class="text-md flex items-center gap-2 text-gray-900 font-semibold dark:text-white">
              <SvgIcon icon="mdi:filter" class="text-primary" />
              {{ $t('page.game.statistics.filterConditions') }}
            </h4>

            <div :class="isMobile ? 'space-y-3' : 'grid grid-cols-2 lg:grid-cols-3 gap-3'">
              <div class="flex flex-col gap-1">
                <span class="text-sm text-gray-600 font-medium dark:text-gray-300">
                  {{ $t('page.game.statistics.timeRange') }}
                </span>
                <NDatePicker
                  v-model:value="dateRange"
                  type="daterange"
                  size="small"
                  clearable
                  @update:value="handleDateRangeChange"
                />
              </div>

              <div class="flex flex-col gap-1">
                <span class="text-sm text-gray-600 font-medium dark:text-gray-300">
                  {{ $t('page.game.statistics.region') }}
                </span>
                <NSelect
                  v-model:value="filterOptions.region"
                  :options="regionOptions"
                  size="small"
                  @update:value="handleFilterChange"
                />
              </div>

              <div class="flex flex-col gap-1">
                <span class="text-sm text-gray-600 font-medium dark:text-gray-300">
                  {{ $t('page.game.statistics.currency') }}
                </span>
                <NSelect
                  v-model:value="filterOptions.currency"
                  :options="currencyOptions"
                  size="small"
                  @update:value="handleFilterChange"
                />
              </div>
            </div>
          </div>
        </NCard>

        <!-- 统计数据概览 -->
        <NCard :bordered="false" class="bg-white dark:bg-gray-800">
          <div class="space-y-4">
            <h4 class="text-md flex items-center gap-2 text-gray-900 font-semibold dark:text-white">
              <SvgIcon icon="mdi:chart-line" class="text-primary" />
              {{ $t('page.game.statistics.overview') }}
            </h4>

            <NGrid :cols="isMobile ? 2 : 4" :x-gap="16" :y-gap="16">
              <!-- 销售金额 -->
              <NGridItem>
                <div
                  class="h-24 flex items-center border-l-4 border-blue-500 rounded-lg bg-blue-50 p-4 dark:bg-blue-900/20"
                >
                  <div class="w-full flex items-center justify-between">
                    <div class="min-w-0 flex-1">
                      <p class="truncate text-sm text-blue-600 font-medium dark:text-blue-400">
                        {{ $t('page.game.statistics.gameTotalSalesAmount') }}
                      </p>
                      <p class="mt-1 text-2xl text-blue-900 font-bold dark:text-blue-100">
                        ${{ mockData.statistics.totalSalesAmount.toLocaleString() }}
                      </p>
                    </div>
                    <SvgIcon icon="mdi:currency-usd" class="ml-3 flex-shrink-0 text-3xl text-blue-500" />
                  </div>
                </div>
              </NGridItem>

              <!-- 销量 -->
              <NGridItem>
                <div
                  class="h-24 flex items-center border-l-4 border-green-500 rounded-lg bg-green-50 p-4 dark:bg-green-900/20"
                >
                  <div class="w-full flex items-center justify-between">
                    <div class="min-w-0 flex-1">
                      <p class="truncate text-sm text-green-600 font-medium dark:text-green-400">
                        {{ $t('page.game.statistics.gameTotalSalesVolume') }}
                      </p>
                      <p class="mt-1 text-2xl text-green-900 font-bold dark:text-green-100">
                        {{ mockData.statistics.totalSalesVolume.toLocaleString() }}
                      </p>
                    </div>
                    <SvgIcon icon="mdi:cart" class="ml-3 flex-shrink-0 text-3xl text-green-500" />
                  </div>
                </div>
              </NGridItem>

              <!-- 退单数量 -->
              <NGridItem>
                <div
                  class="h-24 flex items-center border-l-4 border-orange-500 rounded-lg bg-orange-50 p-4 dark:bg-orange-900/20"
                >
                  <div class="w-full flex items-center justify-between">
                    <div class="min-w-0 flex-1">
                      <p class="truncate text-sm text-orange-600 font-medium dark:text-orange-400">
                        {{ $t('page.game.statistics.gameRefundCount') }}
                      </p>
                      <p class="mt-1 text-2xl text-orange-900 font-bold dark:text-orange-100">
                        {{ mockData.statistics.refundCount.toLocaleString() }}
                      </p>
                    </div>
                    <SvgIcon icon="mdi:undo" class="ml-3 flex-shrink-0 text-3xl text-orange-500" />
                  </div>
                </div>
              </NGridItem>

              <!-- 退单率 -->
              <NGridItem>
                <div
                  class="h-24 flex items-center border-l-4 border-red-500 rounded-lg bg-red-50 p-4 dark:bg-red-900/20"
                >
                  <div class="w-full flex items-center justify-between">
                    <div class="min-w-0 flex-1">
                      <p class="truncate text-sm text-red-600 font-medium dark:text-red-400">
                        {{ $t('page.game.statistics.gameRefundRate') }}
                      </p>
                      <p class="mt-1 text-2xl text-red-900 font-bold dark:text-red-100">
                        {{ mockData.statistics.refundRate }}%
                      </p>
                    </div>
                    <SvgIcon icon="mdi:percent" class="ml-3 flex-shrink-0 text-3xl text-red-500" />
                  </div>
                </div>
              </NGridItem>
            </NGrid>
          </div>
        </NCard>

        <!-- 图表区域 -->
        <div class="space-y-6">
          <!-- 饼图行 -->
          <NGrid :key="`pie-charts-${chartRenderKey}`" :cols="isMobile ? 1 : 2" :x-gap="16" :y-gap="16">
            <NGridItem>
              <NCard :bordered="false" class="h-80">
                <div ref="regionVolumeChartRef" class="h-full w-full"></div>
              </NCard>
            </NGridItem>
            <NGridItem>
              <NCard :bordered="false" class="h-80">
                <div ref="currencyVolumeChartRef" class="h-full w-full"></div>
              </NCard>
            </NGridItem>
            <NGridItem>
              <NCard :bordered="false" class="h-80">
                <div ref="regionAmountChartRef" class="h-full w-full"></div>
              </NCard>
            </NGridItem>
            <NGridItem>
              <NCard :bordered="false" class="h-80">
                <div ref="currencyAmountChartRef" class="h-full w-full"></div>
              </NCard>
            </NGridItem>
          </NGrid>

          <!-- 柱状图和曲线图 -->
          <NGrid :key="`line-bar-charts-${chartRenderKey}`" :cols="isMobile ? 1 : 2" :x-gap="16" :y-gap="16">
            <NGridItem>
              <NCard :bordered="false" class="h-96">
                <div ref="dailyChartRef" class="h-full w-full"></div>
              </NCard>
            </NGridItem>
            <NGridItem>
              <NCard :bordered="false" class="h-96">
                <div ref="hourlyChartRef" class="h-full w-full"></div>
              </NCard>
            </NGridItem>
          </NGrid>
        </div>
      </div>

      <div v-else class="h-64 flex items-center justify-center">
        <div class="text-center text-gray-500">
          <SvgIcon icon="mdi:chart-box-outline" class="mb-4 text-6xl" />
          <p>{{ $t('page.game.statistics.selectGame') }}</p>
        </div>
      </div>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped>
/* 自定义样式 */
</style>
