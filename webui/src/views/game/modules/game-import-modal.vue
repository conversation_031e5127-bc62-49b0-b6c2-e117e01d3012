<script setup lang="tsx">
import { computed, h, reactive, ref, watch } from 'vue';
import type { DataTableColumns, DataTableRowKey } from 'naive-ui';
import { NButton, NTag } from 'naive-ui';
import { useAppStore } from '@/store/modules/app';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';

defineOptions({
  name: 'GameImportModal'
});

interface Props {
  /** 模态框可见性 */
  visible: boolean;
}

interface Emits {
  (e: 'update:visible', visible: boolean): void;
  (e: 'imported', games: ImportGameData[]): void;
}

interface ImportGameVariant {
  id: string;
  name: string;
  code: string;
  price: number;
  onHand: number;
  type: 'default' | 'special' | 'collector' | 'limited';
  status: 'available' | 'unavailable';
  description?: string;
}

interface ImportGameData {
  id: string;
  name: string;
  code: string;
  price: number;
  category: string;
  platform: string;
  status: 'available' | 'unavailable';
  description?: string;
  coverImage: string;
  variants: ImportGameVariant[];
  isExpanded?: boolean;
}

const props = withDefaults(defineProps<Props>(), {});
const emit = defineEmits<Emits>();

const appStore = useAppStore();
const isMobile = computed(() => appStore.isMobile);

// 响应式宽度设置
const modalClass = computed(() => {
  if (isMobile.value) {
    return 'size-full top-0px rounded-0';
  }
  return 'w-85vw max-w-1400px min-w-1000px top-20px';
});

// 搜索表单
const searchForm = reactive({
  name: '',
  code: '',
  priceRange: [0, 200] as [number, number],
  category: '',
  platform: ''
});

// 状态
const loading = ref(false);
const importLoading = ref(false);
const checkedRowKeys = ref<DataTableRowKey[]>([]);

// 展开的行键 - 默认全部展开
const expandedRowKeys = ref<DataTableRowKey[]>([]);

// 模拟待导入游戏数据
const availableGames = ref<ImportGameData[]>([
  {
    id: 'IMPORT001',
    name: 'Fantasy Adventure',
    code: 'FANTASY_ADV_001',
    price: 29.99,
    category: 'RPG',
    platform: 'PC',
    status: 'available',
    description: 'An exciting role-playing adventure game',
    coverImage: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=300&fit=crop&crop=center',
    variants: [
      {
        id: 'IMPORT001-1',
        name: 'Standard Edition',
        code: 'FANTASY_ADV_001_STD',
        price: 29.99,
        onHand: 150,
        type: 'default',
        status: 'available',
        description: 'Standard game edition'
      },
      {
        id: 'IMPORT001-2',
        name: 'Deluxe Edition',
        code: 'FANTASY_ADV_001_DLX',
        price: 49.99,
        onHand: 80,
        type: 'special',
        status: 'available',
        description: 'Includes additional DLC and digital content'
      },
      {
        id: 'IMPORT001-3',
        name: 'Collector\'s Edition',
        code: 'FANTASY_ADV_001_COL',
        price: 89.99,
        onHand: 30,
        type: 'collector',
        status: 'available',
        description: 'Limited collector\'s edition with physical items'
      }
    ]
  },
  {
    id: 'IMPORT002',
    name: 'Galactic Warfare',
    code: 'SPACE_WAR_002',
    price: 39.99,
    category: 'Strategy',
    platform: 'PC',
    status: 'available',
    description: 'Epic space strategy game',
    coverImage: 'https://images.unsplash.com/photo-1446776653964-20c1d3a81b06?w=200&h=300&fit=crop&crop=center',
    variants: [
      {
        id: 'IMPORT002-1',
        name: 'Standard Edition',
        code: 'SPACE_WAR_002_STD',
        price: 39.99,
        onHand: 200,
        type: 'default',
        status: 'available',
        description: 'Standard game edition'
      },
      {
        id: 'IMPORT002-2',
        name: 'Commander Edition',
        code: 'SPACE_WAR_002_CMD',
        price: 59.99,
        onHand: 100,
        type: 'special',
        status: 'available',
        description: 'Includes additional campaigns and units'
      }
    ]
  },
  {
    id: 'IMPORT003',
    name: 'Speed Rush',
    code: 'RACING_003',
    price: 19.99,
    category: 'Racing',
    platform: 'PC',
    status: 'unavailable',
    description: 'Thrilling racing game experience',
    coverImage: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=200&h=300&fit=crop&crop=center',
    variants: [
      {
        id: 'IMPORT003-1',
        name: 'Standard Edition',
        code: 'RACING_003_STD',
        price: 19.99,
        onHand: 0,
        type: 'default',
        status: 'unavailable',
        description: 'Standard game edition'
      }
    ]
  },
  {
    id: 'IMPORT004',
    name: 'Magic Academy',
    code: 'MAGIC_SCHOOL_004',
    price: 24.99,
    category: 'Simulation',
    platform: 'PC',
    status: 'available',
    description: 'Magical academy management game',
    coverImage: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=300&fit=crop&crop=center',
    variants: [
      {
        id: 'IMPORT004-1',
        name: 'Standard Edition',
        code: 'MAGIC_SCHOOL_004_STD',
        price: 24.99,
        onHand: 120,
        type: 'default',
        status: 'available',
        description: 'Standard game edition'
      },
      {
        id: 'IMPORT004-2',
        name: 'Academy Edition',
        code: 'MAGIC_SCHOOL_004_EDU',
        price: 34.99,
        onHand: 60,
        type: 'special',
        status: 'available',
        description: 'Includes tutorial mode and additional content'
      }
    ]
  },
  {
    id: 'IMPORT005',
    name: 'Apocalypse Survival',
    code: 'SURVIVAL_005',
    price: 34.99,
    category: 'Survival',
    platform: 'PC',
    status: 'available',
    description: 'Post-apocalyptic survival game',
    coverImage: 'https://images.unsplash.com/photo-1511512578047-dfb367046420?w=200&h=300&fit=crop&crop=center',
    variants: [
      {
        id: 'IMPORT005-1',
        name: 'Standard Edition',
        code: 'SURVIVAL_005_STD',
        price: 34.99,
        onHand: 180,
        type: 'default',
        status: 'available',
        description: 'Standard game edition'
      },
      {
        id: 'IMPORT005-2',
        name: 'Survivor Edition',
        code: 'SURVIVAL_005_SUR',
        price: 54.99,
        onHand: 90,
        type: 'special',
        status: 'available',
        description: 'Includes additional survival tools and maps'
      },
      {
        id: 'IMPORT005-3',
        name: 'Apocalypse Collector\'s Edition',
        code: 'SURVIVAL_005_APO',
        price: 99.99,
        onHand: 25,
        type: 'collector',
        status: 'available',
        description: 'Limited edition with physical survival manual'
      }
    ]
  },
  {
    id: 'IMPORT006',
    name: 'Mystery Quest',
    code: 'MYSTERY_006',
    price: 15.99,
    category: 'Puzzle',
    platform: 'PC',
    status: 'available',
    description: 'Mind-bending puzzle adventure',
    coverImage: 'https://images.unsplash.com/photo-1606092195730-5d7b9af1efc5?w=200&h=300&fit=crop&crop=center',
    variants: [
      {
        id: 'IMPORT006-1',
        name: 'Standard Edition',
        code: 'MYSTERY_006_STD',
        price: 15.99,
        onHand: 250,
        type: 'default',
        status: 'available',
        description: 'Standard game edition'
      }
    ]
  },
  {
    id: 'IMPORT007',
    name: 'Battlefield Elite',
    code: 'WAR_FRONT_007',
    price: 49.99,
    category: 'Shooter',
    platform: 'PlayStation',
    status: 'available',
    description: 'First-person shooter experience',
    coverImage: 'https://images.unsplash.com/photo-1552820728-8b83bb6b773f?w=200&h=300&fit=crop&crop=center',
    variants: [
      {
        id: 'IMPORT007-1',
        name: 'Standard Edition',
        code: 'WAR_FRONT_007_STD',
        price: 49.99,
        onHand: 100,
        type: 'default',
        status: 'available',
        description: 'Standard game edition'
      },
      {
        id: 'IMPORT007-2',
        name: 'Tactical Edition',
        code: 'WAR_FRONT_007_TAC',
        price: 69.99,
        onHand: 50,
        type: 'special',
        status: 'available',
        description: 'Includes additional weapons and maps'
      }
    ]
  },
  {
    id: 'IMPORT008',
    name: 'Farm Paradise',
    code: 'FARM_STORY_008',
    price: 22.99,
    category: 'Simulation',
    platform: 'Nintendo Switch',
    status: 'unavailable',
    description: 'Cozy farm management game',
    coverImage: 'https://images.unsplash.com/photo-1500382017468-9049fed747ef?w=200&h=300&fit=crop&crop=center',
    variants: [
      {
        id: 'IMPORT008-1',
        name: 'Standard Edition',
        code: 'FARM_STORY_008_STD',
        price: 22.99,
        onHand: 0,
        type: 'default',
        status: 'unavailable',
        description: 'Standard game edition'
      }
    ]
  },
  {
    id: 'IMPORT009',
    name: 'Metro Architect',
    code: 'CITY_BUILDER_009',
    price: 39.99,
    category: 'Strategy',
    platform: 'PC',
    status: 'available',
    description: 'City building simulation game',
    coverImage: 'https://images.unsplash.com/photo-1480714378408-67cf0d13bc1f?w=200&h=300&fit=crop&crop=center',
    variants: [
      {
        id: 'IMPORT009-1',
        name: 'Standard Edition',
        code: 'CITY_BUILDER_009_STD',
        price: 39.99,
        onHand: 140,
        type: 'default',
        status: 'available',
        description: 'Standard game edition'
      },
      {
        id: 'IMPORT009-2',
        name: 'Mayor Edition',
        code: 'CITY_BUILDER_009_MAY',
        price: 59.99,
        onHand: 70,
        type: 'special',
        status: 'available',
        description: 'Includes additional buildings and policies'
      }
    ]
  },
  {
    id: 'IMPORT010',
    name: 'Rhythm Master',
    code: 'MUSIC_BEAT_010',
    price: 12.99,
    category: 'Music',
    platform: 'PC',
    status: 'available',
    description: 'Rhythmic music game experience',
    coverImage: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=200&h=300&fit=crop&crop=center',
    variants: [
      {
        id: 'IMPORT010-1',
        name: 'Standard Edition',
        code: 'MUSIC_BEAT_010_STD',
        price: 12.99,
        onHand: 300,
        type: 'default',
        status: 'available',
        description: 'Standard game edition'
      },
      {
        id: 'IMPORT010-2',
        name: 'Musician Edition',
        code: 'MUSIC_BEAT_010_MUS',
        price: 19.99,
        onHand: 150,
        type: 'special',
        status: 'available',
        description: 'Includes additional tracks and editor'
      }
    ]
  }
]);

// 获取变体类型标签颜色
function getVariantTypeColor(type: ImportGameVariant['type']) {
  const colorMap = {
    default: 'default',
    special: 'primary',
    collector: 'warning',
    limited: 'error'
  } as const;
  return colorMap[type] || 'default';
}

// 获取变体类型文本
function getVariantTypeText(type: ImportGameVariant['type']) {
  const textMap = {
    default: 'Standard',
    special: 'Special',
    collector: 'Collector',
    limited: 'Limited'
  };
  return textMap[type] || 'Unknown';
}

// 展开所有行
function expandAll() {
  expandedRowKeys.value = availableGames.value.map(item => item.id);
}

// 收起所有行
function collapseAll() {
  expandedRowKeys.value = [];
}

// 筛选后的游戏数据
const filteredGames = computed(() => {
  return availableGames.value.filter(game => {
    const nameMatch = !searchForm.name || game.name.toLowerCase().includes(searchForm.name.toLowerCase());
    const codeMatch = !searchForm.code || game.code.toLowerCase().includes(searchForm.code.toLowerCase());
    const priceMatch = game.price >= searchForm.priceRange[0] && game.price <= searchForm.priceRange[1];
    const categoryMatch = !searchForm.category || game.category === searchForm.category;
    const platformMatch = !searchForm.platform || game.platform === searchForm.platform;

    return nameMatch && codeMatch && priceMatch && categoryMatch && platformMatch;
  });
});

// 创建扁平化的表格数据（包含游戏和变体）
const flatTableData = computed(() => {
  const result: Array<ImportGameData | (ImportGameVariant & { isVariant: true; parentId: string })> = [];

  filteredGames.value.forEach(game => {
    result.push(game);

    if (expandedRowKeys.value.includes(game.id)) {
      game.variants.forEach(variant => {
        result.push({
          ...variant,
          isVariant: true,
          parentId: game.id
        });
      });
    }
  });

  return result;
});

// 表格列定义
const columns: DataTableColumns<ImportGameData | (ImportGameVariant & { isVariant: true; parentId: string })> = [
  {
    title: '',
    key: 'expand',
    width: 50,
    render: row => {
      if ('isVariant' in row && row.isVariant) {
        return null;
      }

      const game = row as ImportGameData;
      const isExpanded = expandedRowKeys.value.includes(game.id);

      return h(
        NButton,
        {
          size: 'small',
          quaternary: true,
          onClick: () => {
            if (isExpanded) {
              expandedRowKeys.value = expandedRowKeys.value.filter(key => key !== game.id);
            } else {
              expandedRowKeys.value.push(game.id);
            }
          }
        },
        {
          icon: () =>
            h(SvgIcon, {
              icon: isExpanded ? 'mdi:chevron-down' : 'mdi:chevron-right',
              class: 'text-14px'
            })
        }
      );
    }
  },
  {
    type: 'selection',
    disabled: row => row.status === 'unavailable',
    multiple: true
  },
  {
    title: () => $t('page.game.coverImage'),
    key: 'coverImage',
    width: 100,
    render: row => {
      if ('isVariant' in row && row.isVariant) {
        return h('span', { class: 'text-gray-400 text-sm pl-6' }, '-');
      }

      const game = row as ImportGameData;
      return h(
        'div',
        { class: 'flex justify-center' },
        h('img', {
          src: game.coverImage,
          alt: game.name,
          class: 'w-12 h-16 object-cover rounded-md shadow-sm border border-gray-200 dark:border-gray-600',
          style: 'min-width: 48px; min-height: 64px;',
          onError: (e: Event) => {
            // 图片加载失败时显示默认图片
            const target = e.target as HTMLImageElement;
            target.src =
              'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA0OCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQ4IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNiAyNEgyMFYyOEgxNlYyNFpNMjQgMjRIMjhWMjhIMjRWMjRaTTMyIDI0SDM2VjI4SDMyVjI0Wk0xNiAzMkgyMFYzNkgxNlYzMlpNMjQgMzJIMjhWMzZIMjRWMzJaTTMyIDMySDM2VjM2SDMyVjMyWiIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K';
          }
        })
      );
    }
  },
  {
    title: () => $t('page.game.gameName'),
    key: 'name',
    width: 160,
    render: row => {
      if ('isVariant' in row && row.isVariant) {
        const variant = row as ImportGameVariant & { isVariant: true };
        return h('div', { class: 'flex items-center gap-2 pl-6' }, [
          h(
            NTag,
            {
              size: 'small',
              type: getVariantTypeColor(variant.type)
            },
            { default: () => getVariantTypeText(variant.type) }
          ),
          h('span', { class: 'text-gray-600' }, variant.name)
        ]);
      }

      const game = row as ImportGameData;
      return h('div', { class: 'flex items-center gap-2 font-medium' }, [
        h(SvgIcon, { icon: 'mdi:gamepad-variant', class: 'text-primary text-16px' }),
        h('span', game.name)
      ]);
    }
  },
  {
    title: () => $t('page.game.gameCode'),
    key: 'code',
    width: 140,
    render: row => {
      const isVariant = 'isVariant' in row && row.isVariant;
      return h(
        'span',
        {
          class: isVariant ? 'text-gray-500 pl-6 font-mono text-sm' : 'font-mono'
        },
        row.code
      );
    }
  },
  {
    title: () => $t('page.game.onHand'),
    key: 'onHand',
    width: 100,
    render: row => {
      if ('isVariant' in row && row.isVariant) {
        const variant = row as ImportGameVariant & { isVariant: true };
        let color: 'success' | 'warning' | 'error' = 'error';
        if (variant.onHand > 100) {
          color = 'success';
        } else if (variant.onHand > 50) {
          color = 'warning';
        }
        return h(NTag, { type: color, size: 'small' }, { default: () => variant.onHand.toString() });
      }
      return h('span', { class: 'text-gray-400' }, '-');
    }
  },
  {
    title: () => $t('page.game.price'),
    key: 'price',
    width: 100,
    render: row => {
      const isVariant = 'isVariant' in row && row.isVariant;
      return h(
        'span',
        {
          class: isVariant ? 'text-sm font-medium text-primary' : 'font-medium'
        },
        `$${row.price}`
      );
    }
  },
  {
    title: () => $t('page.game.import.category'),
    key: 'category',
    width: 100,
    render: row => {
      if ('isVariant' in row && row.isVariant) {
        return h('span', { class: 'text-gray-400 text-sm pl-6' }, '-');
      }
      const game = row as ImportGameData;
      return h('span', game.category);
    }
  },
  {
    title: () => $t('page.game.import.platform'),
    key: 'platform',
    width: 120,
    render: row => {
      if ('isVariant' in row && row.isVariant) {
        return h('span', { class: 'text-gray-400 text-sm pl-6' }, '-');
      }
      const game = row as ImportGameData;
      return h('span', game.platform);
    }
  },
  {
    title: () => $t('common.status'),
    key: 'status',
    width: 100,
    render: row => {
      const isVariant = 'isVariant' in row && row.isVariant;
      return h(
        NTag,
        {
          type: row.status === 'available' ? 'success' : 'error',
          size: isVariant ? 'small' : 'medium'
        },
        {
          default: () =>
            row.status === 'available' ? $t('page.game.import.available') : $t('page.game.import.unavailable')
        }
      );
    }
  },
  {
    title: () => $t('page.game.import.description'),
    key: 'description',
    ellipsis: {
      tooltip: true
    },
    render: row => {
      const isVariant = 'isVariant' in row && row.isVariant;
      return h(
        'span',
        {
          class: isVariant ? 'text-gray-500 text-sm pl-6' : ''
        },
        row.description || '-'
      );
    }
  }
];

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  itemCount: computed(() => flatTableData.value.length),
  onChange: (page: number) => {
    pagination.page = page;
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
  }
});

// 分类选项
const categoryOptions = [
  { label: 'RPG', value: 'RPG' },
  { label: 'Strategy', value: 'Strategy' },
  { label: 'Racing', value: 'Racing' },
  { label: 'Simulation', value: 'Simulation' },
  { label: 'Survival', value: 'Survival' },
  { label: 'Puzzle', value: 'Puzzle' },
  { label: 'Shooter', value: 'Shooter' },
  { label: 'Music', value: 'Music' }
];

// 平台选项
const platformOptions = [
  { label: 'PC', value: 'PC' },
  { label: 'PlayStation', value: 'PlayStation' },
  { label: 'Xbox', value: 'Xbox' },
  { label: 'Nintendo Switch', value: 'Nintendo Switch' }
];

// 搜索功能
function handleSearch() {
  loading.value = true;
  setTimeout(() => {
    loading.value = false;
    pagination.page = 1;
  }, 500);
}

// 重置搜索
function handleReset() {
  Object.assign(searchForm, {
    name: '',
    code: '',
    priceRange: [0, 200],
    category: '',
    platform: ''
  });
  checkedRowKeys.value = [];
}

// 全选/取消全选
function handleSelectAll() {
  const availableIds: string[] = [];

  // 收集所有可用的游戏和变体ID
  filteredGames.value.forEach(game => {
    if (game.status === 'available') {
      availableIds.push(game.id);
    }
    game.variants.forEach(variant => {
      if (variant.status === 'available') {
        availableIds.push(variant.id);
      }
    });
  });

  if (checkedRowKeys.value.length === availableIds.length) {
    checkedRowKeys.value = [];
  } else {
    checkedRowKeys.value = availableIds;
  }
}

// 导入选中的游戏
async function handleImport() {
  if (checkedRowKeys.value.length === 0) {
    window.$message?.warning($t('page.game.import.noGamesSelected'));
    return;
  }

  importLoading.value = true;

  try {
    const selectedItems: any[] = [];

    // 收集选中的游戏和变体
    filteredGames.value.forEach(game => {
      const gameSelected = checkedRowKeys.value.includes(game.id);
      const selectedVariants = game.variants.filter(variant => checkedRowKeys.value.includes(variant.id));

      if (gameSelected || selectedVariants.length > 0) {
        selectedItems.push({
          ...game,
          variants: gameSelected ? game.variants : selectedVariants
        });
      }
    });

    // 模拟导入过程，显示进度
    window.$message?.loading($t('page.game.import.importing_'), { duration: 0 });

    // 模拟批量导入，每个游戏间隔一点时间
    for (let i = 0; i < selectedItems.length; i++) {
      await new Promise(resolve => setTimeout(resolve, 300));
      // 这里可以添加单个游戏导入的逻辑
    }

    // 清除加载消息
    window.$message?.destroyAll();

    emit('imported', selectedItems);
    window.$message?.success($t('page.game.import.importSuccess', { count: selectedItems.length }));
    closeModal();
  } catch (error) {
    window.$message?.destroyAll();
    window.$message?.error($t('page.game.import.importFailed'));
  } finally {
    importLoading.value = false;
  }
}

// 关闭模态框
function closeModal() {
  emit('update:visible', false);
}

// 监听模态框可见性变化
watch(
  () => props.visible,
  visible => {
    if (visible) {
      // 模态框打开时，默认展开所有游戏
      expandAll();
    } else {
      // 重置状态
      handleReset();
    }
  }
);
</script>

<template>
  <NModal
    :show="visible"
    preset="dialog"
    :title="$t('page.game.import.title')"
    :positive-text="importLoading ? $t('page.game.import.importing') : $t('page.game.import.importSelected')"
    :negative-text="$t('common.cancel')"
    :loading="importLoading"
    :positive-button-props="{ disabled: checkedRowKeys.length === 0 }"
    class="fixed left-0 right-0 min-w-1200px"
    :class="modalClass"
    @positive-click="handleImport"
    @negative-click="closeModal"
    @update:show="emit('update:visible', $event)"
  >
    <div class="space-y-4">
      <!-- 紧凑搜索区域 -->
      <div class="rounded-lg bg-gray-50 p-4 dark:bg-gray-800">
        <!-- 搜索字段和按钮 -->
        <div :class="isMobile ? 'space-y-3' : 'flex flex-wrap gap-4 items-end'">
          <div class="min-w-40 flex flex-col flex-1 gap-1">
            <span class="text-sm text-gray-600 font-medium dark:text-gray-300">{{ $t('page.game.gameName') }}</span>
            <NInput
              v-model:value="searchForm.name"
              :placeholder="$t('page.game.import.searchPlaceholder')"
              size="small"
              clearable
            >
              <template #prefix>
                <SvgIcon icon="mdi:gamepad-variant" class="text-gray-400" />
              </template>
            </NInput>
          </div>

          <div class="min-w-32 flex flex-col flex-1 gap-1">
            <span class="text-sm text-gray-600 font-medium dark:text-gray-300">{{ $t('page.game.gameCode') }}</span>
            <NInput
              v-model:value="searchForm.code"
              :placeholder="$t('page.game.import.codeSearchPlaceholder')"
              size="small"
              clearable
            >
              <template #prefix>
                <SvgIcon icon="mdi:barcode" class="text-gray-400" />
              </template>
            </NInput>
          </div>

          <div class="min-w-40 flex flex-col flex-1 gap-1">
            <span class="text-sm text-gray-600 font-medium dark:text-gray-300">{{ $t('page.game.priceRange') }}</span>
            <div class="px-2">
              <NSlider
                v-model:value="searchForm.priceRange"
                range
                :min="0"
                :max="200"
                :step="1"
                :format-tooltip="(value: number) => `$${value}`"
                size="small"
              />
            </div>
          </div>

          <div class="min-w-24 flex flex-col flex-1 gap-1">
            <span class="text-sm text-gray-600 font-medium dark:text-gray-300">
              {{ $t('page.game.import.category') }}
            </span>
            <NSelect
              v-model:value="searchForm.category"
              :placeholder="$t('page.game.import.selectCategory')"
              :options="categoryOptions"
              size="small"
              clearable
            />
          </div>

          <div class="min-w-28 flex flex-col flex-1 gap-1">
            <span class="text-sm text-gray-600 font-medium dark:text-gray-300">
              {{ $t('page.game.import.platform') }}
            </span>
            <NSelect
              v-model:value="searchForm.platform"
              :placeholder="$t('page.game.import.selectPlatform')"
              :options="platformOptions"
              size="small"
              clearable
            />
          </div>

          <!-- 操作按钮 -->
          <div class="flex gap-2" :class="isMobile ? 'w-full justify-center' : 'flex-shrink-0'">
            <NButton size="small" @click="handleReset">
              {{ $t('common.reset') }}
            </NButton>
            <NButton type="primary" size="small" :loading="loading" @click="handleSearch">
              <template #icon>
                <SvgIcon icon="mdi:magnify" />
              </template>
              {{ $t('common.search') }}
            </NButton>
          </div>
        </div>
      </div>

      <!-- 统计信息和控制按钮 -->
      <div class="flex items-center justify-between rounded-lg bg-blue-50 p-3 dark:bg-blue-900/20">
        <div class="flex items-center gap-4 text-sm">
          <span class="text-gray-600 dark:text-gray-300">
            {{ $t('page.game.totalGames') }}: {{ filteredGames.length }} {{ $t('common.unit.games') }}
          </span>
          <span class="text-green-600 dark:text-green-400">
            {{ $t('page.game.import.availableGames') }}:
            {{ filteredGames.filter(g => g.status === 'available').length }} {{ $t('common.unit.games') }}
          </span>
          <span class="text-red-600 dark:text-red-400">
            {{ $t('page.game.import.unavailable') }}:
            {{ filteredGames.filter(g => g.status === 'unavailable').length }} {{ $t('common.unit.games') }}
          </span>
          <span class="text-blue-600 font-medium dark:text-blue-400">
            {{ $t('page.game.import.selectedCount', { count: checkedRowKeys.length }) }}
          </span>
        </div>

        <div class="flex items-center gap-2">
          <!-- 展开/收起控制 -->
          <NButton size="small" @click="expandAll">
            <template #icon>
              <SvgIcon icon="mdi:expand-all" />
            </template>
            {{ $t('page.game.import.expandAll') }}
          </NButton>
          <NButton size="small" @click="collapseAll">
            <template #icon>
              <SvgIcon icon="mdi:collapse-all" />
            </template>
            {{ $t('page.game.import.collapseAll') }}
          </NButton>

          <!-- 全选按钮 -->
          <NButton size="small" type="primary" @click="handleSelectAll">
            {{ checkedRowKeys.length > 0 ? $t('page.game.import.deselectAll') : $t('page.game.import.selectAll') }}
          </NButton>
        </div>
      </div>

      <!-- 游戏列表表格 -->
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="flatTableData"
        :loading="loading"
        :pagination="pagination"
        :bordered="false"
        size="small"
        flex-height
        style="height: 400px"
        :row-key="row => row.id"
        :row-class-name="
          row => {
            if ('isVariant' in row && row.isVariant) {
              return 'variant-row';
            }
            return 'game-row';
          }
        "
      />
    </div>
  </NModal>
</template>

<style scoped>
:deep(.game-row) {
  background-color: var(--n-color);
  font-weight: 500;
}

:deep(.variant-row) {
  background-color: var(--n-color-hover);
  border-left: 3px solid var(--n-color-target);
}

:deep(.variant-row:hover) {
  background-color: var(--n-color-hover) !important;
}

:deep(.game-row:hover) {
  background-color: var(--n-color-hover);
}

/* 为变体行添加轻微的缩进效果 */
:deep(.variant-row .n-data-table-td) {
  position: relative;
}

:deep(.variant-row .n-data-table-td:first-child) {
  padding-left: 24px;
}

:deep(.variant-row .n-data-table-td:first-child::before) {
  content: '';
  position: absolute;
  left: 12px;
  top: 50%;
  width: 8px;
  height: 1px;
  background-color: var(--n-border-color);
  transform: translateY(-50%);
}
</style>
