<script setup lang="tsx">
import { computed, h, reactive, ref } from 'vue';
import type { DataTableColumns, DataTableRowKey } from 'naive-ui';
import { NButton, NDataTable, NIcon, NInput, NSelect, NSlider, NSpace, NTag, NTooltip } from 'naive-ui';
import { useAppStore } from '@/store/modules/app';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';
import ButtonIcon from '@/components/custom/button-icon.vue';
import GameImportModal from './game-import-modal.vue';
import GameStatsDrawer from './game-stats-drawer.vue';
import GameOrdersDrawer from './game-orders-drawer.vue';

defineOptions({
  name: 'GameList'
});

interface GameVariant {
  id: string;
  name: string;
  code: string;
  onHand: number;
  price: number;
  enabled: boolean;
  type: 'default' | 'special' | 'collector' | 'limited';
  description?: string;
}

interface GameData {
  id: string;
  coverImage: string;
  name: string;
  code: string;
  multiLangName: Record<string, string>;
  languages: string[];
  onHand: number;
  enabled: boolean;
  price: number;
  sales: number;
  variants: GameVariant[];
  isExpanded?: boolean;
}

const searchForm = reactive({
  name: '',
  code: '',
  priceRange: [0, 1000] as [number, number],
  enabled: ''
});

const loading = ref(false);

const appStore = useAppStore();
const isMobile = computed(() => appStore.isMobile);

// 导入模态框状态
const importModalVisible = ref(false);

// 游戏统计抽屉状态
const gameStatsDrawerVisible = ref(false);
const selectedGameForStats = ref<GameData | null>(null);

// 销售订单详情抽屉状态
const gameOrdersDrawerVisible = ref(false);
const selectedGameForOrders = ref<GameData | null>(null);
const selectedVariantForOrders = ref<GameVariant | null>(null);

// 展开的行键
const expandedRowKeys = ref<DataTableRowKey[]>([]);

const tableData = ref<GameData[]>([
  {
    id: '1',
    coverImage: 'https://images.unsplash.com/photo-1552820728-8b83bb6b773f?w=200&h=300&fit=crop&crop=center',
    name: 'Mystic Adventure',
    code: 'GAME001',
    multiLangName: {
      'zh-CN': 'Mystic Adventure',
      'en-US': 'Mystic Adventure'
    },
    languages: ['EN', 'JP', 'KR'],
    onHand: 500,
    enabled: true,
    price: 29.99,
    sales: 15420,
    variants: [
      {
        id: '1-1',
        name: 'Standard Edition',
        code: 'GAME001-STD',
        onHand: 300,
        price: 29.99,
        enabled: true,
        type: 'default',
        description: 'Standard game edition'
      },
      {
        id: '1-2',
        name: 'Deluxe Edition',
        code: 'GAME001-DLX',
        onHand: 150,
        price: 49.99,
        enabled: true,
        type: 'special',
        description: 'Deluxe edition with additional content'
      },
      {
        id: '1-3',
        name: "Collector's Edition",
        code: 'GAME001-COL',
        onHand: 50,
        price: 99.99,
        enabled: true,
        type: 'collector',
        description: "Limited collector's edition with physical items"
      }
    ]
  },
  {
    id: '2',
    coverImage: 'https://images.unsplash.com/photo-1511512578047-dfb367046420?w=200&h=300&fit=crop&crop=center',
    name: 'Space Odyssey',
    code: 'GAME002',
    multiLangName: {
      'zh-CN': 'Space Odyssey',
      'en-US': 'Space Odyssey'
    },
    languages: ['EN', 'FR'],
    onHand: 250,
    enabled: true,
    price: 19.99,
    sales: 8750,
    variants: [
      {
        id: '2-1',
        name: 'Standard Edition',
        code: 'GAME002-STD',
        onHand: 200,
        price: 19.99,
        enabled: true,
        type: 'default',
        description: 'Standard game edition'
      },
      {
        id: '2-2',
        name: 'Digital Deluxe',
        code: 'GAME002-DIG',
        onHand: 50,
        price: 29.99,
        enabled: false,
        type: 'special',
        description: 'Digital deluxe edition with DLC'
      }
    ]
  }
]);

// 获取变体类型标签颜色
function getVariantTypeColor(type: GameVariant['type']) {
  const colorMap = {
    default: 'default',
    special: 'primary',
    collector: 'warning',
    limited: 'error'
  } as const;
  return colorMap[type] || 'default';
}

// 获取变体类型文本
function getVariantTypeText(type: GameVariant['type']) {
  const textMap = {
    default: 'Standard',
    special: 'Special',
    collector: 'Collector',
    limited: 'Limited'
  };
  return textMap[type] || 'Unknown';
}

// 展开所有行
function expandAll() {
  expandedRowKeys.value = tableData.value.map(item => item.id);
}

// 收起所有行
function collapseAll() {
  expandedRowKeys.value = [];
}

// 创建扁平化的表格数据（包含游戏和变体）
const flatTableData = computed(() => {
  const result: Array<GameData | (GameVariant & { isVariant: true; parentId: string })> = [];

  tableData.value.forEach(game => {
    result.push(game);

    if (expandedRowKeys.value.includes(game.id)) {
      game.variants.forEach(variant => {
        result.push({
          ...variant,
          isVariant: true,
          parentId: game.id
        });
      });
    }
  });

  return result;
});

const columns: DataTableColumns<GameData | (GameVariant & { isVariant: true; parentId: string })> = [
  {
    title: '',
    key: 'expand',
    width: 50,
    render: row => {
      if ('isVariant' in row && row.isVariant) {
        return null;
      }

      const game = row as GameData;
      const isExpanded = expandedRowKeys.value.includes(game.id);

      return h(
        NButton,
        {
          size: 'small',
          quaternary: true,
          onClick: () => {
            if (isExpanded) {
              expandedRowKeys.value = expandedRowKeys.value.filter(key => key !== game.id);
            } else {
              expandedRowKeys.value.push(game.id);
            }
          }
        },
        {
          icon: () =>
            h(SvgIcon, {
              icon: isExpanded ? 'mdi:chevron-down' : 'mdi:chevron-right',
              class: 'text-14px'
            })
        }
      );
    }
  },
  {
    title: $t('page.game.coverImage'),
    key: 'coverImage',
    width: 120,
    render: row => {
      if ('isVariant' in row && row.isVariant) {
        return h('span', { class: 'text-gray-400 text-sm pl-6' }, '-');
      }

      const game = row as GameData;
      return h(
        'div',
        { class: 'flex justify-center' },
        h('img', {
          src: game.coverImage,
          alt: game.name,
          class: 'w-16 h-20 object-cover rounded-md shadow-sm border border-gray-200 dark:border-gray-600',
          style: 'min-width: 64px; min-height: 80px;',
          onError: (e: Event) => {
            // 图片加载失败时显示默认图片
            const target = e.target as HTMLImageElement;
            target.src =
              'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA2NCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMCAzMkgyNFYzNkgyMFYzMlpNMjggMzJIMzJWMzZIMjhWMzJaTTM2IDMySDQwVjM2SDM2VjMyWk0yMCA0MEgyNFY0NEgyMFY0MFpNMjggNDBIMzJWNDRIMjhWNDBaTTM2IDQwSDQwVjQ0SDM2VjQwWiIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K';
          }
        })
      );
    }
  },
  {
    title: $t('page.game.name'),
    key: 'name',
    width: 200,
    render: row => {
      if ('isVariant' in row && row.isVariant) {
        const variant = row as GameVariant & { isVariant: true };
        return h('div', { class: 'flex items-center gap-2 pl-6' }, [
          h(
            NTag,
            {
              size: 'small',
              type: getVariantTypeColor(variant.type)
            },
            { default: () => getVariantTypeText(variant.type) }
          ),
          h('span', { class: 'text-gray-600' }, variant.name)
        ]);
      }

      const game = row as GameData;
      return h('div', { class: 'flex items-center gap-2 font-medium' }, [
        h(SvgIcon, { icon: 'mdi:gamepad-variant', class: 'text-primary text-16px' }),
        h('span', game.name)
      ]);
    }
  },
  {
    title: $t('page.game.code'),
    key: 'code',
    width: 140,
    render: row => {
      const isVariant = 'isVariant' in row && row.isVariant;
      return h(
        'span',
        {
          class: isVariant ? 'text-gray-500 pl-6 font-mono text-sm' : 'font-mono'
        },
        row.code
      );
    }
  },
  {
    title: $t('page.game.multiLangName'),
    key: 'multiLangName',
    width: 130,
    render: row => {
      if ('isVariant' in row && row.isVariant) {
        const variant = row as GameVariant & { isVariant: true };
        return variant.description
          ? h(
              NTooltip,
              {},
              {
                trigger: () => h('span', { class: 'text-gray-500 text-sm pl-6' }, 'Description'),
                default: () => variant.description
              }
            )
          : h('span', { class: 'text-gray-400 text-sm pl-6' }, '-');
      }

      const game = row as GameData;
      const langs = Object.keys(game.multiLangName);
      return h(
        NTooltip,
        {},
        {
          trigger: () => h('span', `${langs.length}${$t('page.game.languages')}`),
          default: () => {
            return langs.map(lang => h('div', { key: lang }, `${lang}: ${game.multiLangName[lang]}`));
          }
        }
      );
    }
  },
  {
    title: $t('page.game.onHand'),
    key: 'onHand',
    width: 120,
    render: row => {
      let color: 'success' | 'warning' | 'error' = 'error';
      if (row.onHand > 100) {
        color = 'success';
      } else if (row.onHand > 50) {
        color = 'warning';
      }

      const isVariant = 'isVariant' in row && row.isVariant;
      return h(
        NTag,
        {
          type: color,
          size: isVariant ? 'small' : 'medium'
        },
        { default: () => row.onHand.toString() }
      );
    }
  },
  {
    title: $t('page.game.enabled'),
    key: 'enabled',
    width: 100,
    render: row => {
      const isVariant = 'isVariant' in row && row.isVariant;
      return h(
        NTag,
        {
          type: row.enabled ? 'success' : 'error',
          size: isVariant ? 'small' : 'medium'
        },
        { default: () => (row.enabled ? $t('page.game.enable') : $t('page.game.disable')) }
      );
    }
  },
  // 去掉语言显示
  // {
  //   title: $t('page.game.supportedLanguages'),
  //   key: 'languages',
  //   width: 120,
  //   render: row => {
  //     if ('isVariant' in row && row.isVariant) {
  //       return h('span', { class: 'text-gray-400 text-sm pl-6' }, '-');
  //     }

  //     const game = row as GameData;
  //     return h(
  //       'div',
  //       { class: 'flex flex-wrap gap-1' },
  //       game.languages.map(lang =>
  //         h(
  //           NTag,
  //           {
  //             key: lang,
  //             size: 'small',
  //             type: 'info'
  //           },
  //           { default: () => lang }
  //         )
  //       )
  //     );
  //   }
  // },
  {
    title: $t('page.game.sales'),
    key: 'sales',
    width: 100,
    render: row => {
      if ('isVariant' in row && row.isVariant) {
        return h('span', { class: 'text-gray-400 text-sm pl-6' }, '-');
      }

      const game = row as GameData;
      const salesNum = game.sales;
      let color: 'success' | 'warning' | 'default' = 'default';

      if (salesNum > 10000) {
        color = 'success';
      } else if (salesNum > 5000) {
        color = 'warning';
      }

      // 格式化销量数字
      const formatSales = (num: number) => {
        if (num >= 10000) {
          return `${(num / 10000).toFixed(1)}M`;
        }
        return num.toLocaleString();
      };

      return h(NTag, { type: color, size: 'small' }, { default: () => formatSales(salesNum) });
    }
  },
  {
    title: $t('page.game.price'),
    key: 'price',
    width: 100,
    render: row => {
      const isVariant = 'isVariant' in row && row.isVariant;
      return h(
        'span',
        {
          class: isVariant ? 'text-sm font-medium text-primary' : 'font-medium'
        },
        `$${row.price}`
      );
    }
  },
  {
    title: $t('page.game.actions'),
    key: 'actions',
    width: 120,
    render: row => {
      const isVariant = 'isVariant' in row && row.isVariant;

      if (isVariant) {
        const variant = row as GameVariant & { isVariant: true; parentId: string };
        const parentGame = tableData.value.find(game => game.id === variant.parentId);

        // 变体行的操作按钮
        return h(
          NSpace,
          { size: 6 },
          {
            default: () => [
              h(ButtonIcon, {
                icon: 'mdi:chart-box',
                tooltipContent: $t('page.game.stats'),
                class: 'text-success',
                size: 'small',
                onClick: () => handleVariantStats(parentGame!, variant)
              }),
              h(ButtonIcon, {
                icon: 'mdi:receipt-text',
                tooltipContent: $t('page.game.orders.title' as any),
                class: 'text-primary',
                size: 'small',
                onClick: () => handleVariantOrders(parentGame!, variant)
              })
            ]
          }
        );
      }

      // 游戏行的操作按钮
      return h(
        NSpace,
        { size: 8 },
        {
          default: () => [
            h(ButtonIcon, {
              icon: 'mdi:chart-box',
              tooltipContent: $t('page.game.stats'),
              class: 'text-success',
              onClick: () => handleGameStats(row as GameData)
            })
            /* 隐藏掉查看游戏订单列表功能  2025.6.12
            h(ButtonIcon, {
              icon: 'mdi:receipt-text',
              tooltipContent: $t('page.game.orders.title' as any),
              class: 'text-primary',
              onClick: () => handleGameOrders(row as GameData)
            })
            */
          ]
        }
      );
    }
  }
];

const pagination = reactive({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  onChange: (page: number) => {
    pagination.page = page;
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
  }
});

function handleSearch() {
  loading.value = true;
  setTimeout(() => {
    loading.value = false;
  }, 1000);
}

function handleReset() {
  Object.assign(searchForm, {
    name: '',
    code: '',
    priceRange: [0, 1000],
    enabled: ''
  });
}

function handleImportGame() {
  importModalVisible.value = true;
}

function handleGameImported(games: any[]) {
  // 将导入的游戏添加到现有列表中
  games.forEach(game => {
    const newGame: GameData = {
      id: String(Date.now() + Math.random()),
      coverImage: `https://picsum.photos/200/300?random=${Math.floor(Math.random() * 1000)}`,
      name: game.name,
      code: game.code,
      multiLangName: {
        'zh-CN': game.name,
        'en-US': game.name
      },
      languages: ['EN', 'JP'],
      onHand: Math.floor(Math.random() * 500) + 100,
      enabled: true,
      price: game.price,
      sales: Math.floor(Math.random() * 20000) + 1000,
      variants: [
        {
          id: `${Date.now()}-${Math.random()}-1`,
          name: 'Standard Edition',
          code: `${game.code}-STD`,
          onHand: Math.floor(Math.random() * 300) + 50,
          price: game.price,
          enabled: true,
          type: 'default',
          description: 'Standard game edition'
        }
      ]
    };
    tableData.value.unshift(newGame);
  });

  window.$message?.success(`Successfully imported ${games.length} games to the game list`);
}

function handleGameStats(game: GameData) {
  selectedGameForStats.value = game;
  gameStatsDrawerVisible.value = true;
}

function handleVariantStats(game: GameData, _variant: GameVariant) {
  // 对于变体统计，我们传递游戏数据，但可以在统计组件中区分是否为变体
  selectedGameForStats.value = game;
  gameStatsDrawerVisible.value = true;
}

function handleGameOrders(game: GameData) {
  selectedGameForOrders.value = game;
  selectedVariantForOrders.value = null;
  gameOrdersDrawerVisible.value = true;
}

function handleVariantOrders(game: GameData, variant: GameVariant) {
  selectedGameForOrders.value = game;
  selectedVariantForOrders.value = variant;
  gameOrdersDrawerVisible.value = true;
}

function handleRefresh() {
  loading.value = true;
  // 模拟刷新数据
  setTimeout(() => {
    loading.value = false;
    // 这里可以调用实际的数据刷新API
  }, 1000);
}
</script>

<template>
  <div class="space-y-3">
    <!-- 紧凑筛选器 -->
    <div class="rounded-lg bg-gray-50 p-4 dark:bg-gray-800">
      <!-- 搜索字段和按钮 -->
      <div :class="isMobile ? 'space-y-3' : 'flex flex-wrap gap-4 items-end'">
        <div class="min-w-40 flex flex-col flex-1 gap-1">
          <span class="text-sm text-gray-600 font-medium dark:text-gray-300">{{ $t('page.game.gameName') }}</span>
          <NInput
            v-model:value="searchForm.name"
            :placeholder="$t('page.game.gameNamePlaceholder')"
            size="small"
            clearable
          >
            <template #prefix>
              <SvgIcon icon="mdi:gamepad-variant" class="text-gray-400" />
            </template>
          </NInput>
        </div>

        <div class="min-w-32 flex flex-col flex-1 gap-1">
          <span class="text-sm text-gray-600 font-medium dark:text-gray-300">{{ $t('page.game.gameCode') }}</span>
          <NInput
            v-model:value="searchForm.code"
            :placeholder="$t('page.game.gameCodePlaceholder')"
            size="small"
            clearable
          >
            <template #prefix>
              <SvgIcon icon="mdi:barcode" class="text-gray-400" />
            </template>
          </NInput>
        </div>

        <div class="min-w-48 flex flex-col flex-1 gap-1">
          <span class="text-sm text-gray-600 font-medium dark:text-gray-300">{{ $t('page.game.priceRange') }}</span>
          <div class="px-2">
            <NSlider
              v-model:value="searchForm.priceRange"
              range
              :min="0"
              :max="1000"
              :step="1"
              :format-tooltip="(value: number) => `$${value}`"
              size="small"
            />
          </div>
        </div>

        <div class="min-w-28 flex flex-col flex-1 gap-1">
          <span class="text-sm text-gray-600 font-medium dark:text-gray-300">{{ $t('common.status') }}</span>
          <NSelect
            v-model:value="searchForm.enabled"
            :placeholder="$t('page.game.statusPlaceholder')"
            :options="[
              { label: $t('page.game.enable'), value: 'true' },
              { label: $t('page.game.disable'), value: 'false' }
            ]"
            size="small"
            clearable
          />
        </div>

        <!-- 操作按钮 -->
        <div class="flex gap-2" :class="isMobile ? 'w-full justify-center' : 'flex-shrink-0'">
          <NButton size="small" @click="handleReset">
            {{ $t('common.reset') }}
          </NButton>
          <NButton type="primary" size="small" :loading="loading" @click="handleSearch">
            <template #icon>
              <SvgIcon icon="mdi:magnify" />
            </template>
            {{ $t('common.search') }}
          </NButton>
        </div>
      </div>
    </div>

    <!-- 操作栏 -->
    <div :class="isMobile ? 'space-y-3' : 'flex items-center justify-between'">
      <div class="flex items-center gap-2">
        <!--  隐藏 导入游戏按钮 -->
        <!--
 <NButton type="primary" @click="handleImportGame">
          <template #icon>
            <SvgIcon icon="mdi:import" />
          </template>
          {{ $t('page.game.importGame') }}
        </NButton>
-->
        <NButton :loading="loading" @click="handleRefresh">
          <template #icon>
            <SvgIcon icon="mdi:refresh" />
          </template>
          {{ $t('common.refresh') }}
        </NButton>
      </div>

      <!-- 展开/收起控制 -->
      <div class="flex items-center gap-2" :class="isMobile ? 'justify-center' : ''">
        <NButton size="small" @click="expandAll">
          <template #icon>
            <SvgIcon icon="mdi:expand-all" />
          </template>
          {{ $t('page.game.import.expandAll') }}
        </NButton>
        <NButton size="small" @click="collapseAll">
          <template #icon>
            <SvgIcon icon="mdi:collapse-all" />
          </template>
          {{ $t('page.game.import.collapseAll') }}
        </NButton>
      </div>
    </div>

    <!-- 数据表格 -->
    <NDataTable
      :columns="columns"
      :data="flatTableData"
      :loading="loading"
      :pagination="pagination"
      :bordered="false"
      size="small"
      flex-height
      class="h-500px"
      scroll-x="1200px"
      :row-class-name="
        row => {
          if ('isVariant' in row && row.isVariant) {
            return 'variant-row';
          }
          return 'game-row';
        }
      "
    />

    <!-- 游戏导入模态框 -->
    <GameImportModal v-model:visible="importModalVisible" @imported="handleGameImported" />

    <!-- 游戏统计抽屉 -->
    <GameStatsDrawer v-model:visible="gameStatsDrawerVisible" :game="selectedGameForStats" />

    <!-- 销售订单详情抽屉 -->
    <GameOrdersDrawer
      v-model:visible="gameOrdersDrawerVisible"
      :game="selectedGameForOrders"
      :variant="selectedVariantForOrders"
    />
  </div>
</template>

<style scoped>
:deep(.game-row) {
  background-color: var(--n-color);
  font-weight: 500;
}

:deep(.variant-row) {
  background-color: var(--n-color-hover);
  border-left: 3px solid var(--n-color-target);
}

:deep(.variant-row:hover) {
  background-color: var(--n-color-hover) !important;
}

:deep(.game-row:hover) {
  background-color: var(--n-color-hover);
}

/* 为变体行添加轻微的缩进效果 */
:deep(.variant-row .n-data-table-td) {
  position: relative;
}

:deep(.variant-row .n-data-table-td:first-child) {
  padding-left: 24px;
}

:deep(.variant-row .n-data-table-td:first-child::before) {
  content: '';
  position: absolute;
  left: 12px;
  top: 50%;
  width: 8px;
  height: 1px;
  background-color: var(--n-border-color);
  transform: translateY(-50%);
}
</style>
