<script setup lang="tsx">
import { computed } from 'vue';
import { useAppStore } from '@/store/modules/app';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';
import GameList from './modules/game-list.vue';
import GameStats from './modules/game-stats.vue';

defineOptions({
  name: 'Game'
});

const appStore = useAppStore();

const gap = computed(() => (appStore.isMobile ? 0 : 16));
</script>

<template>
  <NSpace vertical :size="16">
    <NCard :bordered="false" class="card-wrapper">
      <template #header>
        <div class="flex items-center gap-2">
          <SvgIcon icon="mdi:gamepad-variant" class="text-20px" />
          <span class="text-18px font-semibold">{{ $t('route.game') }}</span>
        </div>
      </template>
      <GameStats />
    </NCard>

    <NCard :bordered="false" class="card-wrapper">
      <GameList />
    </NCard>
  </NSpace>
</template>

<style scoped>
.card-wrapper {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
