<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useMessage } from 'naive-ui';
import { fetchInvitationInfo, fetchAcceptInvitation } from '@/service/api/invitation';
import { useAuthStore } from '@/store/modules/auth';
import { useAppStore } from '@/store/modules/app';
import { $t } from '@/locales';
import SystemLogo from '@/components/common/system-logo.vue';
// @ts-ignore
// import {GridArrayBg} from "./modules/GridArrayBg.module.js"
import { RandomCubesBg } from "./modules/RandomCubesBg.module.js"

defineOptions({
  name: 'JoinPage'
});

const route = useRoute();
const router = useRouter();
const message = useMessage();
const authStore = useAuthStore();
const appStore = useAppStore();

// State management
const loading = ref(false);
const accepting = ref(false);
const invitationInfo = ref<Api.Organization.InvitationInfo | null>(null);
const error = ref<string>('');

// Computed properties
const invitationId = computed(() => route.query.id as string);
const isLoggedIn = computed(() => authStore.isLogin);
const canAcceptInvitation = computed(() => {
  if (!invitationInfo.value) return false;
  return invitationInfo.value.is_valid && !invitationInfo.value.is_expired && !invitationInfo.value.has_reached_usage_limit;
});

// Fetch invitation info
async function loadInvitationInfo() {
  if (!invitationId.value) {
    error.value = $t('invitation.invalidLink');
    return;
  }

  loading.value = true;
  error.value = '';

  try {
    const { data } = await fetchInvitationInfo(invitationId.value);
    if (data && data.success) {
      invitationInfo.value = data.data || null;
    } else {
      error.value = data?.message || $t('invitation.joinFailed');
    }
  } catch (err: any) {
    error.value = err?.response?.data?.message || err?.message || $t('invitation.joinFailed');
  } finally {
    loading.value = false;
  }
}

// Accept invitation
async function acceptInvitation() {
  if (!invitationId.value || !canAcceptInvitation.value) return;

  accepting.value = true;

  try {
    const { data } = await fetchAcceptInvitation(invitationId.value);
    if (data && data.success) {
      message.success($t('invitation.joinSuccess'));
      // Refresh user info
      await authStore.initUserInfo();
      // Redirect to home or organization page
      router.push('/');
    } else {
      message.error(data?.message || $t('invitation.joinFailed'));
    }
  } catch (err: any) {
    const errorMsg = err?.response?.data?.message || err?.message || $t('invitation.joinFailed');
    message.error(errorMsg);
  } finally {
    accepting.value = false;
  }
}

// Go to register page
function goToRegister() {
  const query: Record<string, string> = {};

  if (invitationId.value) {
    query.invitation_id = invitationId.value;
  }

  if (invitationInfo.value?.email_restriction) {
    query.email = invitationInfo.value.email_restriction;
  }

  router.push({
    path: '/login/register',
    query
  });
}

// Go to login page
function goToLogin() {
  const query: Record<string, string> = {};

  if (invitationId.value) {
    query.invitation_id = invitationId.value;
  }

  if (invitationInfo.value?.email_restriction) {
    query.email = invitationInfo.value.email_restriction;
  }

  router.push({
    path: '/login/pwd-login',
    query
  });
}

// Get invitation info on page load
onMounted(() => {
  loadInvitationInfo();
  // Try different rotation config options
  // const gridBg = new GridArrayBg({
  //   dom: "box",
  //   colors: ["#3155A0","#2984C3","#5ABAD5","#ABEDE6","#32BFAE","#118AAD"],
  //   loop: true,
  //   // Try various possible spin options
  //   spin: true,
  // });

  new RandomCubesBg({
    dom: "box",
    colors: ["#193153","#CEDCEF","#95BDE2","#5886C8"],
    loop: true
  });
});
</script>

<template>
  <div class="min-h-screen relative">
    <!-- Background layer -->
    <div id="box" class="absolute inset-0 z-0"></div>

    <!-- Content layer -->
    <div class="relative z-10 min-h-screen flex items-center justify-center p-4">
      <NCard class="w-full max-w-md shadow-xl bg-white/95 backdrop-blur-sm">
        <!-- Header area -->
        <div class="flex items-center justify-between mb-6 pt-2">
          <!-- Logo and title -->
          <div class="flex items-center">
            <SystemLogo class="text-32px text-primary mr-2" style="fill: #007aff" />
            <h2 class="text-18px text-primary font-600">{{ $t('system.title') }}</h2>
          </div>

          <!-- Language switch button -->
          <div class="flex items-center">
            <NDropdown
              :value="appStore.locale"
              :options="appStore.localeOptions"
              trigger="hover"
              @select="appStore.changeLocale"
            >
              <NButton quaternary size="small" class="flex items-center gap-1">
                <template #icon>
                  <SvgIcon icon="heroicons:language" class="text-base" />
                </template>
                <span class="text-xs">{{ $t('icon.lang') }}</span>
              </NButton>
            </NDropdown>
          </div>
        </div>

        <!-- Loading state -->
        <div v-if="loading" class="text-center py-8">
          <NSpin size="large" />
          <p class="mt-4 text-gray-600">{{ $t('invitation.loading') }}</p>
        </div>

        <!-- Error state -->
        <div v-else-if="error" class="text-center py-8">
          <div class="text-red-500 text-6xl mb-4">
            <i class="i-mdi-alert-circle-outline" />
          </div>
          <h2 class="text-xl font-semibold text-gray-800 mb-2">{{ $t('invitation.invalid') }}</h2>
          <p class="text-gray-600 mb-6">{{ error }}</p>
          <NButton @click="router.push('/')">{{ $t('invitation.backToHome') }}</NButton>
        </div>

        <!-- Invitation info display -->
        <div v-else-if="invitationInfo" class="pb-6">
          <div class="text-center mb-6">
            <div class="text-blue-500 text-6xl mb-4">
              <i class="i-mdi-account-plus-outline" />
            </div>
            <h1 class="text-2xl font-bold text-gray-800 mb-2">{{ $t('invitation.title') }}</h1>
            <p class="text-gray-600">{{ $t('invitation.invitedToJoin') }}</p>
          </div>

          <!-- Organization info -->
          <div class="bg-gray-50 rounded-lg p-4 mb-6">
            <h3 class="font-semibold text-lg text-gray-800 mb-2">{{ invitationInfo.model_name }}</h3>
            <div class="space-y-2 text-sm text-gray-600">
              <div class="flex justify-between">
                <span>{{ $t('invitation.inviterRole') }}：</span>
                <span class="font-medium">{{ $t(`invitation.roles.${invitationInfo.role}`) }}</span>
              </div>
              <div class="flex justify-between">
                <span>{{ $t('invitation.inviter') }}：</span>
                <span class="font-medium">{{ invitationInfo.created_by.name }}</span>
              </div>
              <div v-if="invitationInfo.expires_at" class="flex justify-between">
                <span>{{ $t('invitation.expirationTime') }}：</span>
                <span class="font-medium">{{ new Date(invitationInfo.expires_at).toLocaleString() }}</span>
              </div>
              <div v-if="invitationInfo.email_restriction" class="flex justify-between">
                <span>{{ $t('invitation.restrictedEmail') }}：</span>
                <span class="font-medium">{{ invitationInfo.email_restriction }}</span>
              </div>
            </div>
          </div>

          <!-- Invitation status tips -->
          <div v-if="!canAcceptInvitation" class="mb-6">
            <NAlert
              v-if="invitationInfo.is_expired"
              type="error"
              :title="$t('invitation.expired')"
              class="mb-4"
            >
              {{ $t('invitation.expiredLink') }}
            </NAlert>
            <NAlert
              v-else-if="invitationInfo.has_reached_usage_limit"
              type="error"
              :title="$t('invitation.usageLimitReached')"
              class="mb-4"
            >
              {{ $t('invitation.usageLimitLink') }}
            </NAlert>
            <NAlert
              v-else-if="!invitationInfo.is_valid"
              type="error"
              :title="$t('invitation.invalid')"
              class="mb-4"
            >
              {{ $t('invitation.invalidLink') }}
            </NAlert>
          </div>

          <!-- Action buttons -->
          <div class="space-y-3">
            <!-- Logged-in user -->
            <template v-if="isLoggedIn">
              <NButton
                v-if="canAcceptInvitation"
                type="primary"
                size="large"
                block
                :loading="accepting"
                @click="acceptInvitation"
              >
                {{ $t('invitation.acceptInvitation') }}
              </NButton>
              <NButton v-else size="large" block disabled>
                {{ $t('invitation.cannotAccept') }}
              </NButton>
            </template>

            <!-- Not logged-in user -->
            <template v-else>
              <template v-if="canAcceptInvitation">
                <NButton
                  type="primary"
                  size="large"
                  block
                  @click="goToLogin"
                >
                  {{ $t('invitation.loginToAccept') }}
                </NButton>
                <NButton
                  type="default"
                  size="large"
                  block
                  @click="goToRegister"
                >
                  {{ $t('invitation.registerToAccept') }}
                </NButton>
              </template>
              <NButton v-else size="large" block disabled>
                {{ $t('invitation.invalid') }}
              </NButton>
            </template>
          </div>
        </div>
      </NCard>
    </div>
  </div>
</template>

<style scoped>
.i-mdi-alert-circle-outline,
.i-mdi-account-plus-outline {
  display: inline-block;
  width: 1em;
  height: 1em;
}

/* Background container style */
#box {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* CSS rotation animation as a fallback */
#box canvas {
  animation: rotateCanvas 30s linear infinite;
}

@keyframes rotateCanvas {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Ensure the card is centered on all screen sizes */
@media (max-height: 600px) {
  .min-h-screen {
    min-height: 100vh;
    display: flex;
    align-items: center;
  }
}
</style>
