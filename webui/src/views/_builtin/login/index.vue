<script setup lang="ts">
import { computed } from 'vue';
import type { Component } from 'vue';
import { getPaletteColorByNumber, mixColor } from '@sa/color';
import { loginModuleRecord } from '@/constants/app';
import { useAppStore } from '@/store/modules/app';
import { useThemeStore } from '@/store/modules/theme';
import { $t } from '@/locales';
import GameBg from '@/components/custom/game-bg.vue';
import PwdLogin from './modules/pwd-login.vue';
import CodeLogin from './modules/code-login.vue';
import Register from './modules/register.vue';
import ResetPwd from './modules/reset-pwd.vue';
import BindWechat from './modules/bind-wechat.vue';

interface Props {
  /** The login module */
  module?: UnionKey.LoginModule;
}

const props = defineProps<Props>();

const appStore = useAppStore();
const themeStore = useThemeStore();

interface LoginModule {
  label: string;
  component: Component;
}

const moduleMap: Record<UnionKey.LoginModule, LoginModule> = {
  'pwd-login': { label: loginModuleRecord['pwd-login'], component: PwdLogin },
  'code-login': { label: loginModuleRecord['code-login'], component: CodeLogin },
  register: { label: loginModuleRecord.register, component: Register },
  'reset-pwd': { label: loginModuleRecord['reset-pwd'], component: ResetPwd },
  'bind-wechat': { label: loginModuleRecord['bind-wechat'], component: BindWechat }
};

const activeModule = computed(() => moduleMap[props.module || 'pwd-login']);

const bgThemeColor = computed(() => {
  // 使用更鲜艳的蓝色作为主题色
  const vibrantBlue = '#007aff';
  return themeStore.darkMode ? getPaletteColorByNumber(vibrantBlue, 700) : vibrantBlue;
});

const bgColor = computed(() => {
  const vibrantBlue = '#007aff';
  const COLOR_WHITE = '#ffffff';
  const COLOR_DARK = '#0a0a0a';

  if (themeStore.darkMode) {
    // 深色模式：使用深蓝色背景
    return mixColor(COLOR_DARK, vibrantBlue, 0.15);
  }
  // 浅色模式：使用浅蓝色背景
  return mixColor(COLOR_WHITE, vibrantBlue, 0.08);
});

const cardStyle = computed(() => ({
  backgroundColor: themeStore.darkMode ? 'rgba(255, 255, 255, 0.08)' : 'rgba(255, 255, 255, 0.92)',
  backdropFilter: 'blur(15px)',
  border: `1px solid ${themeStore.darkMode ? 'rgba(0, 122, 255, 0.3)' : 'rgba(0, 122, 255, 0.2)'}`,
  boxShadow: themeStore.darkMode
    ? '0 25px 50px -12px rgba(0, 122, 255, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05)'
    : '0 25px 50px -12px rgba(0, 122, 255, 0.15), 0 0 0 1px rgba(0, 122, 255, 0.1)'
}));
</script>

<template>
  <div class="relative size-full flex-center overflow-hidden" :style="{ backgroundColor: bgColor }">
    <GameBg :theme-color="bgThemeColor" />
    <NCard :bordered="false" class="relative z-4 w-auto rd-16px shadow-2xl backdrop-blur-sm" :style="cardStyle">
      <div class="w-400px lt-sm:w-300px">
        <header class="flex-y-center justify-between">
          <SystemLogo class="text-64px text-primary drop-shadow-sm lt-sm:text-48px" style="fill: #007aff" />
          <h3 class="text-28px text-primary font-600 drop-shadow-sm lt-sm:text-22px">{{ $t('system.title') }}</h3>
          <div class="i-flex-col">
            <ThemeSchemaSwitch
              :theme-schema="themeStore.themeScheme"
              :show-tooltip="false"
              class="text-20px lt-sm:text-18px"
              @switch="themeStore.toggleThemeScheme"
            />
            <LangSwitch
              v-if="themeStore.header.multilingual.visible"
              :lang="appStore.locale"
              :lang-options="appStore.localeOptions"
              :show-tooltip="false"
              @change-lang="appStore.changeLocale"
            />
          </div>
        </header>
        <main class="pt-24px">
          <h3 class="text-18px text-primary font-medium">{{ $t(activeModule.label) }}</h3>
          <div class="pt-24px">
            <Transition :name="themeStore.page.animateMode" mode="out-in" appear>
              <component :is="activeModule.component" />
            </Transition>
          </div>
        </main>
      </div>
    </NCard>
  </div>
</template>

<style scoped></style>
