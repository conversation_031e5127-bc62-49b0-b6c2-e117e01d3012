<script setup lang="ts">
import { computed, reactive, ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { loginModuleRecord } from '@/constants/app';
import { useAuthStore } from '@/store/modules/auth';
import { useRouterPush } from '@/hooks/common/router';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { localStg } from '@/utils/storage';
import { fetchAcceptInvitation } from '@/service/api/invitation';
import { $t } from '@/locales';

defineOptions({
  name: 'PwdLogin'
});

const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();
const { toggleLoginModule } = useRouterPush();
const { formRef, validate } = useNaiveForm();

interface FormModel {
  userName: string;
  password: string;
}

const model: FormModel = reactive({
  userName: '',
  password: ''
});

// Remember me state
const rememberMe = ref(false);

// 邀请相关状态
const invitationId = computed(() => route.query.invitation_id as string);
const restrictedEmail = computed(() => route.query.email as string);
const isInvitationLogin = computed(() => Boolean(invitationId.value));
const isEmailRestricted = computed(() => Boolean(restrictedEmail.value));

const rules = computed<Record<keyof FormModel, App.Global.FormRule[]>>(() => {
  // inside computed to make locale reactive, if not apply i18n, you can define it without computed
  const { formRules } = useFormRules();

  return {
    userName: formRules.userNameOrEmail,
    password: formRules.pwd
  };
});

// Load remembered login info on initialization
onMounted(() => {
  // 如果是邀请登录且有邮箱限制，预填充邮箱
  if (isInvitationLogin.value && restrictedEmail.value) {
    model.userName = restrictedEmail.value;
  } else {
    // 否则加载记住的登录信息
    const rememberedLogin = localStg.get('rememberedLogin');
    if (rememberedLogin) {
      model.userName = rememberedLogin.userName || '';
      model.password = rememberedLogin.password || '';
      rememberMe.value = true;
    }
  }
});

async function handleSubmit() {
  await validate();

  // Handle remember me functionality (只在非邀请登录时处理)
  if (!isInvitationLogin.value) {
    if (rememberMe.value) {
      // Save login information
      localStg.set('rememberedLogin', {
        userName: model.userName,
        password: model.password
      });
    } else {
      // Clear saved login information
      localStg.remove('rememberedLogin');
    }
  }

  // 如果是邀请登录，使用特殊的登录流程
  if (isInvitationLogin.value && invitationId.value) {
    await handleInvitationLogin();
  } else {
    // 普通登录流程
    await authStore.login(model.userName, model.password);
  }
}

// 邀请登录的特殊处理
async function handleInvitationLogin() {
  if (!invitationId.value) return;

  try {
    // 1. 使用AuthStore的login方法进行登录，但不自动跳转
    await authStore.login(model.userName, model.password, false);

    // 2. 检查登录是否成功
    if (!authStore.isLogin) {
      window.$message?.error($t('invitation.joinFailed'));
      return;
    }

    // 3. 调用邀请接受API
    const { data: invitationResponse } = await fetchAcceptInvitation(invitationId.value);

    if (invitationResponse && invitationResponse.success) {
      // 显示邀请接受成功提示
      window.$notification?.success({
        title: $t('invitation.joinSuccess'),
        content: $t('invitation.joinSuccess'),
        duration: 3000
      });

      // 等待3秒后跳转到后台首页
      setTimeout(() => {
        router.push('/');
      }, 3000);
    } else {
      window.$message?.error(invitationResponse?.message || $t('invitation.joinFailed'));
    }
  } catch (err: any) {
    const errorMsg = err?.response?.data?.message || err?.message || $t('invitation.joinFailed');
    window.$message?.error(errorMsg);
  }
}

type AccountKey = 'super' | 'admin' | 'user';

interface Account {
  key: AccountKey;
  label: string;
  userName: string;
  password: string;
}

const accounts = computed<Account[]>(() => [
  {
    key: 'super',
    label: $t('page.login.pwdLogin.superAdmin'),
    userName: '<EMAIL>',
    password: '123456'
  },
  {
    key: 'admin',
    label: $t('page.login.pwdLogin.admin'),
    userName: '<EMAIL>',
    password: '123456'
  },
  {
    key: 'user',
    label: $t('page.login.pwdLogin.user'),
    userName: '<EMAIL>',
    password: '123456'
  }
]);

async function handleAccountLogin(account: Account) {
  // Auto-fill form and set remember me when using quick login
  model.userName = account.userName;
  model.password = account.password;
  rememberMe.value = true;

  // Save login information
  localStg.set('rememberedLogin', {
    userName: account.userName,
    password: account.password
  });

  await authStore.login(account.userName, account.password);
}
</script>

<template>
  <NForm ref="formRef" :model="model" :rules="rules" size="large" :show-label="false" @keyup.enter="handleSubmit">
    <!-- 邀请登录提示 -->
    <div v-if="isInvitationLogin" class="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
      <div class="flex items-center text-blue-600 text-sm">
        <i class="i-mdi-information-outline mr-2" />
        <span v-if="isEmailRestricted">{{ $t('invitation.emailRestriction', { email: restrictedEmail }) }}</span>
        <span v-else>{{ $t('invitation.loginToAccept') }}</span>
      </div>
    </div>

    <NFormItem path="userName">
      <NInput
        v-model:value="model.userName"
        :placeholder="$t('page.login.common.userNamePlaceholder')"
        :readonly="isEmailRestricted"
        :disabled="isEmailRestricted"
      />
    </NFormItem>
    <NFormItem path="password">
      <NInput
        v-model:value="model.password"
        type="password"
        show-password-on="click"
        :placeholder="$t('page.login.common.passwordPlaceholder')"
      />
    </NFormItem>
    <NSpace vertical :size="24">
      <!-- 非邀请登录时显示记住我和忘记密码 -->
      <div v-if="!isInvitationLogin" class="flex-y-center justify-between">
        <NCheckbox v-model:checked="rememberMe">{{ $t('page.login.pwdLogin.rememberMe') }}</NCheckbox>
        <NButton quaternary @click="toggleLoginModule('reset-pwd')">
          {{ $t('page.login.pwdLogin.forgetPassword') }}
        </NButton>
      </div>

      <NButton type="primary" size="large" round block :loading="authStore.loginLoading" @click="handleSubmit">
        {{ isInvitationLogin ? $t('invitation.acceptInvitation') : $t('common.confirm') }}
      </NButton>

      <!-- 非邀请登录时显示其他登录选项 -->
      <template v-if="!isInvitationLogin">
        <div class="flex-y-center justify-between gap-12px">
          <NButton class="flex-1" block @click="toggleLoginModule('code-login')">
            {{ $t(loginModuleRecord['code-login']) }}
          </NButton>
          <NButton class="flex-1" block @click="toggleLoginModule('register')">
            {{ $t(loginModuleRecord.register) }}
          </NButton>
        </div>
        <NDivider class="text-14px text-#666 !m-0">{{ $t('page.login.pwdLogin.otherAccountLogin') }}</NDivider>
        <div class="flex-center gap-12px">
          <NButton v-for="item in accounts" :key="item.key" type="primary" @click="handleAccountLogin(item)">
            {{ item.label }}
          </NButton>
        </div>
      </template>
    </NSpace>
  </NForm>
</template>

<style scoped></style>
