<script setup lang="ts">
import { computed, reactive, onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useRouterPush } from '@/hooks/common/router';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { useCaptchaEmail } from '@/hooks/business/captcha-email';
import { useAuthStore } from '@/store/modules/auth';
import { fetchRegisterUser } from '@/service/api/user';
import { fetchAcceptInvitation } from '@/service/api/invitation';
import { $t } from '@/locales';

defineOptions({
  name: 'Register'
});

const route = useRoute();
const router = useRouter();
const { toggleLoginModule } = useRouterPush();
const { formRef, validate } = useNaiveForm();
const { label, isCounting, loading, getCaptchaEmail } = useCaptchaEmail();
const authStore = useAuthStore();

// 注册状态
const registering = ref(false);

interface FormModel {
  name: string;
  email: string;
  code: string;
  password: string;
  confirmPassword: string;
}

const model: FormModel = reactive({
  name: '',
  email: '',
  code: '',
  password: '',
  confirmPassword: ''
});

// 邮箱限制状态
const restrictedEmail = computed(() => route.query.email as string || '');
const isEmailRestricted = computed(() => !!restrictedEmail.value);

// 邀请ID
const invitationId = computed(() => route.query.invitation_id as string || '');

const rules = computed<Record<keyof FormModel, App.Global.FormRule[]>>(() => {
  const { formRules, createConfirmPwdRule } = useFormRules();

  return {
    name: formRules.userName,
    email: formRules.email,
    code: formRules.code,
    password: formRules.pwd,
    confirmPassword: createConfirmPwdRule(model.password)
  };
});

// 页面加载时处理邮箱限制
onMounted(() => {
  if (isEmailRestricted.value && restrictedEmail.value) {
    model.email = restrictedEmail.value;
  }
});

// 邀请注册的特殊处理
async function handleInvitationRegister() {
  if (!invitationId.value) return;

  try {
    // 1. 注册成功后自动登录，但不自动跳转
    await authStore.login(model.email, model.password, false);

    // 2. 检查登录是否成功
    if (!authStore.isLogin) {
      window.$message?.error($t('invitation.joinFailed'));
      return;
    }

    // 3. 调用邀请接受API
    const { data: invitationResponse } = await fetchAcceptInvitation(invitationId.value);

    if (invitationResponse && invitationResponse.success) {
      // 显示邀请接受成功提示
      window.$notification?.success({
        title: $t('invitation.joinSuccess'),
        content: $t('invitation.joinSuccess'),
        duration: 3000
      });

      // 刷新用户信息以获取新的组织权限
      await authStore.initUserInfo();

      // 等待3秒后跳转到首页
      setTimeout(() => {
        router.push('/');
      }, 3000);
    } else {
      window.$message?.error(invitationResponse?.message || $t('invitation.joinFailed'));
    }
  } catch (err: any) {
    const errorMsg = err?.response?.data?.message || err?.message || $t('invitation.joinFailed');
    window.$message?.error(errorMsg);
  }
}

async function handleSubmit() {
  await validate();

  registering.value = true;

  try {
    // 调用注册API
    const { data } = await fetchRegisterUser({
      name: model.name,
      email: model.email,
      password: model.password,
      password_confirmation: model.confirmPassword,
      verification_code: model.code
    });

    if (data && data.success) {
      window.$message?.success($t('common.addSuccess'));

      // 如果有邀请ID，使用特殊的注册+邀请流程
      if (invitationId.value) {
        await handleInvitationRegister();
      } else {
        // 普通注册流程：自动登录并跳转
        await authStore.login(model.email, model.password);
      }
    } else {
      window.$message?.error(data?.message || $t('invitation.joinFailed'));
    }
  } catch (err: any) {
    const errorMsg = err?.response?.data?.message || err?.message || $t('invitation.joinFailed');
    window.$message?.error(errorMsg);
  } finally {
    registering.value = false;
  }
}
</script>

<template>
  <NForm ref="formRef" :model="model" :rules="rules" size="large" :show-label="false" @keyup.enter="handleSubmit">
    <!-- 邮箱限制提示 -->
    <div v-if="isEmailRestricted" class="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
      <div class="flex items-center text-blue-600 text-sm">
        <i class="i-mdi-information-outline mr-2" />
        <span>{{ $t('invitation.emailRestriction', { email: restrictedEmail }) }}</span>
      </div>
    </div>

    <NFormItem path="name">
      <NInput
        v-model:value="model.name"
        :placeholder="$t('page.login.common.userNamePlaceholder')"
      />
    </NFormItem>
    <NFormItem path="email">
      <NInput
        v-model:value="model.email"
        :placeholder="$t('invitation.emailPlaceholder')"
        :readonly="isEmailRestricted"
        :disabled="isEmailRestricted"
      />
    </NFormItem>
    <NFormItem path="code">
      <div class="w-full flex-y-center gap-16px">
        <NInput v-model:value="model.code" :placeholder="$t('page.login.common.codePlaceholder')" />
        <NButton
          size="large"
          :disabled="isCounting || !model.email"
          :loading="loading"
          @click="getCaptchaEmail(model.email)"
        >
          {{ label }}
        </NButton>
      </div>
    </NFormItem>
    <NFormItem path="password">
      <NInput
        v-model:value="model.password"
        type="password"
        show-password-on="click"
        :placeholder="$t('page.login.common.passwordPlaceholder')"
      />
    </NFormItem>
    <NFormItem path="confirmPassword">
      <NInput
        v-model:value="model.confirmPassword"
        type="password"
        show-password-on="click"
        :placeholder="$t('page.login.common.confirmPasswordPlaceholder')"
      />
    </NFormItem>
    <NSpace vertical :size="18" class="w-full">
      <NButton type="primary" size="large" round block :loading="registering" @click="handleSubmit">
        {{ $t('common.confirm') }}
      </NButton>
      <NButton size="large" round block @click="toggleLoginModule('pwd-login')">
        {{ $t('page.login.common.back') }}
      </NButton>
    </NSpace>
  </NForm>
</template>

<style scoped></style>
