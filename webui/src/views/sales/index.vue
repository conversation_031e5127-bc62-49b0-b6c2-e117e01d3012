<script setup lang="tsx">
import { computed, ref } from 'vue';
import { useAppStore } from '@/store/modules/app';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';
import SalesStats from './modules/sales-stats.vue';
import SalesOverview from './modules/sales-overview.vue';
import OrderAnalysis from './modules/order-analysis.vue';
import RefundAnalysis from './modules/refund-analysis.vue';
import RegionalAnalysis from './modules/regional-analysis.vue';
import CustomerAnalysis from './modules/customer-analysis.vue';
import TopRanking from './modules/top-ranking.vue';

defineOptions({
  name: 'Sales'
});

const appStore = useAppStore();

const gap = computed(() => (appStore.isMobile ? 0 : 16));

// Tab相关状态
const activeTab = ref('overview');

// Tab选项
const tabOptions = computed(() => [
  {
    key: 'overview',
    label: $t('page.sales.tabs.overview'),
    icon: 'mdi:chart-line'
  },
  {
    key: 'orders',
    label: $t('page.sales.tabs.orders'),
    icon: 'mdi:receipt'
  },
  {
    key: 'refunds',
    label: $t('page.sales.tabs.refunds'),
    icon: 'mdi:undo-variant'
  },
  {
    key: 'regional',
    label: $t('page.sales.tabs.regional'),
    icon: 'mdi:earth'
  },
  {
    key: 'customers',
    label: $t('page.sales.tabs.customers'),
    icon: 'mdi:account-group'
  },
  {
    key: 'topRanking',
    label: $t('page.sales.tabs.topRanking'),
    icon: 'mdi:trophy'
  }
]);
</script>

<template>
  <NSpace vertical :size="16">
    <!-- 顶部统计卡片区域（保持不变） -->
    <NCard :bordered="false" class="card-wrapper">
      <template #header>
        <div class="flex items-center gap-2">
          <SvgIcon icon="mdi:chart-line" class="text-20px" />
          <span class="text-18px font-semibold">{{ $t('route.sales') }}</span>
        </div>
      </template>
      <SalesStats />
    </NCard>

    <!-- 图表区域（新增Tab切换） -->
    <NCard :bordered="false" class="card-wrapper">
      <NTabs v-model:value="activeTab" type="line" animated>
        <NTabPane v-for="tab in tabOptions" :key="tab.key" :name="tab.key" :tab="tab.label">
          <template #tab>
            <div class="flex items-center gap-2">
              <SvgIcon :icon="tab.icon" class="text-16px" />
              <span>{{ tab.label }}</span>
            </div>
          </template>

          <!-- 根据选中Tab显示对应内容 -->
          <SalesOverview v-if="tab.key === 'overview'" />
          <OrderAnalysis v-else-if="tab.key === 'orders'" />
          <RefundAnalysis v-else-if="tab.key === 'refunds'" />
          <RegionalAnalysis v-else-if="tab.key === 'regional'" />
          <CustomerAnalysis v-else-if="tab.key === 'customers'" />
          <TopRanking v-else-if="tab.key === 'topRanking'" />
        </NTabPane>
      </NTabs>
    </NCard>
  </NSpace>
</template>

<style scoped>
.card-wrapper {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
