<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue';
import { useAppStore } from '@/store/modules/app';
import { useEcharts } from '@/hooks/common/echarts';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';

defineOptions({
  name: 'RefundAnalysis'
});

const appStore = useAppStore();
const isMobile = computed(() => appStore.isMobile);

const dateRange = ref<[number, number]>([Date.now() - 30 * 24 * 60 * 60 * 1000, Date.now()]);

const chartOptions = reactive({
  region: 'all',
  currency: 'all'
});

// 统一的配色方案
const colorScheme = {
  primary: '#2a4a67',
  success: '#52c41a',
  purple: '#722ed1',
  orange: '#fa8c16',
  red: '#ff4d4f',
  gray: '#8c8c8c'
};

// 模拟退单原因数据
const refundReasonData = computed(() => [
  { name: $t('page.sales.productQualityIssue'), value: 35, color: colorScheme.red },
  { name: $t('page.sales.notMeetExpectation'), value: 28, color: colorScheme.orange },
  { name: $t('page.sales.technicalIssue'), value: 20, color: colorScheme.purple },
  { name: $t('page.sales.priceFactor'), value: 12, color: colorScheme.gray },
  { name: $t('page.sales.otherReasons'), value: 5, color: colorScheme.primary }
]);

// 模拟退单率趋势数据
const refundRateData = computed(() => [
  { date: '01-01', rate: 2.5 },
  { date: '01-02', rate: 1.8 },
  { date: '01-03', rate: 3.2 },
  { date: '01-04', rate: 2.1 },
  { date: '01-05', rate: 1.9 },
  { date: '01-06', rate: 2.8 },
  { date: '01-07', rate: 2.3 }
]);

// 模拟退单金额趋势数据
const refundAmountData = computed(() => [
  { date: '01-01', amount: 1250 },
  { date: '01-02', amount: 890 },
  { date: '01-03', amount: 1680 },
  { date: '01-04', amount: 1120 },
  { date: '01-05', amount: 950 },
  { date: '01-06', amount: 1450 },
  { date: '01-07', amount: 1180 }
]);

// 模拟各地区退单率数据
const regionalRefundData = computed(() => [
  { region: $t('page.sales.northAmerica'), rate: 2.1 },
  { region: $t('page.sales.europe'), rate: 1.8 },
  { region: $t('page.sales.asia'), rate: 2.5 },
  { region: $t('page.sales.others'), rate: 3.2 }
]);

function handleDateRangeChange() {
  // TODO: 实现日期范围变更逻辑
}

function handleExportData() {
  // TODO: 实现数据导出逻辑
}

// 退单原因分布饼图
const { domRef: refundReasonChartRef, updateOptions: updateRefundReasonChart } = useEcharts(() => ({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c}% ({d}%)'
  },
  legend: {
    bottom: '8%',
    left: 'center',
    itemStyle: {
      borderWidth: 0
    },
    textStyle: {
      fontSize: 12
    }
  },
  series: [
    {
      name: $t('page.sales.refundReason'),
      type: 'pie',
      radius: ['35%', '60%'],
      center: ['50%', '40%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 6,
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '14',
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      data: [] as { name: string; value: number }[]
    }
  ]
}));

// 退单率趋势图
const { domRef: refundRateChartRef, updateOptions: updateRefundRateChart } = useEcharts(() => ({
  tooltip: {
    trigger: 'axis',
    formatter: '{a} <br/>{b}: {c}%'
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: [] as string[]
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: '{value}%'
    }
  },
  series: [
    {
      name: $t('page.sales.refundRate'),
      type: 'line',
      smooth: true,
      itemStyle: {
        color: colorScheme.red
      },
      lineStyle: {
        color: colorScheme.red
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: colorScheme.red
            },
            {
              offset: 1,
              color: 'rgba(255, 77, 79, 0.1)'
            }
          ]
        }
      },
      data: [] as number[]
    }
  ]
}));

// 退单金额趋势图
const { domRef: refundAmountChartRef, updateOptions: updateRefundAmountChart } = useEcharts(() => ({
  tooltip: {
    trigger: 'axis',
    formatter: '{a} <br/>{b}: ${c}'
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: [] as string[]
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: '${value}'
    }
  },
  series: [
    {
      name: $t('page.sales.refundAmount'),
      type: 'bar',
      barWidth: '60%',
      itemStyle: {
        borderRadius: [4, 4, 0, 0],
        color: colorScheme.orange
      },
      data: [] as number[]
    }
  ]
}));

// 各地区退单率对比图
const { domRef: regionalRefundChartRef, updateOptions: updateRegionalRefundChart } = useEcharts(() => ({
  tooltip: {
    trigger: 'axis',
    formatter: '{a} <br/>{b}: {c}%'
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: [] as string[]
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: '{value}%'
    }
  },
  series: [
    {
      name: $t('page.sales.refundRate'),
      type: 'bar',
      barWidth: '60%',
      itemStyle: {
        borderRadius: [4, 4, 0, 0],
        color: colorScheme.purple
      },
      data: [] as number[]
    }
  ]
}));

// 初始化图表数据
async function initCharts() {
  await new Promise(resolve => {
    setTimeout(resolve, 1000);
  });

  // 更新退单原因分布饼图
  updateRefundReasonChart(opts => {
    opts.series[0].data = refundReasonData.value.map(item => ({
      name: item.name,
      value: item.value
    }));
    (opts as any).color = refundReasonData.value.map(item => item.color);
    return opts;
  });

  // 更新退单率趋势图
  updateRefundRateChart(opts => {
    opts.xAxis.data = refundRateData.value.map(item => item.date);
    opts.series[0].data = refundRateData.value.map(item => item.rate);
    return opts;
  });

  // 更新退单金额趋势图
  updateRefundAmountChart(opts => {
    opts.xAxis.data = refundAmountData.value.map(item => item.date);
    opts.series[0].data = refundAmountData.value.map(item => item.amount);
    return opts;
  });

  // 更新各地区退单率对比图
  updateRegionalRefundChart(opts => {
    opts.xAxis.data = regionalRefundData.value.map(item => item.region);
    opts.series[0].data = regionalRefundData.value.map(item => item.rate);
    return opts;
  });
}

// 更新国际化
function updateLocale() {
  updateRefundReasonChart((opts, factory) => {
    const originOpts = factory();
    opts.series[0].name = originOpts.series[0].name;
    opts.series[0].data = refundReasonData.value.map(item => ({
      name: item.name,
      value: item.value
    }));
    return opts;
  });

  updateRefundRateChart((opts, factory) => {
    const originOpts = factory();
    opts.series[0].name = originOpts.series[0].name;
    return opts;
  });

  updateRefundAmountChart((opts, factory) => {
    const originOpts = factory();
    opts.series[0].name = originOpts.series[0].name;
    return opts;
  });

  updateRegionalRefundChart((opts, factory) => {
    const originOpts = factory();
    opts.series[0].name = originOpts.series[0].name;
    opts.xAxis.data = regionalRefundData.value.map(item => item.region);
    return opts;
  });
}

watch(
  () => appStore.locale,
  () => {
    updateLocale();
  }
);

// 初始化
initCharts();
</script>

<template>
  <div class="space-y-6">
    <!-- 紧凑筛选器 -->
    <div class="rounded-lg bg-gray-50 p-4 dark:bg-gray-800">
      <div :class="isMobile ? 'space-y-3' : 'flex flex-wrap gap-4 items-end'">
        <div class="min-w-64 flex flex-col flex-1 gap-1">
          <span class="text-sm text-gray-600 font-medium dark:text-gray-300">{{ $t('page.sales.timeRange') }}</span>
          <NDatePicker
            v-model:value="dateRange"
            type="daterange"
            size="small"
            clearable
            @update:value="handleDateRangeChange"
          />
        </div>

        <div class="min-w-32 flex flex-col flex-1 gap-1">
          <span class="text-sm text-gray-600 font-medium dark:text-gray-300">{{ $t('page.sales.region') }}</span>
          <NSelect
            v-model:value="chartOptions.region"
            :placeholder="$t('page.sales.selectRegion')"
            :options="[
              { label: $t('page.sales.allRegions'), value: 'all' },
              { label: $t('page.sales.northAmerica'), value: 'na' },
              { label: $t('page.sales.europe'), value: 'eu' },
              { label: $t('page.sales.asia'), value: 'asia' }
            ]"
            size="small"
          />
        </div>

        <div class="min-w-28 flex flex-col flex-1 gap-1">
          <span class="text-sm text-gray-600 font-medium dark:text-gray-300">{{ $t('page.sales.currency') }}</span>
          <NSelect
            v-model:value="chartOptions.currency"
            :placeholder="$t('page.sales.selectCurrency')"
            :options="[
              { label: $t('page.sales.allCurrencies'), value: 'all' },
              { label: 'USD', value: 'usd' },
              { label: 'EUR', value: 'eur' },
              { label: 'CNY', value: 'cny' }
            ]"
            size="small"
          />
        </div>

        <div class="flex gap-2" :class="isMobile ? 'w-full justify-center' : 'flex-shrink-0'">
          <NButton type="primary" size="small" @click="handleExportData">
            <template #icon>
              <SvgIcon icon="mdi:download" />
            </template>
            {{ $t('page.sales.exportData') }}
          </NButton>
        </div>
      </div>
    </div>

    <!-- 退单统计卡片 -->
    <div class="grid grid-cols-1 mb-6 gap-4 md:grid-cols-3">
      <!-- 退单率卡片 -->
      <div
        class="relative overflow-hidden border-l-4 border-red-500 rounded-lg from-red-50 to-red-100 bg-gradient-to-br p-4 dark:from-red-900/20 dark:to-red-800/20"
      >
        <div class="flex items-center justify-between">
          <div class="flex-1">
            <div class="mb-3 flex items-center gap-3">
              <div class="h-8 w-8 flex items-center justify-center rounded-lg bg-red-500/10 dark:bg-red-500/20">
                <SvgIcon icon="mdi:undo-variant" class="text-lg text-red-500" />
              </div>
              <span class="text-sm text-gray-600 font-medium dark:text-gray-300">
                {{ $t('page.sales.refundRate') }}
              </span>
            </div>
            <div class="flex items-baseline gap-1">
              <span class="text-2xl text-red-600 font-bold dark:text-red-400">2.3</span>
              <span class="text-base text-red-500 font-medium dark:text-red-400">%</span>
            </div>
          </div>
          <div class="absolute h-16 w-16 rounded-full bg-red-500/5 -right-3 -top-3 dark:bg-red-500/10"></div>
        </div>
      </div>

      <!-- 退单金额卡片 -->
      <div
        class="relative overflow-hidden border-l-4 border-orange-500 rounded-lg from-orange-50 to-orange-100 bg-gradient-to-br p-4 dark:from-orange-900/20 dark:to-orange-800/20"
      >
        <div class="flex items-center justify-between">
          <div class="flex-1">
            <div class="mb-3 flex items-center gap-3">
              <div class="h-8 w-8 flex items-center justify-center rounded-lg bg-orange-500/10 dark:bg-orange-500/20">
                <SvgIcon icon="mdi:currency-usd" class="text-lg text-orange-500" />
              </div>
              <span class="text-sm text-gray-600 font-medium dark:text-gray-300">
                {{ $t('page.sales.refundAmount') }}
              </span>
            </div>
            <div class="flex items-baseline gap-1">
              <span class="text-base text-orange-500 font-medium dark:text-orange-400">$</span>
              <span class="text-2xl text-orange-600 font-bold dark:text-orange-400">12,580</span>
            </div>
          </div>
          <div class="absolute h-16 w-16 rounded-full bg-orange-500/5 -right-3 -top-3 dark:bg-orange-500/10"></div>
        </div>
      </div>

      <!-- 退单数量卡片 -->
      <div
        class="relative overflow-hidden border-l-4 border-purple-500 rounded-lg from-purple-50 to-purple-100 bg-gradient-to-br p-4 dark:from-purple-900/20 dark:to-purple-800/20"
      >
        <div class="flex items-center justify-between">
          <div class="flex-1">
            <div class="mb-3 flex items-center gap-3">
              <div class="h-8 w-8 flex items-center justify-center rounded-lg bg-purple-500/10 dark:bg-purple-500/20">
                <SvgIcon icon="mdi:receipt" class="text-lg text-purple-500" />
              </div>
              <span class="text-sm text-gray-600 font-medium dark:text-gray-300">
                {{ $t('page.sales.refundCount') }}
              </span>
            </div>
            <div class="flex items-baseline gap-1">
              <span class="text-2xl text-purple-600 font-bold dark:text-purple-400">156</span>
              <span class="text-sm text-purple-500 font-medium dark:text-purple-400">
                {{ $t('common.unit.orders') }}
              </span>
            </div>
          </div>
          <div class="absolute h-16 w-16 rounded-full bg-purple-500/5 -right-3 -top-3 dark:bg-purple-500/10"></div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <NGrid :cols="2" :x-gap="16" :y-gap="16">
      <!-- 退单原因分布 -->
      <NGi>
        <NCard :bordered="false" class="h-420px">
          <template #header>
            <span class="text-16px font-semibold">{{ $t('page.sales.refundReasonDistribution') }}</span>
          </template>
          <div ref="refundReasonChartRef" class="h-350px overflow-hidden"></div>
        </NCard>
      </NGi>

      <!-- 退单率趋势 -->
      <NGi>
        <NCard :bordered="false" class="h-420px">
          <template #header>
            <span class="text-16px font-semibold">{{ $t('page.sales.refundRateTrend') }}</span>
          </template>
          <div ref="refundRateChartRef" class="h-350px overflow-hidden"></div>
        </NCard>
      </NGi>

      <!-- 退单金额趋势 -->
      <NGi>
        <NCard :bordered="false" class="h-420px">
          <template #header>
            <span class="text-16px font-semibold">{{ $t('page.sales.refundAmountTrend') }}</span>
          </template>
          <div ref="refundAmountChartRef" class="h-350px overflow-hidden"></div>
        </NCard>
      </NGi>

      <!-- 各地区退单率对比 -->
      <NGi>
        <NCard :bordered="false" class="h-420px">
          <template #header>
            <span class="text-16px font-semibold">{{ $t('page.sales.regionalRefundRate') }}</span>
          </template>
          <div ref="regionalRefundChartRef" class="h-350px overflow-hidden"></div>
        </NCard>
      </NGi>
    </NGrid>
  </div>
</template>

<style scoped></style>
