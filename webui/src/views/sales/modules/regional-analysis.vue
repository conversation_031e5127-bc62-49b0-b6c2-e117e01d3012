<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue';
import { useAppStore } from '@/store/modules/app';
import { useEcharts } from '@/hooks/common/echarts';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';

defineOptions({
  name: 'RegionalAnalysis'
});

const appStore = useAppStore();
const isMobile = computed(() => appStore.isMobile);

const dateRange = ref<[number, number]>([Date.now() - 30 * 24 * 60 * 60 * 1000, Date.now()]);

const chartOptions = reactive({
  currency: 'all',
  period: 'month'
});

// 统一的配色方案
const colorScheme = {
  primary: '#2a4a67',
  success: '#52c41a',
  purple: '#722ed1',
  orange: '#fa8c16',
  red: '#ff4d4f',
  gray: '#8c8c8c'
};

// 模拟各地区销售额数据
const regionalSalesData = computed(() => [
  { region: $t('page.sales.northAmerica'), amount: 125680 },
  { region: $t('page.sales.europe'), amount: 98450 },
  { region: $t('page.sales.asia'), amount: 156720 },
  { region: $t('page.sales.others'), amount: 45890 }
]);

// 模拟各地区销量数据
const regionalVolumeData = computed(() => [
  { region: $t('page.sales.northAmerica'), volume: 2580 },
  { region: $t('page.sales.europe'), volume: 1950 },
  { region: $t('page.sales.asia'), volume: 3120 },
  { region: $t('page.sales.others'), volume: 890 }
]);

// 模拟地区销售增长率数据
const regionalGrowthData = computed(() => [
  { region: $t('page.sales.northAmerica'), growth: 15.2 },
  { region: $t('page.sales.europe'), growth: 8.7 },
  { region: $t('page.sales.asia'), growth: 22.5 },
  { region: $t('page.sales.others'), growth: -3.2 }
]);

// 模拟地区客单价数据
const regionalAvgOrderData = computed(() => [
  { region: $t('page.sales.northAmerica'), avgOrder: 48.7 },
  { region: $t('page.sales.europe'), avgOrder: 50.5 },
  { region: $t('page.sales.asia'), value: 50.2 },
  { region: $t('page.sales.others'), value: 51.6 }
]);

function handleDateRangeChange() {
  // TODO: 实现日期范围变更逻辑
}

function handleExportData() {
  // TODO: 实现数据导出逻辑
}

// 各地区销售额对比柱状图
const { domRef: regionalSalesChartRef, updateOptions: updateRegionalSalesChart } = useEcharts(() => ({
  tooltip: {
    trigger: 'axis',
    formatter: '{a} <br/>{b}: ${c}'
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: [] as string[]
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: '${value}'
    }
  },
  series: [
    {
      name: $t('page.sales.salesAmount'),
      type: 'bar',
      barWidth: '60%',
      itemStyle: {
        borderRadius: [4, 4, 0, 0],
        color: colorScheme.primary
      },
      data: [] as number[]
    }
  ]
}));

// 各地区销量对比柱状图
const { domRef: regionalVolumeChartRef, updateOptions: updateRegionalVolumeChart } = useEcharts(() => ({
  tooltip: {
    trigger: 'axis',
    formatter: (params: any) => {
      const param = Array.isArray(params) ? params[0] : params;
      return `${param.seriesName} <br/>${param.name}: ${param.value}${$t('common.unit.orders')}`;
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: [] as string[]
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: (value: any) => `${value}${$t('common.unit.orders')}`
    }
  },
  series: [
    {
      name: $t('page.sales.orderVolume'),
      type: 'bar',
      barWidth: '60%',
      itemStyle: {
        borderRadius: [4, 4, 0, 0],
        color: colorScheme.success
      },
      data: [] as number[]
    }
  ]
}));

// 地区销售增长率对比图
const { domRef: regionalGrowthChartRef, updateOptions: updateRegionalGrowthChart } = useEcharts(() => ({
  tooltip: {
    trigger: 'axis',
    formatter: '{a} <br/>{b}: {c}%'
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: [] as string[]
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: '{value}%'
    }
  },
  series: [
    {
      name: $t('page.sales.growthRate'),
      type: 'bar',
      barWidth: '60%',
      itemStyle: {
        borderRadius: [4, 4, 0, 0],
        color: (params: any) => {
          return params.value >= 0 ? colorScheme.success : colorScheme.red;
        }
      },
      data: [] as number[]
    }
  ]
}));

// 地区客单价对比雷达图
const { domRef: regionalRadarChartRef, updateOptions: updateRegionalRadarChart } = useEcharts(() => ({
  tooltip: {
    trigger: 'item'
  },
  radar: {
    indicator: [
      { name: $t('page.sales.northAmerica'), max: 60 },
      { name: $t('page.sales.europe'), max: 60 },
      { name: $t('page.sales.asia'), max: 60 },
      { name: $t('page.sales.others'), max: 60 }
    ],
    center: ['50%', '50%'],
    radius: '60%'
  },
  series: [
    {
      name: $t('page.sales.regionalAvgOrderValue'),
      type: 'radar',
      data: [
        {
          value: [] as number[],
          name: $t('page.sales.avgOrderValue'),
          itemStyle: {
            color: colorScheme.purple
          },
          areaStyle: {
            color: colorScheme.purple,
            opacity: 0.3
          }
        }
      ]
    }
  ]
}));

// 初始化图表数据
async function initCharts() {
  await new Promise(resolve => {
    setTimeout(resolve, 1000);
  });

  // 更新各地区销售额对比图
  updateRegionalSalesChart(opts => {
    opts.xAxis.data = regionalSalesData.value.map(item => item.region);
    opts.series[0].data = regionalSalesData.value.map(item => item.amount);
    return opts;
  });

  // 更新各地区销量对比图
  updateRegionalVolumeChart(opts => {
    opts.xAxis.data = regionalVolumeData.value.map(item => item.region);
    opts.series[0].data = regionalVolumeData.value.map(item => item.volume);
    return opts;
  });

  // 更新地区销售增长率对比图
  updateRegionalGrowthChart(opts => {
    opts.xAxis.data = regionalGrowthData.value.map(item => item.region);
    opts.series[0].data = regionalGrowthData.value.map(item => item.growth);
    return opts;
  });

  // 更新地区客单价对比雷达图
  updateRegionalRadarChart(opts => {
    opts.series[0].data[0].value = [48.7, 50.5, 50.2, 51.6];
    return opts;
  });
}

// 更新国际化
function updateLocale() {
  updateRegionalSalesChart((opts, factory) => {
    const originOpts = factory();
    opts.series[0].name = originOpts.series[0].name;
    opts.xAxis.data = regionalSalesData.value.map(item => item.region);
    return opts;
  });

  updateRegionalVolumeChart((opts, factory) => {
    const originOpts = factory();
    opts.series[0].name = originOpts.series[0].name;
    opts.xAxis.data = regionalVolumeData.value.map(item => item.region);
    // 更新坐标轴单位
    (opts as any).yAxis.axisLabel.formatter = `{value}${$t('common.unit.orders')}`;
    // 更新tooltip格式
    (opts as any).tooltip.formatter = (params: any) => {
      const param = Array.isArray(params) ? params[0] : params;
      return `${param.seriesName} <br/>${param.name}: ${param.value}${$t('common.unit.orders')}`;
    };
    return opts;
  });

  updateRegionalGrowthChart((opts, factory) => {
    const originOpts = factory();
    opts.series[0].name = originOpts.series[0].name;
    opts.xAxis.data = regionalGrowthData.value.map(item => item.region);
    return opts;
  });

  updateRegionalRadarChart((opts, factory) => {
    const originOpts = factory();
    opts.radar.indicator = [
      { name: $t('page.sales.northAmerica'), max: 60 },
      { name: $t('page.sales.europe'), max: 60 },
      { name: $t('page.sales.asia'), max: 60 },
      { name: $t('page.sales.others'), max: 60 }
    ];
    opts.series[0].name = originOpts.series[0].name;
    opts.series[0].data[0].name = $t('page.sales.avgOrderValue');
    return opts;
  });
}

watch(
  () => appStore.locale,
  () => {
    updateLocale();
  }
);

// 初始化
initCharts();
</script>

<template>
  <div class="space-y-6">
    <!-- 紧凑筛选器 -->
    <div class="rounded-lg bg-gray-50 p-4 dark:bg-gray-800">
      <div :class="isMobile ? 'space-y-3' : 'flex flex-wrap gap-4 items-end'">
        <div class="min-w-64 flex flex-col flex-1 gap-1">
          <span class="text-sm text-gray-600 font-medium dark:text-gray-300">{{ $t('page.sales.timeRange') }}</span>
          <NDatePicker
            v-model:value="dateRange"
            type="daterange"
            size="small"
            clearable
            @update:value="handleDateRangeChange"
          />
        </div>

        <div class="min-w-28 flex flex-col flex-1 gap-1">
          <span class="text-sm text-gray-600 font-medium dark:text-gray-300">{{ $t('page.sales.currency') }}</span>
          <NSelect
            v-model:value="chartOptions.currency"
            :placeholder="$t('page.sales.selectCurrency')"
            :options="[
              { label: $t('page.sales.allCurrencies'), value: 'all' },
              { label: 'USD', value: 'usd' },
              { label: 'EUR', value: 'eur' },
              { label: 'CNY', value: 'cny' }
            ]"
            size="small"
          />
        </div>

        <div class="min-w-28 flex flex-col flex-1 gap-1">
          <span class="text-sm text-gray-600 font-medium dark:text-gray-300">
            {{ $t('page.sales.statisticalPeriod') }}
          </span>
          <NSelect
            v-model:value="chartOptions.period"
            :options="[
              { label: $t('page.sales.monthly'), value: 'month' },
              { label: $t('page.sales.quarterly'), value: 'quarter' },
              { label: $t('page.sales.yearly'), value: 'year' }
            ]"
            size="small"
          />
        </div>

        <div class="flex gap-2" :class="isMobile ? 'w-full justify-center' : 'flex-shrink-0'">
          <NButton type="primary" size="small" @click="handleExportData">
            <template #icon>
              <SvgIcon icon="mdi:download" />
            </template>
            {{ $t('page.sales.exportData') }}
          </NButton>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <NGrid :cols="2" :x-gap="16" :y-gap="16">
      <!-- 各地区销售额对比 -->
      <NGi>
        <NCard :bordered="false" class="h-420px">
          <template #header>
            <span class="text-16px font-semibold">{{ $t('page.sales.regionalSalesComparison') }}</span>
          </template>
          <div ref="regionalSalesChartRef" class="h-350px overflow-hidden"></div>
        </NCard>
      </NGi>

      <!-- 各地区销量对比 -->
      <NGi>
        <NCard :bordered="false" class="h-420px">
          <template #header>
            <span class="text-16px font-semibold">{{ $t('page.sales.regionalVolumeComparison') }}</span>
          </template>
          <div ref="regionalVolumeChartRef" class="h-350px overflow-hidden"></div>
        </NCard>
      </NGi>

      <!-- 地区销售增长率对比 -->
      <NGi>
        <NCard :bordered="false" class="h-420px">
          <template #header>
            <span class="text-16px font-semibold">{{ $t('page.sales.regionalGrowthRate') }}</span>
          </template>
          <div ref="regionalGrowthChartRef" class="h-350px overflow-hidden"></div>
        </NCard>
      </NGi>

      <!-- 地区客单价对比雷达图 -->
      <NGi>
        <NCard :bordered="false" class="h-420px">
          <template #header>
            <span class="text-16px font-semibold">{{ $t('page.sales.regionalAvgOrderValue') }}</span>
          </template>
          <div ref="regionalRadarChartRef" class="h-350px overflow-hidden"></div>
        </NCard>
      </NGi>
    </NGrid>
  </div>
</template>

<style scoped></style>
