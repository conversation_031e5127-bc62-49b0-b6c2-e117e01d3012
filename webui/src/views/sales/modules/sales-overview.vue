<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue';
import { useAppStore } from '@/store/modules/app';
import { useEcharts } from '@/hooks/common/echarts';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';

defineOptions({
  name: 'SalesOverview'
});

const appStore = useAppStore();
const isMobile = computed(() => appStore.isMobile);

const dateRange = ref<[number, number]>([Date.now() - 30 * 24 * 60 * 60 * 1000, Date.now()]);

const chartOptions = reactive({
  region: 'all',
  currency: 'all',
  orderStatus: 'all'
});

// 统一的配色方案
const colorScheme = {
  primary: '#2a4a67',
  success: '#52c41a',
  purple: '#722ed1',
  orange: '#fa8c16',
  red: '#ff4d4f',
  gray: '#8c8c8c'
};

// 模拟图表数据
const regionData = computed(() => [
  { name: $t('page.sales.northAmerica'), value: 35, color: colorScheme.primary },
  { name: $t('page.sales.europe'), value: 28, color: colorScheme.success },
  { name: $t('page.sales.asia'), value: 25, color: colorScheme.purple },
  { name: $t('page.sales.others'), value: 12, color: colorScheme.orange }
]);

const currencyData = computed(() => [
  { name: 'USD', value: 45, color: colorScheme.primary },
  { name: 'EUR', value: 25, color: colorScheme.success },
  { name: 'CNY', value: 20, color: colorScheme.purple },
  { name: $t('page.sales.others'), value: 10, color: colorScheme.orange }
]);

// 模拟日销售数据
const dailySalesData = computed(() => [
  { date: '01-01', sales: 12000 },
  { date: '01-02', sales: 15000 },
  { date: '01-03', sales: 18000 },
  { date: '01-04', sales: 14000 },
  { date: '01-05', sales: 22000 },
  { date: '01-06', sales: 19000 },
  { date: '01-07', sales: 25000 }
]);

// 模拟分时数据
const hourlyData = computed(() => [
  { time: '00:00', value: 120 },
  { time: '02:00', value: 80 },
  { time: '04:00', value: 60 },
  { time: '06:00', value: 100 },
  { time: '08:00', value: 180 },
  { time: '10:00', value: 220 },
  { time: '12:00', value: 280 },
  { time: '14:00', value: 320 },
  { time: '16:00', value: 290 },
  { time: '18:00', value: 250 },
  { time: '20:00', value: 200 },
  { time: '22:00', value: 150 }
]);

function handleDateRangeChange() {
  // TODO: 实现日期范围变更逻辑
}

function handleExportData() {
  // TODO: 实现数据导出逻辑
}

// 地区分布饼图
const { domRef: regionChartRef, updateOptions: updateRegionChart } = useEcharts(() => ({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c}% ({d}%)'
  },
  legend: {
    bottom: '8%',
    left: 'center',
    itemStyle: {
      borderWidth: 0
    },
    textStyle: {
      fontSize: 12
    }
  },
  series: [
    {
      name: $t('page.sales.regionDistribution'),
      type: 'pie',
      radius: ['35%', '60%'],
      center: ['50%', '40%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 6,
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '14',
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      data: [] as { name: string; value: number }[]
    }
  ]
}));

// 货币分布饼图
const { domRef: currencyChartRef, updateOptions: updateCurrencyChart } = useEcharts(() => ({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c}% ({d}%)'
  },
  legend: {
    bottom: '8%',
    left: 'center',
    itemStyle: {
      borderWidth: 0
    },
    textStyle: {
      fontSize: 12
    }
  },
  series: [
    {
      name: $t('page.sales.currencyDistribution'),
      type: 'pie',
      radius: ['35%', '60%'],
      center: ['50%', '40%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 6,
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '14',
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      data: [] as { name: string; value: number }[]
    }
  ]
}));

// 日销售柱状图
const { domRef: dailySalesChartRef, updateOptions: updateDailySalesChart } = useEcharts(() => ({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    },
    formatter: '{a} <br/>{b}: ${c}'
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: [] as string[],
    axisTick: {
      alignWithLabel: true
    }
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: '${value}'
    }
  },
  series: [
    {
      name: $t('page.sales.dailySales'),
      type: 'bar',
      barWidth: '60%',
      itemStyle: {
        borderRadius: [4, 4, 0, 0],
        color: colorScheme.primary
      },
      data: [] as number[]
    }
  ]
}));

// 分时曲线图
const { domRef: hourlyTrendChartRef, updateOptions: updateHourlyTrendChart } = useEcharts(() => ({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      label: {
        backgroundColor: '#6a7985'
      }
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: [] as string[]
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: $t('page.sales.hourlyTrend'),
      type: 'line',
      smooth: true,
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: colorScheme.primary
            },
            {
              offset: 1,
              color: 'rgba(0, 122, 255, 0.1)'
            }
          ]
        }
      },
      itemStyle: {
        color: colorScheme.primary
      },
      data: [] as number[]
    }
  ]
}));

// 初始化图表数据
async function initCharts() {
  await new Promise(resolve => {
    setTimeout(resolve, 1000);
  });

  // 更新地区分布饼图
  updateRegionChart(opts => {
    opts.series[0].data = regionData.value.map(item => ({
      name: item.name,
      value: item.value
    }));
    (opts as any).color = regionData.value.map(item => item.color);
    return opts;
  });

  // 更新货币分布饼图
  updateCurrencyChart(opts => {
    opts.series[0].data = currencyData.value.map(item => ({
      name: item.name,
      value: item.value
    }));
    (opts as any).color = currencyData.value.map(item => item.color);
    return opts;
  });

  // 更新日销售柱状图
  updateDailySalesChart(opts => {
    opts.xAxis.data = dailySalesData.value.map(item => item.date);
    opts.series[0].data = dailySalesData.value.map(item => item.sales);
    return opts;
  });

  // 更新分时曲线图
  updateHourlyTrendChart(opts => {
    opts.xAxis.data = hourlyData.value.map(item => item.time);
    opts.series[0].data = hourlyData.value.map(item => item.value);
    return opts;
  });
}

// 更新国际化
function updateLocale() {
  updateRegionChart((opts, factory) => {
    const originOpts = factory();
    opts.series[0].name = originOpts.series[0].name;
    opts.series[0].data = regionData.value.map(item => ({
      name: item.name,
      value: item.value
    }));
    return opts;
  });

  updateCurrencyChart((opts, factory) => {
    const originOpts = factory();
    opts.series[0].name = originOpts.series[0].name;
    opts.series[0].data = currencyData.value.map(item => ({
      name: item.name,
      value: item.value
    }));
    return opts;
  });

  updateDailySalesChart((opts, factory) => {
    const originOpts = factory();
    opts.series[0].name = originOpts.series[0].name;
    return opts;
  });

  updateHourlyTrendChart((opts, factory) => {
    const originOpts = factory();
    opts.series[0].name = originOpts.series[0].name;
    return opts;
  });
}

watch(
  () => appStore.locale,
  () => {
    updateLocale();
  }
);

// 初始化
initCharts();
</script>

<template>
  <div class="space-y-6">
    <!-- 紧凑筛选器 -->
    <div class="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
      <!-- 搜索字段和按钮 -->
      <div :class="isMobile ? 'space-y-3' : 'flex flex-wrap gap-4 items-end'">
        <div class="flex flex-col gap-1 min-w-64 flex-1">
          <span class="text-sm text-gray-600 dark:text-gray-300 font-medium">{{ $t('page.sales.timeRange') }}</span>
          <NDatePicker
            v-model:value="dateRange"
            type="daterange"
            size="small"
            clearable
            @update:value="handleDateRangeChange"
          />
        </div>

        <div class="flex flex-col gap-1 min-w-32 flex-1">
          <span class="text-sm text-gray-600 dark:text-gray-300 font-medium">{{ $t('page.sales.region') }}</span>
          <NSelect
            v-model:value="chartOptions.region"
            :placeholder="$t('page.sales.selectRegion')"
            :options="[
              { label: $t('page.sales.allRegions'), value: 'all' },
              { label: $t('page.sales.northAmerica'), value: 'na' },
              { label: $t('page.sales.europe'), value: 'eu' },
              { label: $t('page.sales.asia'), value: 'asia' }
            ]"
            size="small"
          />
        </div>

        <div class="flex flex-col gap-1 min-w-28 flex-1">
          <span class="text-sm text-gray-600 dark:text-gray-300 font-medium">{{ $t('page.sales.currency') }}</span>
          <NSelect
            v-model:value="chartOptions.currency"
            :placeholder="$t('page.sales.selectCurrency')"
            :options="[
              { label: $t('page.sales.allCurrencies'), value: 'all' },
              { label: 'USD', value: 'usd' },
              { label: 'EUR', value: 'eur' },
              { label: 'CNY', value: 'cny' }
            ]"
            size="small"
          />
        </div>

        <!-- 操作按钮 -->
        <div class="flex gap-2" :class="isMobile ? 'w-full justify-center' : 'flex-shrink-0'">
          <NButton type="primary" size="small" @click="handleExportData">
            <template #icon>
              <SvgIcon icon="mdi:download" />
            </template>
            {{ $t('page.sales.exportData') }}
          </NButton>
        </div>
      </div>
    </div>

    <!-- 饼图区域 -->
    <NGrid :cols="2" :x-gap="16" :y-gap="16">
      <NGi>
        <NCard :bordered="false" class="h-420px">
          <template #header>
            <span class="text-16px font-semibold">{{ $t('page.sales.regionDistribution') }}</span>
          </template>
          <div ref="regionChartRef" class="h-350px overflow-hidden"></div>
        </NCard>
      </NGi>
      <NGi>
        <NCard :bordered="false" class="h-420px">
          <template #header>
            <span class="text-16px font-semibold">{{ $t('page.sales.currencyDistribution') }}</span>
          </template>
          <div ref="currencyChartRef" class="h-350px overflow-hidden"></div>
        </NCard>
      </NGi>
    </NGrid>

    <!-- 日销售柱状图 -->
    <NCard :bordered="false">
      <template #header>
        <span class="text-16px font-semibold">{{ $t('page.sales.dailySales') }}</span>
      </template>
      <div ref="dailySalesChartRef" class="h-300px overflow-hidden"></div>
    </NCard>

    <!-- 分时曲线图 -->
    <NCard :bordered="false">
      <template #header>
        <span class="text-16px font-semibold">{{ $t('page.sales.hourlyTrend') }}</span>
      </template>
      <div ref="hourlyTrendChartRef" class="h-300px overflow-hidden"></div>
    </NCard>
  </div>
</template>

<style scoped></style>
