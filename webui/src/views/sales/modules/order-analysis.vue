<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue';
import { useAppStore } from '@/store/modules/app';
import { useEcharts } from '@/hooks/common/echarts';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';

defineOptions({
  name: 'OrderAnalysis'
});

const appStore = useAppStore();
const isMobile = computed(() => appStore.isMobile);

const dateRange = ref<[number, number]>([Date.now() - 30 * 24 * 60 * 60 * 1000, Date.now()]);

const chartOptions = reactive({
  region: 'all',
  currency: 'all'
});

// 统一的配色方案
const colorScheme = {
  primary: '#2a4a67',
  success: '#52c41a',
  purple: '#722ed1',
  orange: '#fa8c16',
  red: '#ff4d4f',
  gray: '#8c8c8c'
};

// 模拟订单状态数据
const orderStatusData = computed(() => [
  { name: $t('page.sales.completed'), value: 78, color: colorScheme.success },
  { name: $t('page.sales.processing'), value: 15, color: colorScheme.orange },
  { name: $t('page.sales.cancelled'), value: 5, color: colorScheme.red },
  { name: $t('page.sales.refunded'), value: 2, color: colorScheme.purple }
]);

// 模拟订单完成率趋势数据
const completionRateData = computed(() => [
  { date: '01-01', rate: 85 },
  { date: '01-02', rate: 88 },
  { date: '01-03', rate: 92 },
  { date: '01-04', rate: 87 },
  { date: '01-05', rate: 90 },
  { date: '01-06', rate: 89 },
  { date: '01-07', rate: 93 }
]);

// 模拟平均订单价值数据
const avgOrderValueData = computed(() => [
  { date: '01-01', value: 45.6 },
  { date: '01-02', value: 52.3 },
  { date: '01-03', value: 48.9 },
  { date: '01-04', value: 55.2 },
  { date: '01-05', value: 49.8 },
  { date: '01-06', value: 51.7 },
  { date: '01-07', value: 58.4 }
]);

// 模拟订单时段分布数据
const hourlyOrderData = computed(() => [
  { hour: '00', count: 12 },
  { hour: '02', count: 8 },
  { hour: '04', count: 6 },
  { hour: '06', count: 15 },
  { hour: '08', count: 28 },
  { hour: '10', count: 35 },
  { hour: '12', count: 42 },
  { hour: '14', count: 48 },
  { hour: '16', count: 38 },
  { hour: '18', count: 32 },
  { hour: '20', count: 25 },
  { hour: '22', count: 18 }
]);

function handleDateRangeChange() {
  // TODO: 实现日期范围变更逻辑
}

function handleExportData() {
  // TODO: 实现数据导出逻辑
}

// 订单状态分布饼图
const { domRef: orderStatusChartRef, updateOptions: updateOrderStatusChart } = useEcharts(() => ({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c}% ({d}%)'
  },
  legend: {
    bottom: '8%',
    left: 'center',
    itemStyle: {
      borderWidth: 0
    },
    textStyle: {
      fontSize: 12
    }
  },
  series: [
    {
      name: $t('page.sales.orderStatus'),
      type: 'pie',
      radius: ['35%', '60%'],
      center: ['50%', '40%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 6,
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '14',
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      data: [] as { name: string; value: number }[]
    }
  ]
}));

// 订单完成率趋势图
const { domRef: completionRateChartRef, updateOptions: updateCompletionRateChart } = useEcharts(() => ({
  tooltip: {
    trigger: 'axis',
    formatter: '{a} <br/>{b}: {c}%'
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: [] as string[]
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: '{value}%'
    }
  },
  series: [
    {
      name: $t('page.sales.orderCompletionRate'),
      type: 'line',
      smooth: true,
      itemStyle: {
        color: colorScheme.success
      },
      lineStyle: {
        color: colorScheme.success
      },
      data: [] as number[]
    }
  ]
}));

// 平均订单价值趋势图
const { domRef: avgOrderValueChartRef, updateOptions: updateAvgOrderValueChart } = useEcharts(() => ({
  tooltip: {
    trigger: 'axis',
    formatter: '{a} <br/>{b}: ${c}'
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: [] as string[]
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: '${value}'
    }
  },
  series: [
    {
      name: $t('page.sales.avgOrderValue'),
      type: 'line',
      smooth: true,
      itemStyle: {
        color: colorScheme.primary
      },
      lineStyle: {
        color: colorScheme.primary
      },
      data: [] as number[]
    }
  ]
}));

// 订单时段分布柱状图
const { domRef: hourlyOrderChartRef, updateOptions: updateHourlyOrderChart } = useEcharts(() => ({
  tooltip: {
    trigger: 'axis',
    formatter: (params: any) => {
      const param = Array.isArray(params) ? params[0] : params;
      return `${param.seriesName} <br/>${param.name}时: ${param.value}${$t('common.unit.orders')}`;
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: [] as string[]
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: (value: any) => `${value}${$t('common.unit.orders')}`
    }
  },
  series: [
    {
      name: $t('page.sales.orderVolume'),
      type: 'bar',
      barWidth: '60%',
      itemStyle: {
        borderRadius: [4, 4, 0, 0],
        color: colorScheme.purple
      },
      data: [] as number[]
    }
  ]
}));

// 初始化图表数据
async function initCharts() {
  await new Promise(resolve => {
    setTimeout(resolve, 1000);
  });

  // 更新订单状态分布饼图
  updateOrderStatusChart(opts => {
    opts.series[0].data = orderStatusData.value.map(item => ({
      name: item.name,
      value: item.value
    }));
    (opts as any).color = orderStatusData.value.map(item => item.color);
    return opts;
  });

  // 更新订单完成率趋势图
  updateCompletionRateChart(opts => {
    opts.xAxis.data = completionRateData.value.map(item => item.date);
    opts.series[0].data = completionRateData.value.map(item => item.rate);
    return opts;
  });

  // 更新平均订单价值趋势图
  updateAvgOrderValueChart(opts => {
    opts.xAxis.data = avgOrderValueData.value.map(item => item.date);
    opts.series[0].data = avgOrderValueData.value.map(item => item.value);
    return opts;
  });

  // 更新订单时段分布图
  updateHourlyOrderChart(opts => {
    opts.xAxis.data = hourlyOrderData.value.map(item => item.hour);
    opts.series[0].data = hourlyOrderData.value.map(item => item.count);
    return opts;
  });
}

// 更新国际化
function updateLocale() {
  updateOrderStatusChart((opts, factory) => {
    const originOpts = factory();
    opts.series[0].name = originOpts.series[0].name;
    opts.series[0].data = orderStatusData.value.map(item => ({
      name: item.name,
      value: item.value
    }));
    return opts;
  });

  updateCompletionRateChart((opts, factory) => {
    const originOpts = factory();
    opts.series[0].name = originOpts.series[0].name;
    return opts;
  });

  updateAvgOrderValueChart((opts, factory) => {
    const originOpts = factory();
    opts.series[0].name = originOpts.series[0].name;
    return opts;
  });

  updateHourlyOrderChart((opts, factory) => {
    const originOpts = factory();
    opts.series[0].name = originOpts.series[0].name;
    // 更新坐标轴单位
    (opts as any).yAxis.axisLabel.formatter = `{value}${$t('common.unit.orders')}`;
    // 更新tooltip格式
    (opts as any).tooltip.formatter = (params: any) => {
      const param = Array.isArray(params) ? params[0] : params;
      return `${param.seriesName} <br/>${param.name}时: ${param.value}${$t('common.unit.orders')}`;
    };
    return opts;
  });
}

watch(
  () => appStore.locale,
  () => {
    updateLocale();
  }
);

// 初始化
initCharts();
</script>

<template>
  <div class="space-y-6">
    <!-- 紧凑筛选器 -->
    <div class="rounded-lg bg-gray-50 p-4 dark:bg-gray-800">
      <div :class="isMobile ? 'space-y-3' : 'flex flex-wrap gap-4 items-end'">
        <div class="min-w-64 flex flex-col flex-1 gap-1">
          <span class="text-sm text-gray-600 font-medium dark:text-gray-300">{{ $t('page.sales.timeRange') }}</span>
          <NDatePicker
            v-model:value="dateRange"
            type="daterange"
            size="small"
            clearable
            @update:value="handleDateRangeChange"
          />
        </div>

        <div class="min-w-32 flex flex-col flex-1 gap-1">
          <span class="text-sm text-gray-600 font-medium dark:text-gray-300">{{ $t('page.sales.region') }}</span>
          <NSelect
            v-model:value="chartOptions.region"
            :placeholder="$t('page.sales.selectRegion')"
            :options="[
              { label: $t('page.sales.allRegions'), value: 'all' },
              { label: $t('page.sales.northAmerica'), value: 'na' },
              { label: $t('page.sales.europe'), value: 'eu' },
              { label: $t('page.sales.asia'), value: 'asia' }
            ]"
            size="small"
          />
        </div>

        <div class="min-w-28 flex flex-col flex-1 gap-1">
          <span class="text-sm text-gray-600 font-medium dark:text-gray-300">{{ $t('page.sales.currency') }}</span>
          <NSelect
            v-model:value="chartOptions.currency"
            :placeholder="$t('page.sales.selectCurrency')"
            :options="[
              { label: $t('page.sales.allCurrencies'), value: 'all' },
              { label: 'USD', value: 'usd' },
              { label: 'EUR', value: 'eur' },
              { label: 'CNY', value: 'cny' }
            ]"
            size="small"
          />
        </div>

        <div class="flex gap-2" :class="isMobile ? 'w-full justify-center' : 'flex-shrink-0'">
          <NButton type="primary" size="small" @click="handleExportData">
            <template #icon>
              <SvgIcon icon="mdi:download" />
            </template>
            {{ $t('page.sales.exportData') }}
          </NButton>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <NGrid :cols="2" :x-gap="16" :y-gap="16">
      <!-- 订单状态分布 -->
      <NGi>
        <NCard :bordered="false" class="h-420px">
          <template #header>
            <span class="text-16px font-semibold">{{ $t('page.sales.orderStatus') }}</span>
          </template>
          <div ref="orderStatusChartRef" class="h-350px overflow-hidden"></div>
        </NCard>
      </NGi>

      <!-- 订单完成率趋势 -->
      <NGi>
        <NCard :bordered="false" class="h-420px">
          <template #header>
            <span class="text-16px font-semibold">{{ $t('page.sales.orderCompletionRate') }}</span>
          </template>
          <div ref="completionRateChartRef" class="h-350px overflow-hidden"></div>
        </NCard>
      </NGi>

      <!-- 平均订单价值趋势 -->
      <NGi>
        <NCard :bordered="false" class="h-420px">
          <template #header>
            <span class="text-16px font-semibold">{{ $t('page.sales.avgOrderValue') }}</span>
          </template>
          <div ref="avgOrderValueChartRef" class="h-350px overflow-hidden"></div>
        </NCard>
      </NGi>

      <!-- 订单时段分布 -->
      <NGi>
        <NCard :bordered="false" class="h-420px">
          <template #header>
            <span class="text-16px font-semibold">{{ $t('page.sales.orderTimeDistribution') }}</span>
          </template>
          <div ref="hourlyOrderChartRef" class="h-350px overflow-hidden"></div>
        </NCard>
      </NGi>
    </NGrid>
  </div>
</template>

<style scoped></style>
