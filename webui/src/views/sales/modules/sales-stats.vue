<script setup lang="tsx">
import { $t } from '@/locales';
import StatsCard from '@/components/custom/stats-card.vue';
import StatsGrid from '@/components/custom/stats-grid.vue';

defineOptions({
  name: 'SalesStats'
});
</script>

<template>
  <StatsGrid>
    <StatsCard
      :title="$t('page.sales.totalSales')"
      :value="2456789"
      unit="$"
      :unit-as-prefix="true"
      :color="{ start: '#007aff', end: '#0056cc' }"
      icon="mdi:currency-usd"
    />
    <StatsCard
      :title="$t('page.sales.totalOrders')"
      :value="15678"
      :unit="$t('common.unit.orders')"
      :color="{ start: '#52c41a', end: '#389e0d' }"
      icon="mdi:receipt"
    />
    <StatsCard
      :title="$t('page.sales.todaySales')"
      :value="12456"
      unit="$"
      :unit-as-prefix="true"
      :color="{ start: '#722ed1', end: '#531dab' }"
      icon="mdi:trending-up"
    />
    <StatsCard
      :title="$t('page.sales.monthSales')"
      :value="456789"
      unit="$"
      :unit-as-prefix="true"
      :color="{ start: '#fa8c16', end: '#d46b08' }"
      icon="mdi:calendar-month"
    />
  </StatsGrid>
</template>
