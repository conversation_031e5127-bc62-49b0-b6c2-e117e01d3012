<script setup lang="ts">
import { computed, ref } from 'vue';
import { useAppStore } from '@/store/modules/app';
import GameStatsDrawer from '@/views/game/modules/game-stats-drawer.vue';
import TopSalesRanking from './top-sales-ranking.vue';
import TopRefundRanking from './top-refund-ranking.vue';

defineOptions({
  name: 'TopRanking'
});

const appStore = useAppStore();

const gap = computed(() => (appStore.isMobile ? 0 : 16));

// 游戏数据接口（与GameStatsDrawer兼容）
interface GameData {
  id: string;
  coverImage: string;
  name: string;
  code: string;
  multiLangName: Record<string, string>;
  languages: string[];
  onHand: number;
  enabled: boolean;
  price: number;
  sales: number;
}

// 游戏统计抽屉相关
const gameStatsDrawerVisible = ref(false);
const selectedGameForStats = ref<GameData | null>(null);

// 处理游戏点击事件
function handleGameClick(game: GameData) {
  selectedGameForStats.value = game;
  gameStatsDrawerVisible.value = true;
}
</script>

<template>
  <div>
    <NSpace vertical :size="16">
      <!-- TOP10排行榜 -->
      <NGrid :x-gap="gap" :y-gap="16" responsive="screen" item-responsive>
        <!-- 销售单数TOP10 -->
        <NGi span="24 s:24 m:12">
          <NCard :bordered="false" class="card-wrapper">
            <TopSalesRanking @game-click="handleGameClick" />
          </NCard>
        </NGi>
        <!-- 退单单数TOP10 -->
        <NGi span="24 s:24 m:12">
          <NCard :bordered="false" class="card-wrapper">
            <TopRefundRanking @game-click="handleGameClick" />
          </NCard>
        </NGi>
      </NGrid>
    </NSpace>

    <!-- 游戏统计抽屉 -->
    <GameStatsDrawer
      v-model:visible="gameStatsDrawerVisible"
      :game="selectedGameForStats"
    />
  </div>
</template>

<style scoped></style>
