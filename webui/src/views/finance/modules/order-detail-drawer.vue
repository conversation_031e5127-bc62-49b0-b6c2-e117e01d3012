<script setup lang="tsx">
import { computed, h, onMounted, ref, withDefaults } from 'vue';
import type { DataTableColumns } from 'naive-ui';
import {
  NCard,
  NDataTable,
  NDescriptions,
  NDescriptionsItem,
  NDivider,
  NGrid,
  NGridItem,
  NSpace,
  NSpin,
  NStatistic,
  NTag
} from 'naive-ui';
import { useAppStore } from '@/store/modules/app';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';

defineOptions({
  name: 'OrderDetailDrawer'
});

interface Props {
  orderId: string;
}

interface OrderItem {
  id: string;
  name: string;
  type: 'game' | 'dlc' | 'expansion';
  version: string;
  category: string;
  quantity: number;
  originalPrice: number;
  discount: number;
  finalPrice: number;
  totalAmount: number;
}

interface OrderData {
  id: string;
  orderDate: string;
  buyerName: string;
  distributor: string;
  gameName: string;
  quantity: number;
  originalPrice: number;
  discount: number;
  finalPrice: number;
  orderAmount: number;
  orderStatus: 'completed' | 'processing' | 'cancelled' | 'refunded';
  // 扩展字段用于详情显示
  buyerEmail?: string;
  buyerPhone?: string;
  shippingAddress?: string;
  paymentMethod?: string;
  transactionId?: string;
  gameVersion?: string;
  gameCategory?: string;
  orderNotes?: string;
  createdAt?: string;
  updatedAt?: string;
  // 订单子项目
  items?: OrderItem[];
}

interface Emits {
  (e: 'close'): void;
}

const props = withDefaults(defineProps<Props>(), {});
const emit = defineEmits<Emits>();

const appStore = useAppStore();
const isMobile = computed(() => appStore.isMobile);

const loading = ref(true);
const orderData = ref<OrderData | null>(null);

// 隐私处理函数
function maskEmail(email: string): string {
  if (!email) return '';
  const [username, domain] = email.split('@');
  if (username.length <= 2) return email;
  const maskedUsername = username.charAt(0) + '*'.repeat(username.length - 2) + username.charAt(username.length - 1);
  return `${maskedUsername}@${domain}`;
}

function maskPhone(phone: string): string {
  if (!phone) return '';
  // 保留前3位和后4位，中间用*替换
  if (phone.length <= 7) return phone;
  const start = phone.substring(0, 3);
  const end = phone.substring(phone.length - 4);
  const middle = '*'.repeat(phone.length - 7);
  return `${start}${middle}${end}`;
}

function maskAddress(address: string): string {
  if (!address) return '';
  // 只显示城市和国家，具体地址用*替换
  const parts = address.split(',');
  if (parts.length < 2) return address;
  const city = parts[parts.length - 2]?.trim();
  const country = parts[parts.length - 1]?.trim();
  return `*****, ${city}, ${country}`;
}

// Mock 订单详情数据
const mockOrderDetails: Record<string, OrderData> = {
  ORD001: {
    id: 'ORD001',
    orderDate: '2024-01-15',
    buyerName: 'John Smith',
    distributor: 'TechCorp Solutions',
    gameName: 'Mystic Adventure - Standard Edition',
    quantity: 1,
    originalPrice: 29.99,
    discount: 0.1,
    finalPrice: 26.99,
    orderAmount: 26.99,
    orderStatus: 'completed',
    buyerEmail: '<EMAIL>',
    buyerPhone: '******-0123',
    shippingAddress: '123 Main St, New York, NY 10001, USA',
    paymentMethod: 'Credit Card',
    transactionId: 'TXN-20240115-001',
    gameVersion: 'Standard Edition',
    gameCategory: 'Adventure',
    orderNotes: 'Customer requested expedited shipping',
    createdAt: '2024-01-15 10:30:00',
    updatedAt: '2024-01-15 14:45:00',
    items: [
      {
        id: 'ITEM001',
        name: 'Mystic Adventure - Standard Edition',
        type: 'game',
        version: 'Standard Edition',
        category: 'Adventure',
        quantity: 1,
        originalPrice: 29.99,
        discount: 0.1,
        finalPrice: 26.99,
        totalAmount: 26.99
      }
    ]
  },
  ORD002: {
    id: 'ORD002',
    orderDate: '2024-01-16',
    buyerName: 'Emily Johnson',
    distributor: 'Digital Innovations Ltd',
    gameName: 'Space Odyssey - Deluxe Edition',
    quantity: 2,
    originalPrice: 39.99,
    discount: 0.15,
    finalPrice: 33.99,
    orderAmount: 67.98,
    orderStatus: 'completed',
    buyerEmail: '<EMAIL>',
    buyerPhone: '******-0456',
    shippingAddress: '456 Oak Ave, Los Angeles, CA 90210, USA',
    paymentMethod: 'PayPal',
    transactionId: 'TXN-20240116-002',
    gameVersion: 'Deluxe Edition',
    gameCategory: 'Sci-Fi',
    orderNotes: 'Gift order - include gift message',
    createdAt: '2024-01-16 09:15:00',
    updatedAt: '2024-01-16 16:20:00',
    items: [
      {
        id: 'ITEM002-1',
        name: 'Space Odyssey - Deluxe Edition',
        type: 'game',
        version: 'Deluxe Edition',
        category: 'Sci-Fi',
        quantity: 1,
        originalPrice: 39.99,
        discount: 0.15,
        finalPrice: 33.99,
        totalAmount: 33.99
      },
      {
        id: 'ITEM002-2',
        name: 'Space Odyssey - Season Pass',
        type: 'dlc',
        version: 'DLC Pack',
        category: 'Sci-Fi',
        quantity: 1,
        originalPrice: 19.99,
        discount: 0.15,
        finalPrice: 16.99,
        totalAmount: 16.99
      },
      {
        id: 'ITEM002-3',
        name: 'Space Odyssey - Soundtrack',
        type: 'dlc',
        version: 'Digital Soundtrack',
        category: 'Music',
        quantity: 1,
        originalPrice: 9.99,
        discount: 0.15,
        finalPrice: 8.49,
        totalAmount: 8.49
      },
      {
        id: 'ITEM002-4',
        name: 'Space Odyssey - Art Book',
        type: 'dlc',
        version: 'Digital Art Book',
        category: 'Art',
        quantity: 1,
        originalPrice: 12.99,
        discount: 0.15,
        finalPrice: 11.04,
        totalAmount: 11.04
      }
    ]
  },
  ORD003: {
    id: 'ORD003',
    orderDate: '2024-01-17',
    buyerName: 'Michael Chen',
    distributor: 'GameWorld Distribution',
    gameName: "Fantasy Adventure - Collector's Edition",
    quantity: 1,
    originalPrice: 89.99,
    discount: 0.05,
    finalPrice: 85.49,
    orderAmount: 85.49,
    orderStatus: 'processing',
    buyerEmail: '<EMAIL>',
    buyerPhone: '******-0789',
    shippingAddress: '789 Pine St, Chicago, IL 60601, USA',
    paymentMethod: 'Bank Transfer',
    transactionId: 'TXN-********-003',
    gameVersion: "Collector's Edition",
    gameCategory: 'Fantasy',
    orderNotes: 'Special packaging requested',
    createdAt: '2024-01-17 11:45:00',
    updatedAt: '2024-01-17 11:45:00',
    items: [
      {
        id: 'ITEM003-1',
        name: "Fantasy Adventure - Collector's Edition",
        type: 'game',
        version: "Collector's Edition",
        category: 'Fantasy',
        quantity: 1,
        originalPrice: 89.99,
        discount: 0.05,
        finalPrice: 85.49,
        totalAmount: 85.49
      }
    ]
  },
  ORD004: {
    id: 'ORD004',
    orderDate: '2024-01-18',
    buyerName: 'Sarah Williams',
    distributor: 'TechCorp Solutions',
    gameName: 'Speed Rush - Standard Edition',
    quantity: 1,
    originalPrice: 19.99,
    discount: 0.2,
    finalPrice: 15.99,
    orderAmount: 15.99,
    orderStatus: 'completed',
    buyerEmail: '<EMAIL>',
    buyerPhone: '******-0321',
    shippingAddress: '321 Elm St, Miami, FL 33101, USA',
    paymentMethod: 'Credit Card',
    transactionId: 'TXN-20240118-004',
    gameVersion: 'Standard Edition',
    gameCategory: 'Racing',
    orderNotes: 'Customer requested digital delivery',
    createdAt: '2024-01-18 14:20:00',
    updatedAt: '2024-01-18 16:30:00'
  },
  ORD005: {
    id: 'ORD005',
    orderDate: '2024-01-19',
    buyerName: 'David Brown',
    distributor: 'Retro Gaming Hub',
    gameName: 'Magic Academy - Standard Edition',
    quantity: 3,
    originalPrice: 24.99,
    discount: 0.0,
    finalPrice: 24.99,
    orderAmount: 74.97,
    orderStatus: 'cancelled',
    buyerEmail: '<EMAIL>',
    buyerPhone: '******-0654',
    shippingAddress: '654 Maple Ave, Seattle, WA 98101, USA',
    paymentMethod: 'PayPal',
    transactionId: 'TXN-20240119-005',
    gameVersion: 'Standard Edition',
    gameCategory: 'RPG',
    orderNotes: 'Order cancelled by customer request',
    createdAt: '2024-01-19 09:30:00',
    updatedAt: '2024-01-19 10:15:00',
    items: [
      {
        id: 'ITEM005-1',
        name: 'Magic Academy - Standard Edition',
        type: 'game',
        version: 'Standard Edition',
        category: 'RPG',
        quantity: 2,
        originalPrice: 24.99,
        discount: 0.0,
        finalPrice: 24.99,
        totalAmount: 49.98
      },
      {
        id: 'ITEM005-2',
        name: 'Retro Classics Collection',
        type: 'game',
        version: 'Bundle Edition',
        category: 'Retro',
        quantity: 1,
        originalPrice: 24.99,
        discount: 0.0,
        finalPrice: 24.99,
        totalAmount: 24.99
      }
    ]
  },
  ORD006: {
    id: 'ORD006',
    orderDate: '2024-01-20',
    buyerName: 'Lisa Garcia',
    distributor: 'Digital Innovations Ltd',
    gameName: 'Mystic Adventure - Deluxe Edition',
    quantity: 1,
    originalPrice: 49.99,
    discount: 0.25,
    finalPrice: 37.49,
    orderAmount: 37.49,
    orderStatus: 'completed',
    buyerEmail: '<EMAIL>',
    buyerPhone: '******-0987',
    shippingAddress: '987 Oak Blvd, Austin, TX 73301, USA',
    paymentMethod: 'Credit Card',
    transactionId: 'TXN-20240120-006',
    gameVersion: 'Deluxe Edition',
    gameCategory: 'Adventure',
    orderNotes: 'VIP customer - priority shipping',
    createdAt: '2024-01-20 11:00:00',
    updatedAt: '2024-01-20 13:45:00'
  },
  ORD007: {
    id: 'ORD007',
    orderDate: '2024-01-21',
    buyerName: 'Robert Taylor',
    distributor: 'GameWorld Distribution',
    gameName: 'Galactic Warfare - Commander Edition',
    quantity: 1,
    originalPrice: 59.99,
    discount: 0.12,
    finalPrice: 52.79,
    orderAmount: 52.79,
    orderStatus: 'refunded',
    buyerEmail: '<EMAIL>',
    buyerPhone: '******-0147',
    shippingAddress: '147 Cedar St, Denver, CO 80201, USA',
    paymentMethod: 'Bank Transfer',
    transactionId: 'TXN-********-007',
    gameVersion: 'Commander Edition',
    gameCategory: 'Strategy',
    orderNotes: 'Refund processed due to technical issues',
    createdAt: '2024-01-21 15:30:00',
    updatedAt: '2024-01-21 17:20:00'
  },
  ORD008: {
    id: 'ORD008',
    orderDate: '2024-01-22',
    buyerName: 'Jennifer Davis',
    distributor: 'TechCorp Solutions',
    gameName: 'Rhythm Master - Musician Edition',
    quantity: 2,
    originalPrice: 19.99,
    discount: 0.08,
    finalPrice: 18.39,
    orderAmount: 36.78,
    orderStatus: 'processing',
    buyerEmail: '<EMAIL>',
    buyerPhone: '******-0258',
    shippingAddress: '258 Birch Ln, Portland, OR 97201, USA',
    paymentMethod: 'PayPal',
    transactionId: 'TXN-********-008',
    gameVersion: 'Musician Edition',
    gameCategory: 'Music',
    orderNotes: 'Customer requested express delivery',
    createdAt: '2024-01-22 08:45:00',
    updatedAt: '2024-01-22 08:45:00'
  }
};

// 状态映射
const statusMap = {
  completed: { type: 'success', text: $t('page.finance.completed'), color: '#52c41a' },
  processing: { type: 'warning', text: $t('page.finance.processing'), color: '#faad14' },
  cancelled: { type: 'error', text: $t('page.finance.cancelled'), color: '#ff4d4f' },
  refunded: { type: 'default', text: $t('page.finance.refunded'), color: '#d9d9d9' }
};

// 项目类型映射
const itemTypeMap = {
  game: { text: $t('page.finance.game') },
  dlc: { text: $t('page.finance.dlc') },
  expansion: { text: $t('page.finance.expansion') }
};

// 订单子项目表格列定义
const itemColumns = computed<DataTableColumns<OrderItem>>(() => [
  {
    title: $t('page.finance.itemName'),
    key: 'name',
    width: 200,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: $t('page.finance.itemType'),
    key: 'type',
    width: 80,
    render: row => {
      const typeInfo = itemTypeMap[row.type];
      return h(NTag, { type: 'info', size: 'small' }, { default: () => typeInfo.text });
    }
  },
  {
    title: $t('page.finance.itemVersion'),
    key: 'version',
    width: 120,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: $t('page.finance.itemCategory'),
    key: 'category',
    width: 100
  },
  {
    title: $t('page.finance.itemQuantity'),
    key: 'quantity',
    width: 80,
    render: row => h('span', { class: 'text-center font-medium' }, row.quantity.toString())
  },
  {
    title: $t('page.finance.itemOriginalPrice'),
    key: 'originalPrice',
    width: 100,
    render: row => h('span', { class: 'text-gray-500' }, `$${row.originalPrice.toFixed(2)}`)
  },
  {
    title: $t('page.finance.itemDiscount'),
    key: 'discount',
    width: 80,
    render: row => {
      const discountPercent = (row.discount * 100).toFixed(0);
      return h(
        'span',
        {
          class: row.discount > 0 ? 'font-medium text-red-500' : 'text-gray-400'
        },
        row.discount > 0 ? `-${discountPercent}%` : '0%'
      );
    }
  },
  {
    title: $t('page.finance.itemFinalPrice'),
    key: 'finalPrice',
    width: 100,
    render: row => h('span', { class: 'font-medium text-green-600' }, `$${row.finalPrice.toFixed(2)}`)
  },
  {
    title: $t('page.finance.itemTotalAmount'),
    key: 'totalAmount',
    width: 120,
    render: row => h('span', { class: 'font-semibold text-primary' }, `$${row.totalAmount.toFixed(2)}`)
  }
]);

// 获取订单详情
async function fetchOrderDetail() {
  loading.value = true;
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 800));

    const detail = mockOrderDetails[props.orderId];
    if (detail) {
      orderData.value = detail;
    } else {
      // 如果没有找到详情，使用基础数据
      orderData.value = {
        id: props.orderId,
        orderDate: '2024-01-01',
        buyerName: 'Unknown',
        distributor: 'Unknown',
        gameName: 'Unknown Game',
        quantity: 0,
        originalPrice: 0,
        discount: 0,
        finalPrice: 0,
        orderAmount: 0,
        orderStatus: 'processing'
      };
    }
  } catch (error) {
    console.error('Failed to fetch order detail:', error);
  } finally {
    loading.value = false;
  }
}

onMounted(() => {
  fetchOrderDetail();
});
</script>

<template>
  <NSpin :show="loading">
    <div v-if="orderData" class="space-y-4">
      <!-- 订单基本信息 -->
      <NCard :title="$t('page.finance.orderBasicInfo')" :bordered="false" size="small" class="order-info-card">
        <template #header-extra>
          <NTag :type="statusMap[orderData.orderStatus].type as any" size="medium">
            {{ statusMap[orderData.orderStatus].text }}
          </NTag>
        </template>

        <NDescriptions :column="isMobile ? 1 : 2" label-placement="left" size="small">
          <NDescriptionsItem :label="$t('page.finance.orderId')">
            <span class="text-primary font-mono">{{ orderData.id }}</span>
          </NDescriptionsItem>
          <NDescriptionsItem :label="$t('page.finance.orderDate')">
            {{ orderData.orderDate }}
          </NDescriptionsItem>
          <NDescriptionsItem :label="$t('page.finance.distributor')">
            {{ orderData.distributor }}
          </NDescriptionsItem>
          <NDescriptionsItem v-if="orderData.transactionId" :label="$t('page.finance.transactionId')">
            <span class="font-mono">{{ orderData.transactionId }}</span>
          </NDescriptionsItem>
          <NDescriptionsItem v-if="orderData.createdAt" :label="$t('page.finance.createdAt')">
            {{ orderData.createdAt }}
          </NDescriptionsItem>
          <NDescriptionsItem v-if="orderData.updatedAt" :label="$t('page.finance.updatedAt')">
            {{ orderData.updatedAt }}
          </NDescriptionsItem>
        </NDescriptions>
      </NCard>

      <!-- 购买者信息 -->
      <NCard :title="$t('page.finance.buyerInfo')" :bordered="false" size="small" class="buyer-info-card">
        <NDescriptions :column="isMobile ? 1 : 2" label-placement="left" size="small">
          <NDescriptionsItem :label="$t('page.finance.buyerName')">
            <div class="flex items-center gap-2">
              <SvgIcon icon="mdi:account" class="text-primary" />
              <span class="font-medium">{{ orderData.buyerName }}</span>
            </div>
          </NDescriptionsItem>
          <NDescriptionsItem v-if="orderData.buyerEmail" :label="$t('page.finance.buyerEmail')">
            <div class="flex items-center gap-2">
              <SvgIcon icon="mdi:email" class="text-info" />
              <span class="text-sm font-mono">{{ maskEmail(orderData.buyerEmail) }}</span>
            </div>
          </NDescriptionsItem>
          <NDescriptionsItem v-if="orderData.buyerPhone" :label="$t('page.finance.buyerPhone')">
            <div class="flex items-center gap-2">
              <SvgIcon icon="mdi:phone" class="text-success" />
              <span class="text-sm font-mono">{{ maskPhone(orderData.buyerPhone) }}</span>
            </div>
          </NDescriptionsItem>
          <NDescriptionsItem v-if="orderData.shippingAddress" :label="$t('page.finance.shippingAddress')" :span="2">
            <div class="flex items-start gap-2">
              <SvgIcon icon="mdi:map-marker" class="mt-1 text-warning" />
              <span class="text-sm">{{ maskAddress(orderData.shippingAddress) }}</span>
            </div>
          </NDescriptionsItem>
        </NDescriptions>
      </NCard>

      <!-- 游戏信息 -->
      <NCard :title="$t('page.finance.gameInfo')" :bordered="false" size="small" class="game-info-card">
        <NDescriptions :column="isMobile ? 1 : 2" label-placement="left" size="small">
          <NDescriptionsItem :label="$t('page.finance.gameName')" :span="2">
            <div class="flex items-center gap-2">
              <SvgIcon icon="mdi:gamepad-variant" class="text-primary" />
              <span class="font-medium">{{ orderData.gameName }}</span>
            </div>
          </NDescriptionsItem>
          <NDescriptionsItem v-if="orderData.gameVersion" :label="$t('page.finance.gameVersion')">
            {{ orderData.gameVersion }}
          </NDescriptionsItem>
          <NDescriptionsItem v-if="orderData.gameCategory" :label="$t('page.finance.gameCategory')">
            <NTag size="small" type="info">{{ orderData.gameCategory }}</NTag>
          </NDescriptionsItem>
          <NDescriptionsItem :label="$t('page.finance.quantity')">
            <span class="text-primary font-medium">{{ orderData.quantity }}</span>
          </NDescriptionsItem>
        </NDescriptions>
      </NCard>

      <!-- 价格信息 -->
      <NCard :title="$t('page.finance.priceInfo')" :bordered="false" size="small" class="price-info-card">
        <NGrid :cols="isMobile ? 2 : 4" :x-gap="16" :y-gap="16">
          <NGridItem>
            <NStatistic :label="$t('page.finance.originalPrice')" :value="orderData.originalPrice" :precision="2">
              <template #prefix>$</template>
            </NStatistic>
          </NGridItem>
          <NGridItem>
            <NStatistic
              :label="$t('page.finance.discount')"
              :value="orderData.discount * 100"
              :precision="0"
              :value-style="{ color: orderData.discount > 0 ? '#ff4d4f' : '#d9d9d9' }"
            >
              <template #suffix>%</template>
            </NStatistic>
          </NGridItem>
          <NGridItem>
            <NStatistic
              :label="$t('page.finance.finalPrice')"
              :value="orderData.finalPrice"
              :precision="2"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>$</template>
            </NStatistic>
          </NGridItem>
          <NGridItem>
            <NStatistic
              :label="$t('page.finance.orderAmount')"
              :value="orderData.orderAmount"
              :precision="2"
              :value-style="{ color: '#1890ff', fontWeight: 'bold' }"
            >
              <template #prefix>$</template>
            </NStatistic>
          </NGridItem>
        </NGrid>
      </NCard>

      <!-- 支付信息 -->
      <NCard
        v-if="orderData.paymentMethod"
        :title="$t('page.finance.paymentInfo')"
        :bordered="false"
        size="small"
        class="payment-info-card"
      >
        <NDescriptions :column="isMobile ? 1 : 2" label-placement="left" size="small">
          <NDescriptionsItem :label="$t('page.finance.paymentMethod')">
            <div class="flex items-center gap-2">
              <SvgIcon icon="mdi:credit-card" class="text-success" />
              <span>{{ orderData.paymentMethod }}</span>
            </div>
          </NDescriptionsItem>
          <NDescriptionsItem v-if="orderData.transactionId" :label="$t('page.finance.transactionId')">
            <span class="text-info font-mono">{{ orderData.transactionId }}</span>
          </NDescriptionsItem>
        </NDescriptions>
      </NCard>

      <!-- 订单子项目 -->
      <NCard
        v-if="orderData.items && orderData.items.length > 0"
        :title="$t('page.finance.orderItems')"
        :bordered="false"
        size="small"
        class="items-card mt-4"
      >
        <NDataTable
          :columns="itemColumns"
          :data="orderData.items"
          :pagination="false"
          :bordered="false"
          size="small"
          :scroll-x="900"
          class="order-items-table"
        />
      </NCard>

      <!-- 订单备注 -->
      <NCard
        v-if="orderData.orderNotes"
        :title="$t('page.finance.orderNotes')"
        :bordered="false"
        size="small"
        class="notes-card"
      >
        <div class="flex items-start gap-2">
          <SvgIcon icon="mdi:note-text" class="mt-1 text-warning" />
          <span class="text-gray-600 dark:text-gray-300">{{ orderData.orderNotes }}</span>
        </div>
      </NCard>
    </div>
  </NSpin>
</template>

<style scoped>
/* 订单基本信息卡片 - 蓝色主题 */
.order-info-card {
  border-left: 4px solid #1890ff;
}

/* 购买者信息卡片 - 绿色主题 */
.buyer-info-card {
  border-left: 4px solid #52c41a;
}

/* 游戏信息卡片 - 紫色主题 */
.game-info-card {
  border-left: 4px solid #722ed1;
}

/* 价格信息卡片 - 橙色主题 */
.price-info-card {
  border-left: 4px solid #fa8c16;
}

/* 支付信息卡片 - 青色主题 */
.payment-info-card {
  border-left: 4px solid #13c2c2;
}

/* 订单子项目卡片 - 紫红色主题 */
.items-card {
  border-left: 4px solid #eb2f96;
}

/* 备注卡片 - 灰色主题 */
.notes-card {
  border-left: 4px solid #8c8c8c;
}

/* 暗色主题适配 - 保持简洁，只需要左侧颜色条 */

/* 卡片标题加粗 */
.order-info-card :deep(.n-card-header__main),
.buyer-info-card :deep(.n-card-header__main),
.game-info-card :deep(.n-card-header__main),
.price-info-card :deep(.n-card-header__main),
.payment-info-card :deep(.n-card-header__main),
.items-card :deep(.n-card-header__main),
.notes-card :deep(.n-card-header__main) {
  font-weight: 600;
}
</style>
