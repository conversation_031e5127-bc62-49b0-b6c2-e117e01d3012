<script setup lang="tsx">
import { computed, h, reactive, ref, watch } from 'vue';
import type { DataTableColumns, DataTableRowKey } from 'naive-ui';
import { NButton, NDataTable, NDatePicker, NInput, NModal, NSelect, NSpace, NTag } from 'naive-ui';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';

defineOptions({
  name: 'OrderSearchModal'
});

interface OrderData {
  id: string;
  orderDate: string;
  buyerName: string;
  buyerCountry: string;
  currency: string;
  distributor: string;
  gameName: string;
  quantity: number;
  originalPrice: number;
  discount: number;
  finalPrice: number;
  orderAmount: number;
  orderStatus: 'completed' | 'processing' | 'cancelled' | 'refunded';
}

interface Props {
  show?: boolean;
  excludedOrderIds?: string[];
}

interface Emits {
  (e: 'update:show', show: boolean): void;
  (e: 'ordersSelected', orders: OrderData[]): void;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  excludedOrderIds: () => []
});

const emit = defineEmits<Emits>();

const visible = computed({
  get: () => props.show,
  set: (value: boolean) => emit('update:show', value)
});

const searchForm = reactive({
  timeFilter: 'thisMonth',
  customDateRange: null as [number, number] | null,
  distributor: '',
  orderId: '',
  gameName: '',
  buyerCountry: '',
  currency: '',
  orderStatus: 'completed'
});

const loading = ref(false);
const checkedRowKeys = ref<DataTableRowKey[]>([]);

// Mock order data - 与订单列表组件相同的数据
const allOrderData = ref<OrderData[]>([
  {
    id: 'ORD001',
    orderDate: '2024-01-15',
    buyerName: 'John Smith',
    buyerCountry: 'US',
    currency: 'USD',
    distributor: 'TechCorp Solutions',
    gameName: 'Mystic Adventure - Standard Edition',
    quantity: 1,
    originalPrice: 29.99,
    discount: 0.1,
    finalPrice: 26.99,
    orderAmount: 26.99,
    orderStatus: 'completed'
  },
  {
    id: 'ORD002',
    orderDate: '2024-01-16',
    buyerName: 'Emily Johnson',
    buyerCountry: 'CA',
    currency: 'CAD',
    distributor: 'Digital Innovations Ltd',
    gameName: 'Space Odyssey - Deluxe Edition',
    quantity: 2,
    originalPrice: 39.99,
    discount: 0.15,
    finalPrice: 33.99,
    orderAmount: 67.98,
    orderStatus: 'completed'
  },
  {
    id: 'ORD003',
    orderDate: '2024-01-17',
    buyerName: 'Michael Chen',
    buyerCountry: 'CN',
    currency: 'CNY',
    distributor: 'GameWorld Distribution',
    gameName: "Fantasy Adventure - Collector's Edition",
    quantity: 1,
    originalPrice: 89.99,
    discount: 0.05,
    finalPrice: 85.49,
    orderAmount: 85.49,
    orderStatus: 'processing'
  },
  {
    id: 'ORD004',
    orderDate: '2024-01-18',
    buyerName: 'Sarah Williams',
    buyerCountry: 'GB',
    currency: 'GBP',
    distributor: 'TechCorp Solutions',
    gameName: 'Speed Rush - Standard Edition',
    quantity: 1,
    originalPrice: 19.99,
    discount: 0.2,
    finalPrice: 15.99,
    orderAmount: 15.99,
    orderStatus: 'completed'
  },
  {
    id: 'ORD005',
    orderDate: '2024-01-19',
    buyerName: 'David Brown',
    buyerCountry: 'AU',
    currency: 'AUD',
    distributor: 'Retro Gaming Hub',
    gameName: 'Magic Academy - Standard Edition',
    quantity: 3,
    originalPrice: 24.99,
    discount: 0.0,
    finalPrice: 24.99,
    orderAmount: 74.97,
    orderStatus: 'cancelled'
  },
  {
    id: 'ORD006',
    orderDate: '2024-01-20',
    buyerName: 'Lisa Garcia',
    buyerCountry: 'ES',
    currency: 'EUR',
    distributor: 'Digital Innovations Ltd',
    gameName: 'Mystic Adventure - Deluxe Edition',
    quantity: 1,
    originalPrice: 49.99,
    discount: 0.25,
    finalPrice: 37.49,
    orderAmount: 37.49,
    orderStatus: 'completed'
  },
  {
    id: 'ORD007',
    orderDate: '2024-01-21',
    buyerName: 'Robert Taylor',
    buyerCountry: 'JP',
    currency: 'JPY',
    distributor: 'GameWorld Distribution',
    gameName: 'Galactic Warfare - Commander Edition',
    quantity: 1,
    originalPrice: 59.99,
    discount: 0.12,
    finalPrice: 52.79,
    orderAmount: 52.79,
    orderStatus: 'refunded'
  },
  {
    id: 'ORD008',
    orderDate: '2024-01-22',
    buyerName: 'Jennifer Davis',
    buyerCountry: 'DE',
    currency: 'EUR',
    distributor: 'TechCorp Solutions',
    gameName: 'Rhythm Master - Musician Edition',
    quantity: 2,
    originalPrice: 19.99,
    discount: 0.08,
    finalPrice: 18.39,
    orderAmount: 36.78,
    orderStatus: 'processing'
  },
  // 添加更多模拟数据
  {
    id: 'ORD009',
    orderDate: '2024-01-23',
    buyerName: 'Alex Johnson',
    buyerCountry: 'FR',
    currency: 'EUR',
    distributor: 'GameWorld Distribution',
    gameName: 'Adventure Quest - Premium Edition',
    quantity: 1,
    originalPrice: 45.99,
    discount: 0.1,
    finalPrice: 41.39,
    orderAmount: 41.39,
    orderStatus: 'completed'
  },
  {
    id: 'ORD010',
    orderDate: '2024-01-24',
    buyerName: 'Maria Rodriguez',
    buyerCountry: 'ES',
    currency: 'EUR',
    distributor: 'Retro Gaming Hub',
    gameName: 'Puzzle Master - Deluxe Edition',
    quantity: 2,
    originalPrice: 29.99,
    discount: 0.15,
    finalPrice: 25.49,
    orderAmount: 50.98,
    orderStatus: 'completed'
  }
]);

// Helper function to get country flag emoji
function getCountryFlag(countryCode: string): string {
  const flagMap: Record<string, string> = {
    US: '🇺🇸',
    CA: '🇨🇦',
    GB: '🇬🇧',
    DE: '🇩🇪',
    FR: '🇫🇷',
    ES: '🇪🇸',
    IT: '🇮🇹',
    JP: '🇯🇵',
    CN: '🇨🇳',
    KR: '🇰🇷',
    AU: '🇦🇺',
    BR: '🇧🇷',
    IN: '🇮🇳'
  };
  return flagMap[countryCode] || '🏳️';
}

// Helper function to get country name
function getCountryName(countryCode: string): string {
  const nameMap: Record<string, string> = {
    US: 'United States',
    CA: 'Canada',
    GB: 'United Kingdom',
    DE: 'Germany',
    FR: 'France',
    ES: 'Spain',
    IT: 'Italy',
    JP: 'Japan',
    CN: 'China',
    KR: 'South Korea',
    AU: 'Australia',
    BR: 'Brazil',
    IN: 'India'
  };
  return nameMap[countryCode] || countryCode;
}

// Organization options
const distributorOptions = computed(() => [
  { label: $t('page.finance.allDistributors'), value: '' },
  { label: 'TechCorp Solutions', value: 'TechCorp Solutions' },
  { label: 'Digital Innovations Ltd', value: 'Digital Innovations Ltd' },
  { label: 'GameWorld Distribution', value: 'GameWorld Distribution' },
  { label: 'Retro Gaming Hub', value: 'Retro Gaming Hub' }
]);

// Time filter options
const timeFilterOptions = computed(() => [
  { label: $t('page.finance.thisWeek'), value: 'thisWeek' },
  { label: $t('page.finance.thisMonth'), value: 'thisMonth' },
  { label: $t('page.finance.thisQuarter'), value: 'thisQuarter' },
  { label: $t('page.finance.thisYear'), value: 'thisYear' },
  { label: $t('page.finance.customRange'), value: 'customRange' }
]);

// Order status filter options
const orderStatusOptions = computed(() => [
  { label: $t('page.finance.allStatus'), value: '' },
  { label: $t('page.finance.completed'), value: 'completed' },
  { label: $t('page.finance.processing'), value: 'processing' },
  { label: $t('page.finance.cancelled'), value: 'cancelled' },
  { label: $t('page.finance.refunded'), value: 'refunded' }
]);

// Country options with flag emojis
const countryOptions = computed(() => [
  { label: $t('page.finance.allCountries'), value: '' },
  { label: '🇺🇸 United States', value: 'US' },
  { label: '🇨🇦 Canada', value: 'CA' },
  { label: '🇬🇧 United Kingdom', value: 'GB' },
  { label: '🇩🇪 Germany', value: 'DE' },
  { label: '🇫🇷 France', value: 'FR' },
  { label: '🇪🇸 Spain', value: 'ES' },
  { label: '🇮🇹 Italy', value: 'IT' },
  { label: '🇯🇵 Japan', value: 'JP' },
  { label: '🇨🇳 China', value: 'CN' },
  { label: '🇰🇷 South Korea', value: 'KR' },
  { label: '🇦🇺 Australia', value: 'AU' },
  { label: '🇧🇷 Brazil', value: 'BR' },
  { label: '🇮🇳 India', value: 'IN' }
]);

// Currency options
const currencyOptions = computed(() => [
  { label: $t('page.finance.allCurrencies'), value: '' },
  { label: 'USD - US Dollar', value: 'USD' },
  { label: 'EUR - Euro', value: 'EUR' },
  { label: 'GBP - British Pound', value: 'GBP' },
  { label: 'JPY - Japanese Yen', value: 'JPY' },
  { label: 'CNY - Chinese Yuan', value: 'CNY' },
  { label: 'CAD - Canadian Dollar', value: 'CAD' },
  { label: 'AUD - Australian Dollar', value: 'AUD' },
  { label: 'KRW - South Korean Won', value: 'KRW' },
  { label: 'BRL - Brazilian Real', value: 'BRL' },
  { label: 'INR - Indian Rupee', value: 'INR' }
]);

// 多语言文本
const searchAndAddOrderText = computed(() => {
  try {
    return $t('page.finance.searchAndAddOrder' as any);
  } catch {
    return '搜索并添加订单';
  }
});

const selectedCountText = computed(() => {
  try {
    return $t('page.finance.selectedCount' as any);
  } catch {
    return '已选择';
  }
});

const ordersText = computed(() => {
  try {
    return $t('page.finance.orders' as any);
  } catch {
    return '条订单';
  }
});

const foundOrdersText = computed(() => {
  try {
    return $t('page.finance.foundOrders' as any);
  } catch {
    return '共找到';
  }
});

const availableOrdersText = computed(() => {
  try {
    return $t('page.finance.availableOrders' as any);
  } catch {
    return '条可用订单';
  }
});

const addSelectedOrdersText = computed(() => {
  try {
    return $t('page.finance.addSelectedOrders' as any);
  } catch {
    return '添加选中订单';
  }
});

const pleaseSelectOrdersToAddText = computed(() => {
  try {
    return $t('page.finance.pleaseSelectOrdersToAdd' as any);
  } catch {
    return '请选择要添加的订单';
  }
});

// Filtered table data based on multiple criteria and excluded orders
const filteredTableData = computed(() => {
  let filtered = allOrderData.value.filter(order => {
    // 排除已选中的订单
    if (props.excludedOrderIds.includes(order.id)) {
      return false;
    }

    // Order status filter
    if (searchForm.orderStatus && order.orderStatus !== searchForm.orderStatus) {
      return false;
    }

    // Country filter
    if (searchForm.buyerCountry && order.buyerCountry !== searchForm.buyerCountry) {
      return false;
    }

    // Currency filter
    if (searchForm.currency && order.currency !== searchForm.currency) {
      return false;
    }

    // Organization filter
    if (searchForm.distributor && order.distributor !== searchForm.distributor) {
      return false;
    }

    // Order ID filter
    if (searchForm.orderId && !order.id.toLowerCase().includes(searchForm.orderId.toLowerCase())) {
      return false;
    }

    // Game name filter
    if (searchForm.gameName && !order.gameName.toLowerCase().includes(searchForm.gameName.toLowerCase())) {
      return false;
    }

    return true;
  });

  return filtered;
});

const columns = computed<DataTableColumns<OrderData>>(() => [
  {
    type: 'selection',
    multiple: true
  },
  {
    title: $t('page.finance.orderId'),
    key: 'id',
    width: 100,
    render: row => h('span', { class: 'font-mono text-xs' }, row.id)
  },
  {
    title: $t('page.finance.orderDate'),
    key: 'orderDate',
    width: 100
  },
  {
    title: $t('page.finance.buyerCountry'),
    key: 'buyerCountry',
    width: 120,
    render: row =>
      h('span', { class: 'flex items-center gap-1' }, [
        h('span', { class: 'text-lg' }, getCountryFlag(row.buyerCountry)),
        h('span', { class: 'text-sm' }, getCountryName(row.buyerCountry))
      ])
  },
  {
    title: $t('page.finance.currency'),
    key: 'currency',
    width: 80,
    render: row => h('span', { class: 'font-mono text-sm font-medium' }, row.currency)
  },
  {
    title: $t('page.finance.distributor'),
    key: 'distributor',
    width: 140,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: $t('page.finance.gameName'),
    key: 'gameName',
    width: 180,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: $t('page.finance.quantity'),
    key: 'quantity',
    width: 80,
    render: row => h('span', { class: 'text-center' }, row.quantity.toString())
  },
  {
    title: $t('page.finance.finalPrice'),
    key: 'finalPrice',
    width: 100,
    render: row => h('span', { class: 'font-medium text-green-600' }, `$${row.finalPrice.toFixed(2)}`)
  },
  {
    title: $t('page.finance.orderAmount'),
    key: 'orderAmount',
    width: 120,
    render: row => h('span', { class: 'font-semibold text-primary' }, `$${row.orderAmount.toFixed(2)}`)
  },
  {
    title: $t('page.finance.orderStatus'),
    key: 'orderStatus',
    width: 100,
    render: row => {
      const statusMap = {
        completed: { type: 'success', text: $t('page.finance.completed') },
        processing: { type: 'warning', text: $t('page.finance.processing') },
        cancelled: { type: 'error', text: $t('page.finance.cancelled') },
        refunded: { type: 'default', text: $t('page.finance.refunded') }
      };
      const status = statusMap[row.orderStatus];
      return h(NTag, { type: status.type as any, size: 'small' }, { default: () => status.text });
    }
  }
]);

// Selected orders count
const selectedOrdersCount = computed(() => checkedRowKeys.value.length);

// Selected orders data
const selectedOrders = computed(() => {
  if (!filteredTableData.value || !checkedRowKeys.value) {
    return [];
  }
  return filteredTableData.value.filter(order => checkedRowKeys.value.includes(order.id));
});

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  get itemCount() {
    return filteredTableData.value.length;
  },
  onChange: (page: number) => {
    pagination.page = page;
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
  }
});

function handleSearch() {
  loading.value = true;
  setTimeout(() => {
    loading.value = false;
  }, 1000);
}

function handleReset() {
  Object.assign(searchForm, {
    timeFilter: 'thisMonth',
    customDateRange: null,
    distributor: '',
    orderId: '',
    gameName: '',
    buyerCountry: '',
    currency: '',
    orderStatus: 'completed'
  });
  checkedRowKeys.value = [];
}

function handleSelectAll() {
  if (checkedRowKeys.value.length === filteredTableData.value.length) {
    checkedRowKeys.value = [];
  } else {
    checkedRowKeys.value = filteredTableData.value.map(order => order.id);
  }
}

function handleConfirm() {
  if (selectedOrders.value.length === 0) {
    window.$message?.warning(pleaseSelectOrdersToAddText.value);
    return;
  }
  emit('ordersSelected', selectedOrders.value);
}

function handleCancel() {
  visible.value = false;
  checkedRowKeys.value = [];
}

// 监听对话框关闭，重置选择状态
watch(visible, (newVisible) => {
  if (!newVisible) {
    checkedRowKeys.value = [];
    // 重置搜索条件
    Object.assign(searchForm, {
      timeFilter: 'thisMonth',
      customDateRange: null,
      distributor: '',
      orderId: '',
      gameName: '',
      buyerCountry: '',
      currency: '',
      orderStatus: 'completed'
    });
  }
});
</script>

<template>
  <NModal
    v-model:show="visible"
    preset="card"
    :title="searchAndAddOrderText"
    class="max-w-6xl w-full"
    :bordered="false"
    size="huge"
    :segmented="true"
    :mask-closable="false"
    :close-on-esc="false"
  >
    <template #header-extra>
      <div class="flex items-center gap-2 text-sm text-gray-500">
        <SvgIcon icon="mdi:information-outline" />
        <span>{{ selectedCountText }} {{ selectedOrdersCount }} {{ ordersText }}</span>
      </div>
    </template>

    <div class="space-y-4">
      <!-- 筛选条件 -->
      <div class="rounded-lg bg-gray-50 p-4 dark:bg-gray-800">
        <!-- 时间筛选 -->
        <div class="mb-4">
          <span class="mb-2 block text-sm text-gray-600 font-medium dark:text-gray-300">
            {{ $t('page.finance.timeFilter') }}
          </span>
          <div class="flex gap-2 items-center">
            <NSelect
              v-model:value="searchForm.timeFilter"
              :options="timeFilterOptions"
              class="w-40"
              size="small"
            />
            <NDatePicker
              v-if="searchForm.timeFilter === 'customRange'"
              v-model:value="searchForm.customDateRange"
              type="daterange"
              :placeholder="$t('page.finance.selectDateRange')"
              class="w-64"
              size="small"
              clearable
            />
          </div>
        </div>

        <!-- 其他筛选条件 -->
        <div class="flex flex-wrap gap-4 items-end">
          <div class="min-w-40 flex flex-col flex-1 gap-1">
            <span class="text-sm text-gray-600 font-medium dark:text-gray-300">
              {{ $t('page.finance.distributor') }}
            </span>
            <NSelect
              v-model:value="searchForm.distributor"
              :options="distributorOptions"
              :placeholder="$t('page.finance.distributorPlaceholder')"
              size="small"
              clearable
            />
          </div>

          <div class="min-w-32 flex flex-col flex-1 gap-1">
            <span class="text-sm text-gray-600 font-medium dark:text-gray-300">
              {{ $t('page.finance.orderId') }}
            </span>
            <NInput v-model:value="searchForm.orderId" :placeholder="$t('page.finance.orderId')" size="small" clearable>
              <template #prefix>
                <SvgIcon icon="mdi:identifier" class="text-gray-400" />
              </template>
            </NInput>
          </div>

          <div class="min-w-40 flex flex-col flex-1 gap-1">
            <span class="text-sm text-gray-600 font-medium dark:text-gray-300">
              {{ $t('page.finance.gameName') }}
            </span>
            <NInput v-model:value="searchForm.gameName" :placeholder="$t('page.finance.gameName')" size="small" clearable>
              <template #prefix>
                <SvgIcon icon="mdi:gamepad-variant" class="text-gray-400" />
              </template>
            </NInput>
          </div>

          <div class="min-w-32 flex flex-col flex-1 gap-1">
            <span class="text-sm text-gray-600 font-medium dark:text-gray-300">
              {{ $t('page.finance.buyerCountry') }}
            </span>
            <NSelect
              v-model:value="searchForm.buyerCountry"
              :options="countryOptions"
              :placeholder="$t('page.finance.countryPlaceholder')"
              size="small"
              clearable
            />
          </div>

          <div class="min-w-32 flex flex-col flex-1 gap-1">
            <span class="text-sm text-gray-600 font-medium dark:text-gray-300">
              {{ $t('page.finance.currency') }}
            </span>
            <NSelect
              v-model:value="searchForm.currency"
              :options="currencyOptions"
              :placeholder="$t('page.finance.currencyPlaceholder')"
              size="small"
              clearable
            />
          </div>

          <div class="min-w-32 flex flex-col flex-1 gap-1">
            <span class="text-sm text-gray-600 font-medium dark:text-gray-300">
              {{ $t('page.finance.orderStatus') }}
            </span>
            <NSelect
              v-model:value="searchForm.orderStatus"
              :options="orderStatusOptions"
              :placeholder="$t('page.finance.orderStatusPlaceholder')"
              size="small"
              clearable
            />
          </div>

          <!-- 操作按钮 -->
          <div class="flex gap-2 flex-shrink-0">
            <NButton size="small" @click="handleReset">
              {{ $t('common.reset') }}
            </NButton>
            <NButton type="primary" size="small" :loading="loading" @click="handleSearch">
              <template #icon>
                <SvgIcon icon="mdi:magnify" />
              </template>
              {{ $t('common.search') }}
            </NButton>
          </div>
        </div>
      </div>

      <!-- 操作栏 -->
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-2">
          <NButton size="small" @click="handleSelectAll">
            <template #icon>
              <SvgIcon icon="mdi:select-all" />
            </template>
            {{ checkedRowKeys.length === filteredTableData.length ? $t('common.deselectAll') : $t('common.selectAll') }}
          </NButton>
          <span v-if="selectedOrdersCount > 0" class="text-sm text-gray-600 dark:text-gray-300">
            {{ selectedCountText }} {{ selectedOrdersCount }} {{ ordersText }}
          </span>
        </div>
        <div class="text-sm text-gray-500">
          {{ foundOrdersText }} {{ filteredTableData.length }} {{ availableOrdersText }}
        </div>
      </div>

      <!-- 数据表格 -->
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="filteredTableData"
        :loading="loading"
        :pagination="pagination"
        :bordered="false"
        size="small"
        flex-height
        :scroll-x="1400"
        class="h-400px"
        :row-key="(row: OrderData) => row.id"
      />
    </div>

    <template #action>
      <div class="flex justify-end gap-2">
        <NButton @click="handleCancel">
          {{ $t('common.cancel') }}
        </NButton>
        <NButton type="primary" :disabled="selectedOrdersCount === 0" @click="handleConfirm">
          <template #icon>
            <SvgIcon icon="mdi:plus" />
          </template>
          {{ addSelectedOrdersText }} ({{ selectedOrdersCount }})
        </NButton>
      </div>
    </template>
  </NModal>
</template>

<style scoped></style>
