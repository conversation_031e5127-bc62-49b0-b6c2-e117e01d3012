<script setup lang="tsx">
import { $t } from '@/locales';
import StatsCard from '@/components/custom/stats-card.vue';
import StatsGrid from '@/components/custom/stats-grid.vue';

defineOptions({
  name: 'FinanceStats'
});
</script>

<template>
  <StatsGrid>
    <StatsCard
      :title="$t('page.finance.totalRevenue')"
      :value="5678900"
      unit="$"
      :unit-as-prefix="true"
      :color="{ start: '#007aff', end: '#0056cc' }"
      icon="mdi:currency-usd"
    />
    <StatsCard
      :title="$t('page.finance.monthlyRevenue')"
      :value="456789"
      unit="$"
      :unit-as-prefix="true"
      :color="{ start: '#52c41a', end: '#389e0d' }"
      icon="mdi:trending-up"
    />
    <StatsCard
      :title="$t('page.finance.pendingReports')"
      :value="23"
      :unit="$t('common.unit.reports')"
      :color="{ start: '#fa8c16', end: '#d46b08' }"
      icon="mdi:file-clock"
    />
    <StatsCard
      :title="$t('page.finance.publishedReports')"
      :value="156"
      :unit="$t('common.unit.reports')"
      :color="{ start: '#722ed1', end: '#531dab' }"
      icon="mdi:file-check"
    />
  </StatsGrid>
</template>
