<script setup lang="tsx">
import { computed, h, nextTick, reactive, ref, watch } from 'vue';
import type { DataTableColumns, DataTableRowKey } from 'naive-ui';
import { NButton, NDataTable, NDatePicker, NDrawer, NDrawerContent, NInput, NSelect, NSpace, NTag } from 'naive-ui';
import { useAppStore } from '@/store/modules/app';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';
import ButtonIcon from '@/components/custom/button-icon.vue';
import OrderDetailDrawer from './order-detail-drawer.vue';

defineOptions({
  name: 'OrderList'
});

interface Props {
  preSelectedOrders?: OrderData[];
  isEditMode?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  preSelectedOrders: () => [],
  isEditMode: false
});

interface OrderData {
  id: string;
  orderDate: string;
  buyerName: string;
  buyerCountry: string;
  currency: string;
  distributor: string;
  gameName: string;
  quantity: number;
  originalPrice: number;
  discount: number;
  finalPrice: number;
  orderAmount: number;
  orderStatus: 'completed' | 'processing' | 'cancelled' | 'refunded';
}

interface Emits {
  (e: 'ordersSelected', orders: OrderData[]): void;
  (e: 'generateReport'): void;
}

const emit = defineEmits<Emits>();

const searchForm = reactive({
  timeFilter: 'thisMonth',
  customDateRange: null as [number, number] | null,
  distributor: '',
  orderId: '',
  gameName: '',
  buyerName: '',
  buyerCountry: '',
  currency: '',
  orderStatus: 'completed' // 默认显示已完成状态
});

const loading = ref(false);
const checkedRowKeys = ref<DataTableRowKey[]>([]);

// 订单详情抽屉状态
const showOrderDrawer = ref(false);
const selectedOrderId = ref<string>('');

const appStore = useAppStore();
const isMobile = computed(() => appStore.isMobile);

// Helper function to get country flag emoji
function getCountryFlag(countryCode: string): string {
  const flagMap: Record<string, string> = {
    US: '🇺🇸',
    CA: '🇨🇦',
    GB: '🇬🇧',
    DE: '🇩🇪',
    FR: '🇫🇷',
    ES: '🇪🇸',
    IT: '🇮🇹',
    JP: '🇯🇵',
    CN: '🇨🇳',
    KR: '🇰🇷',
    AU: '🇦🇺',
    BR: '🇧🇷',
    IN: '🇮🇳'
  };
  return flagMap[countryCode] || '🏳️';
}

// Helper function to get country name
function getCountryName(countryCode: string): string {
  const nameMap: Record<string, string> = {
    US: 'United States',
    CA: 'Canada',
    GB: 'United Kingdom',
    DE: 'Germany',
    FR: 'France',
    ES: 'Spain',
    IT: 'Italy',
    JP: 'Japan',
    CN: 'China',
    KR: 'South Korea',
    AU: 'Australia',
    BR: 'Brazil',
    IN: 'India'
  };
  return nameMap[countryCode] || countryCode;
}

// Mock order data - 基于用户的个人订单
const tableData = ref<OrderData[]>([
  {
    id: 'ORD001',
    orderDate: '2024-01-15',
    buyerName: 'John Smith',
    buyerCountry: 'US',
    currency: 'USD',
    distributor: 'TechCorp Solutions',
    gameName: 'Mystic Adventure - Standard Edition',
    quantity: 1,
    originalPrice: 29.99,
    discount: 0.1,
    finalPrice: 26.99,
    orderAmount: 26.99,
    orderStatus: 'completed'
  },
  {
    id: 'ORD002',
    orderDate: '2024-01-16',
    buyerName: 'Emily Johnson',
    buyerCountry: 'CA',
    currency: 'CAD',
    distributor: 'Digital Innovations Ltd',
    gameName: 'Space Odyssey - Deluxe Edition',
    quantity: 2,
    originalPrice: 39.99,
    discount: 0.15,
    finalPrice: 33.99,
    orderAmount: 67.98,
    orderStatus: 'completed'
  },
  {
    id: 'ORD003',
    orderDate: '2024-01-17',
    buyerName: 'Michael Chen',
    buyerCountry: 'CN',
    currency: 'CNY',
    distributor: 'GameWorld Distribution',
    gameName: "Fantasy Adventure - Collector's Edition",
    quantity: 1,
    originalPrice: 89.99,
    discount: 0.05,
    finalPrice: 85.49,
    orderAmount: 85.49,
    orderStatus: 'processing'
  },
  {
    id: 'ORD004',
    orderDate: '2024-01-18',
    buyerName: 'Sarah Williams',
    buyerCountry: 'GB',
    currency: 'GBP',
    distributor: 'TechCorp Solutions',
    gameName: 'Speed Rush - Standard Edition',
    quantity: 1,
    originalPrice: 19.99,
    discount: 0.2,
    finalPrice: 15.99,
    orderAmount: 15.99,
    orderStatus: 'completed'
  },
  {
    id: 'ORD005',
    orderDate: '2024-01-19',
    buyerName: 'David Brown',
    buyerCountry: 'AU',
    currency: 'AUD',
    distributor: 'Retro Gaming Hub',
    gameName: 'Magic Academy - Standard Edition',
    quantity: 3,
    originalPrice: 24.99,
    discount: 0.0,
    finalPrice: 24.99,
    orderAmount: 74.97,
    orderStatus: 'cancelled'
  },
  {
    id: 'ORD006',
    orderDate: '2024-01-20',
    buyerName: 'Lisa Garcia',
    buyerCountry: 'ES',
    currency: 'EUR',
    distributor: 'Digital Innovations Ltd',
    gameName: 'Mystic Adventure - Deluxe Edition',
    quantity: 1,
    originalPrice: 49.99,
    discount: 0.25,
    finalPrice: 37.49,
    orderAmount: 37.49,
    orderStatus: 'completed'
  },
  {
    id: 'ORD007',
    orderDate: '2024-01-21',
    buyerName: 'Robert Taylor',
    buyerCountry: 'JP',
    currency: 'JPY',
    distributor: 'GameWorld Distribution',
    gameName: 'Galactic Warfare - Commander Edition',
    quantity: 1,
    originalPrice: 59.99,
    discount: 0.12,
    finalPrice: 52.79,
    orderAmount: 52.79,
    orderStatus: 'refunded'
  },
  {
    id: 'ORD008',
    orderDate: '2024-01-22',
    buyerName: 'Jennifer Davis',
    buyerCountry: 'DE',
    currency: 'EUR',
    distributor: 'TechCorp Solutions',
    gameName: 'Rhythm Master - Musician Edition',
    quantity: 2,
    originalPrice: 19.99,
    discount: 0.08,
    finalPrice: 18.39,
    orderAmount: 36.78,
    orderStatus: 'processing'
  }
]);

// Organization options
const distributorOptions = computed(() => [
  { label: $t('page.finance.allDistributors'), value: '' },
  { label: 'TechCorp Solutions', value: 'TechCorp Solutions' },
  { label: 'Digital Innovations Ltd', value: 'Digital Innovations Ltd' },
  { label: 'GameWorld Distribution', value: 'GameWorld Distribution' },
  { label: 'Retro Gaming Hub', value: 'Retro Gaming Hub' }
]);

// Time filter options
const timeFilterOptions = computed(() => [
  { label: $t('page.finance.thisWeek'), value: 'thisWeek' },
  { label: $t('page.finance.thisMonth'), value: 'thisMonth' },
  { label: $t('page.finance.thisQuarter'), value: 'thisQuarter' },
  { label: $t('page.finance.thisYear'), value: 'thisYear' },
  { label: $t('page.finance.customRange'), value: 'customRange' }
]);

// Order status filter options
const orderStatusOptions = computed(() => [
  { label: $t('page.finance.allStatus'), value: '' },
  { label: $t('page.finance.completed'), value: 'completed' },
  { label: $t('page.finance.processing'), value: 'processing' },
  { label: $t('page.finance.cancelled'), value: 'cancelled' },
  { label: $t('page.finance.refunded'), value: 'refunded' }
]);

// Country options with flag emojis
const countryOptions = computed(() => [
  { label: $t('page.finance.allCountries'), value: '' },
  { label: '🇺🇸 United States', value: 'US' },
  { label: '🇨🇦 Canada', value: 'CA' },
  { label: '🇬🇧 United Kingdom', value: 'GB' },
  { label: '🇩🇪 Germany', value: 'DE' },
  { label: '🇫🇷 France', value: 'FR' },
  { label: '🇪🇸 Spain', value: 'ES' },
  { label: '🇮🇹 Italy', value: 'IT' },
  { label: '🇯🇵 Japan', value: 'JP' },
  { label: '🇨🇳 China', value: 'CN' },
  { label: '🇰🇷 South Korea', value: 'KR' },
  { label: '🇦🇺 Australia', value: 'AU' },
  { label: '🇧🇷 Brazil', value: 'BR' },
  { label: '🇮🇳 India', value: 'IN' }
]);

// Currency options
const currencyOptions = computed(() => [
  { label: $t('page.finance.allCurrencies'), value: '' },
  { label: 'USD - US Dollar', value: 'USD' },
  { label: 'EUR - Euro', value: 'EUR' },
  { label: 'GBP - British Pound', value: 'GBP' },
  { label: 'JPY - Japanese Yen', value: 'JPY' },
  { label: 'CNY - Chinese Yuan', value: 'CNY' },
  { label: 'CAD - Canadian Dollar', value: 'CAD' },
  { label: 'AUD - Australian Dollar', value: 'AUD' },
  { label: 'KRW - South Korean Won', value: 'KRW' },
  { label: 'BRL - Brazilian Real', value: 'BRL' },
  { label: 'INR - Indian Rupee', value: 'INR' }
]);

// 排序状态
const sortState = ref({
  column: '',
  order: false as false | 'ascend' | 'descend'
});

// Filtered table data based on multiple criteria
const filteredTableData = computed(() => {
  let filtered = tableData.value.filter(order => {
    // Order status filter
    if (searchForm.orderStatus && order.orderStatus !== searchForm.orderStatus) {
      return false;
    }

    // Country filter
    if (searchForm.buyerCountry && order.buyerCountry !== searchForm.buyerCountry) {
      return false;
    }

    // Currency filter
    if (searchForm.currency && order.currency !== searchForm.currency) {
      return false;
    }

    // Organization filter
    if (searchForm.distributor && order.distributor !== searchForm.distributor) {
      return false;
    }

    // Order ID filter
    if (searchForm.orderId && !order.id.toLowerCase().includes(searchForm.orderId.toLowerCase())) {
      return false;
    }

    // Game name filter
    if (searchForm.gameName && !order.gameName.toLowerCase().includes(searchForm.gameName.toLowerCase())) {
      return false;
    }

    // Buyer name filter
    if (searchForm.buyerName && !order.buyerName.toLowerCase().includes(searchForm.buyerName.toLowerCase())) {
      return false;
    }

    return true;
  });

  // 应用排序
  if (sortState.value.column && sortState.value.order !== false) {
    filtered = [...filtered].sort((a, b) => {
      let aValue: any;
      let bValue: any;

      switch (sortState.value.column) {
        case 'orderDate':
          aValue = new Date(a.orderDate);
          bValue = new Date(b.orderDate);
          break;
        case 'gameName':
          aValue = a.gameName.toLowerCase();
          bValue = b.gameName.toLowerCase();
          break;
        case 'buyerCountry':
          aValue = getCountryName(a.buyerCountry).toLowerCase();
          bValue = getCountryName(b.buyerCountry).toLowerCase();
          break;
        default:
          return 0;
      }

      if (aValue < bValue) {
        return sortState.value.order === 'ascend' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortState.value.order === 'ascend' ? 1 : -1;
      }
      return 0;
    });
  }

  return filtered;
});

// 统计数据
const statisticsData = computed(() => {
  const data = filteredTableData.value;
  return {
    totalOrders: data.length,
    totalAmount: data.reduce((sum, order) => sum + order.orderAmount, 0)
  };
});

// 排序处理函数
function handleSorterChange(sorter: any) {
  if (sorter) {
    sortState.value.column = sorter.columnKey;
    sortState.value.order = sorter.order;
  } else {
    sortState.value.column = '';
    sortState.value.order = false;
  }
}

const columns = computed<DataTableColumns<OrderData>>(() => [
  {
    type: 'selection',
    multiple: true
  },
  {
    title: $t('page.finance.orderId'),
    key: 'id',
    width: 100,
    render: row => h('span', { class: 'font-mono text-xs' }, row.id)
  },
  {
    title: $t('page.finance.orderDate'),
    key: 'orderDate',
    width: 100,
    sorter: true,
    sortOrder: sortState.value.column === 'orderDate' ? sortState.value.order : false
  },
  // 暂时不显示购买者姓名
  // {
  //   title: $t('page.finance.buyerName'),
  //   key: 'buyerName',
  //   width: 120,
  //   ellipsis: {
  //     tooltip: true
  //   }
  // },
  {
    title: $t('page.finance.buyerCountry'),
    key: 'buyerCountry',
    width: 120,
    sorter: true,
    sortOrder: sortState.value.column === 'buyerCountry' ? sortState.value.order : false,
    render: row =>
      h('span', { class: 'flex items-center gap-1' }, [
        h('span', { class: 'text-lg' }, getCountryFlag(row.buyerCountry)),
        h('span', { class: 'text-sm' }, getCountryName(row.buyerCountry))
      ])
  },
  {
    title: $t('page.finance.currency'),
    key: 'currency',
    width: 80,
    render: row => h('span', { class: 'font-mono text-sm font-medium' }, row.currency)
  },
  {
    title: $t('page.finance.distributor'),
    key: 'distributor',
    width: 140,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: $t('page.finance.gameName'),
    key: 'gameName',
    width: 180,
    sorter: true,
    sortOrder: sortState.value.column === 'gameName' ? sortState.value.order : false,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: $t('page.finance.quantity'),
    key: 'quantity',
    width: 80,
    render: row => h('span', { class: 'text-center' }, row.quantity.toString())
  },
  {
    title: $t('page.finance.originalPrice'),
    key: 'originalPrice',
    width: 100,
    render: row => h('span', { class: 'font-medium text-gray-500' }, `$${row.originalPrice.toFixed(2)}`)
  },
  {
    title: $t('page.finance.discount'),
    key: 'discount',
    width: 80,
    render: row => {
      const discountPercent = (row.discount * 100).toFixed(0);
      return h(
        'span',
        {
          class: row.discount > 0 ? 'font-medium text-red-500' : 'text-gray-400'
        },
        row.discount > 0 ? `-${discountPercent}%` : '0%'
      );
    }
  },
  {
    title: $t('page.finance.finalPrice'),
    key: 'finalPrice',
    width: 100,
    render: row => h('span', { class: 'font-medium text-green-600' }, `$${row.finalPrice.toFixed(2)}`)
  },
  {
    title: $t('page.finance.orderAmount'),
    key: 'orderAmount',
    width: 120,
    render: row => h('span', { class: 'font-semibold text-primary' }, `$${row.orderAmount.toFixed(2)}`)
  },
  {
    title: $t('page.finance.orderStatus'),
    key: 'orderStatus',
    width: 100,
    render: row => {
      const statusMap = {
        completed: { type: 'success', text: $t('page.finance.completed') },
        processing: { type: 'warning', text: $t('page.finance.processing') },
        cancelled: { type: 'error', text: $t('page.finance.cancelled') },
        refunded: { type: 'default', text: $t('page.finance.refunded') }
      };
      const status = statusMap[row.orderStatus];
      return h(NTag, { type: status.type as any, size: 'small' }, { default: () => status.text });
    }
  },
  {
    title: $t('common.action'),
    key: 'actions',
    width: 80,
    render: row => {
      return h(ButtonIcon, {
        icon: 'mdi:eye',
        tooltipContent: $t('common.view'),
        class: 'text-primary',
        onClick: () => handleViewOrder(row.id)
      });
    }
  }
]);

// Item count for pagination
const itemCount = computed(() => filteredTableData.value.length);

const pagination = reactive({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  get itemCount() {
    return itemCount.value;
  },
  onChange: (page: number) => {
    pagination.page = page;
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
  }
});

// Selected orders count
const selectedOrdersCount = computed(() => checkedRowKeys.value.length);

// Selected orders data
const selectedOrders = computed(() => {
  if (!filteredTableData.value || !checkedRowKeys.value) {
    return [];
  }
  return filteredTableData.value.filter(order => checkedRowKeys.value.includes(order.id));
});

// Watch for selection changes and emit to parent
watch(
  selectedOrders,
  newSelectedOrders => {
    if (newSelectedOrders) {
      emit('ordersSelected', newSelectedOrders);
    }
  },
  { deep: true, immediate: true }
);

// Watch for pre-selected orders in edit mode
watch(
  () => [props.preSelectedOrders, props.isEditMode],
  ([newPreSelectedOrders, isEditMode]) => {
    if (isEditMode && Array.isArray(newPreSelectedOrders) && newPreSelectedOrders.length > 0) {
      const newKeys = newPreSelectedOrders.map((order: OrderData) => order.id);
      // 只有当选中的键值发生变化时才更新，避免不必要的更新
      if (JSON.stringify(checkedRowKeys.value.sort()) !== JSON.stringify(newKeys.sort())) {
        // 使用 nextTick 避免同步更新导致的递归问题
        nextTick(() => {
          checkedRowKeys.value = newKeys;
        });
      }
    }
    // 移除自动清空选择的逻辑，让用户手动控制选择
  },
  { immediate: true }
);

function handleSearch() {
  loading.value = true;
  setTimeout(() => {
    loading.value = false;
  }, 1000);
}

function handleReset() {
  Object.assign(searchForm, {
    timeFilter: 'thisMonth',
    customDateRange: null,
    distributor: '',
    orderId: '',
    gameName: '',
    buyerName: '',
    buyerCountry: '',
    currency: '',
    orderStatus: 'completed' // 重置时也默认为已完成状态
  });
  checkedRowKeys.value = [];
}

function handleSelectAll() {
  if (checkedRowKeys.value.length === filteredTableData.value.length) {
    checkedRowKeys.value = [];
  } else {
    checkedRowKeys.value = filteredTableData.value.map(order => order.id);
  }
}

function handleGenerateReport() {
  emit('generateReport');
}

function handleViewOrder(orderId: string) {
  selectedOrderId.value = orderId;
  showOrderDrawer.value = true;
}

function handleRefresh() {
  loading.value = true;
  // 模拟刷新数据
  setTimeout(() => {
    loading.value = false;
    // 这里可以调用实际的数据刷新API
  }, 1000);
}
</script>

<template>
  <div class="space-y-3">
    <!-- 筛选条件 -->
    <div class="rounded-lg bg-gray-50 p-4 dark:bg-gray-800">
      <!-- 时间筛选 -->
      <div class="mb-4">
        <span class="mb-2 block text-sm text-gray-600 font-medium dark:text-gray-300">
          {{ $t('page.finance.timeFilter') }}
        </span>
        <div :class="isMobile ? 'space-y-2' : 'flex gap-2 items-center'">
          <NSelect
            v-model:value="searchForm.timeFilter"
            :options="timeFilterOptions"
            :class="isMobile ? 'w-full' : 'w-40'"
            size="small"
          />
          <NDatePicker
            v-if="searchForm.timeFilter === 'customRange'"
            v-model:value="searchForm.customDateRange"
            type="daterange"
            :placeholder="$t('page.finance.selectDateRange')"
            :class="isMobile ? 'w-full' : 'w-64'"
            size="small"
            clearable
          />
        </div>
      </div>

      <!-- 其他筛选条件 -->
      <div :class="isMobile ? 'space-y-3' : 'flex flex-wrap gap-4 items-end'">
        <div class="min-w-40 flex flex-col flex-1 gap-1">
          <span class="text-sm text-gray-600 font-medium dark:text-gray-300">
            {{ $t('page.finance.distributor') }}
          </span>
          <NSelect
            v-model:value="searchForm.distributor"
            :options="distributorOptions"
            :placeholder="$t('page.finance.distributorPlaceholder')"
            size="small"
            clearable
          />
        </div>

        <div class="min-w-32 flex flex-col flex-1 gap-1">
          <span class="text-sm text-gray-600 font-medium dark:text-gray-300">
            {{ $t('page.finance.orderId') }}
          </span>
          <NInput v-model:value="searchForm.orderId" :placeholder="$t('page.finance.orderId')" size="small" clearable>
            <template #prefix>
              <SvgIcon icon="mdi:identifier" class="text-gray-400" />
            </template>
          </NInput>
        </div>

        <div class="min-w-40 flex flex-col flex-1 gap-1">
          <span class="text-sm text-gray-600 font-medium dark:text-gray-300">
            {{ $t('page.finance.gameName') }}
          </span>
          <NInput v-model:value="searchForm.gameName" :placeholder="$t('page.finance.gameName')" size="small" clearable>
            <template #prefix>
              <SvgIcon icon="mdi:gamepad-variant" class="text-gray-400" />
            </template>
          </NInput>
        </div>
        <!-- 暂时去掉按购买者姓名筛选 -->
        <!--
 <div class="min-w-32 flex flex-col flex-1 gap-1">
          <span class="text-sm text-gray-600 font-medium dark:text-gray-300">
            {{ $t('page.finance.buyerName') }}
          </span>
          <NInput
            v-model:value="searchForm.buyerName"
            :placeholder="$t('page.finance.buyerName')"
            size="small"
            clearable
          >
            <template #prefix>
              <SvgIcon icon="mdi:account" class="text-gray-400" />
            </template>
          </NInput>
        </div>
-->

        <div class="min-w-32 flex flex-col flex-1 gap-1">
          <span class="text-sm text-gray-600 font-medium dark:text-gray-300">
            {{ $t('page.finance.buyerCountry') }}
          </span>
          <NSelect
            v-model:value="searchForm.buyerCountry"
            :options="countryOptions"
            :placeholder="$t('page.finance.countryPlaceholder')"
            size="small"
            clearable
          />
        </div>

        <div class="min-w-32 flex flex-col flex-1 gap-1">
          <span class="text-sm text-gray-600 font-medium dark:text-gray-300">
            {{ $t('page.finance.currency') }}
          </span>
          <NSelect
            v-model:value="searchForm.currency"
            :options="currencyOptions"
            :placeholder="$t('page.finance.currencyPlaceholder')"
            size="small"
            clearable
          />
        </div>

        <div class="min-w-32 flex flex-col flex-1 gap-1">
          <span class="text-sm text-gray-600 font-medium dark:text-gray-300">
            {{ $t('page.finance.orderStatus') }}
          </span>
          <NSelect
            v-model:value="searchForm.orderStatus"
            :options="orderStatusOptions"
            :placeholder="$t('page.finance.orderStatusPlaceholder')"
            size="small"
            clearable
          />
        </div>

        <!-- 操作按钮 -->
        <div class="flex gap-2" :class="isMobile ? 'w-full justify-center' : 'flex-shrink-0'">
          <NButton size="small" @click="handleReset">
            {{ $t('common.reset') }}
          </NButton>
          <NButton type="primary" size="small" :loading="loading" @click="handleSearch">
            <template #icon>
              <SvgIcon icon="mdi:magnify" />
            </template>
            {{ $t('common.search') }}
          </NButton>
        </div>
      </div>
    </div>

    <!-- 操作栏 -->
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-2">
        <NButton size="small" @click="handleSelectAll">
          <template #icon>
            <SvgIcon icon="mdi:select-all" />
          </template>
          {{ checkedRowKeys.length === filteredTableData.length ? $t('common.deselectAll') : $t('common.selectAll') }}
        </NButton>
        <span v-if="selectedOrdersCount > 0" class="text-sm text-gray-600 dark:text-gray-300">
          {{ $t('page.finance.selectedCount', { count: selectedOrdersCount }) }}
        </span>
      </div>

      <!-- 生成报表按钮 -->
      <div class="flex items-center gap-2">
        <NButton :loading="loading" size="small" @click="handleRefresh">
          <template #icon>
            <SvgIcon icon="mdi:refresh" />
          </template>
          {{ $t('common.refresh') }}
        </NButton>
        <NButton type="primary" size="small" :disabled="selectedOrdersCount === 0" @click="handleGenerateReport">
          <template #icon>
            <SvgIcon icon="mdi:file-plus" />
          </template>
          {{ $t('page.finance.generateReport') }}
        </NButton>
      </div>
    </div>

    <!-- 数据表格 -->
    <NDataTable
      v-model:checked-row-keys="checkedRowKeys"
      :columns="columns"
      :data="filteredTableData"
      :loading="loading"
      :pagination="pagination"
      :bordered="false"
      size="small"
      flex-height
      :scroll-x="1600"
      class="h-500px"
      :row-key="(row: OrderData) => row.id"
      @update:sorter="handleSorterChange"
    />

    <!-- 统计栏 -->
    <div class="mt-4 rounded-lg bg-gray-50 p-4 dark:bg-gray-800">
      <div class="flex items-center justify-between">
        <h3 class="text-sm text-gray-700 font-medium dark:text-gray-300">
          {{ $t('page.finance.statisticsSummary') }}
        </h3>
        <div class="flex items-center gap-6">
          <div class="flex items-center gap-2">
            <span class="text-sm text-gray-600 dark:text-gray-400">{{ $t('page.finance.totalOrdersCount') }}:</span>
            <span class="text-primary font-semibold">
              {{ statisticsData.totalOrders }}
            </span>
          </div>
          <div class="flex items-center gap-2">
            <span class="text-sm text-gray-600 dark:text-gray-400">{{ $t('page.finance.totalAmountSum') }}:</span>
            <span class="text-green-600 font-semibold">${{ statisticsData.totalAmount.toFixed(2) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 订单详情抽屉 -->
    <NDrawer
      v-model:show="showOrderDrawer"
      :width="isMobile ? '100%' : '66.67%'"
      placement="left"
      :mask-closable="true"
      :close-on-esc="true"
    >
      <NDrawerContent :title="$t('page.finance.orderDetail')" closable>
        <OrderDetailDrawer v-if="showOrderDrawer" :order-id="selectedOrderId" @close="showOrderDrawer = false" />
      </NDrawerContent>
    </NDrawer>
  </div>
</template>

<style scoped></style>
