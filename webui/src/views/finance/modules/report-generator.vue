<script setup lang="tsx">
import { computed, h, nextTick, reactive, ref, watch, withDefaults } from 'vue';
import type { DataTableColumns, FormInst } from 'naive-ui';
import {
  NButton,
  NCollapse,
  NCollapseItem,
  NDataTable,
  NDatePicker,
  NForm,
  NFormItem,
  NInput,
  NSelect,
  NTag
} from 'naive-ui';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';
import ButtonIcon from '@/components/custom/button-icon.vue';
import OrderSearchModal from './order-search-modal.vue';

defineOptions({
  name: 'ReportGenerator'
});

interface OrderData {
  id: string;
  orderDate: string;
  buyerName: string;
  buyerCountry: string;
  currency: string;
  distributor: string;
  gameName: string;
  quantity: number;
  originalPrice: number;
  discount: number;
  finalPrice: number;
  orderAmount: number;
  orderStatus: 'completed' | 'processing' | 'cancelled' | 'refunded';
}

interface Props {
  selectedOrders?: OrderData[];
  isEditMode?: boolean;
  editingReportData?: any;
}

const props = withDefaults(defineProps<Props>(), {
  selectedOrders: () => [],
  isEditMode: false,
  editingReportData: null
});

// 多语言计算属性
const editFinancialReportText = computed(() => {
  try {
    return $t('page.finance.editFinancialReport' as any);
  } catch {
    return '编辑财务报告';
  }
});

const generateFinancialReportText = computed(() => {
  try {
    return $t('page.finance.generateFinancialReport' as any);
  } catch {
    return '生成财务报告';
  }
});

const reportUpdateSuccessText = computed(() => {
  try {
    return $t('page.finance.reportUpdateSuccess' as any);
  } catch {
    return '财务报告更新成功';
  }
});

const reportGenerateSuccessText = computed(() => {
  try {
    return $t('page.finance.reportGenerateSuccess' as any);
  } catch {
    return '财务报告生成成功';
  }
});

const reportOperationFailedText = computed(() => {
  try {
    return $t('page.finance.reportOperationFailed' as any);
  } catch {
    return '报告操作失败';
  }
});

const pleaseSelectOrdersText = computed(() => {
  try {
    return $t('page.finance.pleaseSelectOrders' as any);
  } catch {
    return '请选择订单';
  }
});

// 添加修正记录相关的多语言文本
const addCorrectionRecordText = computed(() => {
  try {
    return $t('page.finance.addCorrectionRecord' as any);
  } catch {
    return '添加修正记录';
  }
});

const addOrderRecordText = computed(() => {
  try {
    return $t('page.finance.addOrderRecord' as any);
  } catch {
    return '添加订单记录';
  }
});

const orderListText = computed(() => {
  try {
    return $t('page.finance.orderList' as any);
  } catch {
    return '订单列表';
  }
});

const recordsCountText = computed(() => {
  try {
    return $t('page.finance.recordsCount' as any);
  } catch {
    return '条记录';
  }
});

const searchAndAddOrderText = computed(() => {
  try {
    return $t('page.finance.searchAndAddOrder' as any);
  } catch {
    return '搜索并添加订单';
  }
});

const selectedCountText = computed(() => {
  try {
    return $t('page.finance.selectedCount' as any);
  } catch {
    return '已选择';
  }
});

const ordersText = computed(() => {
  try {
    return $t('page.finance.orders' as any);
  } catch {
    return '条订单';
  }
});

const orderAddedSuccessText = computed(() => {
  try {
    return $t('page.finance.orderAddedSuccess' as any);
  } catch {
    return '订单记录已添加';
  }
});

const duplicateOrderWarningText = computed(() => {
  try {
    return $t('page.finance.duplicateOrderWarning' as any);
  } catch {
    return '所选订单已存在，未添加重复记录';
  }
});

const orderDeletedSuccessText = computed(() => {
  try {
    return $t('page.finance.orderDeletedSuccess' as any);
  } catch {
    return '订单记录已删除';
  }
});

const pleaseSelectOrdersToAddText = computed(() => {
  try {
    return $t('page.finance.pleaseSelectOrdersToAdd' as any);
  } catch {
    return '请选择要添加的订单';
  }
});

interface Emits {
  (e: 'reportGenerated', reportData: any): void;
  (e: 'ordersUpdated', orders: OrderData[]): void;
}

const emit = defineEmits<Emits>();

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const loading = ref(false);

// 订单搜索对话框状态
const showOrderSearchModal = ref(false);

// 当前选中的订单列表（可编辑）
const currentOrders = ref<OrderData[]>([]);

const reportForm = reactive({
  reportTitle: '',
  startDate: null as number | null,
  endDate: null as number | null,
  reportType: 'monthly',
  distributor: '',
  notes: ''
});

const rules = {
  reportTitle: defaultRequiredRule,
  reportType: defaultRequiredRule
};

// Report type options
const reportTypeOptions = computed(() => [
  { label: 'Monthly Report', value: 'monthly' },
  { label: 'Quarterly Report', value: 'quarterly' },
  { label: 'Annual Report', value: 'annual' },
  { label: 'Custom Report', value: 'custom' }
]);

// Organization options
const distributorOptions = computed(() => [
  { label: $t('page.finance.allDistributors'), value: '' },
  { label: 'TechCorp Solutions', value: 'TechCorp Solutions' },
  { label: 'Digital Innovations Ltd', value: 'Digital Innovations Ltd' },
  { label: 'GameWorld Distribution', value: 'GameWorld Distribution' },
  { label: 'Retro Gaming Hub', value: 'Retro Gaming Hub' }
]);

// Calculate summary statistics
const orderSummary = computed(() => {
  const orders = currentOrders.value;

  if (!orders || orders.length === 0) {
    return {
      totalOrders: 0,
      totalAmount: 0,
      totalQuantity: 0,
      organizationCount: 0,
      gameCount: 0,
      statusBreakdown: {}
    };
  }

  const totalAmount = orders.reduce((sum, order) => sum + order.orderAmount, 0);
  const totalQuantity = orders.reduce((sum, order) => sum + order.quantity, 0);

  const organizations = new Set(orders.map(order => order.distributor));
  const games = new Set(orders.map(order => order.gameName));

  const statusBreakdown = orders.reduce(
    (acc, order) => {
      acc[order.orderStatus] = (acc[order.orderStatus] || 0) + 1;
      return acc;
    },
    {} as Record<string, number>
  );

  return {
    totalOrders: orders.length,
    totalAmount,
    totalQuantity,
    organizationCount: organizations.size,
    gameCount: games.size,
    statusBreakdown
  };
});

// Generate default report title based on selected orders
const defaultReportTitle = computed(() => {
  if (!currentOrders.value || currentOrders.value.length === 0) return '';

  try {
    const dates = currentOrders.value.map(order => new Date(order.orderDate));
    const minDate = new Date(Math.min(...dates.map(d => d.getTime())));
    const maxDate = new Date(Math.max(...dates.map(d => d.getTime())));

    const formatDate = (date: Date) => date.toISOString().split('T')[0];

    if (minDate.getTime() === maxDate.getTime()) {
      return `Financial Report - ${formatDate(minDate)}`;
    }
    return `Financial Report - ${formatDate(minDate)} to ${formatDate(maxDate)}`;
  } catch {
    return 'Financial Report';
  }
});

// Calculate start and end dates based on selected orders
const calculatedDateRange = computed(() => {
  if (!currentOrders.value || currentOrders.value.length === 0) {
    return { startDate: null, endDate: null };
  }

  try {
    const dates = currentOrders.value.map(order => new Date(order.orderDate));
    const minDate = new Date(Math.min(...dates.map(d => d.getTime())));
    const maxDate = new Date(Math.max(...dates.map(d => d.getTime())));

    // Set time to start of day for start date and end of day for end date
    minDate.setHours(0, 0, 0, 0);
    maxDate.setHours(23, 59, 59, 999);

    return {
      startDate: minDate.getTime(),
      endDate: maxDate.getTime()
    };
  } catch {
    return { startDate: null, endDate: null };
  }
});

// Order list columns for expandable table
const orderColumns = computed<DataTableColumns<OrderData>>(() => [
  {
    title: $t('page.finance.orderId'),
    key: 'id',
    width: 100,
    render: row => h('span', { class: 'font-mono text-xs' }, row.id)
  },
  {
    title: $t('page.finance.orderDate'),
    key: 'orderDate',
    width: 100
  },
  {
    title: $t('page.finance.buyerName'),
    key: 'buyerName',
    width: 120,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: $t('page.finance.distributor'),
    key: 'distributor',
    width: 140,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: $t('page.finance.gameName'),
    key: 'gameName',
    width: 180,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: $t('page.finance.quantity'),
    key: 'quantity',
    width: 80,
    render: row => h('span', { class: 'text-center' }, row.quantity.toString())
  },
  {
    title: $t('page.finance.finalPrice'),
    key: 'finalPrice',
    width: 100,
    render: row => h('span', { class: 'text-green-600 font-medium' }, `$${row.finalPrice.toFixed(2)}`)
  },
  {
    title: $t('page.finance.orderAmount'),
    key: 'orderAmount',
    width: 120,
    render: row => h('span', { class: 'font-semibold' }, `$${row.orderAmount.toFixed(2)}`)
  },
  {
    title: $t('common.action'),
    key: 'actions',
    width: 80,
    render: row => {
      return h(ButtonIcon, {
        icon: 'mdi:delete',
        tooltipContent: $t('common.delete'),
        class: 'text-error',
        onClick: () => handleRemoveOrder(row.id)
      });
    }
  }
]);

// 防止循环更新的标志
const isUpdatingOrders = ref(false);
const isInitializing = ref(false);

// 初始化当前订单列表
watch(
  () => props.selectedOrders,
  newOrders => {
    // 防止在更新过程中触发循环
    if (isUpdatingOrders.value) return;

    isInitializing.value = true;

    if (newOrders && newOrders.length > 0) {
      // 初始化当前订单列表
      currentOrders.value = [...newOrders];
    } else {
      currentOrders.value = [];
    }

    // 延迟重置初始化标志
    nextTick(() => {
      isInitializing.value = false;
    });
  },
  { immediate: true }
);

// Auto-fill report title and date range when orders change
watch(
  currentOrders,
  (newOrders, oldOrders) => {
    // 防止在初始化或更新过程中触发不必要的操作
    if (isInitializing.value || isUpdatingOrders.value) return;

    // 检查是否真的有变化（避免相同数据触发）
    if (oldOrders && JSON.stringify(newOrders) === JSON.stringify(oldOrders)) return;

    if (newOrders && newOrders.length > 0) {
      // 只在新建模式下自动填充标题
      if (!props.isEditMode && !reportForm.reportTitle && defaultReportTitle.value) {
        reportForm.reportTitle = defaultReportTitle.value;
      }
      // Auto-fill date range only if both fields are empty (to allow manual editing)
      if (calculatedDateRange.value.startDate && calculatedDateRange.value.endDate) {
        if (!reportForm.startDate && !reportForm.endDate) {
          reportForm.startDate = calculatedDateRange.value.startDate;
          reportForm.endDate = calculatedDateRange.value.endDate;
        }
      }
    }

    // 只在非初始化状态下触发事件，避免循环
    if (!isInitializing.value) {
      // 使用防抖机制避免频繁触发
      isUpdatingOrders.value = true;
      nextTick(() => {
        emit('ordersUpdated', [...newOrders]);
        // 延迟重置标志，确保父组件有时间处理
        setTimeout(() => {
          isUpdatingOrders.value = false;
        }, 100);
      });
    }
  },
  { deep: true }
);

// Watch for edit mode changes and populate form with existing data
watch(
  () => [props.isEditMode, props.editingReportData],
  ([isEdit, reportData], prevValues) => {
    // 安全地解构前一个值，避免 undefined 导致的错误
    const [prevIsEdit, prevReportData] = prevValues || [false, null];

    if (isEdit && reportData) {
      // 只有当编辑数据真正发生变化时才更新表单
      const dataChanged = !prevReportData ||
        prevReportData.id !== reportData.id ||
        !prevIsEdit;

      if (dataChanged) {
        // 使用 nextTick 避免同步更新导致的递归问题
        nextTick(() => {
          // 填充表单数据
          Object.assign(reportForm, {
            reportTitle: reportData.reportTitle || '',
            startDate: reportData.startDate || null,
            endDate: reportData.endDate || null,
            reportType: reportData.reportType || 'monthly',
            distributor: reportData.distributor || '',
            notes: reportData.notes || ''
          });
        });
      }
    } else if (!isEdit && prevIsEdit) {
      // 从编辑模式切换到新建模式时，重置表单
      nextTick(() => {
        Object.assign(reportForm, {
          reportTitle: '',
          startDate: null,
          endDate: null,
          reportType: 'monthly',
          distributor: '',
          notes: ''
        });
      });
    }
  },
  { immediate: true }
);

async function handleGenerateReport() {
  if (!currentOrders.value || currentOrders.value.length === 0) {
    window.$message?.warning(pleaseSelectOrdersText.value);
    return;
  }

  try {
    await validate();
    loading.value = true;

    // Simulate report generation
    await new Promise(resolve => setTimeout(resolve, 2000));

    const reportData = {
      ...reportForm,
      orders: currentOrders.value,
      summary: orderSummary.value,
      generatedAt: new Date().toISOString(),
      reportId: props.isEditMode && props.editingReportData ? props.editingReportData.id : `RPT${Date.now()}`,
      isUpdate: props.isEditMode
    };

    emit('reportGenerated', reportData);

    if (props.isEditMode) {
      window.$message?.success(reportUpdateSuccessText.value);
    } else {
      window.$message?.success(reportGenerateSuccessText.value);
    }

    // Reset form only if not in edit mode
    if (!props.isEditMode) {
      Object.assign(reportForm, {
        reportTitle: '',
        startDate: null,
        endDate: null,
        reportType: 'monthly',
        distributor: '',
        notes: ''
      });
      restoreValidation();
    }
  } catch {
    window.$message?.error(reportOperationFailedText.value);
  } finally {
    loading.value = false;
  }
}

function getStatusColor(status: string): 'success' | 'warning' | 'error' | 'default' {
  const colorMap: Record<string, 'success' | 'warning' | 'error' | 'default'> = {
    completed: 'success',
    processing: 'warning',
    cancelled: 'error',
    refunded: 'default'
  };
  return colorMap[status] || 'default';
}

const getStatusText = computed(() => {
  const textMap = {
    completed: $t('page.finance.completed'),
    processing: $t('page.finance.processing'),
    cancelled: $t('page.finance.cancelled'),
    refunded: $t('page.finance.refunded')
  };
  return (status: string) => textMap[status as keyof typeof textMap] || status;
});

// Reset date range to calculated values
function resetDateRange() {
  if (calculatedDateRange.value.startDate && calculatedDateRange.value.endDate) {
    reportForm.startDate = calculatedDateRange.value.startDate;
    reportForm.endDate = calculatedDateRange.value.endDate;
    window.$message?.success($t('page.finance.dateRangeResetSuccess'));
  }
}

// 打开订单搜索对话框
function handleAddCorrectionRecord() {
  showOrderSearchModal.value = true;
}

// 处理订单搜索结果
function handleOrdersSelected(selectedOrders: OrderData[]) {
  // 过滤掉已存在的订单，避免重复
  const existingOrderIds = new Set(currentOrders.value.map(order => order.id));
  const newOrders = selectedOrders.filter(order => !existingOrderIds.has(order.id));

  if (newOrders.length > 0) {
    currentOrders.value = [...currentOrders.value, ...newOrders];
    window.$message?.success(`${orderAddedSuccessText.value} ${newOrders.length} 条`);
  } else {
    window.$message?.warning(duplicateOrderWarningText.value);
  }

  showOrderSearchModal.value = false;
}

// 删除指定订单
function handleRemoveOrder(orderId: string) {
  const index = currentOrders.value.findIndex(order => order.id === orderId);
  if (index > -1) {
    currentOrders.value.splice(index, 1);
    window.$message?.success(orderDeletedSuccessText.value);
  }
}

// 暴露函数给父组件
defineExpose({
  handleGenerateReport
});
</script>

<template>
  <div class="space-y-6">
    <!-- 选中订单概览 -->
    <div class="rounded-lg bg-blue-50 p-4 dark:bg-blue-900/20">
      <div class="mb-4 flex items-center gap-2">
        <SvgIcon icon="mdi:file-document-outline" class="text-20px text-primary" />
        <span class="font-semibold">{{ $t('page.finance.selectOrders') }}</span>
      </div>

      <div v-if="!currentOrders || currentOrders.length === 0" class="py-8 text-center text-gray-500">
        <SvgIcon icon="mdi:file-document-plus" class="mb-2 text-48px" />
        <p>{{ $t('page.finance.noOrdersSelected') }}</p>
        <NButton type="primary" size="small" class="mt-4" @click="handleAddCorrectionRecord">
          <template #icon>
            <SvgIcon icon="mdi:plus" />
          </template>
          {{ addOrderRecordText }}
        </NButton>
      </div>

      <div v-else class="space-y-4">
        <!-- 统计概览 -->
        <div class="grid grid-cols-2 gap-4 lg:grid-cols-5 md:grid-cols-3">
          <div class="rounded-lg bg-white p-3 text-center dark:bg-gray-800">
            <div class="text-2xl text-primary font-bold">{{ orderSummary.totalOrders }}</div>
            <div class="text-sm text-gray-600 dark:text-gray-300">{{ $t('page.finance.totalOrders') }}</div>
          </div>
          <div class="rounded-lg bg-white p-3 text-center dark:bg-gray-800">
            <div class="text-2xl text-green-600 font-bold">${{ orderSummary.totalAmount.toFixed(2) }}</div>
            <div class="text-sm text-gray-600 dark:text-gray-300">{{ $t('page.finance.totalAmount') }}</div>
          </div>
          <div class="rounded-lg bg-white p-3 text-center dark:bg-gray-800">
            <div class="text-2xl text-blue-600 font-bold">{{ orderSummary.totalQuantity }}</div>
            <div class="text-sm text-gray-600 dark:text-gray-300">{{ $t('page.finance.totalQuantity') }}</div>
          </div>
          <div class="rounded-lg bg-white p-3 text-center dark:bg-gray-800">
            <div class="text-2xl text-purple-600 font-bold">{{ orderSummary.organizationCount }}</div>
            <div class="text-sm text-gray-600 dark:text-gray-300">{{ $t('page.finance.distributors') }}</div>
          </div>
          <div class="rounded-lg bg-white p-3 text-center dark:bg-gray-800">
            <div class="text-2xl text-orange-600 font-bold">{{ orderSummary.gameCount }}</div>
            <div class="text-sm text-gray-600 dark:text-gray-300">{{ $t('page.finance.games') }}</div>
          </div>
        </div>

        <!-- 状态分布 -->
        <div class="flex flex-wrap gap-2">
          <span class="text-sm text-gray-600 font-medium dark:text-gray-300">
            {{ $t('page.finance.orderStatus') }}:
          </span>
          <NTag
            v-for="(count, status) in orderSummary.statusBreakdown"
            :key="status"
            :type="getStatusColor(status)"
            size="small"
          >
            {{ getStatusText(status) }}: {{ count }}
          </NTag>
        </div>

        <!-- 订单列表操作栏 -->
        <div class="flex items-center justify-between mt-4">
          <div class="flex items-center gap-2">
            <span class="text-sm text-gray-600 font-medium dark:text-gray-300">
              {{ orderListText }} ({{ currentOrders.length }} {{ recordsCountText }})
            </span>
          </div>
          <NButton size="small" type="primary" @click="handleAddCorrectionRecord">
            <template #icon>
              <SvgIcon icon="mdi:plus" />
            </template>
            {{ addCorrectionRecordText }}
          </NButton>
        </div>

        <!-- 可展开的订单列表 -->
        <NCollapse class="mt-2">
          <NCollapseItem :title="$t('page.finance.viewOrderDetails')" name="order-details">
            <template #header-extra>
              <span class="text-sm text-gray-500">({{ currentOrders.length }} {{ $t('page.finance.orders') }})</span>
            </template>
            <NDataTable
              :columns="orderColumns"
              :data="currentOrders"
              :pagination="false"
              :max-height="300"
              :scroll-x="1050"
              size="small"
              striped
            />
          </NCollapseItem>
        </NCollapse>
      </div>
    </div>

    <!-- 报表生成表单 -->
    <div>
      <div class="mb-4 flex items-center gap-2">
        <SvgIcon
          :icon="isEditMode ? 'mdi:pencil' : 'mdi:file-plus'"
          class="text-20px"
          :class="isEditMode ? 'text-info' : 'text-success'"
        />
        <span class="font-semibold">
          {{ isEditMode ? editFinancialReportText : generateFinancialReportText }}
        </span>
      </div>

      <NForm
        ref="formRef"
        :model="reportForm"
        :rules="rules"
        label-placement="top"
        require-mark-placement="right-hanging"
      >
        <div class="space-y-4">
          <!-- 报表标题 -->
          <NFormItem :label="$t('page.finance.reportTitle')" path="reportTitle">
            <NInput v-model:value="reportForm.reportTitle" :placeholder="$t('page.finance.reportTitle')" />
          </NFormItem>

          <!-- 报表时间段 -->
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium">{{ $t('page.finance.period') }}</span>
              <NButton
                v-if="calculatedDateRange.startDate && calculatedDateRange.endDate"
                size="small"
                type="tertiary"
                @click="resetDateRange"
              >
                <template #icon>
                  <SvgIcon icon="mdi:refresh" />
                </template>
                {{ $t('page.finance.resetToOrderDateRange') }}
              </NButton>
            </div>

            <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
              <NFormItem :label="$t('page.finance.startDate')" path="startDate">
                <NDatePicker
                  v-model:value="reportForm.startDate"
                  type="date"
                  :placeholder="$t('page.finance.startDate')"
                  clearable
                  class="w-full"
                />
              </NFormItem>

              <NFormItem :label="$t('page.finance.endDate')" path="endDate">
                <NDatePicker
                  v-model:value="reportForm.endDate"
                  type="date"
                  :placeholder="$t('page.finance.endDate')"
                  clearable
                  class="w-full"
                />
              </NFormItem>
            </div>
          </div>

          <!-- 报表类型和机构 -->
          <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
            <NFormItem label="Report Type" path="reportType">
              <NSelect
                v-model:value="reportForm.reportType"
                :options="reportTypeOptions"
                placeholder="Select report type"
              />
            </NFormItem>

            <NFormItem :label="$t('page.finance.distributor')" path="distributor">
              <NSelect
                v-model:value="reportForm.distributor"
                :options="distributorOptions"
                :placeholder="$t('page.finance.distributorPlaceholder')"
                clearable
              />
            </NFormItem>
          </div>
        </div>

        <NFormItem label="Notes">
          <NInput
            v-model:value="reportForm.notes"
            type="textarea"
            placeholder="Additional notes for the report..."
            :rows="3"
          />
        </NFormItem>
      </NForm>
    </div>

    <!-- 订单搜索对话框 -->
    <OrderSearchModal
      v-model:show="showOrderSearchModal"
      :excluded-order-ids="currentOrders.map(order => order.id)"
      @orders-selected="handleOrdersSelected"
    />
  </div>
</template>

<style scoped></style>
