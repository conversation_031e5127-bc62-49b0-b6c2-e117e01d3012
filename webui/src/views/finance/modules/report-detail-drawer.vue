<script setup lang="tsx">
import { computed, h, onMounted, reactive, ref, withDefaults } from 'vue';
import type { DataTableColumns } from 'naive-ui';
import {
  NButton,
  NCard,
  NDataTable,
  NDescriptions,
  NDescriptionsItem,
  NGrid,
  NGridItem,
  NSpace,
  NSpin,
  NStatistic,
  NTag
} from 'naive-ui';
import { useAppStore } from '@/store/modules/app';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';

defineOptions({
  name: 'ReportDetailDrawer'
});

interface Props {
  reportId: string;
}

interface OrderData {
  id: string;
  orderDate: string;
  buyerName: string;
  distributor: string;
  gameName: string;
  quantity: number;
  originalPrice: number;
  discount: number;
  finalPrice: number;
  orderAmount: number;
  orderStatus: 'completed' | 'processing' | 'cancelled' | 'refunded';
  country: string;
  productCode: string;
}

interface GameSummaryData {
  productCode: string;
  productName: string;
  country: string;
  orderCount: number;
  quantitySold: number;
  totalRevenue: number;
}

interface ReportDetailData {
  id: string;
  reportName: string;
  reportPeriod: string;
  reportNotes: string;
  organization: string;
  status: 'draft' | 'pending' | 'published';
  createTime: string;
  auditTime?: string;
  publishTime?: string;
  totalAmount: number;
  orders: OrderData[];
  gameSummary: GameSummaryData[];
}

const props = withDefaults(defineProps<Props>(), {});

const emit = defineEmits<{
  close: [];
}>();

const appStore = useAppStore();
const isMobile = computed(() => appStore.isMobile);

const loading = ref(false);
const reportData = ref<ReportDetailData | null>(null);

// 选中的游戏汇总项（单选）
const selectedGameSummaryItem = ref<GameSummaryData | null>(null);

// 分页配置
const orderPagination = reactive({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  onChange: (page: number) => {
    orderPagination.page = page;
  },
  onUpdatePageSize: (pageSize: number) => {
    orderPagination.pageSize = pageSize;
    orderPagination.page = 1;
  }
});

const gamePagination = reactive({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  onChange: (page: number) => {
    gamePagination.page = page;
  },
  onUpdatePageSize: (pageSize: number) => {
    gamePagination.pageSize = pageSize;
    gamePagination.page = 1;
  }
});

// 订单状态颜色映射
function getStatusColor(status: string): 'success' | 'warning' | 'error' | 'info' | 'default' {
  const colorMap: Record<string, 'success' | 'warning' | 'error' | 'info' | 'default'> = {
    completed: 'success',
    processing: 'warning',
    cancelled: 'error',
    refunded: 'info'
  };
  return colorMap[status] || 'default';
}

// 订单状态文本映射
const getStatusText = computed(() => {
  const textMap = {
    completed: $t('page.finance.completed'),
    processing: $t('page.finance.processing'),
    cancelled: $t('page.finance.cancelled'),
    refunded: $t('page.finance.refunded')
  };
  return (status: string) => textMap[status as keyof typeof textMap] || status;
});

// 报表状态颜色映射
function getReportStatusColor(status: string): 'success' | 'warning' | 'error' | 'info' | 'default' {
  const colorMap: Record<string, 'success' | 'warning' | 'error' | 'info' | 'default'> = {
    draft: 'default',
    pending: 'warning',
    published: 'success'
  };
  return colorMap[status] || 'default';
}

// 报表状态文本映射
const getReportStatusText = computed(() => {
  const textMap = {
    draft: $t('page.finance.draft'),
    pending: $t('page.finance.pending'),
    published: $t('page.finance.published')
  };
  return (status: string) => textMap[status as keyof typeof textMap] || status;
});

// 处理游戏汇总项选中（单选）
function handleGameSummaryRowClick(row: GameSummaryData) {
  // 如果点击的是已选中的项，则取消选中
  if (
    selectedGameSummaryItem.value &&
    selectedGameSummaryItem.value.productCode === row.productCode &&
    selectedGameSummaryItem.value.country === row.country
  ) {
    selectedGameSummaryItem.value = null;
  } else {
    // 否则选中当前项
    selectedGameSummaryItem.value = row;
  }

  // 重置订单分页到第一页
  orderPagination.page = 1;
}

// 检查游戏汇总项是否被选中
function isGameSummaryRowSelected(row: GameSummaryData): boolean {
  return (
    selectedGameSummaryItem.value !== null &&
    selectedGameSummaryItem.value.productCode === row.productCode &&
    selectedGameSummaryItem.value.country === row.country
  );
}

// 游戏汇总表格列定义
const gameSummaryColumns = computed<DataTableColumns<GameSummaryData>>(() => [
  {
    title: $t('page.finance.productCode'),
    key: 'productCode',
    width: 120
  },
  {
    title: $t('page.finance.productName'),
    key: 'productName',
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: $t('page.finance.country'),
    key: 'country',
    width: 100
  },
  {
    title: $t('page.finance.orderCount'),
    key: 'orderCount',
    width: 120,
    render: row => h('span', { class: 'font-mono' }, row.orderCount)
  },
  {
    title: $t('page.finance.quantitySold'),
    key: 'quantitySold',
    width: 120,
    render: row => h('span', { class: 'font-mono' }, row.quantitySold)
  },
  {
    title: $t('page.finance.totalRevenue'),
    key: 'totalRevenue',
    width: 140,
    render: row => h('span', { class: 'font-mono text-green-600' }, `$${row.totalRevenue.toFixed(2)}`)
  }
]);

// 订单明细表格列定义
const orderColumns = computed<DataTableColumns<OrderData>>(() => [
  {
    title: $t('page.finance.orderId'),
    key: 'id',
    width: 120,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: $t('page.finance.orderDate'),
    key: 'orderDate',
    width: 120
  },
  {
    title: $t('page.finance.buyerName'),
    key: 'buyerName',
    width: 120,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: $t('page.finance.distributor'),
    key: 'distributor',
    width: 120,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: $t('page.finance.gameName'),
    key: 'gameName',
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: $t('page.finance.quantity'),
    key: 'quantity',
    width: 80,
    render: row => h('span', { class: 'font-mono' }, row.quantity)
  },
  {
    title: $t('page.finance.finalPrice'),
    key: 'finalPrice',
    width: 100,
    render: row => h('span', { class: 'font-mono' }, `$${row.finalPrice.toFixed(2)}`)
  },
  {
    title: $t('page.finance.orderAmount'),
    key: 'orderAmount',
    width: 120,
    render: row => h('span', { class: 'font-mono text-green-600' }, `$${row.orderAmount.toFixed(2)}`)
  },
  {
    title: $t('page.finance.orderStatus'),
    key: 'orderStatus',
    width: 100,
    render: row =>
      h(
        NTag,
        { type: getStatusColor(row.orderStatus), size: 'small' },
        { default: () => getStatusText.value(row.orderStatus) }
      )
  }
]);

// 根据选中的游戏汇总项过滤订单（单选）
const filteredOrders = computed(() => {
  if (!reportData.value) return [];

  // 如果没有选中任何游戏汇总项，返回全部订单
  if (!selectedGameSummaryItem.value) {
    return reportData.value.orders;
  }

  // 根据选中的游戏汇总项过滤订单
  return reportData.value.orders.filter(order => {
    // 通过产品代码和国家匹配订单
    return (
      order.productCode === selectedGameSummaryItem.value!.productCode &&
      order.country === selectedGameSummaryItem.value!.country
    );
  });
});

// 计算汇总统计（基于过滤后的订单）
const summaryStats = computed(() => {
  if (!reportData.value) return null;

  const orders = filteredOrders.value;
  const totalOrders = orders.length;
  const totalAmount = orders.reduce((sum, order) => sum + order.orderAmount, 0);
  const totalQuantity = orders.reduce((sum, order) => sum + order.quantity, 0);
  const uniqueOrganizations = new Set(orders.map(order => order.distributor)).size;
  const uniqueGames = new Set(orders.map(order => order.gameName)).size;
  const uniqueCountries = new Set(orders.map(order => order.country)).size;

  return {
    totalOrders,
    totalAmount,
    totalQuantity,
    uniqueOrganizations,
    uniqueGames,
    uniqueCountries
  };
});

// 导出报表
function handleExportReport() {
  window.$message?.info($t('page.finance.exportReport'));
}

// 模拟加载报表数据
async function loadReportData() {
  loading.value = true;
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 模拟数据
    reportData.value = {
      id: props.reportId,
      reportName: `Report - ${props.reportId}`,
      reportPeriod: 'January 2024',
      reportNotes: 'This report contains all orders from January 2024, and has been reviewed.',
      organization: 'OrgA',
      status: 'published',
      createTime: '2024-01-31',
      auditTime: '2024-02-01',
      publishTime: '2024-02-02',
      totalAmount: 125600,
      orders: generateMockOrders(),
      gameSummary: generateMockGameSummary()
    };
  } catch (error) {
    window.$message?.error('加载报表数据失败');
  } finally {
    loading.value = false;
  }
}

// 生成模拟订单数据
function generateMockOrders(): OrderData[] {
  const orders: OrderData[] = [];
  const games = ['Game A', 'Game B', 'Game C', 'Game D', 'Game E'];
  const distributors = ['Organization 1', 'Organization 2', 'Organization 3'];
  const countries = ['US', 'JP', 'DE', 'FR', 'UK'];
  const statuses: OrderData['orderStatus'][] = ['completed', 'processing', 'cancelled', 'refunded'];

  // eslint-disable-next-line no-plusplus
  for (let i = 1; i <= 50; i++) {
    const quantity = Math.floor(Math.random() * 5) + 1;
    const originalPrice = Math.floor(Math.random() * 50) + 10;
    const discount = Math.floor(Math.random() * 20);
    const finalPrice = originalPrice - discount;

    // 随机选择游戏和国家
    const gameIndex = Math.floor(Math.random() * games.length);
    const countryIndex = Math.floor(Math.random() * countries.length);
    const selectedGame = games[gameIndex];
    const selectedCountry = countries[countryIndex];

    // 根据游戏和国家生成对应的产品代码（与游戏汇总数据匹配）
    const productCode = `PROD${String(gameIndex * 10 + countryIndex + 1).padStart(3, '0')}`;

    orders.push({
      id: `ORD${String(i).padStart(3, '0')}`,
      orderDate: `2024-01-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
      buyerName: `Buyer ${i}`,
      distributor: distributors[Math.floor(Math.random() * distributors.length)],
      gameName: selectedGame,
      quantity,
      originalPrice,
      discount,
      finalPrice,
      orderAmount: finalPrice * quantity,
      orderStatus: statuses[Math.floor(Math.random() * statuses.length)],
      country: selectedCountry,
      productCode
    });
  }

  return orders;
}

// 生成模拟游戏汇总数据
function generateMockGameSummary(): GameSummaryData[] {
  const games = ['Game A', 'Game B', 'Game C', 'Game D', 'Game E'];
  const countries = ['US', 'JP', 'DE', 'FR', 'UK'];
  const summary: GameSummaryData[] = [];

  games.forEach((game, gameIndex) => {
    countries.forEach((country, countryIndex) => {
      const orderCount = Math.floor(Math.random() * 10) + 1;
      const quantitySold = Math.floor(Math.random() * 50) + 10;
      const totalRevenue = Math.floor(Math.random() * 5000) + 1000;

      summary.push({
        productCode: `PROD${String(gameIndex * 10 + countryIndex + 1).padStart(3, '0')}`,
        productName: game,
        country,
        orderCount,
        quantitySold,
        totalRevenue
      });
    });
  });

  return summary;
}

onMounted(() => {
  loadReportData();
});
</script>

<template>
  <NSpin :show="loading">
    <div class="min-h-500px">
      <!-- 页面头部 -->
      <div class="mb-4 flex items-center justify-between">
        <h2 class="text-18px font-semibold">{{ reportData?.reportName || $t('page.finance.reportDetail') }}</h2>
        <NButton type="primary" @click="handleExportReport">
          <template #icon>
            <SvgIcon icon="mdi:download" />
          </template>
          {{ $t('page.finance.exportReport') }}
        </NButton>
      </div>

      <NSpace vertical :size="16">
        <!-- 报表基本信息 -->
        <NCard :title="$t('page.finance.reportBasicInfo')" :bordered="false" size="small" class="info-card">
          <template #header-extra>
            <NTag v-if="reportData" :type="getReportStatusColor(reportData.status)" size="small">
              {{ getReportStatusText(reportData.status) }}
            </NTag>
          </template>

          <NDescriptions v-if="reportData" :column="isMobile ? 1 : 2" label-placement="left" size="small">
            <NDescriptionsItem :label="$t('page.finance.reportId')">
              {{ reportData.id }}
            </NDescriptionsItem>
            <NDescriptionsItem :label="$t('page.finance.organization')">
              {{ reportData.organization }}
            </NDescriptionsItem>
            <NDescriptionsItem :label="$t('page.finance.reportPeriod')">
              {{ reportData.reportPeriod }}
            </NDescriptionsItem>
            <NDescriptionsItem :label="$t('page.finance.createTime')">
              {{ reportData.createTime }}
            </NDescriptionsItem>
            <NDescriptionsItem :label="$t('page.finance.totalAmount')" :span="2">
              <span class="text-green-600 font-semibold">${{ reportData.totalAmount.toLocaleString() }}</span>
            </NDescriptionsItem>
            <NDescriptionsItem v-if="reportData.reportNotes" :label="$t('page.finance.reportNotes')" :span="2">
              {{ reportData.reportNotes }}
            </NDescriptionsItem>
          </NDescriptions>
        </NCard>

        <!-- 报表统计概览 -->
        <NCard :title="$t('page.finance.reportSummaryStats')" :bordered="false" size="small" class="stats-card">
          <NGrid v-if="summaryStats" :cols="isMobile ? 2 : 3" :x-gap="16" :y-gap="16">
            <NGridItem>
              <div class="stat-item">
                <NStatistic :label="$t('page.finance.totalOrders')" :value="summaryStats.totalOrders">
                  <template #prefix>
                    <div class="stat-icon stat-icon-blue">
                      <SvgIcon icon="mdi:file-document-multiple" class="text-white" />
                    </div>
                  </template>
                </NStatistic>
              </div>
            </NGridItem>
            <NGridItem>
              <div class="stat-item">
                <NStatistic
                  :label="$t('page.finance.totalAmount')"
                  :value="`$${summaryStats.totalAmount.toLocaleString()}`"
                >
                  <template #prefix>
                    <div class="stat-icon stat-icon-green">
                      <SvgIcon icon="mdi:currency-usd" class="text-white" />
                    </div>
                  </template>
                </NStatistic>
              </div>
            </NGridItem>
            <NGridItem>
              <div class="stat-item">
                <NStatistic :label="$t('page.finance.totalQuantity')" :value="summaryStats.totalQuantity">
                  <template #prefix>
                    <div class="stat-icon stat-icon-orange">
                      <SvgIcon icon="mdi:package-variant" class="text-white" />
                    </div>
                  </template>
                </NStatistic>
              </div>
            </NGridItem>
            <NGridItem>
              <div class="stat-item">
                <NStatistic :label="$t('page.finance.distributors')" :value="summaryStats.uniqueOrganizations">
                  <template #prefix>
                    <div class="stat-icon stat-icon-purple">
                      <SvgIcon icon="mdi:account-group" class="text-white" />
                    </div>
                  </template>
                </NStatistic>
              </div>
            </NGridItem>
            <NGridItem>
              <div class="stat-item">
                <NStatistic :label="$t('page.finance.games')" :value="summaryStats.uniqueGames">
                  <template #prefix>
                    <div class="stat-icon stat-icon-red">
                      <SvgIcon icon="mdi:gamepad-variant" class="text-white" />
                    </div>
                  </template>
                </NStatistic>
              </div>
            </NGridItem>
            <NGridItem>
              <div class="stat-item">
                <NStatistic :label="$t('page.finance.country')" :value="summaryStats.uniqueCountries">
                  <template #prefix>
                    <div class="stat-icon stat-icon-cyan">
                      <SvgIcon icon="mdi:earth" class="text-white" />
                    </div>
                  </template>
                </NStatistic>
              </div>
            </NGridItem>
          </NGrid>
        </NCard>

        <!-- 游戏汇总表格 -->
        <NCard :title="$t('page.finance.gameSummary')" :bordered="false" size="small" class="game-summary-card">
          <template #header-extra>
            <span v-if="selectedGameSummaryItem" class="text-sm text-gray-500">
              {{ $t('page.finance.selectedItem') }}
            </span>
          </template>
          <NDataTable
            v-if="reportData"
            :columns="gameSummaryColumns"
            :data="reportData.gameSummary"
            :loading="loading"
            :pagination="gamePagination"
            :bordered="false"
            :row-class-name="(row: GameSummaryData) => isGameSummaryRowSelected(row) ? 'selected-row' : 'clickable-row'"
            :row-props="(row: GameSummaryData) => ({
              onClick: () => handleGameSummaryRowClick(row),
              style: isGameSummaryRowSelected(row) ? {
                background: 'linear-gradient(135deg, #722ed1 0%, #9254de 100%)',
                backgroundColor: '#722ed1',
                color: '#ffffff',
                fontWeight: '700',
                border: '3px solid #531dab',
                borderRadius: '8px',
                boxShadow: '0 4px 20px rgba(114, 46, 209, 0.4)',
                transform: 'scale(1.02)'
              } : {}
            })"
            size="small"
            flex-height
            class="h-300px"
          />
        </NCard>

        <!-- 订单明细列表 -->
        <NCard :title="$t('page.finance.orderDetails')" :bordered="false" size="small" class="order-details-card">
          <template #header-extra>
            <span class="text-sm text-gray-500">
              {{ selectedGameSummaryItem ? $t('page.finance.filteredOrders') : $t('page.finance.allOrders') }} ({{
                filteredOrders.length
              }})
            </span>
          </template>
          <NDataTable
            v-if="reportData"
            :columns="orderColumns"
            :data="filteredOrders"
            :loading="loading"
            :pagination="orderPagination"
            :bordered="false"
            size="small"
            flex-height
            class="h-400px"
          />
        </NCard>
      </NSpace>
    </div>
  </NSpin>
</template>

<style scoped>
/* 报表基本信息卡片 - 淡蓝色主题 */
.info-card {
  background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
  border-left: 4px solid #1890ff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

.info-card :deep(.n-card-header) {
  background: rgba(24, 144, 255, 0.05);
  border-bottom: 1px solid rgba(24, 144, 255, 0.1);
  margin-bottom: 16px;
  padding-bottom: 16px;
}

.info-card :deep(.n-card__content) {
  padding-top: 0;
}

/* 统计概览卡片 - 淡绿色主题 */
.stats-card {
  background: linear-gradient(135deg, #f6ffed 0%, #f0f9e8 100%);
  border-left: 4px solid #52c41a;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(82, 196, 26, 0.1);
}

.stats-card :deep(.n-card-header) {
  background: rgba(82, 196, 26, 0.05);
  border-bottom: 1px solid rgba(82, 196, 26, 0.1);
  margin-bottom: 16px;
  padding-bottom: 16px;
}

.stats-card :deep(.n-card__content) {
  padding-top: 0;
}

/* 游戏汇总卡片 - 淡紫色主题 */
.game-summary-card {
  background: linear-gradient(135deg, #f9f0ff 0%, #f4e6ff 100%);
  border-left: 4px solid #722ed1;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(114, 46, 209, 0.1);
}

.game-summary-card :deep(.n-card-header) {
  background: rgba(114, 46, 209, 0.05);
  border-bottom: 1px solid rgba(114, 46, 209, 0.1);
  margin-bottom: 16px;
  padding-bottom: 16px;
}

.game-summary-card :deep(.n-card__content) {
  padding-top: 0;
}

/* 订单明细卡片 - 淡橙色主题 */
.order-details-card {
  background: linear-gradient(135deg, #fff7e6 0%, #fff2d9 100%);
  border-left: 4px solid #fa8c16;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(250, 140, 22, 0.1);
}

.order-details-card :deep(.n-card-header) {
  background: rgba(250, 140, 22, 0.05);
  border-bottom: 1px solid rgba(250, 140, 22, 0.1);
  margin-bottom: 16px;
  padding-bottom: 16px;
}

.order-details-card :deep(.n-card__content) {
  padding-top: 0;
}

/* 统计图标样式 */
.stat-item {
  padding: 12px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.stat-icon-blue {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
}

.stat-icon-green {
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
}

.stat-icon-orange {
  background: linear-gradient(135deg, #fa8c16 0%, #d46b08 100%);
}

.stat-icon-purple {
  background: linear-gradient(135deg, #722ed1 0%, #531dab 100%);
}

.stat-icon-red {
  background: linear-gradient(135deg, #f5222d 0%, #cf1322 100%);
}

.stat-icon-cyan {
  background: linear-gradient(135deg, #13c2c2 0%, #08979c 100%);
}

/* 统计卡片内的数字样式 */
:deep(.n-statistic-value) {
  font-weight: 600;
  color: #262626;
  font-size: 24px;
}

:deep(.n-statistic-label) {
  font-weight: 500;
  color: #595959;
  margin-bottom: 4px;
}

/* 表格样式优化 */
:deep(.n-data-table) {
  border-radius: 6px;
  overflow: hidden;
  background: transparent;
}

:deep(.n-data-table-wrapper) {
  background: transparent;
}

:deep(.n-data-table-table) {
  background: transparent;
}

/* 游戏汇总表格样式 */
.game-summary-card :deep(.n-data-table-th) {
  background: rgba(114, 46, 209, 0.08);
  font-weight: 600;
  color: #722ed1;
  border-bottom: 1px solid rgba(114, 46, 209, 0.15);
}

.game-summary-card :deep(.n-data-table-tr:hover) {
  background: rgba(114, 46, 209, 0.05);
}

.game-summary-card :deep(.n-data-table-td) {
  border-bottom: 1px solid rgba(114, 46, 209, 0.08);
}

/* 可点击行样式 */
.game-summary-card :deep(.clickable-row) {
  cursor: pointer;
  transition: all 0.2s ease;
}

.game-summary-card :deep(.clickable-row:hover) {
  background: rgba(114, 46, 209, 0.08) !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(114, 46, 209, 0.15);
}

/* 选中行样式 - 整行明显高亮 */
.game-summary-card :deep(.n-data-table .n-data-table-tbody .selected-row) {
  background-color: #722ed1 !important;
  background-image: linear-gradient(135deg, #722ed1 0%, #9254de 100%) !important;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 3px solid #531dab !important;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(114, 46, 209, 0.4);
  transform: scale(1.02);
  position: relative;
}

.game-summary-card :deep(.n-data-table .n-data-table-tbody .selected-row:hover) {
  background-color: #531dab !important;
  background-image: linear-gradient(135deg, #531dab 0%, #722ed1 100%) !important;
  transform: scale(1.02) translateY(-2px);
  box-shadow: 0 6px 24px rgba(114, 46, 209, 0.5);
}

.game-summary-card :deep(.n-data-table .n-data-table-tbody .selected-row .n-data-table-td) {
  font-weight: 700;
  color: #ffffff !important;
  border-bottom: none !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  background-color: transparent !important;
  background-image: none !important;
}

/* 强制覆盖所有可能的背景样式 */
.game-summary-card :deep(.selected-row),
.game-summary-card :deep(.selected-row td),
.game-summary-card :deep(.selected-row .n-data-table-td) {
  background: #722ed1 !important;
  background-color: #722ed1 !important;
}

/* 订单明细表格样式 */
.order-details-card :deep(.n-data-table-th) {
  background: rgba(250, 140, 22, 0.08);
  font-weight: 600;
  color: #fa8c16;
  border-bottom: 1px solid rgba(250, 140, 22, 0.15);
}

.order-details-card :deep(.n-data-table-tr:hover) {
  background: rgba(250, 140, 22, 0.05);
}

.order-details-card :deep(.n-data-table-td) {
  border-bottom: 1px solid rgba(250, 140, 22, 0.08);
}

/* 表格分页器样式 */
.game-summary-card :deep(.n-pagination) {
  background: rgba(114, 46, 209, 0.03);
  border-radius: 6px;
  padding: 8px;
  margin-top: 12px;
}

.order-details-card :deep(.n-pagination) {
  background: rgba(250, 140, 22, 0.03);
  border-radius: 6px;
  padding: 8px;
  margin-top: 12px;
}

/* 表格内容文字颜色 */
.game-summary-card :deep(.n-data-table-td) {
  color: #595959;
}

.order-details-card :deep(.n-data-table-td) {
  color: #595959;
}

/* 表格边框圆角 */
.game-summary-card :deep(.n-data-table-wrapper) {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid rgba(114, 46, 209, 0.1);
}

.order-details-card :deep(.n-data-table-wrapper) {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid rgba(250, 140, 22, 0.1);
}

/* 描述列表样式 */
:deep(.n-descriptions-item-label) {
  font-weight: 500;
  color: #595959;
}

:deep(.n-descriptions-item-content) {
  color: #262626;
}

/* 暗色主题适配 */
.dark .info-card {
  background: linear-gradient(135deg, #0f1419 0%, #1a1f2e 100%);
  border-left-color: #4096ff;
}

.dark .stats-card {
  background: linear-gradient(135deg, #0f1a0f 0%, #1a2e1a 100%);
  border-left-color: #73d13d;
}

.dark .game-summary-card {
  background: linear-gradient(135deg, #1a0f1a 0%, #2e1a2e 100%);
  border-left-color: #9254de;
}

.dark .order-details-card {
  background: linear-gradient(135deg, #1a150f 0%, #2e251a 100%);
  border-left-color: #ffa940;
}

.dark :deep(.n-card-header) {
  background: rgba(255, 255, 255, 0.02) !important;
  border-bottom-color: rgba(255, 255, 255, 0.06) !important;
}

/* 暗色主题下的游戏汇总表格 */
.dark .game-summary-card :deep(.n-data-table-th) {
  background: rgba(146, 84, 222, 0.15);
  color: #9254de;
  border-bottom-color: rgba(146, 84, 222, 0.2);
}

.dark .game-summary-card :deep(.n-data-table-tr:hover) {
  background: rgba(146, 84, 222, 0.08);
}

.dark .game-summary-card :deep(.n-data-table-td) {
  border-bottom-color: rgba(146, 84, 222, 0.1);
}

/* 暗色主题下的可点击行样式 */
.dark .game-summary-card :deep(.clickable-row:hover) {
  background: rgba(146, 84, 222, 0.12) !important;
  box-shadow: 0 2px 8px rgba(146, 84, 222, 0.2);
}

/* 暗色主题下的选中行样式 - 整行明显高亮 */
.dark .game-summary-card :deep(.n-data-table .n-data-table-tbody .selected-row) {
  background-color: #9254de !important;
  background-image: linear-gradient(135deg, #9254de 0%, #b37feb 100%) !important;
  border-color: #722ed1 !important;
  box-shadow: 0 4px 20px rgba(146, 84, 222, 0.5);
  transform: scale(1.02);
}

.dark .game-summary-card :deep(.n-data-table .n-data-table-tbody .selected-row:hover) {
  background-color: #722ed1 !important;
  background-image: linear-gradient(135deg, #722ed1 0%, #9254de 100%) !important;
  box-shadow: 0 6px 24px rgba(146, 84, 222, 0.6);
  transform: scale(1.02) translateY(-2px);
}

.dark .game-summary-card :deep(.n-data-table .n-data-table-tbody .selected-row .n-data-table-td) {
  color: #ffffff !important;
  font-weight: 700;
  border-bottom: none !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  background-color: transparent !important;
  background-image: none !important;
}

/* 暗色主题强制覆盖所有可能的背景样式 */
.dark .game-summary-card :deep(.selected-row),
.dark .game-summary-card :deep(.selected-row td),
.dark .game-summary-card :deep(.selected-row .n-data-table-td) {
  background: #9254de !important;
  background-color: #9254de !important;
}

/* 暗色主题下的订单明细表格 */
.dark .order-details-card :deep(.n-data-table-th) {
  background: rgba(255, 169, 64, 0.15);
  color: #ffa940;
  border-bottom-color: rgba(255, 169, 64, 0.2);
}

.dark .order-details-card :deep(.n-data-table-tr:hover) {
  background: rgba(255, 169, 64, 0.08);
}

.dark .order-details-card :deep(.n-data-table-td) {
  border-bottom-color: rgba(255, 169, 64, 0.1);
}

/* 暗色主题下的分页器 */
.dark .game-summary-card :deep(.n-pagination) {
  background: rgba(146, 84, 222, 0.08);
}

.dark .order-details-card :deep(.n-pagination) {
  background: rgba(255, 169, 64, 0.08);
}

/* 暗色主题下的表格边框 */
.dark .game-summary-card :deep(.n-data-table-wrapper) {
  border-color: rgba(146, 84, 222, 0.15);
}

.dark .order-details-card :deep(.n-data-table-wrapper) {
  border-color: rgba(255, 169, 64, 0.15);
}

/* 暗色主题下的表格文字 */
.dark .game-summary-card :deep(.n-data-table-td) {
  color: #d9d9d9;
}

.dark .order-details-card :deep(.n-data-table-td) {
  color: #d9d9d9;
}

/* 暗色主题下的统计项样式 */
.dark .stat-item {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

.dark .stat-item:hover {
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.dark :deep(.n-statistic-value) {
  color: #ffffff;
}

.dark :deep(.n-statistic-label) {
  color: #d9d9d9;
}
</style>
