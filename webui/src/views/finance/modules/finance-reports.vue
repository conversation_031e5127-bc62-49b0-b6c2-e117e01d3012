<script setup lang="tsx">
import { computed, h, reactive, ref } from 'vue';
import type { DataTableColumns, DataTableRowKey } from 'naive-ui';
import { NButton, NDataTable, NDatePicker, NDrawer, NDrawerContent, NInput, NSelect, NSpace, NTag } from 'naive-ui';
import { useAppStore } from '@/store/modules/app';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';
import ButtonIcon from '@/components/custom/button-icon.vue';
import ReportDetailDrawer from './report-detail-drawer.vue';

defineOptions({
  name: 'FinanceReports'
});

interface Emits {
  (e: 'switchToOrders'): void;
  (e: 'editReport', reportId: string): void;
}

const emit = defineEmits<Emits>();

interface ReportData {
  id: string;
  organization: string;
  period: string;
  status: 'draft' | 'pending' | 'published';
  createTime: string;
  auditTime?: string;
  publishTime?: string;
  totalAmount: number;
}

const searchForm = reactive({
  organization: '',
  status: '',
  period: null
});

const loading = ref(false);

const appStore = useAppStore();
const isMobile = computed(() => appStore.isMobile);

// 抽屉相关状态
const showReportDrawer = ref(false);
const selectedReportId = ref<string>('');

// 批量选择相关状态
const checkedRowKeys = ref<DataTableRowKey[]>([]);

const tableData = ref<ReportData[]>([
  {
    id: 'RPT001',
    organization: 'OrgA',
    period: '2024-01',
    status: 'published',
    createTime: '2024-01-31',
    auditTime: '2024-02-01',
    publishTime: '2024-02-02',
    totalAmount: 125600
  },
  {
    id: 'RPT002',
    organization: 'OrgB',
    period: '2024-01',
    status: 'pending',
    createTime: '2024-01-31',
    auditTime: '2024-02-01',
    totalAmount: 89400
  },
  {
    id: 'RPT003',
    organization: 'OrgC',
    period: '2024-01',
    status: 'draft',
    createTime: '2024-01-31',
    totalAmount: 67800
  }
]);

const columns = computed<DataTableColumns<ReportData>>(() => [
  {
    type: 'selection',
    width: 50
  },
  {
    title: $t('page.finance.reportId'),
    key: 'id',
    width: 120
  },
  {
    title: $t('page.finance.organization'),
    key: 'organization',
    width: 150
  },
  {
    title: $t('page.finance.period'),
    key: 'period',
    width: 120
  },
  {
    title: $t('page.finance.status'),
    key: 'status',
    width: 100,
    render: row => {
      const statusMap = {
        draft: { type: 'default', text: $t('page.finance.draft') },
        pending: { type: 'warning', text: $t('page.finance.pending') },
        published: { type: 'success', text: $t('page.finance.published') }
      };
      const status = statusMap[row.status];
      return h(NTag, { type: status.type as any }, { default: () => status.text });
    }
  },
  {
    title: $t('page.finance.createTime'),
    key: 'createTime',
    width: 120
  },
  {
    title: $t('page.finance.auditTime'),
    key: 'auditTime',
    width: 120,
    render: row => row.auditTime || '-'
  },
  {
    title: $t('page.finance.publishTime'),
    key: 'publishTime',
    width: 120,
    render: row => row.publishTime || '-'
  },
  {
    title: $t('page.finance.totalAmount'),
    key: 'totalAmount',
    width: 120,
    render: row => `$${row.totalAmount.toLocaleString()}`
  },
  {
    title: $t('page.finance.actions'),
    key: 'actions',
    width: 120,
    render: row => {
      const actions: any[] = [];

      actions.push(
        h(ButtonIcon, {
          icon: 'mdi:eye',
          tooltipContent: $t('page.finance.view'),
          class: 'text-primary',
          onClick: () => handleViewReport(row.id)
        })
      );

      if (row.status === 'draft') {
        actions.push(
          h(ButtonIcon, {
            icon: 'mdi:pencil',
            tooltipContent: $t('common.edit'),
            class: 'text-info',
            onClick: () => handleEditReport(row.id)
          })
        );
      }

      if (row.status === 'pending') {
        actions.push(
          h(ButtonIcon, {
            icon: 'mdi:check-circle',
            tooltipContent: $t('page.finance.audit'),
            class: 'text-success',
            onClick: () => handleAuditReport(row.id)
          })
        );
      }

      return h(NSpace, { size: 8 }, { default: () => actions });
    }
  }
]);

// Status options for select
const statusOptions = computed(() => [
  { label: $t('page.finance.draft'), value: 'draft' },
  { label: $t('page.finance.pending'), value: 'pending' },
  { label: $t('page.finance.published'), value: 'published' }
]);

// 多语言计算属性
const batchAuditText = computed(() => {
  try {
    return $t('page.finance.batchAudit' as any);
  } catch {
    return '批量审核';
  }
});

const selectedCountText = computed(() => {
  try {
    return $t('page.finance.selectedCount' as any);
  } catch {
    return '已选择';
  }
});

const itemsText = computed(() => {
  try {
    return $t('page.finance.items' as any);
  } catch {
    return '项';
  }
});

const selectAllText = computed(() => {
  try {
    return $t('page.finance.selectAll' as any);
  } catch {
    return '全选';
  }
});

const clearSelectionText = computed(() => {
  try {
    return $t('page.finance.clearSelection' as any);
  } catch {
    return '清空选择';
  }
});

const batchAuditConfirmText = computed(() => {
  try {
    return $t('page.finance.batchAuditConfirm' as any);
  } catch {
    return '确认批量审核选中的报告？';
  }
});

const batchAuditSuccessText = computed(() => {
  try {
    return $t('page.finance.batchAuditSuccess' as any);
  } catch {
    return '批量审核完成';
  }
});

const noReportsSelectedText = computed(() => {
  try {
    return $t('page.finance.noReportsSelected' as any);
  } catch {
    return '请先选择要审核的报告';
  }
});

const noPendingReportsText = computed(() => {
  try {
    return $t('page.finance.noPendingReports' as any);
  } catch {
    return '所选报告中没有待审核的报告';
  }
});

const confirmOperationText = computed(() => {
  try {
    return $t('page.finance.confirmOperation' as any);
  } catch {
    return '确认操作';
  }
});

const confirmText = computed(() => {
  try {
    return $t('common.confirm' as any);
  } catch {
    return '确认';
  }
});

const cancelText = computed(() => {
  try {
    return $t('common.cancel' as any);
  } catch {
    return '取消';
  }
});

const pagination = reactive({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  onChange: (page: number) => {
    pagination.page = page;
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
  }
});

function handleSearch() {
  loading.value = true;
  setTimeout(() => {
    loading.value = false;
  }, 1000);
}

function handleReset() {
  Object.assign(searchForm, {
    organization: '',
    status: '',
    period: null
  });
}

function handleViewReport(reportId: string) {
  selectedReportId.value = reportId;
  showReportDrawer.value = true;
}

function handleEditReport(reportId: string) {
  // 触发编辑报告事件，传递报告ID给父组件
  emit('editReport', reportId);
}

function handleAuditReport(reportId: string) {
  window.$message?.info(`审核报表: ${reportId}`);
}

function handleGenerateReport() {
  // 切换到订单列表tab，开始新建报告流程
  emit('switchToOrders');
}

// 批量选择相关函数
function handleRowCheck(rowKeys: DataTableRowKey[]) {
  checkedRowKeys.value = rowKeys;
}

function handleSelectAll() {
  const allKeys = tableData.value.map(item => item.id);
  checkedRowKeys.value = allKeys;
}

function handleClearSelection() {
  checkedRowKeys.value = [];
}

function handleBatchAudit() {
  if (checkedRowKeys.value.length === 0) {
    window.$message?.warning(noReportsSelectedText.value);
    return;
  }

  // 只能审核状态为 pending 的报告
  const pendingReports = tableData.value.filter(
    item => checkedRowKeys.value.includes(item.id) && item.status === 'pending'
  );

  if (pendingReports.length === 0) {
    window.$message?.warning(noPendingReportsText.value);
    return;
  }

  window.$dialog?.warning({
    title: confirmOperationText.value,
    content: `${batchAuditConfirmText.value} (${pendingReports.length} ${itemsText.value})`,
    positiveText: confirmText.value,
    negativeText: cancelText.value,
    onPositiveClick: () => {
      // 模拟批量审核操作
      pendingReports.forEach(report => {
        const index = tableData.value.findIndex(item => item.id === report.id);
        if (index !== -1) {
          tableData.value[index].status = 'published';
          tableData.value[index].auditTime = new Date().toISOString().split('T')[0];
          tableData.value[index].publishTime = new Date().toISOString().split('T')[0];
        }
      });

      window.$message?.success(`${batchAuditSuccessText.value} (${pendingReports.length} ${itemsText.value})`);

      // 清空选择
      checkedRowKeys.value = [];
    }
  });
}

function handleRefresh() {
  loading.value = true;
  // 模拟刷新数据
  setTimeout(() => {
    loading.value = false;
    // 这里可以调用实际的数据刷新API
  }, 1000);
}
</script>

<template>
  <div class="space-y-3">
    <!-- 紧凑筛选条件 -->
    <div class="rounded-lg bg-gray-50 p-4 dark:bg-gray-800">
      <!-- 搜索字段和按钮 -->
      <div :class="isMobile ? 'space-y-3' : 'flex flex-wrap gap-4 items-end'">
        <div class="min-w-40 flex flex-col flex-1 gap-1">
          <span class="text-sm text-gray-600 font-medium dark:text-gray-300">
            {{ $t('page.finance.organization') }}
          </span>
          <NInput
            v-model:value="searchForm.organization"
            :placeholder="$t('page.finance.organizationPlaceholder')"
            size="small"
            clearable
          >
            <template #prefix>
              <SvgIcon icon="mdi:office-building" class="text-gray-400" />
            </template>
          </NInput>
        </div>

        <div class="min-w-32 flex flex-col flex-1 gap-1">
          <span class="text-sm text-gray-600 font-medium dark:text-gray-300">{{ $t('page.finance.status') }}</span>
          <NSelect
            v-model:value="searchForm.status"
            :placeholder="$t('page.finance.statusPlaceholder')"
            :options="statusOptions"
            size="small"
            clearable
          />
        </div>

        <div class="min-w-40 flex flex-col flex-1 gap-1">
          <span class="text-sm text-gray-600 font-medium dark:text-gray-300">{{ $t('page.finance.period') }}</span>
          <NDatePicker
            v-model:value="searchForm.period"
            type="month"
            :placeholder="$t('page.finance.selectMonth')"
            size="small"
            clearable
          />
        </div>

        <!-- 操作按钮 -->
        <div class="flex gap-2" :class="isMobile ? 'w-full justify-center' : 'flex-shrink-0'">
          <NButton size="small" @click="handleReset">
            {{ $t('common.reset') }}
          </NButton>
          <NButton type="primary" size="small" :loading="loading" @click="handleSearch">
            <template #icon>
              <SvgIcon icon="mdi:magnify" />
            </template>
            {{ $t('common.search') }}
          </NButton>
        </div>
      </div>
    </div>

    <!-- 操作栏 -->
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-2">
        <NButton type="primary" @click="handleGenerateReport">
          <template #icon>
            <SvgIcon icon="mdi:file-plus" />
          </template>
          {{ $t('page.finance.generateReport') }}
        </NButton>
        <NButton :loading="loading" @click="handleRefresh">
          <template #icon>
            <SvgIcon icon="mdi:refresh" />
          </template>
          {{ $t('common.refresh') }}
        </NButton>
        <NButton
          :disabled="checkedRowKeys.length === 0"
          @click="handleBatchAudit"
        >
          <template #icon>
            <SvgIcon icon="mdi:check-all" />
          </template>
          {{ batchAuditText }}
        </NButton>
      </div>

      <!-- 选择状态和批量操作 -->
      <div v-if="checkedRowKeys.length > 0" class="flex items-center gap-2 text-sm text-gray-600">
        <span>{{ selectedCountText }} {{ checkedRowKeys.length }} {{ itemsText }}</span>
        <NButton size="small" text @click="handleSelectAll">
          {{ selectAllText }}
        </NButton>
        <NButton size="small" text @click="handleClearSelection">
          {{ clearSelectionText }}
        </NButton>
      </div>
    </div>

    <!-- 数据表格 -->
    <NDataTable
      :columns="columns"
      :data="tableData"
      :loading="loading"
      :pagination="pagination"
      :bordered="false"
      size="small"
      flex-height
      :scroll-x="1000"
      class="h-500px"
      :row-key="(row: ReportData) => row.id"
      v-model:checked-row-keys="checkedRowKeys"
      @update:checked-row-keys="handleRowCheck"
    />

    <!-- 报表详情抽屉 -->
    <NDrawer
      v-model:show="showReportDrawer"
      :width="isMobile ? '100%' : '66.67%'"
      placement="left"
      :mask-closable="true"
      :close-on-esc="true"
    >
      <NDrawerContent :title="$t('page.finance.reportDetail')" closable>
        <ReportDetailDrawer v-if="showReportDrawer" :report-id="selectedReportId" @close="showReportDrawer = false" />
      </NDrawerContent>
    </NDrawer>
  </div>
</template>

<style scoped></style>
