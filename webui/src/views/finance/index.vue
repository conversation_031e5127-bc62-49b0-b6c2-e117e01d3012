<script setup lang="tsx">
import { computed, nextTick, onErrorCaptured, ref } from 'vue';
import { NButton, NCard, NModal, NSpace, NTabPane, NTabs } from 'naive-ui';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';
import FinanceReports from './modules/finance-reports.vue';
import FinanceStats from './modules/finance-stats.vue';
import OrderList from './modules/order-list.vue';
import ReportGenerator from './modules/report-generator.vue';

defineOptions({
  name: 'FinancePage'
});

interface OrderData {
  id: string;
  orderDate: string;
  buyerName: string;
  buyerCountry: string;
  currency: string;
  distributor: string;
  gameName: string;
  quantity: number;
  originalPrice: number;
  discount: number;
  finalPrice: number;
  orderAmount: number;
  orderStatus: 'completed' | 'processing' | 'cancelled' | 'refunded';
}

// 当前选中的订单
const selectedOrders = ref<OrderData[]>([]);

// 当前活跃的标签页
const activeTab = ref('orders');

// 报表生成弹窗状态
const showReportModal = ref(false);

// 报表生成器引用
const reportGeneratorRef = ref();

// 编辑模式状态
const isEditMode = ref(false);
const editingReportId = ref<string>('');
const editingReportData = ref<any>(null);

// 防止递归更新的标志
const isUpdatingState = ref(false);

// 多语言计算属性
const editReportTitle = computed(() => {
  try {
    return $t('page.finance.editReport' as any);
  } catch {
    return '编辑财务报告';
  }
});

const updateReportText = computed(() => {
  try {
    return $t('page.finance.updateReport' as any);
  } catch {
    return '更新报告';
  }
});

const reportUpdateSuccessText = computed(() => {
  try {
    return $t('page.finance.reportUpdateSuccess' as any);
  } catch {
    return '财务报告更新成功';
  }
});

const reportGenerateSuccessText = computed(() => {
  try {
    return $t('page.finance.reportGenerateSuccess' as any);
  } catch {
    return '财务报告生成成功';
  }
});

// 处理订单选择
function handleOrdersSelected(orders: OrderData[]) {
  // 避免在状态更新过程中触发
  if (!isUpdatingState.value) {
    selectedOrders.value = [...orders];
  }
}

// 处理报告生成器中的订单更新
function handleOrdersUpdated(orders: OrderData[]) {
  // 避免在状态更新过程中触发循环
  if (isUpdatingState.value) return;

  // 检查数据是否真的有变化，避免不必要的更新
  if (JSON.stringify(selectedOrders.value) === JSON.stringify(orders)) return;

  // 使用防抖机制避免频繁更新
  nextTick(() => {
    if (!isUpdatingState.value) {
      selectedOrders.value = [...orders];
    }
  });
}

// 打开报表生成弹窗
function handleOpenReportModal() {
  if (!selectedOrders.value || selectedOrders.value.length === 0) {
    window.$message?.warning($t('page.finance.noOrdersSelected'));
    return;
  }
  showReportModal.value = true;
}

// 处理报表生成
function handleReportGenerated(reportData: any) {
  // 这里可以添加更多的报表处理逻辑，比如保存到数据库、显示预览等
  if (reportData.isUpdate) {
    window.$message?.success(reportUpdateSuccessText.value);
  } else {
    window.$message?.success(reportGenerateSuccessText.value);
  }

  showReportModal.value = false;

  // 重置编辑模式状态
  isEditMode.value = false;
  editingReportId.value = '';
  editingReportData.value = null;

  // 切换到报表列表tab查看结果
  activeTab.value = 'reports';
}

// 调用报表生成器的生成函数
function handleConfirmGenerate() {
  if (reportGeneratorRef.value) {
    reportGeneratorRef.value.handleGenerateReport();
  }
}

// 处理切换到订单列表tab
function handleSwitchToOrders() {
  activeTab.value = 'orders';
  // 重置编辑模式
  isEditMode.value = false;
  editingReportId.value = '';
  editingReportData.value = null;
  selectedOrders.value = [];
}

// 处理编辑报告
function handleEditReport(reportId: string) {
  // 防止重复调用
  if (isUpdatingState.value) {
    return;
  }

  isUpdatingState.value = true;

  try {
    // 模拟获取报告数据
    const mockReportData = {
      id: reportId,
      reportTitle: `财务报告 - ${reportId}`,
      startDate: Date.now() - 30 * 24 * 60 * 60 * 1000, // 30天前
      endDate: Date.now(),
      reportType: 'monthly',
      distributor: '',
      notes: '这是一个示例报告',
      orders: [
        {
          id: 'ORD001',
          orderDate: '2024-01-15',
          buyerName: '张三',
          buyerCountry: 'CN',
          currency: 'CNY',
          distributor: 'Steam',
          gameName: '示例游戏1',
          quantity: 2,
          originalPrice: 100,
          discount: 0.1,
          finalPrice: 90,
          orderAmount: 180,
          orderStatus: 'completed' as const
        },
        {
          id: 'ORD002',
          orderDate: '2024-01-16',
          buyerName: '李四',
          buyerCountry: 'CN',
          currency: 'CNY',
          distributor: 'Epic Games',
          gameName: '示例游戏2',
          quantity: 1,
          originalPrice: 200,
          discount: 0.05,
          finalPrice: 190,
          orderAmount: 190,
          orderStatus: 'completed' as const
        }
      ]
    };

    // 原子性地批量更新所有状态
    isEditMode.value = true;
    editingReportId.value = reportId;
    editingReportData.value = mockReportData;
    selectedOrders.value = [...mockReportData.orders];
    activeTab.value = 'orders';

    // 延迟打开弹窗，确保所有状态都已更新
    nextTick(() => {
      setTimeout(() => {
        showReportModal.value = true;
        // 重置更新标志
        isUpdatingState.value = false;
      }, 100); // 给足够的时间确保状态稳定
    });
  } catch {
    isUpdatingState.value = false;
    window.$message?.error('编辑报告时发生错误，请重试');
  }
}

// 错误捕获
onErrorCaptured((error, _instance, info) => {
  // 记录错误信息用于调试
  if (import.meta.env.DEV) {
    // eslint-disable-next-line no-console
    console.error('Finance module error:', error, info);
  }
  window.$message?.error('An error occurred in the finance module. Please try again.');
  return false;
});
</script>

<template>
  <NSpace vertical :size="16">
    <!-- 财务统计 -->
    <NCard :bordered="false" class="card-wrapper">
      <template #header>
        <div class="flex items-center gap-2">
          <SvgIcon icon="mdi:chart-line" class="text-20px" />
          <span class="text-18px font-semibold">{{ $t('page.finance.stats') }}</span>
        </div>
      </template>
      <FinanceStats />
    </NCard>

    <!-- 主要功能区域 -->
    <NCard :bordered="false" class="card-wrapper">
      <NTabs v-model:value="activeTab" type="line" animated>
        <!-- 订单列表标签页 -->
        <NTabPane name="orders" :tab="$t('page.finance.orders')">
          <OrderList
            :pre-selected-orders="isEditMode ? selectedOrders : []"
            :is-edit-mode="isEditMode"
            @orders-selected="handleOrdersSelected"
            @generate-report="handleOpenReportModal"
          />
        </NTabPane>

        <!-- 财务报表标签页 -->
        <NTabPane name="reports" :tab="$t('page.finance.reports')">
          <FinanceReports @switch-to-orders="handleSwitchToOrders" @edit-report="handleEditReport" />
        </NTabPane>
      </NTabs>
    </NCard>

    <!-- 报表生成弹窗 -->
    <NModal
      v-model:show="showReportModal"
      preset="card"
      :title="isEditMode ? `${editReportTitle} - ${editingReportId}` : $t('page.finance.generateReport')"
      class="max-w-5xl w-full"
      :bordered="false"
      size="huge"
      :segmented="true"
      :mask-closable="false"
      :close-on-esc="false"
    >
      <template #header-extra>
        <div class="flex items-center gap-2 text-sm text-gray-500">
          <SvgIcon icon="mdi:information-outline" />
          <span>{{ $t('page.finance.selectedCount', { count: selectedOrders.length }) }}</span>
        </div>
      </template>

      <ReportGenerator
        ref="reportGeneratorRef"
        :selected-orders="selectedOrders"
        :is-edit-mode="isEditMode"
        :editing-report-data="editingReportData"
        @report-generated="handleReportGenerated"
        @orders-updated="handleOrdersUpdated"
      />

      <template #action>
        <div class="flex justify-end gap-2">
          <NButton @click="showReportModal = false">
            {{ $t('common.cancel') }}
          </NButton>
          <NButton type="primary" @click="handleConfirmGenerate">
            <template #icon>
              <SvgIcon :icon="isEditMode ? 'mdi:content-save' : 'mdi:file-plus'" />
            </template>
            {{ isEditMode ? updateReportText : $t('page.finance.generateReport') }}
          </NButton>
        </div>
      </template>
    </NModal>
  </NSpace>
</template>

<style scoped>
.card-wrapper {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
