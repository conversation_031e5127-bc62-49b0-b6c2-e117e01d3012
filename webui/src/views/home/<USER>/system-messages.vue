<script setup lang="ts">
import { computed } from 'vue';
import { $t } from '@/locales';

defineOptions({
  name: 'SystemMessages'
});

interface MessageItem {
  id: number;
  type: 'order' | 'system' | 'security' | 'maintenance';
  title: string;
  content: string;
  time: string;
  isRead: boolean;
}

const messages = computed<MessageItem[]>(() => [
  {
    id: 1,
    type: 'order',
    title: $t('page.home.newOrder'),
    content: 'User <PERSON> purchased Cyberpunk 2077, order amount $59.99',
    time: '5 ' + $t('page.home.minutes'),
    isRead: false
  },
  {
    id: 2,
    type: 'system',
    title: $t('page.home.systemUpdate'),
    content: 'System will be updated tonight at 23:00, estimated maintenance time 30 minutes',
    time: '1 ' + $t('page.home.hours'),
    isRead: false
  },
  {
    id: 3,
    type: 'security',
    title: $t('page.home.securityAlert'),
    content: 'Abnormal login attempt detected, automatically blocked and security enhanced',
    time: '2 ' + $t('page.home.hours'),
    isRead: true
  },
  {
    id: 4,
    type: 'maintenance',
    title: $t('page.home.maintenanceNotice'),
    content: 'Database optimization completed, system performance improved by 15%',
    time: '1 ' + $t('page.home.days'),
    isRead: true
  },
  {
    id: 5,
    type: 'order',
    title: $t('page.home.newOrder'),
    content: 'User Alice Smith purchased Genshin Impact DLC, order amount $29.99',
    time: '2 ' + $t('page.home.days'),
    isRead: true
  }
]);

const getMessageIcon = (type: string) => {
  const iconMap = {
    order: 'mdi:receipt',
    system: 'mdi:cog',
    security: 'mdi:shield-alert',
    maintenance: 'mdi:wrench'
  };
  return iconMap[type as keyof typeof iconMap] || 'mdi:information';
};

const getMessageColor = (type: string) => {
  const colorMap = {
    order: '#52c41a',
    system: '#1890ff',
    security: '#ff4d4f',
    maintenance: '#fa8c16'
  };
  return colorMap[type as keyof typeof colorMap] || '#666';
};
</script>

<template>
  <NCard :title="$t('page.home.systemMessages')" :bordered="false" size="small" segmented class="card-wrapper">
    <template #header-extra>
      <NSpace>
        <NButton text type="primary" size="small">
          {{ $t('page.home.markAsRead') }}
        </NButton>
        <NButton text type="primary" size="small">
          {{ $t('page.home.viewAll') }}
        </NButton>
      </NSpace>
    </template>
    <NList>
      <NListItem v-for="item in messages" :key="item.id">
        <template #prefix>
          <div
            class="w-10 h-10 rounded-full flex items-center justify-center"
            :style="{ backgroundColor: getMessageColor(item.type) + '20' }"
          >
            <SvgIcon
              :icon="getMessageIcon(item.type)"
              class="text-16px"
              :style="{ color: getMessageColor(item.type) }"
            />
          </div>
        </template>
        <template #suffix>
          <div class="text-xs text-gray-400 whitespace-nowrap min-w-20">{{ item.time }}</div>
        </template>
        <NThing>
          <template #header>
            <div class="flex items-center">
              <span :class="{ 'font-semibold': !item.isRead, 'text-gray-600': item.isRead }">
                {{ item.title }}
              </span>
              <NBadge v-if="!item.isRead" dot class="ml-2" />
            </div>
          </template>
          <template #description>
            <div :class="{ 'text-gray-500': item.isRead }" class="text-sm">
              {{ item.content }}
            </div>
          </template>
        </NThing>
      </NListItem>
    </NList>
  </NCard>
</template>

<style scoped></style>
