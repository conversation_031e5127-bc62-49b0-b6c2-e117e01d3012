<script setup lang="ts">
import { computed, ref } from 'vue';
import { useAppStore } from '@/store/modules/app';
import GameStatsDrawer from '@/views/game/modules/game-stats-drawer.vue';
import HeaderBanner from './modules/header-banner.vue';
import CardData from './modules/card-data.vue';
import DailySalesAmountChart from './modules/daily-sales-amount-chart.vue';
import DailySalesQuantityChart from './modules/daily-sales-quantity-chart.vue';
import RegionalSalesAmountChart from './modules/regional-sales-amount-chart.vue';
import RegionalSalesQuantityChart from './modules/regional-sales-quantity-chart.vue';
import TopSalesAmountRanking from './modules/top-sales-amount-ranking.vue';
import TopSalesQuantityRanking from './modules/top-sales-quantity-ranking.vue';
import SystemMessages from './modules/system-messages.vue';
import Notifications from './modules/notifications.vue';

const appStore = useAppStore();

const gap = computed(() => (appStore.isMobile ? 0 : 16));

// 游戏数据接口（与GameStatsDrawer兼容）
interface GameData {
  id: string;
  coverImage: string;
  name: string;
  code: string;
  multiLangName: Record<string, string>;
  languages: string[];
  onHand: number;
  enabled: boolean;
  price: number;
  sales: number;
}

// 游戏统计抽屉相关
const gameStatsDrawerVisible = ref(false);
const selectedGameForStats = ref<GameData | null>(null);

// 处理游戏点击事件
function handleGameClick(game: GameData) {
  selectedGameForStats.value = game;
  gameStatsDrawerVisible.value = true;
}
</script>

<template>
  <div>
    <NSpace vertical :size="16">
      <HeaderBanner />
      <CardData />
      <!-- 四个图表的2x2布局 -->
      <NGrid :x-gap="gap" :y-gap="16" responsive="screen" item-responsive>
        <!-- 第一行：日销售额折线图 -->
        <NGi span="24 s:24 m:12">
          <NCard :bordered="false" class="card-wrapper">
            <DailySalesAmountChart />
          </NCard>
        </NGi>
        <!-- 第一行：日销量折线图 -->
        <NGi span="24 s:24 m:12">
          <NCard :bordered="false" class="card-wrapper">
            <DailySalesQuantityChart />
          </NCard>
        </NGi>
        <!-- 第二行：分区域销售金额饼图 -->
        <NGi span="24 s:24 m:12">
          <NCard :bordered="false" class="card-wrapper">
            <RegionalSalesAmountChart />
          </NCard>
        </NGi>
        <!-- 第二行：分区域销量饼图 -->
        <NGi span="24 s:24 m:12">
          <NCard :bordered="false" class="card-wrapper">
            <RegionalSalesQuantityChart />
          </NCard>
        </NGi>
      </NGrid>

      <!-- TOP10排行榜 -->
      <NGrid :x-gap="gap" :y-gap="16" responsive="screen" item-responsive>
        <!-- 日销售金额TOP10 -->
        <NGi span="24 s:24 m:12">
          <NCard :bordered="false" class="card-wrapper">
            <TopSalesAmountRanking @game-click="handleGameClick" />
          </NCard>
        </NGi>
        <!-- 日销量TOP10 -->
        <NGi span="24 s:24 m:12">
          <NCard :bordered="false" class="card-wrapper">
            <TopSalesQuantityRanking @game-click="handleGameClick" />
          </NCard>
        </NGi>
      </NGrid>

      <!-- 屏蔽消息部分 -->
      <!--
   <NGrid :x-gap="gap" :y-gap="16" responsive="screen" item-responsive>
        <NGi span="24 s:24 m:14">
          <SystemMessages />
        </NGi>
        <NGi span="24 s:24 m:10">
          <Notifications />
        </NGi>
      </NGrid>
  -->
    </NSpace>

    <!-- 游戏统计抽屉 -->
    <GameStatsDrawer v-model:visible="gameStatsDrawerVisible" :game="selectedGameForStats" />
  </div>
</template>

<style scoped></style>
