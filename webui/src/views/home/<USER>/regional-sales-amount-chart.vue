<script setup lang="ts">
import { watch } from 'vue';
import { useAppStore } from '@/store/modules/app';
import { useEcharts } from '@/hooks/common/echarts';
import { $t } from '@/locales';

defineOptions({
  name: 'RegionalSalesAmountChart'
});

const appStore = useAppStore();

const { domRef, updateOptions } = useEcharts(() => ({
  title: {
    text: $t('page.home.salesAmountByRegion'),
    left: 'center',
    textStyle: {
      fontSize: 16,
      fontWeight: 'normal'
    }
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: ${c} ({d}%)'
  },
  legend: {
    bottom: '5%',
    left: 'center',
    itemStyle: {
      borderWidth: 0
    }
  },
  series: [
    {
      color: ['#5da8ff', '#8e9dff', '#fedc69', '#26deca', '#ff7875'],
      name: $t('page.home.salesAmountByRegion'),
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['50%', '45%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 8,
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: true,
        position: 'outside',
        formatter: '{b}\n${c}'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '14',
          fontWeight: 'bold'
        },
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      labelLine: {
        show: true
      },
      data: [] as { name: string; value: number }[]
    }
  ]
}));

async function mockData() {
  try {
    await new Promise(resolve => {
      setTimeout(resolve, 1000);
    });

    updateOptions(opts => {
      opts.series[0].data = [
        { name: $t('page.sales.northAmerica'), value: 125680 },
        { name: $t('page.sales.europe'), value: 98450 },
        { name: $t('page.sales.asia'), value: 156720 },
        { name: $t('page.sales.others'), value: 45890 }
      ];

      return opts;
    });
  } catch (error) {
    console.error('Failed to load chart data:', error);
  }
}

function updateLocale() {
  try {
    updateOptions((opts, factory) => {
      const originOpts = factory();

      opts.title.text = originOpts.title.text;
      opts.series[0].name = originOpts.series[0].name;

      opts.series[0].data = [
        { name: $t('page.sales.northAmerica'), value: 125680 },
        { name: $t('page.sales.europe'), value: 98450 },
        { name: $t('page.sales.asia'), value: 156720 },
        { name: $t('page.sales.others'), value: 45890 }
      ];

      return opts;
    });
  } catch (error) {
    console.error('Failed to update chart locale:', error);
  }
}

async function init() {
  mockData();
}

watch(
  () => appStore.locale,
  () => {
    updateLocale();
  }
);

// init
init();
</script>

<template>
  <div ref="domRef" class="h-360px overflow-hidden"></div>
</template>

<style scoped></style>
