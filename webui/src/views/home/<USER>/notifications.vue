<script setup lang="ts">
import { computed } from 'vue';
import { $t } from '@/locales';

defineOptions({
  name: 'Notifications'
});

interface NotificationItem {
  id: number;
  type: 'success' | 'warning' | 'info' | 'error';
  title: string;
  content: string;
  time: string;
  isRead: boolean;
}

const notifications = computed<NotificationItem[]>(() => [
  {
    id: 1,
    type: 'success',
    title: 'Sales Target Achieved',
    content: 'Congratulations! Monthly sales reached 120% of target, totaling $2,456,789',
    time: '30 ' + $t('page.home.minutes'),
    isRead: false
  },
  {
    id: 2,
    type: 'warning',
    title: 'Inventory Alert',
    content: 'The Legend of Zelda inventory is low, 15 copies remaining, recommend restocking',
    time: '2 ' + $t('page.home.hours'),
    isRead: false
  },
  {
    id: 3,
    type: 'info',
    title: 'New User Registration',
    content: '156 new users registered today, 23% increase from yesterday',
    time: '4 ' + $t('page.home.hours'),
    isRead: true
  },
  {
    id: 4,
    type: 'error',
    title: 'Payment Exception',
    content: 'Order #12345 payment failed, please contact user to confirm payment info',
    time: '6 ' + $t('page.home.hours'),
    isRead: true
  },
  {
    id: 5,
    type: 'success',
    title: 'Report Generated',
    content: 'November financial report has been generated, view details in Finance module',
    time: '1 ' + $t('page.home.days'),
    isRead: true
  }
]);

const getNotificationIcon = (type: string) => {
  const iconMap = {
    success: 'mdi:check-circle',
    warning: 'mdi:alert',
    info: 'mdi:information',
    error: 'mdi:close-circle'
  };
  return iconMap[type as keyof typeof iconMap] || 'mdi:information';
};

const getNotificationColor = (type: string) => {
  const colorMap = {
    success: '#52c41a',
    warning: '#fa8c16',
    info: '#1890ff',
    error: '#ff4d4f'
  };
  return colorMap[type as keyof typeof colorMap] || '#666';
};
</script>

<template>
  <NCard :title="$t('page.home.notifications')" :bordered="false" size="small" segmented class="card-wrapper">
    <template #header-extra>
      <NSpace>
        <NButton text type="primary" size="small">
          {{ $t('page.home.markAsRead') }}
        </NButton>
        <NButton text type="primary" size="small">
          {{ $t('page.home.viewAll') }}
        </NButton>
      </NSpace>
    </template>
    <NList>
      <NListItem v-for="item in notifications" :key="item.id">
        <template #prefix>
          <div
            class="w-10 h-10 rounded-full flex items-center justify-center"
            :style="{ backgroundColor: getNotificationColor(item.type) + '20' }"
          >
            <SvgIcon
              :icon="getNotificationIcon(item.type)"
              class="text-16px"
              :style="{ color: getNotificationColor(item.type) }"
            />
          </div>
        </template>
        <template #suffix>
          <div class="text-xs text-gray-400 whitespace-nowrap min-w-20">{{ item.time }}</div>
        </template>
        <NThing>
          <template #header>
            <div class="flex items-center">
              <span :class="{ 'font-semibold': !item.isRead, 'text-gray-600': item.isRead }">
                {{ item.title }}
              </span>
              <NBadge v-if="!item.isRead" dot class="ml-2" />
            </div>
          </template>
          <template #description>
            <div :class="{ 'text-gray-500': item.isRead }" class="text-sm">
              {{ item.content }}
            </div>
          </template>
        </NThing>
      </NListItem>
    </NList>
  </NCard>
</template>

<style scoped></style>
