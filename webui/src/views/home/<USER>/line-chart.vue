<script setup lang="ts">
import { watch } from 'vue';
import { useAppStore } from '@/store/modules/app';
import { useEcharts } from '@/hooks/common/echarts';
import { $t } from '@/locales';

defineOptions({
  name: 'LineChart'
});

const appStore = useAppStore();

const { domRef, updateOptions } = useEcharts(() => ({
  title: {
    text: $t('page.home.dailyOrderSales'),
    left: 'center',
    textStyle: {
      fontSize: 16,
      fontWeight: 'normal'
    }
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      label: {
        backgroundColor: '#6a7985'
      }
    },
    formatter: function(params: any) {
      let result = params[0].name + '<br/>';
      params.forEach((param: any) => {
        const unit = param.seriesName === $t('page.home.orderAmount') ? '$' : '';
        result += param.marker + param.seriesName + ': ' + unit + param.value + '<br/>';
      });
      return result;
    }
  },
  legend: {
    data: [$t('page.home.orderAmount'), $t('page.home.orderQuantity')],
    bottom: '5%'
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '15%',
    top: '15%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: [] as string[]
  },
  yAxis: [
    {
      type: 'value',
      name: $t('page.home.orderAmount') + ' ($)',
      position: 'left',
      axisLabel: {
        formatter: '${value}'
      }
    },
    {
      type: 'value',
      name: $t('page.home.orderQuantity'),
      position: 'right',
      axisLabel: {
        formatter: '{value}'
      }
    }
  ],
  series: [
    {
      color: '#8e9dff',
      name: $t('page.home.orderAmount'),
      type: 'line',
      smooth: true,
      yAxisIndex: 0,
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0.25,
              color: '#8e9dff'
            },
            {
              offset: 1,
              color: '#fff'
            }
          ]
        }
      },
      emphasis: {
        focus: 'series'
      },
      data: [] as number[]
    },
    {
      color: '#26deca',
      name: $t('page.home.orderQuantity'),
      type: 'line',
      smooth: true,
      yAxisIndex: 1,
      emphasis: {
        focus: 'series'
      },
      data: []
    }
  ]
}));

async function mockData() {
  await new Promise(resolve => {
    setTimeout(resolve, 1000);
  });

  updateOptions(opts => {
    // 最近7天的日期
    const dates = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      dates.push(date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }));
    }

    opts.xAxis.data = dates;
    // 订单金额数据 (美元)
    opts.series[0].data = [12450, 15680, 18920, 16750, 21340, 19580, 23120];
    // 订单数量数据
    opts.series[1].data = [45, 58, 67, 52, 73, 68, 81];

    return opts;
  });
}

function updateLocale() {
  updateOptions((opts, factory) => {
    const originOpts = factory();

    opts.title.text = originOpts.title.text;
    opts.legend.data = originOpts.legend.data;
    opts.yAxis[0].name = originOpts.yAxis[0].name;
    opts.yAxis[1].name = originOpts.yAxis[1].name;
    opts.series[0].name = originOpts.series[0].name;
    opts.series[1].name = originOpts.series[1].name;

    return opts;
  });
}

async function init() {
  mockData();
}

watch(
  () => appStore.locale,
  () => {
    updateLocale();
  }
);

// init
init();
</script>

<template>
  <NCard :bordered="false" class="card-wrapper">
    <div ref="domRef" class="h-360px overflow-hidden"></div>
  </NCard>
</template>

<style scoped></style>
