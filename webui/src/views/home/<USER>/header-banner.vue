<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue';
import { useAppStore } from '@/store/modules/app';
import { useAuthStore } from '@/store/modules/auth';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';

defineOptions({
  name: 'HeaderBanner'
});

const appStore = useAppStore();
const authStore = useAuthStore();

const gap = computed(() => (appStore.isMobile ? 0 : 16));

// 当前时间显示
const currentTime = ref('');

// 获取时间问候语
const timeGreeting = computed(() => {
  const hour = new Date().getHours();
  const userName = authStore.userInfo.userName;

  if (hour < 12) {
    return $t('page.home.greeting.morning' as any, { userName });
  } else if (hour < 18) {
    return $t('page.home.greeting.afternoon' as any, { userName });
  }
  return $t('page.home.greeting.evening' as any, { userName });
});

// 更新时间
const updateTime = () => {
  const now = new Date();
  currentTime.value = `${now.toLocaleDateString()} ${now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
};

// 定时器
let timeInterval: NodeJS.Timeout;

onMounted(() => {
  updateTime();
  timeInterval = setInterval(updateTime, 1000);
});

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval);
  }
});

// 用户角色信息 - 按组织分组显示
const userRoles = computed(() => {
  const systemRoles = authStore.userSystemRoles;
  const organisationRoles = authStore.userOrganisationRoles;
  const organisations = authStore.userOrganisations;

  const roles = [];

  // 添加系统角色
  if (systemRoles.length > 0) {
    roles.push({
      type: 'system',
      id: 'system-roles', // 添加唯一ID用于key
      label: $t('page.user.systemRoles' as any),
      roles: systemRoles.map(role => role.name),
      color: 'error' as const, // 系统角色用红色
      icon: 'ph:shield-check',
      isPending: false,
      status: 'active'
    });
  }

  // 显示所有用户所属的组织（包括pending状态）
  if (organisations.length > 0) {
    // 按组织分组角色
    const rolesByOrg = new Map<number, any[]>();
    organisationRoles.forEach(role => {
      const orgId = role.organisation_id;
      if (orgId !== null) {
        if (!rolesByOrg.has(orgId)) {
          rolesByOrg.set(orgId, []);
        }
        rolesByOrg.get(orgId)?.push(role);
      }
    });

    // 为每个组织创建角色组（包括没有角色的组织）
    organisations.forEach(org => {
      const orgRoles = rolesByOrg.get(org.id) || [];
      const isPending = org.status === 'pending';

      // 确定显示的角色
      let displayRoles: string[] = [];
      let roleColor: 'primary' | 'warning' | 'error' | 'success' | 'info' = 'primary';

      if (orgRoles.length > 0) {
        // 有角色的情况
        displayRoles = orgRoles.map((role: any) => role.name);
        roleColor = isPending ? 'warning' : 'primary';
      } else if (isPending) {
        // pending状态但没有角色，不显示角色标签，只显示组织名和pending标识
        displayRoles = [];
        roleColor = 'warning';
      } else {
        // 其他状态且没有角色，不显示
        return;
      }

      roles.push({
        type: 'organisation',
        id: `org-${org.id}`,
        label: org.name,
        roles: displayRoles,
        color: roleColor,
        icon: 'ph:buildings',
        isPending,
        status: org.status
      });
    });
  }

  return roles;
});
</script>

<template>
  <NCard :bordered="false" class="card-wrapper">
    <div :class="appStore.isMobile ? 'flex flex-col gap-4' : 'flex items-center justify-between'">
      <!-- 左侧用户信息 -->
      <div class="flex items-center">
        <div class="size-72px shrink-0 overflow-hidden rd-1/2">
          <img src="@/assets/imgs/soybean.jpg" class="size-full" />
        </div>
        <div class="pl-12px">
          <h3 class="text-18px font-semibold">
            {{ timeGreeting }}
          </h3>
          <p class="text-#999 leading-30px">{{ currentTime }}</p>
        </div>
      </div>

      <!-- 右侧角色信息 -->
      <div v-if="userRoles.length > 0" :class="appStore.isMobile ? 'pl-84px' : 'flex flex-col gap-2'">
        <div v-for="roleGroup in userRoles" :key="roleGroup.id" :class="appStore.isMobile ? 'mb-2' : 'text-right'">
          <div
            class="mb-1 flex items-center gap-1 text-sm text-gray-500"
            :class="[appStore.isMobile ? 'justify-start' : 'justify-end']"
          >
            <SvgIcon :icon="roleGroup.icon" class="text-12px" />
            <span>{{ roleGroup.label }}</span>
            <!-- pending状态标识 -->
            <NTag v-if="roleGroup.isPending" type="warning" size="tiny">
              {{ $t('common.pending') }}
            </NTag>
          </div>
          <div
            v-if="roleGroup.roles.length > 0"
            class="flex flex-wrap gap-1"
            :class="[appStore.isMobile ? 'justify-start' : 'justify-end']"
          >
            <NTag v-for="roleName in roleGroup.roles" :key="roleName" :type="roleGroup.color" size="small">
              {{ roleName }}
            </NTag>
          </div>
        </div>
      </div>
    </div>
  </NCard>
</template>

<style scoped></style>
