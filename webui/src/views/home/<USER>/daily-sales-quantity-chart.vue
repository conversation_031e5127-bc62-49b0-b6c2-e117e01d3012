<script setup lang="ts">
import { watch } from 'vue';
import { useAppStore } from '@/store/modules/app';
import { useEcharts } from '@/hooks/common/echarts';
import { $t } from '@/locales';

defineOptions({
  name: 'DailySalesQuantityChart'
});

const appStore = useAppStore();

const { domRef, updateOptions } = useEcharts(() => ({
  title: {
    text: $t('page.home.dailySalesQuantity'),
    left: 'center',
    textStyle: {
      fontSize: 16,
      fontWeight: 'normal'
    }
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      label: {
        backgroundColor: '#6a7985'
      }
    },
    formatter: function(params: any) {
      let result = params[0].name + '<br/>';
      params.forEach((param: any) => {
        result += param.marker + param.seriesName + ': ' + param.value + '<br/>';
      });
      return result;
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '10%',
    top: '15%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: [] as string[]
  },
  yAxis: {
    type: 'value',
    name: $t('page.home.salesQuantity'),
    axisLabel: {
      formatter: '{value}'
    }
  },
  series: [
    {
      color: '#26deca',
      name: $t('page.home.salesQuantity'),
      type: 'line',
      smooth: true,
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0.25,
              color: '#26deca'
            },
            {
              offset: 1,
              color: '#fff'
            }
          ]
        }
      },
      emphasis: {
        focus: 'series'
      },
      data: [] as number[]
    }
  ]
}));

async function mockData() {
  try {
    await new Promise(resolve => {
      setTimeout(resolve, 1000);
    });

    updateOptions(opts => {
      // 最近7天的日期
      const dates = [];
      for (let i = 6; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        dates.push(date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }));
      }

      opts.xAxis.data = dates;
      // 销量数据
      opts.series[0].data = [45, 58, 67, 52, 73, 68, 81];

      return opts;
    });
  } catch (error) {
    console.error('Failed to load chart data:', error);
  }
}

function updateLocale() {
  try {
    updateOptions((opts, factory) => {
      const originOpts = factory();

      opts.title.text = originOpts.title.text;
      opts.yAxis.name = originOpts.yAxis.name;
      opts.series[0].name = originOpts.series[0].name;

      return opts;
    });
  } catch (error) {
    console.error('Failed to update chart locale:', error);
  }
}

async function init() {
  mockData();
}

watch(
  () => appStore.locale,
  () => {
    updateLocale();
  }
);

// init
init();
</script>

<template>
  <div ref="domRef" class="h-360px overflow-hidden"></div>
</template>

<style scoped></style>
