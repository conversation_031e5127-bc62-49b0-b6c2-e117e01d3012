<script setup lang="tsx">
import { computed, h, reactive, ref } from 'vue';
import type { DataTableColumns } from 'naive-ui';
import { NButton, NTag } from 'naive-ui';
import { useAppStore } from '@/store/modules/app';
import { $t } from '@/locales';
import ButtonIcon from '@/components/custom/button-icon.vue';
import SvgIcon from '@/components/custom/svg-icon.vue';

defineOptions({
  name: 'GameImportLogs'
});

interface ImportLogData {
  id: string;
  taskId: string;
  importTime: string;
  dataCount: number;
  importStatus: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  totalItems: number;
  successItems: number;
  failedItems: number;
  errorMessage?: string;
}

const searchForm = reactive({
  taskId: '',
  status: '',
  dateRange: null as [number, number] | null
});

const loading = ref(false);
const retryLoading = ref<Record<string, boolean>>({});
const showDetailsModal = ref(false);
const selectedLog = ref<ImportLogData | null>(null);

const appStore = useAppStore();
const isMobile = computed(() => appStore.isMobile);

// 模拟导入日志数据
const tableData = ref<ImportLogData[]>([
  {
    id: '1',
    taskId: 'TASK_20240115_001',
    importTime: '2024-01-15 10:30:25',
    dataCount: 150,
    importStatus: 'completed',
    totalItems: 150,
    successItems: 148,
    failedItems: 2,
    errorMessage: '2 items failed due to duplicate codes'
  },
  {
    id: '2',
    taskId: 'TASK_20240115_002',
    importTime: '2024-01-15 09:45:18',
    dataCount: 75,
    importStatus: 'failed',
    totalItems: 75,
    successItems: 0,
    failedItems: 75,
    errorMessage: 'Database connection timeout'
  },
  {
    id: '3',
    taskId: 'TASK_20240115_003',
    importTime: '2024-01-15 08:20:45',
    dataCount: 200,
    importStatus: 'processing',
    totalItems: 200,
    successItems: 120,
    failedItems: 0
  },
  {
    id: '4',
    taskId: 'TASK_20240114_001',
    importTime: '2024-01-14 16:15:30',
    dataCount: 300,
    importStatus: 'completed',
    totalItems: 300,
    successItems: 300,
    failedItems: 0
  },
  {
    id: '5',
    taskId: 'TASK_20240114_002',
    importTime: '2024-01-14 14:30:12',
    dataCount: 50,
    importStatus: 'cancelled',
    totalItems: 50,
    successItems: 25,
    failedItems: 0,
    errorMessage: 'Cancelled by user'
  }
]);

// 统计信息计算
const statistics = computed(() => {
  const total = tableData.value.length;
  const completed = tableData.value.filter(item => item.importStatus === 'completed').length;
  const failed = tableData.value.filter(item => item.importStatus === 'failed').length;
  const processing = tableData.value.filter(item => item.importStatus === 'processing').length;

  return {
    total,
    completed,
    failed,
    processing,
    successRate: total > 0 ? Math.round((completed / total) * 100) : 0
  };
});

const columns = computed<DataTableColumns<ImportLogData>>(() => [
  {
    title: $t('page.admin.taskId' as any),
    key: 'taskId',
    width: 160,
    sorter: true
  },
  {
    title: $t('page.admin.importTime' as any),
    key: 'importTime',
    width: 160,
    sorter: true
  },

  {
    title: $t('page.admin.dataCount' as any),
    key: 'dataCount',
    width: 100,
    sorter: true,
    render: row => {
      return h('span', `${row.dataCount} 条`);
    }
  },
  {
    title: $t('page.admin.importStatus' as any),
    key: 'importStatus',
    width: 100,
    render: row => {
      const statusColorMap: Record<string, string> = {
        pending: 'default',
        processing: 'info',
        completed: 'success',
        failed: 'error',
        cancelled: 'warning'
      };
      return h(
        NTag,
        {
          type: statusColorMap[row.importStatus] as any
        },
        {
          default: () => $t(`page.admin.${row.importStatus}` as any)
        }
      );
    }
  },
  {
    title: $t('common.action'),
    key: 'actions',
    width: 120,
    render: row => {
      const canRetry = row.importStatus === 'failed' || row.importStatus === 'cancelled';

      return h('div', { class: 'flex gap-2' }, [
        h(ButtonIcon, {
          icon: 'mdi:eye',
          tooltipContent: $t('page.admin.importDetails' as any),
          class: 'text-primary',
          onClick: () => handleViewDetails(row)
        }),
        canRetry &&
          h(
            NButton,
            {
              size: 'small',
              type: 'primary',
              loading: retryLoading.value[row.id],
              onClick: () => handleRetry(row)
            },
            {
              default: () => $t('page.admin.retry' as any),
              icon: () => h(SvgIcon, { icon: 'mdi:refresh' })
            }
          )
      ]);
    }
  }
]);

const pagination = reactive({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  onChange: (page: number) => {
    pagination.page = page;
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
  }
});

function handleSearch() {
  loading.value = true;
  setTimeout(() => {
    loading.value = false;
  }, 1000);
}

function handleReset() {
  Object.assign(searchForm, {
    taskId: '',
    status: '',
    dateRange: null
  });
}

function handleRefresh() {
  loading.value = true;
  // 模拟刷新数据
  setTimeout(() => {
    loading.value = false;
    // 这里可以调用实际的数据刷新API
  }, 1000);
}

async function handleRetry(row: ImportLogData) {
  retryLoading.value[row.id] = true;

  try {
    // 模拟重试API调用
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 更新状态为处理中
    row.importStatus = 'processing';

    window.$message?.success($t('page.admin.retrySuccess' as any));
  } catch (error) {
    window.$message?.error($t('page.admin.retryFailed' as any));
  } finally {
    retryLoading.value[row.id] = false;
  }
}

function handleViewDetails(row: ImportLogData) {
  selectedLog.value = row;
  showDetailsModal.value = true;
}
</script>

<template>
  <div class="space-y-4">
    <div class="flex items-center justify-between">
      <h3 class="text-16px font-semibold">{{ $t('page.admin.gameImportLogs') }}</h3>
      <div class="flex items-center gap-2">
        <NButton :loading="loading" @click="handleRefresh">
          <template #icon>
            <SvgIcon icon="mdi:refresh" />
          </template>
          {{ $t('common.refresh') }}
        </NButton>
      </div>
    </div>

    <!-- 紧凑日志筛选器 -->
    <div class="rounded-lg bg-gray-50 p-4 dark:bg-gray-800">
      <!-- 搜索字段和按钮 -->
      <div :class="isMobile ? 'space-y-3' : 'flex flex-wrap gap-4 items-end'">
        <div class="min-w-40 flex flex-col flex-1 gap-1">
          <span class="text-sm text-gray-600 font-medium dark:text-gray-300">{{ $t('page.admin.taskId') }}</span>
          <NInput
            v-model:value="searchForm.taskId"
            :placeholder="$t('page.admin.taskIdPlaceholder')"
            size="small"
            clearable
          >
            <template #prefix>
              <SvgIcon icon="mdi:identifier" class="text-gray-400" />
            </template>
          </NInput>
        </div>

        <div class="min-w-24 flex flex-col flex-1 gap-1">
          <span class="text-sm text-gray-600 font-medium dark:text-gray-300">
            {{ $t('page.admin.importStatus') }}
          </span>
          <NSelect
            v-model:value="searchForm.status"
            :placeholder="$t('page.admin.statusPlaceholder')"
            :options="[
              { label: $t('page.admin.pending'), value: 'pending' },
              { label: $t('page.admin.processing'), value: 'processing' },
              { label: $t('page.admin.completed'), value: 'completed' },
              { label: $t('page.admin.failed'), value: 'failed' },
              { label: $t('page.admin.cancelled'), value: 'cancelled' }
            ]"
            size="small"
            clearable
          />
        </div>

        <div class="min-w-64 flex flex-col flex-1 gap-1">
          <span class="text-sm text-gray-600 font-medium dark:text-gray-300">{{ $t('page.admin.timeRange') }}</span>
          <NDatePicker v-model:value="searchForm.dateRange" type="datetimerange" size="small" clearable />
        </div>

        <!-- 操作按钮 -->
        <div class="flex gap-2" :class="isMobile ? 'w-full justify-center' : 'flex-shrink-0'">
          <NButton size="small" @click="handleReset">
            {{ $t('common.reset') }}
          </NButton>
          <NButton type="primary" size="small" :loading="loading" @click="handleSearch">
            <template #icon>
              <SvgIcon icon="mdi:magnify" />
            </template>
            {{ $t('common.search') }}
          </NButton>
        </div>
      </div>
    </div>

    <!-- 统计信息卡片 -->
    <div class="grid grid-cols-2 gap-4 md:grid-cols-4">
      <div class="border border-gray-200 rounded-lg bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400">{{ $t('page.admin.totalTasks') }}</p>
            <p class="text-2xl text-gray-900 font-bold dark:text-white">{{ statistics.total }}</p>
          </div>
          <SvgIcon icon="mdi:database" class="text-2xl text-blue-500" />
        </div>
      </div>

      <div class="border border-gray-200 rounded-lg bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400">{{ $t('page.admin.completedTasks') }}</p>
            <p class="text-2xl text-green-600 font-bold">{{ statistics.completed }}</p>
          </div>
          <SvgIcon icon="mdi:check-circle" class="text-2xl text-green-500" />
        </div>
      </div>

      <div class="border border-gray-200 rounded-lg bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400">{{ $t('page.admin.failedTasks') }}</p>
            <p class="text-2xl text-red-600 font-bold">{{ statistics.failed }}</p>
          </div>
          <SvgIcon icon="mdi:alert-circle" class="text-2xl text-red-500" />
        </div>
      </div>

      <div class="border border-gray-200 rounded-lg bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400">{{ $t('page.admin.successRate') }}</p>
            <p class="text-2xl text-blue-600 font-bold">{{ statistics.successRate }}%</p>
          </div>
          <SvgIcon icon="mdi:chart-line" class="text-2xl text-blue-500" />
        </div>
      </div>
    </div>

    <!-- 导入日志列表表格 -->
    <NDataTable
      :columns="columns"
      :data="tableData"
      :loading="loading"
      :pagination="pagination"
      :bordered="false"
      size="small"
      flex-height
      class="h-500px"
    />

    <!-- 导入详情模态框 -->
    <NModal
      v-model:show="showDetailsModal"
      preset="dialog"
      :title="$t('page.admin.importDetails')"
      :negative-text="$t('common.close')"
      class="w-600px"
    >
      <div v-if="selectedLog" class="space-y-4">
        <NDescriptions :column="2" bordered>
          <NDescriptionsItem :label="$t('page.admin.taskId')">
            {{ selectedLog.taskId }}
          </NDescriptionsItem>
          <NDescriptionsItem :label="$t('page.admin.importTime')">
            {{ selectedLog.importTime }}
          </NDescriptionsItem>

          <NDescriptionsItem :label="$t('page.admin.totalItems')">
            {{ selectedLog.totalItems }}
          </NDescriptionsItem>
          <NDescriptionsItem :label="$t('page.admin.successItems')">
            <span class="text-green-600">{{ selectedLog.successItems }}</span>
          </NDescriptionsItem>
          <NDescriptionsItem :label="$t('page.admin.failedItems')">
            <span class="text-red-600">{{ selectedLog.failedItems }}</span>
          </NDescriptionsItem>
          <NDescriptionsItem :label="$t('page.admin.importStatus')">
            <NTag
              :type="
                selectedLog.importStatus === 'completed'
                  ? 'success'
                  : selectedLog.importStatus === 'failed'
                    ? 'error'
                    : 'info'
              "
            >
              {{ $t(`page.admin.${selectedLog.importStatus}` as any) }}
            </NTag>
          </NDescriptionsItem>
        </NDescriptions>

        <div v-if="selectedLog.errorMessage" class="space-y-2">
          <h4 class="text-14px font-medium">{{ $t('page.admin.errorMessage') }}</h4>
          <div
            class="border border-red-200 rounded bg-red-50 p-3 text-red-700 dark:border-red-800 dark:bg-red-900/20 dark:text-red-300"
          >
            {{ selectedLog.errorMessage }}
          </div>
        </div>
      </div>
    </NModal>
  </div>
</template>

<style scoped></style>
