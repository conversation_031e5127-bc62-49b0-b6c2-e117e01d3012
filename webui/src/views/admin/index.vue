<script setup lang="tsx">
import { computed } from 'vue';
import { useAppStore } from '@/store/modules/app';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';
import AdminSettings from './modules/admin-settings.vue';
import SystemLogs from './modules/system-logs.vue';
import GameImportLogs from './modules/game-import-logs.vue';

defineOptions({
  name: 'Admin'
});

const appStore = useAppStore();

const gap = computed(() => (appStore.isMobile ? 0 : 16));
</script>

<template>
  <NSpace vertical :size="16">
    <NCard :bordered="false" class="card-wrapper">
      <template #header>
        <div class="flex items-center gap-2">
          <SvgIcon icon="mdi:shield-account" class="text-20px" />
          <span class="text-18px font-semibold">{{ $t('route.admin') }}</span>
        </div>
      </template>
      <AdminSettings />
    </NCard>

    <NCard :bordered="false" class="card-wrapper">
      <template #header>
        <div class="flex items-center gap-2">
          <SvgIcon icon="mdi:database-import" class="text-20px" />
          <span class="text-18px font-semibold">{{ $t('page.admin.gameImportLogs') }}</span>
        </div>
      </template>
      <GameImportLogs />
    </NCard>

    <NCard :bordered="false" class="card-wrapper">
      <SystemLogs />
    </NCard>
  </NSpace>
</template>

<style scoped>
.card-wrapper {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
