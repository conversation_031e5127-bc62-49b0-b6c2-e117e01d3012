<script setup lang="tsx">
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';
import UserList from './modules/user-list.vue';
import UserStats from './modules/user-stats.vue';

defineOptions({
  name: 'UserManagement'
});
</script>

<template>
  <NSpace vertical :size="16">
    <NCard :bordered="false" class="card-wrapper">
      <template #header>
        <div class="flex items-center gap-2">
          <SvgIcon icon="mdi:account-group" class="text-20px" />
          <span class="text-18px font-semibold">{{ $t('route.user') }}</span>
        </div>
      </template>
      <UserStats />
    </NCard>

    <NCard :bordered="false" class="card-wrapper">
      <UserList />
    </NCard>
  </NSpace>
</template>

<style scoped>
.card-wrapper {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
