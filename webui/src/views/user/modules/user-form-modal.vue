<script setup lang="tsx">
import { computed, onMounted, reactive, ref, watch } from 'vue';
import { useMessage } from 'naive-ui';
import { fetchCreateUser, fetchOrganizations, fetchAssignableRoles } from '@/service/api';
import Api from '@/service/api';
import { handleApiError } from '@/utils/error-handler';
import { useAppStore } from '@/store/modules/app';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';
import OrganizationFormModal from '@/views/organization/modules/organization-form-modal.vue';

defineOptions({
  name: 'UserFormModal'
});

interface Props {
  /** Modal visibility */
  modelValue: boolean;
}

// Form data interface
interface UserFormData {
  name: string;
  email: string;
  password?: string;
  confirmPassword?: string;
  organisation_ids: number[];
}

interface Emits {
  (e: 'update:modelValue', visible: boolean): void;
  (e: 'submitted', data: Api.Organization.OrganizationUser): void;
}

// No need to expose methods since only create functionality is needed

const props = defineProps<Props>();

const emit = defineEmits<Emits>();

const { formRef, validate, restoreValidation } = useNaiveForm();
const loading = ref(false);
const message = useMessage();

const appStore = useAppStore();

// Responsive width settings
const isMobile = computed(() => appStore.isMobile);
const modalClass = computed(() => {
  if (isMobile.value) {
    return 'size-full top-0px rounded-0';
  }
  return 'w-70vw max-w-900px min-w-600px top-50px';
});

// Form data
const formModel = reactive<UserFormData>({
  name: '',
  email: '',
  password: '',
  confirmPassword: '',
  organisation_ids: []
});

// Avatar upload related state
const avatarUploading = ref(false);
const avatarPreview = ref<string>('');

// Organization related state
const organizationFormVisible = ref(false);
const organizationOptions = ref<{ label: string; value: number }[]>([]);
const roleOptions = ref<{ label: string; value: string }[]>([]);

// Modal title - only for creating new users
const modalTitle = computed(() => $t('page.user.userForm.addTitle'));

// Form validation rules - only for creating new users
const rules = computed(() => {
  const { formRules, createRequiredRule, createConfirmPwdRule } = useFormRules();

  return {
    name: formRules.userName,
    email: formRules.email,
    password: formRules.pwd,
    confirmPassword: createConfirmPwdRule(computed(() => formModel.password || '')),
    organisation_ids: [createRequiredRule($t('page.user.userForm.organizationRequired'))]
  };
});

// Load organization list
async function loadOrganizations() {
  try {
    const { data } = await fetchOrganizations();
    if (data.success) {
      organizationOptions.value = (data.data?.data || []).map(org => ({
        label: org.name,
        value: org.id
      }));
    }
  } catch (error) {
    console.error('Load organizations error:', error);
  }
}

// Load assignable roles
async function loadAssignableRoles() {
  try {
    const { data } = await fetchAssignableRoles();
    if (data.success) {
      // Handle new API response structure with roles and details
      const responseData = data.data || { roles: {}, details: [] };

      // Use details array if available (for non-root/admin users)
      if (responseData.details && responseData.details.length > 0) {
        roleOptions.value = responseData.details.map(role => ({
          label: role.name,
          value: role.name
        }));
      } else {
        // For root/admin users, construct role options from roles structure
        const allRoles = [];
        Object.entries(responseData.roles || {}).forEach(([guardName, roleNames]) => {
          (roleNames as string[]).forEach(roleName => {
            allRoles.push({
              label: roleName,
              value: roleName
            });
          });
        });
        roleOptions.value = allRoles;
      }
    }
  } catch (error) {
    console.error('Load roles error:', error);
  }
}

// Password strength calculation
const passwordStrength = computed(() => {
  const password = formModel.password;

  if (!password) return { level: 0, text: '', color: '' };

  let score = 0;
  const checks = {
    length: password.length >= 8,
    lowercase: /[a-z]/.test(password),
    uppercase: /[A-Z]/.test(password),
    numbers: /\d/.test(password),
    symbols: /[!@#$%^&*(),.?":{}|<>]/.test(password)
  };

  // Calculate score
  if (checks.length) score += 2;
  if (checks.lowercase) score += 1;
  if (checks.uppercase) score += 1;
  if (checks.numbers) score += 1;
  if (checks.symbols) score += 2;

  // Length bonus
  if (password.length >= 12) score += 1;
  if (password.length >= 16) score += 1;

  // Determine strength level
  if (score <= 2) {
    return { level: 1, text: 'Weak', color: '#f56565' };
  } else if (score <= 4) {
    return { level: 2, text: 'Medium', color: '#ed8936' };
  } else if (score <= 6) {
    return { level: 3, text: 'Strong', color: '#38a169' };
  }
  return { level: 4, text: 'Very Strong', color: '#3182ce' };
});

// File input reference
const fileInputRef = ref<HTMLInputElement>();

// Trigger file selection
function triggerFileSelect() {
  fileInputRef.value?.click();
}

// Handle file selection
function handleFileChange(event: Event) {
  const target = event.target as HTMLInputElement;
  const files = target.files;

  if (!files || files.length === 0) return;

  const file = files[0];

  // Validate file type
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
  if (!allowedTypes.includes(file.type)) {
    message.error($t('page.user.userForm.avatarTypeError'));
    return;
  }

  // Validate file size (2MB)
  const maxSize = 2 * 1024 * 1024;
  if (file.size > maxSize) {
    message.error($t('page.user.userForm.avatarSizeError'));
    return;
  }

  avatarUploading.value = true;

  // Create FileReader to preview image
  const reader = new FileReader();
  reader.onload = e => {
    const result = e.target?.result as string;
    avatarPreview.value = result;
    // formModel.avatar = result; // Temporarily commented out as avatar field is not in type definition
    avatarUploading.value = false;
    message.success($t('page.user.userForm.avatarUploadSuccess'));
  };
  reader.onerror = () => {
    avatarUploading.value = false;
    message.error($t('page.user.userForm.avatarUploadError'));
  };
  reader.readAsDataURL(file);

  // Clear input value to allow selecting the same file again
  target.value = '';
}

// Remove avatar
function handleAvatarRemove() {
  // formModel.avatar = ''; // Temporarily commented out as avatar field is not in type definition
  avatarPreview.value = '';
  message.success($t('page.user.userForm.avatarRemoveSuccess'));
}

// Add organization
function handleAddOrganization() {
  organizationFormVisible.value = true;
}

// Organization form submission
function handleOrganizationSubmitted(data?: Api.Organization.Organization) {
  if (!data) {
    // If no data is provided, just reload organizations
    loadOrganizations();
    message.success($t('page.user.userForm.organizationAddSuccess'));
    return;
  }

  // Add new organization to options list
  const newOption = { label: data.name, value: data.id };
  organizationOptions.value.unshift(newOption);

  // Automatically select the newly added organization
  if (!formModel.organisation_ids.includes(data.id)) {
    formModel.organisation_ids.push(data.id);
  }

  message.success($t('page.user.userForm.organizationAddSuccess'));
}

// Watch modal visibility changes - only for resetting form
watch(
  () => props.modelValue,
  (visible) => {
    if (!visible) {
      // Reset form when modal is closed
      resetForm();
    }
  }
);

// Remove setFormData function since only create functionality is needed

// Reset form
function resetForm() {
  Object.assign(formModel, {
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    organisation_ids: []
  });
  avatarPreview.value = '';
  restoreValidation();
}

// Close modal
function closeModal() {
  emit('update:modelValue', false);
}

// Submit form - only for creating new users
async function handleSubmit() {
  // Validate all fields
  await validate();
  try {
    loading.value = true;

    // Create user data
    const submitData: Api.Organization.CreateUserRequest = {
      name: formModel.name,
      email: formModel.email,
      organisation_ids: formModel.organisation_ids,
      password: formModel.password!
    };

    // Create user
    const result = await fetchCreateUser(submitData);

    if (result.data.success) {
      message.success($t('page.user.userForm.addSuccess'));
      emit('submitted', result.data.data!);
      closeModal();
    } else {
      handleApiError({ response: { data: result.data } }, message, $t('page.user.userForm.addFailed'));
    }
  } catch (error) {
    // Check if this is a form validation error (from Naive UI)
    if (error && Array.isArray(error) && error.length > 0) {
      // This is a Naive UI validation error, extract and show the messages
      const validationErrors: string[] = [];
      error.forEach((fieldErrors: any) => {
        if (Array.isArray(fieldErrors)) {
          fieldErrors.forEach((fieldError: any) => {
            if (fieldError && fieldError.message) {
              validationErrors.push(fieldError.message);
            }
          });
        }
      });

      if (validationErrors.length > 0) {
        // Show validation errors to user
        validationErrors.forEach(errorMsg => {
          message.error(errorMsg);
        });
        return;
      }
    }

    // Handle other types of errors (API errors, network errors, etc.)
    handleApiError(error, message, $t('page.user.userForm.addFailed'));
  } finally {
    loading.value = false;
  }
}

// Remove duplicate function definitions, use the existing handleOrganizationSubmitted function above

// Load data when component is mounted
onMounted(() => {
  loadOrganizations();
  loadAssignableRoles();
});
</script>

<template>
  <div>
    <NModal
      :show="modelValue"
      preset="dialog"
      :title="modalTitle"
      :positive-text="$t('page.user.userForm.submit')"
      :negative-text="$t('page.user.userForm.cancel')"
      :loading="loading"
      class="fixed left-0 right-0"
      :class="modalClass"
      @positive-click="handleSubmit"
      @negative-click="closeModal"
      @update:show="emit('update:modelValue', $event)"
    >
      <NForm
        ref="formRef"
        :model="formModel"
        :rules="rules"
        label-placement="top"
        label-width="auto"
        require-mark-placement="right-hanging"
        class="mt-6"
      >
        <NGrid :cols="isMobile ? 1 : 2" :x-gap="16" :y-gap="16">
          <!-- Avatar upload area -->
          <NGi>
            <NFormItem :label="$t('page.user.userForm.avatar')" path="avatar">
              <div class="avatar-upload-wrapper">
                <!-- Hidden file input -->
                <input
                  ref="fileInputRef"
                  type="file"
                  accept="image/*"
                  class="hidden-file-input"
                  @change="handleFileChange"
                />

                <!-- Avatar display area -->
                <div class="avatar-container" @click="triggerFileSelect">
                  <div v-if="avatarPreview" class="avatar-preview-wrapper">
                    <NAvatar :size="100" :src="avatarPreview" class="avatar-display" round>
                      {{ formModel.name ? formModel.name.charAt(0).toUpperCase() : 'U' }}
                    </NAvatar>
                    <div class="avatar-overlay">
                      <SvgIcon icon="mdi:camera" class="camera-icon" />
                    </div>
                    <NButton size="tiny" type="error" circle class="remove-btn" @click.stop="handleAvatarRemove">
                      <template #icon>
                        <SvgIcon icon="mdi:close" />
                      </template>
                    </NButton>
                  </div>
                  <div v-else class="avatar-upload-placeholder">
                    <NSpin :show="avatarUploading" class="upload-spinner">
                      <div class="upload-area">
                        <SvgIcon icon="mdi:account-plus" class="upload-icon" />
                        <div class="upload-text">{{ $t('page.user.userForm.uploadAvatar') }}</div>
                      </div>
                    </NSpin>
                  </div>
                </div>

                <div class="avatar-hint">{{ $t('page.user.userForm.avatarHint') }}</div>
              </div>
            </NFormItem>
          </NGi>
          <NGi>
            <NFormItem :label="$t('page.user.userForm.username')" path="name">
              <NInput
                v-model:value="formModel.name"
                :placeholder="$t('page.user.userForm.usernamePlaceholder')"
              />
            </NFormItem>
            <NFormItem :label="$t('page.user.userForm.email')" path="email">
              <NInput
                v-model:value="formModel.email"
                :placeholder="$t('page.user.userForm.emailPlaceholder')"
              />
            </NFormItem>
          </NGi>
          <NGi>
            <NFormItem :label="$t('page.user.userForm.password')" path="password">
              <NInput
                v-model:value="formModel.password"
                type="password"
                show-password-on="mousedown"
                :placeholder="$t('page.user.userForm.passwordPlaceholder')"
              />
            </NFormItem>
            <!-- Password strength display -->
            <div v-show="formModel.password && formModel.password.length > 0" class="password-strength-container">
              <div class="strength-bars">
                <div
                  v-for="i in 4"
                  :key="i"
                  class="strength-bar"
                  :class="{
                    'strength-bar-active': i <= passwordStrength.level,
                    'strength-bar-weak': i <= passwordStrength.level && passwordStrength.level === 1,
                    'strength-bar-medium': i <= passwordStrength.level && passwordStrength.level === 2,
                    'strength-bar-strong': i <= passwordStrength.level && passwordStrength.level === 3,
                    'strength-bar-very-strong': i <= passwordStrength.level && passwordStrength.level === 4
                  }"
                ></div>
              </div>
              <div class="mt-1 text-xs text-gray-500">
                Strength: {{ passwordStrength.text }} ({{ passwordStrength.level }}/4)
              </div>
            </div>
          </NGi>
          <NGi>
            <NFormItem :label="$t('page.user.userForm.confirmPassword')" path="confirmPassword">
              <NInput
                v-model:value="formModel.confirmPassword"
                type="password"
                show-password-on="mousedown"
                :placeholder="$t('page.user.userForm.confirmPasswordPlaceholder')"
              />
            </NFormItem>
          </NGi>
          <NGi :span="isMobile ? 1 : 2">
            <NFormItem :label="$t('page.user.userForm.organization')" path="organisation_ids">
              <NInputGroup>
                <NSelect
                  v-model:value="formModel.organisation_ids"
                  :options="organizationOptions"
                  :placeholder="$t('page.user.userForm.organizationPlaceholder')"
                  filterable
                  multiple
                  class="organization-select"
                />
                <NButton type="primary" ghost @click="handleAddOrganization">
                  <template #icon>
                    <SvgIcon icon="mdi:plus" />
                  </template>
                  {{ $t('page.user.userForm.addOrganization') }}
                </NButton>
              </NInputGroup>
            </NFormItem>
          </NGi>
        </NGrid>
      </NForm>
    </NModal>

    <!-- Organization form modal -->
    <OrganizationFormModal :visible="organizationFormVisible" @update:visible="organizationFormVisible = $event" @submitted="handleOrganizationSubmitted" />
  </div>
</template>

<style scoped>
.password-strength-container {
  margin-top: 8px;
  width: 100%;
}

.strength-bars {
  display: flex;
  gap: 4px;
  width: 100%;
}

.strength-bar {
  height: 4px;
  flex: 1;
  background-color: #e5e7eb;
  border-radius: 2px;
  transition: background-color 0.3s ease;
}

.strength-bar-active.strength-bar-weak {
  background-color: #f56565;
}

.strength-bar-active.strength-bar-medium {
  background-color: #ed8936;
}

.strength-bar-active.strength-bar-strong {
  background-color: #38a169;
}

.strength-bar-active.strength-bar-very-strong {
  background-color: #3182ce;
}

/* Avatar upload styles */
.hidden-file-input {
  display: none;
}

.avatar-upload-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.avatar-uploader {
  cursor: pointer;
}

.avatar-container {
  position: relative;
  width: 100px;
  height: 100px;
}

.avatar-preview-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.avatar-preview-wrapper:hover {
  transform: scale(1.05);
}

.avatar-display {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.avatar-preview-wrapper:hover .avatar-overlay {
  opacity: 1;
}

.camera-icon {
  font-size: 24px;
  color: white;
}

.remove-btn {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 24px;
  height: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.avatar-upload-placeholder {
  width: 100%;
  height: 100%;
  border: 2px dashed #d9d9d9;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #fafafa;
}

.avatar-upload-placeholder:hover {
  border-color: #40a9ff;
  background-color: #f0f8ff;
}

.upload-spinner {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #666;
}

.upload-icon {
  font-size: 28px;
  color: #d9d9d9;
}

.upload-text {
  font-size: 12px;
  text-align: center;
  line-height: 1.2;
}

.avatar-hint {
  font-size: 11px;
  color: #999;
  text-align: center;
  max-width: 200px;
  line-height: 1.3;
}

/* Organization selection styles */
.organization-select-container {
  display: flex;
  gap: 8px;
  align-items: center;
}

.organization-select {
  flex: 1;
}

/* Removed organization display styles since only create functionality is needed */
</style>
