<script setup lang="tsx">
import { computed, onMounted, reactive, ref, watch } from 'vue';
import { useMessage, useDialog } from 'naive-ui';
import {
  fetchUserRoles,
  fetchAssignableRoles,
  fetchAssignUserRole,
  fetchRemoveUserRole,
  fetchRolesList,
  fetchOrganizations
} from '@/service/api';
import Api from '@/service/api';
import { handleApiError } from '@/utils/error-handler';
import { useAppStore } from '@/store/modules/app';
import { useAuthStore } from '@/store/modules/auth';
import { usePermission } from '@/hooks/common/permission';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';
import OrganizationSelector from '@/components/common/organization-selector.vue';

defineOptions({
  name: 'UserRoleModal'
});

interface Props {
  /** Modal visibility */
  modelValue: boolean;
  /** User data */
  user?: Api.Organization.OrganizationUser | null;
}

interface Emits {
  (e: 'update:modelValue', visible: boolean): void;
  (e: 'updated'): void;
}

const props = withDefaults(defineProps<Props>(), {
  user: null
});

const emit = defineEmits<Emits>();

const loading = ref(false);
const message = useMessage();
const dialog = useDialog();
const appStore = useAppStore();
const authStore = useAuthStore();
const { isSystemAdmin } = usePermission();

// User roles data
const userRolesData = ref<Api.Organization.UserRolesResponse | null>(null);
// Assignable roles from API
const assignableRoles = ref<Api.Auth.Role[]>([]);
// Raw assignable roles response data
const assignableRolesData = ref<{ roles: Record<string, string[]>; details: Api.Auth.Role[] } | null>(null);
// System role assignment form
const systemRoleForm = reactive({
  role_name: ''
});

// Organization role assignment form
const orgRoleForm = reactive({
  organisation_id: undefined as number | undefined,
  role_name: ''
});

// Show system permissions section
const showSystemPermissions = ref(false);

const modalTitle = computed(() => {
  return props.user ? $t('page.user.manageUserRoles', { name: props.user.name }) : '';
});

// System role options - based on assignable roles with guard_name: 'system'
const systemRoleOptions = computed(() => {
  // Filter system roles from assignable roles
  return assignableRoles.value
    .filter(role => role.guard_name === 'system')
    .map(role => ({
      label: role.name,
      value: role.name // Use role name as value
    }));
});

// All organizations data (for root/admin users)
const allOrganizations = ref<Api.Organization.Organization[]>([]);

// Organization role options - based on assignable roles for selected organization
const orgRoleOptions = computed(() => {
  // If no organization is selected, return empty array
  if (!orgRoleForm.organisation_id) {
    return [];
  }

  // For root/admin users, use roles.api array from assignable roles response
  if (isSystemAdmin() && assignableRolesData.value?.roles?.api) {
    return assignableRolesData.value.roles.api.map(roleName => ({
      label: roleName,
      value: roleName
    }));
  }

  // For other users, filter details array by selected organization ID
  if (assignableRolesData.value?.details) {
    return assignableRolesData.value.details
      .filter(role => role.guard_name === 'api' &&
                     role.organisation_id === orgRoleForm.organisation_id)
      .map(role => ({
        label: role.name,
        value: role.name
      }));
  }

  // Fallback to original logic if assignableRolesData is not available
  return assignableRoles.value
    .filter(role => role.guard_name === 'api' &&
                   role.organisation_id === orgRoleForm.organisation_id)
    .map(role => ({
      label: role.name,
      value: role.name
    }));
});

// Compute organization IDs for the organization selector
const allowedOrganizationIds = computed(() => {
  // If current user is root or admin, allow all organizations (no restriction)
  if (isSystemAdmin()) {
    return undefined;
  }

  // For non-admin users, extract organization IDs where they have owner role
  const ownerRoles = assignableRoles.value.filter(role =>
    role.guard_name === 'api' &&
    role.name === 'owner' &&
    role.organisation_id !== null
  );

  return ownerRoles.map(role => role.organisation_id!);
});

// Current user's system roles
const currentSystemRoles = computed(() => {
  return userRolesData.value?.roles?.system_roles || [];
});

// Current user's organization roles (grouped by organization)
const currentOrgRoles = computed(() => {
  const orgRoles = userRolesData.value?.roles?.organisation_roles || [];
  const groupedRoles: Record<number, any[]> = {};

  orgRoles.forEach(role => {
    if (role.organisation_id) {
      if (!groupedRoles[role.organisation_id]) {
        groupedRoles[role.organisation_id] = [];
      }
      groupedRoles[role.organisation_id].push(role);
    }
  });

  return groupedRoles;
});

// Load user roles data
async function loadUserRoles() {
  if (!props.user) return;

  try {
    loading.value = true;
    const { data } = await fetchUserRoles(props.user.id);
    if (data.success) {
      userRolesData.value = data.data;
    } else {
      handleApiError({ response: { data } }, message, $t('page.user.loadUserRolesFailed'));
    }
  } catch (error) {
    handleApiError(error, message, $t('page.user.loadUserRolesFailed'));
  } finally {
    loading.value = false;
  }
}

// Load assignable roles and process organization/system permissions
async function loadAssignableRoles() {
  try {
    const { data } = await fetchAssignableRoles();
    if (data.success) {
      // Handle new API response structure with roles and details
      const responseData = data.data || { roles: {}, details: [] };

      // Store the raw response data for use in orgRoleOptions
      assignableRolesData.value = responseData;

      // Use details array for role processing (contains full role information)
      // If details is empty (for root/admin users), construct from roles structure
      if (responseData.details && responseData.details.length > 0) {
        assignableRoles.value = responseData.details;
      } else {
        // For root/admin users, construct role array from roles structure
        // We'll create minimal role objects for UI purposes
        assignableRoles.value = [];
        Object.entries(responseData.roles || {}).forEach(([guardName, roleNames]) => {
          (roleNames as string[]).forEach(roleName => {
            assignableRoles.value.push({
              id: 0, // Placeholder ID for root/admin users
              name: roleName,
              guard_name: guardName,
              organisation_id: guardName === 'system' ? null : null, // Will be set dynamically
              created_at: '',
              updated_at: '',
              type: guardName === 'system' ? 'system' : 'organisation'
            });
          });
        });
      }

      // Process assignable roles to extract organizations and check system permissions
      processAssignableRoles();
    }
  } catch (error) {
    console.error('Load assignable roles error:', error);
  }
}

// Process assignable roles to determine system permissions visibility
function processAssignableRoles() {
  // Check if there are any system roles (guard_name: 'system')
  const systemRoles = assignableRoles.value.filter(role => role.guard_name === 'system');
  showSystemPermissions.value = systemRoles.length > 0;
}

// Note: loadAvailableRoles and loadOrganizationRoles functions removed
// as we now get all required role data from assignable-roles API

// Load all organizations (for root/admin users)
async function loadAllOrganizations() {
  try {
    const { data } = await fetchOrganizations({ per_page: 1000 });
    if (data.success) {
      allOrganizations.value = data.data?.data || [];
    }
  } catch (error) {
    console.error('Load all organizations error:', error);
    allOrganizations.value = [];
  }
}

// Assign system role
async function handleAssignSystemRole() {
  if (!props.user || !systemRoleForm.role_name) return;

  try {
    loading.value = true;
    await fetchAssignUserRole(props.user.id, {
      role_name: systemRoleForm.role_name
    });

    // If we reach here, the request was successful (no exception thrown)
    console.log('System role assignment successful');
    message.success($t('page.user.assignRoleSuccess'));
    // Reload user roles data
    await loadUserRoles();
    // Reset form
    systemRoleForm.role_name = '';
    emit('updated');
  } catch (error) {
    console.log('System role assignment error caught:', error);
    console.log('Error status:', error?.response?.status);
    handleApiError(error, message, $t('page.user.assignRoleFailed'));
  } finally {
    loading.value = false;
  }
}

// Assign organization role
async function handleAssignOrgRole() {
  if (!props.user || !orgRoleForm.role_name || !orgRoleForm.organisation_id) return;

  try {
    loading.value = true;
    const result = await fetchAssignUserRole(props.user.id, {
      role_name: orgRoleForm.role_name,
      organisation_id: orgRoleForm.organisation_id
    });
    console.log('Organization role assignment successful', result);
    // check if result contains 422 error
    if(result.error) {
      loading.value = false;
      return;
    }
    // If we reach here, the request was successful (no exception thrown)
    message.success($t('page.user.assignRoleSuccess'));
    // Reload user roles data
    await loadUserRoles();
    // Reset form
    orgRoleForm.role_name = '';
    orgRoleForm.organisation_id = undefined;
    emit('updated');
  } catch (error) {
    handleApiError(error, message, $t('page.user.assignRoleFailed'));
  } finally {
    loading.value = false;
  }
}

// Remove role
async function handleRemoveRole(roleId: number, roleName: string) {
  if (!props.user) return;

  const confirmMessage = $t('page.user.confirmRemoveRole', { role: roleName, user: props.user.name });

  dialog.warning({
    title: $t('common.tip'),
    content: confirmMessage,
    positiveText: $t('common.confirm'),
    negativeText: $t('common.cancel'),
    onPositiveClick: async () => {
      try {
        loading.value = true;
        await fetchRemoveUserRole(props.user!.id, roleId);

        // If we reach here, the request was successful (no exception thrown)
        message.success($t('page.user.removeRoleSuccess'));
        // Reload user roles data
        await loadUserRoles();
        emit('updated');
      } catch (error) {
        handleApiError(error, message, $t('page.user.removeRoleFailed'));
      } finally {
        loading.value = false;
      }
    }
  });
}



function closeModal() {
  emit('update:modelValue', false);
}

// Helper function
function getOrganizationName(orgId: number): string {
  // First try to find in user roles data
  let org = userRolesData.value?.organisations?.find(o => o.id === orgId);

  // If not found and current user is system admin, try to find in all organizations
  if (!org && isSystemAdmin()) {
    org = allOrganizations.value.find(o => o.id === orgId);
  }

  return org?.name || `Organization ${orgId}`;
}

// Watch user changes
watch(
  () => props.user,
  async (newUser) => {
    if (newUser) {
      await loadUserRoles();
    }
  },
  { immediate: true }
);

// Watch organization selection changes, reset role selection
watch(
  () => orgRoleForm.organisation_id,
  async (newOrgId, oldOrgId) => {
    if (newOrgId !== oldOrgId) {
      orgRoleForm.role_name = '';
      // Role options are now computed from assignableRoles based on selected organization
    }
  }
);

// Calculate modal style
const modalStyle = computed(() => ({
  width: '900px',
  maxWidth: '90vw'
}));

// Load data on component mount
onMounted(() => {
  loadAssignableRoles();

  // Load all organizations if current user is root or admin
  if (isSystemAdmin()) {
    loadAllOrganizations();
  }
});
</script>

<template>
  <NModal
    :show="modelValue"
    preset="dialog"
    :title="modalTitle"
    :negative-text="$t('common.close')"
    class="fixed left-0 right-0 role-management-modal"
    :style="modalStyle"
    @negative-click="closeModal"
    @update:show="emit('update:modelValue', $event)"
  >
    <div class="space-y-8 mt-4">
      <!-- System permissions management area -->
      <div v-if="showSystemPermissions" class="space-y-4 system-permissions">
        <div class="text-lg font-medium text-gray-800 dark:text-gray-200 border-b pb-2 flex items-center justify-left">
          <SvgIcon icon="mdi:shield-account" class="mr-2" />
          {{ $t('page.user.systemPermissions') }}
        </div>

        <!-- Current system permissions -->
        <div v-if="currentSystemRoles.length > 0" class="space-y-2">
          <div class="text-sm font-medium text-gray-600 dark:text-gray-400">
            {{ $t('page.user.currentSystemRoles') }}
          </div>
          <div class="flex flex-wrap gap-2">
            <NTag
              v-for="role in currentSystemRoles"
              :key="role.id"
              :type="role.name === 'root' ? 'error' : 'warning'"
              closable
              @close="handleRemoveRole(role.id, role.name)"
            >
              <template #icon>
                <SvgIcon :icon="role.name === 'root' ? 'mdi:crown' : 'mdi:shield-account'" />
              </template>
              {{ role.name }}
            </NTag>
          </div>
        </div>

        <!-- Assign system permissions -->
        <div class="space-y-3">
          <div class="text-sm font-medium text-gray-600 dark:text-gray-400">
            {{ $t('page.user.assignSystemRole') }}
          </div>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <NSelect
              v-model:value="systemRoleForm.role_name"
              :options="systemRoleOptions"
              :placeholder="$t('page.user.selectSystemRole')"
              clearable
            />
            <NButton
              type="primary"
              :loading="loading"
              :disabled="!systemRoleForm.role_name"
              @click="handleAssignSystemRole"
            >
              {{ $t('page.user.assignRole') }}
            </NButton>
          </div>
        </div>

        <!-- No system permissions notice -->
        <div v-if="currentSystemRoles.length === 0 && systemRoleOptions.length === 0" class="text-center text-gray-500 py-4">
          {{ $t('page.user.noSystemPermissions') }}
        </div>
      </div>

      <!-- Organization permissions management area -->
      <div class="space-y-4 org-permissions">
        <div class="text-lg font-medium text-gray-800 dark:text-gray-200 border-b pb-2 flex items-center justify-left">
          <SvgIcon icon="mdi:office-building" class="mr-2" />
          {{ $t('page.user.organizationPermissions') }}
        </div>

        <!-- Assign organization permissions -->
        <div class="space-y-3">
          <div class="text-sm font-medium text-gray-600 dark:text-gray-400">
            {{ $t('page.user.assignOrgRole') }}
          </div>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <OrganizationSelector
              v-model="orgRoleForm.organisation_id"
              :placeholder="$t('page.user.selectOrganization')"
              :organization-ids="allowedOrganizationIds"
              clearable
            />
            <NSelect
              v-model:value="orgRoleForm.role_name"
              :options="orgRoleOptions"
              :placeholder="$t('page.user.selectOrgRole')"
              clearable
              :disabled="!orgRoleForm.organisation_id"
            />
            <NButton
              type="primary"
              :loading="loading"
              :disabled="!orgRoleForm.role_name || !orgRoleForm.organisation_id"
              @click="handleAssignOrgRole"
            >
              {{ $t('page.user.assignRole') }}
            </NButton>
          </div>
        </div>

        <!-- Current organization permissions -->
        <div v-if="Object.keys(currentOrgRoles).length > 0" class="space-y-3">
          <div class="text-sm font-medium text-gray-600 dark:text-gray-400">
            {{ $t('page.user.currentOrgRoles') }}
          </div>
          <div
            v-for="(orgRoles, orgId) in currentOrgRoles"
            :key="orgId"
            class="border rounded-lg p-4 bg-gray-50 dark:bg-gray-800"
          >
            <div class="flex items-start justify-between mb-3">
              <div class="font-medium text-gray-700 dark:text-gray-300 flex-1 min-w-0">
                <div class="flex items-center">
                  <SvgIcon icon="mdi:office-building" class="mr-2 flex-shrink-0" />
                  <span class="org-name">{{ getOrganizationName(Number(orgId)) }}</span>
                </div>
              </div>
            </div>

            <div class="flex flex-wrap gap-2">
              <NTag
                v-for="role in orgRoles"
                :key="role.id"
                :type="role.name === 'owner' ? 'error' : role.name === 'member' ? 'info' : 'default'"
                :closable="role.name !== 'owner'"
                @close="handleRemoveRole(role.id, role.name)"
              >
                <template #icon>
                  <SvgIcon
                    :icon="role.name === 'owner' ? 'mdi:crown' :
                           role.name === 'member' ? 'mdi:account' :
                           'mdi:eye'"
                  />
                </template>
                {{ role.name }}
                <span v-if="role.name === 'owner'" class="text-xs opacity-60 ml-1">(Non-removable)</span>
              </NTag>
            </div>
          </div>
        </div>

        <!-- No organization permissions notice -->
        <div v-if="Object.keys(currentOrgRoles).length === 0" class="text-center text-gray-500 py-8">
          {{ $t('page.user.noOrgRolesAssigned') }}
        </div>
      </div>
    </div>
  </NModal>
</template>

<style scoped>
/* Modal width control - based on actual rendered class structure */
/* Rendered classes: n-dialog n-dialog--closable n-dialog--icon-left n-modal fixed left-0 right-0 role-management-modal */
:deep(.n-dialog.n-dialog--closable.n-modal.role-management-modal) {
  width: 900px !important;
  max-width: 90vw !important;
}

/* Fallback selector to ensure override */
:deep(.role-management-modal.n-dialog) {
  width: 900px !important;
  max-width: 90vw !important;
}

/* Highest priority selector */
:deep(.n-modal.role-management-modal) {
  width: 900px !important;
  max-width: 90vw !important;
}

/* Use attribute selector as last resort */
:deep([class*='role-management-modal']) {
  width: 900px !important;
  max-width: 90vw !important;
}

/* Mobile adaptation */
@media (max-width: 768px) {
  :deep(.n-dialog.n-dialog--closable.n-modal.role-management-modal),
  :deep(.role-management-modal.n-dialog),
  :deep(.n-modal.role-management-modal),
  :deep([class*='role-management-modal']) {
    width: 95vw !important;
    max-width: 95vw !important;
  }
}

/* Role tag style optimization */
:deep(.n-tag) {
  transition: all 0.3s ease;
}

:deep(.n-tag:hover) {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* System permissions area styles */
.system-permissions {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border: 1px solid #f59e0b;
  border-radius: 8px;
  padding: 16px;
}

/* Organization permissions area styles */
.org-permissions {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border: 1px solid #3b82f6;
  border-radius: 8px;
  padding: 16px;
}

/* Dark theme adaptation */
.dark .system-permissions {
  background: linear-gradient(135deg, #451a03 0%, #78350f 100%);
  border-color: #d97706;
}

.dark .org-permissions {
  background: linear-gradient(135deg, #1e3a8a 0%, #1d4ed8 100%);
  border-color: #2563eb;
}

/* Role icon animation */
:deep(.n-tag .n-tag__icon) {
  transition: transform 0.2s ease;
}

:deep(.n-tag:hover .n-tag__icon) {
  transform: scale(1.1);
}

/* Organization name display optimization */
.org-name {
  word-break: break-word;
  line-height: 1.4;
}
</style>
