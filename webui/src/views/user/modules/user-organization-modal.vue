<script setup lang="tsx">
import { computed, onMounted, reactive, ref, watch } from 'vue';
import { useMessage } from 'naive-ui';
import {
  fetchUserRoles,
  fetchOrganizations,
  fetchAddUserToOrganization,
  fetchRemoveUserFromOrganization,
  fetchSyncUserOrganizations
} from '@/service/api';
import { handleApiError } from '@/utils/error-handler';
import { useAppStore } from '@/store/modules/app';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';

defineOptions({
  name: 'UserOrganizationModal'
});

interface Props {
  /** 模态框可见性 */
  modelValue: boolean;
  /** 用户数据 */
  user?: Api.Organization.OrganizationUser | null;
}

interface Emits {
  (e: 'update:modelValue', visible: boolean): void;
  (e: 'updated'): void;
}

const props = withDefaults(defineProps<Props>(), {
  user: null
});

const emit = defineEmits<Emits>();

const loading = ref(false);
const message = useMessage();
const appStore = useAppStore();

// 用户当前的组织关联
const userOrganizations = ref<Api.Auth.Organisation[]>([]);
// 所有可用的组织
const allOrganizations = ref<Api.Organization.Organization[]>([]);
// 选中的组织ID列表
const selectedOrganizationIds = ref<number[]>([]);

const modalTitle = computed(() => {
  return props.user ? $t('page.user.manageUserOrganizations', { name: props.user.name }) : '';
});

const isMobile = computed(() => appStore.isMobile);

// 可选择的组织选项
const organizationOptions = computed(() => {
  return allOrganizations.value.map(org => ({
    label: org.name,
    value: org.id,
    disabled: false
  }));
});

// 加载用户的组织关联信息
async function loadUserOrganizations() {
  if (!props.user) return;

  try {
    loading.value = true;
    const { data } = await fetchUserRoles(props.user.id);
    if (data.success) {
      userOrganizations.value = data.data?.organisations || [];
      selectedOrganizationIds.value = userOrganizations.value.map(org => org.id);
    } else {
      handleApiError({ response: { data } }, message, $t('page.user.loadUserOrganizationsFailed'));
    }
  } catch (error) {
    handleApiError(error, message, $t('page.user.loadUserOrganizationsFailed'));
  } finally {
    loading.value = false;
  }
}

// 加载所有组织
async function loadAllOrganizations() {
  try {
    const { data } = await fetchOrganizations();
    if (data.success) {
      allOrganizations.value = data.data?.data || [];
    }
  } catch (error) {
    console.error('Load organizations error:', error);
  }
}

// 同步用户组织关联
async function handleSyncOrganizations() {
  if (!props.user) return;

  try {
    loading.value = true;
    await fetchSyncUserOrganizations(props.user.id, {
      organisation_ids: selectedOrganizationIds.value
    });

    message.success($t('page.user.syncOrganizationsSuccess'));
    emit('updated');
    closeModal();
  } catch (error) {
    handleApiError(error, message, $t('page.user.syncOrganizationsFailed'));
  } finally {
    loading.value = false;
  }
}

function closeModal() {
  emit('update:modelValue', false);
}

// 监听用户变化
watch(
  () => props.user,
  (newUser) => {
    if (newUser) {
      loadUserOrganizations();
    }
  },
  { immediate: true }
);

// 组件挂载时加载数据
onMounted(() => {
  loadAllOrganizations();
});
</script>

<template>
  <NModal
    :show="modelValue"
    preset="dialog"
    :title="modalTitle"
    :positive-text="$t('common.save')"
    :negative-text="$t('common.cancel')"
    :loading="loading"
    class="fixed left-0 right-0"
    :class="isMobile ? 'w-[95vw]' : 'w-[600px]'"
    @positive-click="handleSyncOrganizations"
    @negative-click="closeModal"
    @update:show="emit('update:modelValue', $event)"
  >
    <div class="space-y-4 mt-4">
      <div class="text-sm text-gray-600 dark:text-gray-300">
        {{ $t('page.user.selectUserOrganizations') }}
      </div>

      <NSelect
        v-model="selectedOrganizationIds"
        :options="organizationOptions"
        :placeholder="$t('page.user.selectOrganizationsPlaceholder')"
        multiple
        filterable
        :loading="loading"
        class="w-full"
      />

      <div v-if="userOrganizations.length > 0" class="space-y-2">
        <div class="text-sm font-medium text-gray-700 dark:text-gray-300">
          {{ $t('page.user.currentOrganizations') }}:
        </div>
        <div class="flex flex-wrap gap-2">
          <NTag
            v-for="org in userOrganizations"
            :key="org.id"
            type="info"
            size="small"
          >
            {{ org.name }}
          </NTag>
        </div>
      </div>
    </div>
  </NModal>
</template>

<style scoped>
</style>
