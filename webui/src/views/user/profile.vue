<script setup lang="ts">
import { computed } from 'vue';
import { useAuthStore } from '@/store/modules/auth';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';
import UserInfoCard from '@/components/custom/user-info-card.vue';

defineOptions({
  name: 'UserProfile'
});

const authStore = useAuthStore();

// Check if user info exists
const hasUserInfo = computed(() => Boolean(authStore.completeUserInfo));

// User statistics
const userStats = computed(() => {
  const complete = authStore.completeUserInfo;
  if (!complete) return null;

  return {
    totalOrganisations: complete.organisations.length,
    totalSystemRoles: complete.roles.system_roles.length,
    totalOrganisationRoles: complete.roles.organisation_roles.length,
    totalRoles: complete.roles.all_role_names.length,
    emailVerified: complete.email_verified_at !== null
  };
});

// Quick access computed properties
const quickAccess = computed(() => ({
  email: authStore.userEmail,
  organisations: authStore.userOrganisations,
  systemRoles: authStore.userSystemRoles,
  organisationRoles: authStore.userOrganisationRoles
}));
</script>

<template>
  <div class="user-profile-page">
    <NSpace vertical :size="24">
      <!-- Page title -->
      <NCard :bordered="false" class="header-card">
        <template #header>
          <div class="flex items-center gap-3">
            <SvgIcon icon="ph:user-circle" class="text-24px text-primary" />
            <div>
              <h1 class="text-24px font-bold">{{ $t('common.userCenter') }}</h1>
              <p class="text-14px text-gray-500 mt-1">
                {{ $t('page.user.basicInfo') }}
              </p>
            </div>
          </div>
        </template>
      </NCard>

      <!-- User info card -->
      <div v-if="hasUserInfo" class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main user info -->
        <div class="lg:col-span-2">
          <UserInfoCard />
        </div>

        <!-- Statistics sidebar -->
        <div class="space-y-4">
          <!-- Statistics card -->
          <NCard :bordered="false" class="stats-card">
            <template #header>
              <div class="flex items-center gap-2">
                <SvgIcon icon="ph:chart-bar" class="text-16px" />
                <span class="font-medium">{{ $t('page.user.stats') }}</span>
              </div>
            </template>
            
            <NSpace vertical :size="12">
              <div class="stat-item">
                <div class="flex items-center justify-between">
                  <span class="text-14px text-gray-600">{{ $t('page.user.organisations') }}</span>
                  <NBadge :value="userStats?.totalOrganisations || 0" type="info" />
                </div>
              </div>
              
              <div class="stat-item">
                <div class="flex items-center justify-between">
                  <span class="text-14px text-gray-600">{{ $t('page.user.systemRoles') }}</span>
                  <NBadge :value="userStats?.totalSystemRoles || 0" type="warning" />
                </div>
              </div>
              
              <div class="stat-item">
                <div class="flex items-center justify-between">
                  <span class="text-14px text-gray-600">{{ $t('page.user.organisationRoles') }}</span>
                  <NBadge :value="userStats?.totalOrganisationRoles || 0" type="success" />
                </div>
              </div>
              
              <div class="stat-item">
                <div class="flex items-center justify-between">
                  <span class="text-14px text-gray-600">{{ $t('page.user.email') }}</span>
                  <NTag v-if="userStats?.emailVerified" type="success" size="small">
                    {{ $t('common.verified') }}
                  </NTag>
                  <NTag v-else type="warning" size="small">
                    {{ $t('common.unverified') }}
                  </NTag>
                </div>
              </div>
            </NSpace>
          </NCard>

          <!-- Quick access -->
          <NCard :bordered="false" class="quick-access-card">
            <template #header>
              <div class="flex items-center gap-2">
                <SvgIcon icon="ph:lightning" class="text-16px" />
                <span class="font-medium">{{ $t('page.user.quickAccess') }}</span>
              </div>
            </template>
            
            <NSpace vertical :size="8">
              <NButton 
                v-if="quickAccess.email" 
                text 
                type="primary" 
                size="small"
                class="justify-start"
              >
                <template #icon>
                  <SvgIcon icon="ph:envelope" />
                </template>
                {{ $t('page.user.sendEmail') }}
              </NButton>
              
              <NButton 
                v-if="quickAccess.organisations.length > 0" 
                text 
                type="primary" 
                size="small"
                class="justify-start"
              >
                <template #icon>
                  <SvgIcon icon="ph:buildings" />
                </template>
                {{ $t('page.user.manageOrganizations') }}
              </NButton>
              
              <NButton 
                v-if="quickAccess.systemRoles.length > 0" 
                text 
                type="primary" 
                size="small"
                class="justify-start"
              >
                <template #icon>
                  <SvgIcon icon="ph:shield-check" />
                </template>
                {{ $t('page.user.roleManagement') }}
              </NButton>
            </NSpace>
          </NCard>
        </div>
      </div>

      <!-- No user info state -->
      <NCard v-else :bordered="false" class="empty-state-card">
        <NEmpty 
          :description="$t('page.user.noUserInfo')"
          size="large"
        >
          <template #icon>
            <SvgIcon icon="ph:user-circle-x" class="text-48px text-gray-400" />
          </template>
          <template #extra>
            <NButton type="primary" @click="authStore.initUserInfo">
              {{ $t('page.user.reloadUserInfo') }}
            </NButton>
          </template>
        </NEmpty>
      </NCard>
    </NSpace>
  </div>
</template>

<style scoped>
.user-profile-page {
  padding: 16px;
  min-height: 100vh;
  background: #f5f5f5;
}

.header-card,
.stats-card,
.quick-access-card,
.empty-state-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  background: white;
}

.stat-item {
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.stat-item:last-child {
  border-bottom: none;
}

.quick-access-card :deep(.n-button) {
  width: 100%;
}
</style>
