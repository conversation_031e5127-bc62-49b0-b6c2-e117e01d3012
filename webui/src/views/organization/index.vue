<script setup lang="tsx">
import { computed } from 'vue';
import { useAppStore } from '@/store/modules/app';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';
import OrganizationList from './modules/organization-list.vue';
import OrganizationStats from './modules/organization-stats.vue';

defineOptions({
  name: 'Organization'
});

const appStore = useAppStore();

const gap = computed(() => (appStore.isMobile ? 0 : 16));
</script>

<template>
  <NSpace vertical :size="16">
    <NCard :bordered="false" class="card-wrapper">
      <template #header>
        <div class="flex items-center gap-2">
          <SvgIcon icon="mdi:office-building" class="text-20px" />
          <span class="text-18px font-semibold">{{ $t('route.organization') }}</span>
        </div>
      </template>
      <OrganizationStats />
    </NCard>

    <NCard :bordered="false" class="card-wrapper">
      <OrganizationList />
    </NCard>
  </NSpace>
</template>

<style scoped>
.card-wrapper {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
