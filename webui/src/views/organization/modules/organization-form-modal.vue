/// <reference path="../../../typings/api.d.ts" />

<script setup lang="tsx">
import { computed, reactive, ref, watch } from 'vue';
import type { FormInst } from 'naive-ui';
import { useMessage } from 'naive-ui';
import { useAppStore } from '@/store/modules/app';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { fetchCreateOrganization, fetchUpdateOrganization } from '@/service/api';
import { handleApiError } from '@/utils/error-handler';

defineOptions({
  name: 'OrganizationFormModal'
});

interface Props {
  /** 模态框可见性 */
  visible: boolean;
  /** 表单数据 */
  modelValue?: Partial<Api.Organization.CreateOrganizationRequest>;
  /** 编辑的机构数据 */
  editingOrganization?: Api.Organization.Organization | null;
}

interface Emits {
  (e: 'update:visible', visible: boolean): void;
  (e: 'update:modelValue', value: Partial<Api.Organization.CreateOrganizationRequest>): void;
  (e: 'submitted', data?: Api.Organization.Organization): void;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({}),
  editingOrganization: null
});

const emit = defineEmits<Emits>();

const { formRef, validate, restoreValidation } = useNaiveForm();
const loading = ref(false);
const message = useMessage();

const appStore = useAppStore();

// 响应式宽度设置
const isMobile = computed(() => appStore.isMobile);
const modalClass = computed(() => {
  if (isMobile.value) {
    return 'size-full top-0px rounded-0';
  }
  return 'w-60vw max-w-600px min-w-400px top-50px';
});

// 表单数据
const formModel = reactive({
  name: '',
  code: '',
  details: {} as Record<string, any>,
  remarks: '',
  status: 'pending' as Api.Organization.OrganizationStatus
});

// 是否为编辑模式
const isEdit = computed(() => !!props.editingOrganization);

// 模态框标题
const modalTitle = computed(() => {
  return isEdit.value ? $t('page.organization.editOrganization') : $t('page.organization.addOrganization');
});

// 检查表单是否有效（必填字段是否已填写）
const isFormValid = computed(() => {
  // 机构名称是必填的
  if (!formModel.name.trim()) {
    return false;
  }

  // 机构代码是必填的
  if (!formModel.code.trim()) {
    return false;
  }

  // 编辑模式时，状态也是必填的
  if (isEdit.value && !formModel.status) {
    return false;
  }

  return true;
});

// 表单验证规则
const rules = computed(() => {
  const { createRequiredRule } = useFormRules();

  const baseRules: any = {
    name: [createRequiredRule($t('page.organization.organizationNameRequired'))],
    code: [createRequiredRule($t('page.organization.organizationCodeRequired'))]
  };

  // 编辑模式时才验证状态字段
  if (isEdit.value) {
    baseRules.status = [createRequiredRule($t('page.organization.statusRequired'))];
  }

  return baseRules;
});

// 状态选项 - 编辑时只能选择 active 或 suspended
const statusOptions = computed(() => [
  { label: $t('page.organization.active'), value: 'active' },
  { label: $t('page.organization.suspended'), value: 'suspended' }
]);

// 监听编辑数据变化
watch(
  () => props.editingOrganization,
  newOrganization => {
    if (newOrganization) {
      Object.assign(formModel, {
        name: newOrganization.name,
        code: newOrganization.code || '',
        details: newOrganization.details || {},
        remarks: newOrganization.remarks || '',
        status: newOrganization.status
      });
    }
  },
  { immediate: true }
);

// 监听模态框显示状态
watch(
  () => props.visible,
  visible => {
    if (visible) {
      restoreValidation();
      // 如果有传入的 modelValue，使用它来初始化表单
      if (props.modelValue && Object.keys(props.modelValue).length > 0) {
        Object.assign(formModel, props.modelValue);
      }
    } else {
      resetForm();
    }
  }
);

// 监听表单数据变化，同步到父组件
watch(
  formModel,
  newValue => {
    emit('update:modelValue', { ...newValue });
  },
  { deep: true }
);

// 重置表单
function resetForm() {
  Object.assign(formModel, {
    name: '',
    code: '',
    details: {},
    remarks: '',
    status: 'pending' as Api.Organization.OrganizationStatus
  });
  restoreValidation();
}

// 关闭模态框
function closeModal() {
  emit('update:visible', false);
}

// 提交表单
async function handleSubmit() {
  try {
    await validate();
    loading.value = true;

    if (isEdit.value && props.editingOrganization) {
      // 编辑模式
      const { data } = await fetchUpdateOrganization(props.editingOrganization.id, {
        name: formModel.name,
        code: formModel.code,
        details: formModel.details,
        remarks: formModel.remarks,
        status: formModel.status
      });

      if (data.success) {
        message.success($t('page.organization.editSuccess'));
        emit('submitted', data.data);
        closeModal();
      } else {
        handleApiError({ response: { data } }, message, $t('page.organization.editFailed'));
      }
    } else {
      // 创建模式
      const { data } = await fetchCreateOrganization({
        name: formModel.name,
        code: formModel.code,
        details: formModel.details,
        remarks: formModel.remarks
      });

      if (data.success) {
        message.success($t('page.organization.addSuccess'));
        emit('submitted', data.data);
        closeModal();
      } else {
        handleApiError({ response: { data } }, message, $t('page.organization.addFailed'));
      }
    }
  } catch (error) {
    console.error('Submit organization error:', error);
    handleApiError(error, message,
      isEdit.value ? $t('page.organization.editFailed') : $t('page.organization.addFailed')
    );
  } finally {
    loading.value = false;
  }
}
</script>

<template>
  <NModal
    :show="visible"
    preset="dialog"
    :title="modalTitle"
    :positive-text="$t('common.confirm')"
    :negative-text="$t('common.cancel')"
    :loading="loading"
    :positive-button-props="{ disabled: !isFormValid }"
    class="fixed left-0 right-0"
    :class="modalClass"
    @positive-click="handleSubmit"
    @negative-click="closeModal"
    @update:show="emit('update:visible', $event)"
  >
    <NForm
      ref="formRef"
      :model="formModel"
      :rules="rules"
      label-placement="top"
      label-width="auto"
      require-mark-placement="right-hanging"
      class="mt-6"
    >
      <NFormItem :label="$t('page.organization.organizationName')" path="name">
        <NInput
          v-model:value="formModel.name"
          :placeholder="$t('page.organization.organizationNamePlaceholder')"
        />
      </NFormItem>

      <NFormItem :label="$t('page.organization.organizationCode')" path="code">
        <NInput
          v-model:value="formModel.code"
          :placeholder="$t('page.organization.organizationCodePlaceholder')"
        />
      </NFormItem>

      <NFormItem :label="$t('page.organization.remarks')" path="remarks">
        <NInput
          v-model:value="formModel.remarks"
          type="textarea"
          :placeholder="$t('page.organization.remarksPlaceholder')"
          :autosize="{ minRows: 3, maxRows: 6 }"
        />
      </NFormItem>

      <!-- 只在编辑模式时显示状态字段 -->
      <NFormItem v-if="isEdit" :label="$t('page.organization.status')" path="status">
        <NSelect
          v-model:value="formModel.status"
          :options="statusOptions"
          :placeholder="$t('page.organization.statusPlaceholder')"
        />
      </NFormItem>
    </NForm>
  </NModal>
</template>

<style scoped></style>
