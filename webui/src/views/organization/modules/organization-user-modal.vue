<script setup lang="tsx">
import { computed, h, onMounted, reactive, ref, watch } from 'vue';
import type { DataTableColumns } from 'naive-ui';
import { NButton, NTag, useMessage } from 'naive-ui';
import { useAppStore } from '@/store/modules/app';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';
import ButtonIcon from '@/components/custom/button-icon.vue';
import {
  fetchOrganizationUsers,
  fetchRemoveUserFromOrganization,
  fetchSuspendUser,
  fetchActivateUser
} from '@/service/api';

defineOptions({
  name: 'OrganizationUserModal'
});

interface Props {
  /** 模态框可见性 */
  visible: boolean;
  /** 当前企业信息 */
  organization?: Api.Organization.Organization | null;
}

interface Emits {
  (e: 'update:visible', visible: boolean): void;
}

const props = withDefaults(defineProps<Props>(), {
  organization: null
});

const emit = defineEmits<Emits>();

const appStore = useAppStore();
const isMobile = computed(() => appStore.isMobile);
const message = useMessage();

// 模态框状态
const loading = ref(false);

// 数据状态
const organizationUsers = ref<Api.Organization.OrganizationUser[]>([]);
const totalCount = ref(0);

// 响应式宽度设置
const modalClass = computed(() => {
  if (isMobile.value) {
    return 'size-full top-0px rounded-0';
  }
  return 'w-80vw max-w-1200px min-w-800px top-30px';
});



// 企业用户表格列定义（复用用户管理的列定义）
const userColumns = computed<DataTableColumns<UserData>>(() => [
  {
    type: 'selection',
    width: 50
  },
  {
    title: 'ID',
    key: 'id',
    width: 80
  },
  {
    title: $t('page.user.username'),
    key: 'username',
    width: 120
  },
  {
    title: $t('page.user.email'),
    key: 'email',
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: $t('page.user.avatar'),
    key: 'avatar',
    width: 80,
    render: row => {
      return h(
        NAvatar,
        {
          size: 'small',
          src: row.avatar || undefined
        },
        {
          default: () => row.username.charAt(0).toUpperCase()
        }
      );
    }
  },
  {
    title: $t('page.user.status'),
    key: 'status',
    width: 100,
    render: row => {
      const statusMap = {
        active: { type: 'success' as const, text: $t('page.user.active') },
        inactive: { type: 'error' as const, text: $t('page.user.inactive') },
        pending: { type: 'warning' as const, text: $t('page.user.pending') }
      };
      const status = statusMap[row.status];
      return h(NTag, { type: status.type }, { default: () => status.text });
    }
  },
  {
    title: $t('page.user.role'),
    key: 'role',
    width: 120
  },
  {
    title: $t('page.user.createTime'),
    key: 'createTime',
    width: 120
  },
  {
    title: $t('page.user.actions'),
    key: 'actions',
    width: 160,
    fixed: 'right',
    render: row => {
      const actions = [
        h(ButtonIcon, {
          icon: 'mdi:pencil',
          tooltipContent: $t('common.edit'),
          class: 'text-primary',
          onClick: () => handleEditUser(row)
        })
      ];

      // 根据用户状态添加不同的操作按钮
      if (row.status === 'pending') {
        // 待激活用户：发送激活邮件按钮
        actions.push(
          h(ButtonIcon, {
            icon: 'mdi:email-send',
            tooltipContent: $t('page.user.sendActivationEmail'),
            class: 'text-warning',
            onClick: () => handleSendActivationEmail(row)
          })
        );
      } else {
        // 正常/禁用用户：锁定/解锁按钮
        const isActive = row.status === 'active';
        actions.push(
          h(ButtonIcon, {
            icon: isActive ? 'mdi:lock' : 'mdi:lock-open',
            tooltipContent: isActive ? $t('page.user.disable') : $t('page.user.enable'),
            class: isActive ? 'text-warning' : 'text-success',
            onClick: () => handleToggleUserStatus(row)
          })
        );
      }

      // 删除按钮
      actions.push(
        h(ButtonIcon, {
          icon: 'mdi:delete',
          tooltipContent: $t('common.delete'),
          class: 'text-error',
          onClick: () => handleDeleteUser(row)
        })
      );

      return h(
        NSpace,
        { size: 8 },
        {
          default: () => actions
        }
      );
    }
  }
]);

const pagination = reactive({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  onChange: (page: number) => {
    pagination.page = page;
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
  }
});

// 多语言计算属性
const titleText = computed(() => $t('page.organization.userManagement.title'));
const currentOrganizationText = computed(() => $t('page.organization.userManagement.currentOrganization'));
const userListText = computed(() => $t('page.organization.userManagement.userList'));
const addUserText = computed(() => $t('page.user.addUser'));
const selectedCountText = computed(() => $t('page.user.selectedCount'));
const usersText = computed(() => $t('page.user.users'));

// 批量操作相关多语言
const batchEnableText = computed(() => $t('page.user.batchEnable'));
const batchDisableText = computed(() => $t('page.user.batchDisable'));
const selectAllText = computed(() => $t('page.user.selectAll'));
const clearSelectionText = computed(() => $t('page.user.clearSelection'));
const batchOperationSuccessText = computed(() => $t('page.user.batchOperationSuccess'));
const noUsersSelectedText = computed(() => $t('page.user.noUsersSelected'));
const confirmOperationText = computed(() => $t('page.user.confirmOperation'));
const confirmText = computed(() => $t('common.confirm'));
const cancelText = computed(() => $t('common.cancel'));
const noUsersToEnableText = computed(() => $t('page.user.noUsersToEnable'));
const noUsersToDisableText = computed(() => $t('page.user.noUsersToDisable'));

// 关闭模态框
function closeModal() {
  emit('update:visible', false);
}

// 搜索企业用户
function handleSearch() {
  loading.value = true;
  setTimeout(() => {
    loading.value = false;
  }, 1000);
}

// 重置搜索表单
function handleReset() {
  Object.assign(searchForm, {
    username: '',
    email: '',
    status: '',
    organization: ''
  });
}

// 添加用户（打开用户表单）
function handleAddUser() {
  editingUser.value = null;
  // 为新用户预设组织信息
  const newUserTemplate: UserData = {
    id: '',
    username: '',
    email: '',
    avatar: '',
    status: 'active',
    organization: props.organization?.name || '',
    role: 'normalUser',
    createTime: ''
  };
  editingUser.value = newUserTemplate;
  openUserForm();
}

// 编辑用户
function handleEditUser(user: UserData) {
  editingUser.value = user;
  openUserForm();
}

// 切换用户状态
function handleToggleUserStatus(user: UserData) {
  const newStatus = user.status === 'active' ? 'inactive' : 'active';
  const isDisabling = newStatus === 'inactive';

  const confirmKey = isDisabling ? 'page.user.confirmDisableUser' : 'page.user.confirmEnableUser';
  const confirmMessage = $t(confirmKey, { username: user.username });

  window.$dialog?.warning({
    title: $t('common.tip'),
    content: confirmMessage,
    positiveText: $t('common.confirm'),
    negativeText: $t('common.cancel'),
    onPositiveClick: () => {
      const index = organizationUsers.value.findIndex(item => item.id === user.id);
      if (index > -1) {
        organizationUsers.value[index].status = newStatus;
        window.$message?.success($t('page.user.userStatusUpdated'));
      }
    }
  });
}

// 发送激活邮件
function handleSendActivationEmail(user: UserData) {
  const confirmMessage = $t('page.user.confirmSendActivationEmail', { email: user.email });

  window.$dialog?.info({
    title: $t('common.tip'),
    content: confirmMessage,
    positiveText: $t('common.confirm'),
    negativeText: $t('common.cancel'),
    onPositiveClick: () => {
      // 模拟发送激活邮件
      window.$message?.success($t('page.user.activationEmailSent'));
    }
  });
}

// 删除用户
function handleDeleteUser(user: UserData) {
  window.$dialog?.warning({
    title: $t('common.tip'),
    content: $t('common.confirmDelete'),
    positiveText: $t('common.confirm'),
    negativeText: $t('common.cancel'),
    onPositiveClick: () => {
      const index = organizationUsers.value.findIndex(item => item.id === user.id);
      if (index > -1) {
        organizationUsers.value.splice(index, 1);
        window.$message?.success($t('common.deleteSuccess'));
      }
    }
  });
}

// 处理用户表单提交
function handleUserSubmitted(data: UserData) {
  if (editingUser.value) {
    // 编辑模式：更新现有用户
    const index = organizationUsers.value.findIndex(item => item.id === editingUser.value!.id);
    if (index !== -1) {
      organizationUsers.value[index] = {
        ...data,
        id: editingUser.value.id,
        organization: props.organization?.name || ''
      };
    }
  } else {
    // 新增模式：添加新用户
    const newUser: UserData = {
      ...data,
      id: String(Date.now()),
      createTime: new Date().toISOString().split('T')[0],
      organization: props.organization?.name || ''
    };
    organizationUsers.value.unshift(newUser);
  }
}

// 批量操作处理
function handleBatchOperation(key: string) {
  if (checkedRowKeys.value.length === 0) {
    window.$message?.warning(noUsersSelectedText.value);
    return;
  }

  const selectedUsers = organizationUsers.value.filter(user => checkedRowKeys.value.includes(user.id));

  if (key === 'enable') {
    handleBatchEnable(selectedUsers);
  } else if (key === 'disable') {
    handleBatchDisable(selectedUsers);
  }
}

// 批量启用
function handleBatchEnable(users: UserData[]) {
  const usersToEnable = users.filter(user => user.status !== 'active');

  if (usersToEnable.length === 0) {
    window.$message?.warning(noUsersToEnableText.value);
    return;
  }

  window.$dialog?.warning({
    title: confirmOperationText.value,
    content: `${$t('page.user.batchEnableConfirm')} (${usersToEnable.length} ${usersText.value})`,
    positiveText: confirmText.value,
    negativeText: cancelText.value,
    onPositiveClick: () => {
      usersToEnable.forEach(user => {
        const index = organizationUsers.value.findIndex(item => item.id === user.id);
        if (index !== -1) {
          organizationUsers.value[index].status = 'active';
        }
      });

      window.$message?.success(`${batchOperationSuccessText.value} (${usersToEnable.length} ${usersText.value})`);
      checkedRowKeys.value = [];
    }
  });
}

// 批量禁用
function handleBatchDisable(users: UserData[]) {
  const usersToDisable = users.filter(user => user.status === 'active');

  if (usersToDisable.length === 0) {
    window.$message?.warning(noUsersToDisableText.value);
    return;
  }

  window.$dialog?.warning({
    title: confirmOperationText.value,
    content: `${$t('page.user.batchDisableConfirm')} (${usersToDisable.length} ${usersText.value})`,
    positiveText: confirmText.value,
    negativeText: cancelText.value,
    onPositiveClick: () => {
      usersToDisable.forEach(user => {
        const index = organizationUsers.value.findIndex(item => item.id === user.id);
        if (index !== -1) {
          organizationUsers.value[index].status = 'inactive';
        }
      });

      window.$message?.success(`${batchOperationSuccessText.value} (${usersToDisable.length} ${usersText.value})`);
      checkedRowKeys.value = [];
    }
  });
}

// 全选
function handleSelectAll() {
  checkedRowKeys.value = organizationUsers.value.map(user => user.id);
}

// 清空选择
function handleClearSelection() {
  checkedRowKeys.value = [];
}

// 处理行选择
function handleRowCheck(rowKeys: DataTableRowKey[]) {
  checkedRowKeys.value = rowKeys;
}

// 刷新数据
function handleRefresh() {
  loading.value = true;
  setTimeout(() => {
    loading.value = false;
  }, 1000);
}

// 监听企业变化，重新加载用户数据
watch(
  () => props.organization,
  newOrg => {
    if (newOrg) {
      // 这里可以根据企业ID加载对应的用户数据
      handleRefresh();
    }
  },
  { immediate: true }
);
</script>

<template>
  <NModal
    :show="visible"
    preset="dialog"
    :title="titleText"
    :negative-text="$t('common.close')"
    class="fixed left-0 right-0"
    :class="modalClass"
    @negative-click="closeModal"
    @update:show="emit('update:visible', $event)"
  >
    <div class="mt-6 space-y-4">
      <!-- 当前企业信息 -->
      <div class="border-l-4 border-blue-500 rounded-lg bg-blue-50 p-3 dark:bg-blue-900/20">
        <div class="flex items-center gap-2">
          <SvgIcon icon="mdi:office-building" class="text-blue-500" />
          <span class="font-medium">{{ currentOrganizationText }}:</span>
          <span class="text-blue-600 dark:text-blue-400">{{ organization?.name || '-' }}</span>
        </div>
      </div>

      <!-- 搜索区域 -->
      <div class="rounded-lg bg-gray-50 p-3 space-y-3 dark:bg-gray-800">
        <!-- 搜索条件 -->
        <div class="grid grid-cols-1 gap-3 lg:grid-cols-4 md:grid-cols-2">
          <div>
            <span class="text-sm text-gray-600 dark:text-gray-300">{{ $t('page.user.username') }}:</span>
            <NInput
              v-model:value="searchForm.username"
              :placeholder="$t('page.user.usernamePlaceholder')"
              size="small"
              clearable
              class="mt-1"
            />
          </div>
          <div>
            <span class="text-sm text-gray-600 dark:text-gray-300">{{ $t('page.user.email') }}:</span>
            <NInput
              v-model:value="searchForm.email"
              :placeholder="$t('page.user.emailPlaceholder')"
              size="small"
              clearable
              class="mt-1"
            />
          </div>
          <div>
            <span class="text-sm text-gray-600 dark:text-gray-300">{{ $t('page.user.status') }}:</span>
            <NSelect
              v-model:value="searchForm.status"
              :placeholder="$t('page.user.statusPlaceholder')"
              size="small"
              clearable
              class="mt-1"
              :options="[
                { label: $t('page.user.active'), value: 'active' },
                { label: $t('page.user.inactive'), value: 'inactive' },
                { label: $t('page.user.pending'), value: 'pending' }
              ]"
            />
          </div>
          <div class="flex items-end gap-2">
            <NButton type="primary" size="small" :loading="loading" @click="handleSearch">
              <template #icon>
                <SvgIcon icon="mdi:magnify" />
              </template>
              {{ $t('common.search') }}
            </NButton>
            <NButton size="small" @click="handleReset">
              <template #icon>
                <SvgIcon icon="mdi:refresh" />
              </template>
              {{ $t('common.reset') }}
            </NButton>
          </div>
        </div>
      </div>

      <!-- 操作栏 -->
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-2">
          <NButton type="primary" @click="handleAddUser">
            <template #icon>
              <SvgIcon icon="mdi:plus" />
            </template>
            {{ addUserText }}
          </NButton>
          <NButton :loading="loading" @click="handleRefresh">
            <template #icon>
              <SvgIcon icon="mdi:refresh" />
            </template>
            {{ $t('common.refresh') }}
          </NButton>
          <NDropdown
            trigger="click"
            :options="[
              { label: batchEnableText, key: 'enable' },
              { label: batchDisableText, key: 'disable' }
            ]"
            @select="handleBatchOperation"
          >
            <NButton :disabled="checkedRowKeys.length === 0">
              {{ $t('page.user.batchOperations') }}
              <template #icon>
                <SvgIcon icon="mdi:chevron-down" />
              </template>
            </NButton>
          </NDropdown>
        </div>

        <!-- 选择状态和批量操作 -->
        <div v-if="checkedRowKeys.length > 0" class="flex items-center gap-2 text-sm text-gray-600">
          <span>{{ selectedCountText }} {{ checkedRowKeys.length }} {{ usersText }}</span>
          <NButton size="small" text @click="handleSelectAll">
            {{ selectAllText }}
          </NButton>
          <NButton size="small" text @click="handleClearSelection">
            {{ clearSelectionText }}
          </NButton>
        </div>
      </div>

      <!-- 用户列表 -->
      <div>
        <div class="mb-3 flex items-center gap-2">
          <SvgIcon icon="mdi:account-group" class="text-primary" />
          <span class="font-medium">{{ userListText }}</span>
          <span class="text-sm text-gray-500">({{ organizationUsers.length }} {{ usersText }})</span>
        </div>

        <NDataTable
          v-model:checked-row-keys="checkedRowKeys"
          :columns="userColumns"
          :data="organizationUsers"
          :loading="loading"
          :pagination="pagination"
          :bordered="false"
          size="small"
          flex-height
          scroll-x="1000px"
          class="h-[500px]"
          :row-key="(row: UserData) => row.id"
          @update:checked-row-keys="handleRowCheck"
        />
      </div>
    </div>

    <!-- 用户表单模态框 -->
    <UserFormModal v-model="userFormVisible" :editing-user="editingUser" @submitted="handleUserSubmitted" />
  </NModal>
</template>

<style scoped></style>
