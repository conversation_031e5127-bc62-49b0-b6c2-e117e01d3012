<script setup lang="tsx">
import { computed, h, onMounted, reactive, ref, watch } from 'vue';
import type { DataTableColumns } from 'naive-ui';
import { NButton, useMessage, useDialog } from 'naive-ui';
import { useAppStore } from '@/store/modules/app';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';
import ButtonIcon from '@/components/custom/button-icon.vue';
import {
  fetchOrganizationUsers,
  fetchRemoveUserFromOrganization
} from '@/service/api';
import { handleApiError } from '@/utils/error-handler';

defineOptions({
  name: 'OrganizationUserModal'
});

interface Props {
  /** 模态框可见性 */
  visible: boolean;
  /** 当前企业信息 */
  organization?: Api.Organization.Organization | null;
}

interface Emits {
  (e: 'update:visible', visible: boolean): void;
}

const props = withDefaults(defineProps<Props>(), {
  organization: null
});

const emit = defineEmits<Emits>();

const appStore = useAppStore();
const isMobile = computed(() => appStore.isMobile);
const message = useMessage();
const dialog = useDialog();

// 模态框状态
const loading = ref(false);

// 数据状态
const organizationUsers = ref<Api.Organization.OrganizationUser[]>([]);
const totalCount = ref(0);

// 响应式宽度设置
const modalClass = computed(() => {
  if (isMobile.value) {
    return 'size-full top-0px rounded-0';
  }
  return 'w-80vw max-w-1200px min-w-800px top-30px';
});

// 用户表格列定义
const userColumns = computed<DataTableColumns<Api.Organization.OrganizationUser>>(() => [
  {
    title: 'ID',
    key: 'id',
    width: 80
  },
  {
    title: $t('page.user.name'),
    key: 'name',
    width: 120
  },
  {
    title: $t('page.user.email'),
    key: 'email',
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: $t('page.user.createTime'),
    key: 'created_at',
    width: 150,
    render: row => new Date(row.created_at).toLocaleDateString()
  },
  {
    title: $t('page.user.actions'),
    key: 'actions',
    width: 120,
    render: row => {
      return h(
        ButtonIcon,
        {
          icon: 'mdi:delete',
          tooltipContent: $t('page.organization.removeUser'),
          class: 'text-error',
          onClick: () => handleRemoveUser(row)
        }
      );
    }
  }
]);

const pagination = reactive({
  page: 1,
  pageSize: 15,
  showSizePicker: true,
  pageSizes: [10, 15, 20, 50],
  itemCount: 0,
  onChange: (page: number) => {
    pagination.page = page;
    loadUsers();
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
    loadUsers();
  }
});

// 加载用户列表
async function loadUsers() {
  if (!props.organization) return;

  try {
    loading.value = true;
    const { data } = await fetchOrganizationUsers(props.organization.id, {
      per_page: pagination.pageSize
    });

    if (data.success) {
      organizationUsers.value = data.data?.data || [];
      pagination.itemCount = data.data?.meta.total || 0;
      totalCount.value = data.data?.meta.total || 0;
    } else {
      handleApiError({ response: { data } }, message, $t('page.organization.userManagement.loadUsersFailed'));
    }
  } catch (error) {
    console.error('Load users error:', error);
    handleApiError(error, message, $t('page.organization.userManagement.loadUsersFailed'));
  } finally {
    loading.value = false;
  }
}

// 移除用户
function handleRemoveUser(user: Api.Organization.OrganizationUser) {
  if (!props.organization) return;

  dialog.warning({
    title: $t('page.organization.userManagement.confirmRemoveTitle'),
    content: $t('page.organization.userManagement.removeUserConfirmContent', {
      userName: user.name,
      organizationName: props.organization.name
    }),
    positiveText: $t('page.organization.userManagement.confirmRemoveButton'),
    negativeText: $t('common.cancel'),
    onPositiveClick: async () => {
      try {
        const { data } = await fetchRemoveUserFromOrganization(user.id, props.organization.id);
        if (data.success) {
          message.success($t('page.organization.userManagement.removeUserSuccess'));
          loadUsers();
        } else {
          handleApiError({ response: { data } }, message, $t('page.organization.userManagement.removeUserFailed'));
        }
      } catch (error) {
        console.error('Remove user error:', error);
        handleApiError(error, message, $t('page.organization.userManagement.removeUserFailed'));
      }
    }
  });
}

// 关闭模态框
function closeModal() {
  emit('update:visible', false);
}

// 刷新数据
function handleRefresh() {
  loadUsers();
}

// 监听企业变化，重新加载用户数据
watch(
  () => props.organization,
  newOrg => {
    if (newOrg && props.visible) {
      loadUsers();
    }
  },
  { immediate: true }
);

// 监听模态框显示状态
watch(
  () => props.visible,
  visible => {
    if (visible && props.organization) {
      loadUsers();
    }
  }
);
</script>

<template>
  <NModal
    :show="visible"
    preset="dialog"
    :title="$t('page.organization.userManagement.title')"
    :negative-text="$t('common.close')"
    class="fixed left-0 right-0"
    :class="modalClass"
    @negative-click="closeModal"
    @update:show="emit('update:visible', $event)"
  >
    <div class="mt-6 space-y-4">
      <!-- 当前企业信息 -->
      <div class="border-l-4 border-blue-500 rounded-lg bg-blue-50 p-3 dark:bg-blue-900/20">
        <div class="flex items-center gap-2">
          <SvgIcon icon="mdi:office-building" class="text-blue-500" />
          <span class="font-medium">{{ $t('page.organization.userManagement.currentOrganization') }}:</span>
          <span class="text-blue-600 dark:text-blue-400">{{ organization?.name || '-' }}</span>
        </div>
      </div>

      <!-- 操作栏 -->
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-2">
          <NButton :loading="loading" @click="handleRefresh">
            <template #icon>
              <SvgIcon icon="mdi:refresh" />
            </template>
            {{ $t('common.refresh') }}
          </NButton>
        </div>

        <div class="text-sm text-gray-500">
          {{ $t('page.organization.userManagement.userList') }} ({{ totalCount }} {{ $t('page.user.users') }})
        </div>
      </div>

      <!-- 用户列表 -->
      <NDataTable
        :columns="userColumns"
        :data="organizationUsers"
        :loading="loading"
        :pagination="pagination"
        :bordered="false"
        size="small"
        flex-height
        style="height: 400px"
      />
    </div>
  </NModal>
</template>

<style scoped></style>
