<script setup lang="tsx">
import { computed, reactive, ref, watch } from 'vue';
import { useMessage } from 'naive-ui';
import { fetchCreateInvitation } from '@/service/api';
import { useAppStore } from '@/store/modules/app';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { handleApiError } from '@/utils/error-handler';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';

defineOptions({
  name: 'OrganizationInviteModal'
});

interface Props {
  /** 模态框可见性 */
  visible: boolean;
  /** 当前组织数据 */
  organization?: Api.Organization.Organization | null;
}

interface InviteFormData {
  email: string;
  role: string;
  expires_at?: string;
  max_uses: number;
}

interface Emits {
  (e: 'update:visible', visible: boolean): void;
}

const props = withDefaults(defineProps<Props>(), {
  organization: null
});

const emit = defineEmits<Emits>();

const { formRef, validate, restoreValidation } = useNaiveForm();
const loading = ref(false);
const copyLoading = ref(false);
const sendEmailLoading = ref(false);
const message = useMessage();

const appStore = useAppStore();
const isMobile = computed(() => appStore.isMobile);

// 表单数据
const formModel = reactive<InviteFormData>({
  email: '',
  role: 'member',
  max_uses: 1
});

// 邀请链接和数据
const inviteLink = ref('');
const invitationData = ref<Api.Organization.Invitation | null>(null);
const linkGenerated = ref(false);

// 表单验证规则
const rules = computed(() => {
  const { formRules, createRequiredRule } = useFormRules();

  return {
    email: formRules.email,
    role: [createRequiredRule($t('page.organization.invite.roleRequired'))]
  };
});

// 角色选项
const roleOptions = [
  { label: $t('page.user.owner'), value: 'owner' },
  { label: $t('page.user.member'), value: 'member' }
];

// 检查表单是否有效
const isFormValid = computed(() => {
  // 角色是必填的
  if (!formModel.role) {
    return false;
  }

  // 如果填写了邮箱，需要验证邮箱格式
  if (formModel.email && formModel.email.trim()) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formModel.email.trim())) {
      return false;
    }
  }

  return true;
});

// 生成邀请链接
function generateInviteLink(invitationId: string) {
  const baseUrl = window.location.origin;
  return `${baseUrl}/join?id=${invitationId}`;
}

// 复制链接
async function handleCopyLink() {
  if (!inviteLink.value) return;

  try {
    copyLoading.value = true;
    await navigator.clipboard.writeText(inviteLink.value);
    message.success($t('page.organization.invite.copySuccess'));
  } catch (error) {
    // 降级方案：使用传统方法复制
    const textArea = document.createElement('textarea');
    textArea.value = inviteLink.value;
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand('copy');
    document.body.removeChild(textArea);
    message.success($t('page.organization.invite.copySuccess'));
  } finally {
    copyLoading.value = false;
  }
}

// 发送邮件
async function handleSendEmail() {
  if (!inviteLink.value || !formModel.email || !props.organization) return;

  try {
    sendEmailLoading.value = true;

    // 这里可以调用实际的邮件发送API
    // 目前只是显示成功消息
    await new Promise(resolve => setTimeout(resolve, 1000));

    message.success($t('page.organization.invite.emailSentSuccess'));
  } catch (error) {
    message.error($t('page.organization.invite.emailSentFailed'));
  } finally {
    sendEmailLoading.value = false;
  }
}

// 重置表单
function resetForm() {
  Object.assign(formModel, {
    email: '',
    role: 'member',
    max_uses: 1
  });
  inviteLink.value = '';
  invitationData.value = null;
  linkGenerated.value = false;
  restoreValidation();
}

// 关闭模态框
function closeModal() {
  emit('update:visible', false);
}

// 监听模态框可见性变化
watch(
  () => props.visible,
  visible => {
    if (!visible) {
      resetForm();
    }
  }
);

// 生成邀请链接（提交表单）
async function handleGenerateLink() {
  if (!props.organization) return;

  try {
    await validate();
    loading.value = true;

    // 设置过期时间为7天后
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7);

    const { data } = await fetchCreateInvitation({
      model_type: 'App\\Models\\Organisation',
      model_id: props.organization.id,
      role: formModel.role,
      expires_at: expiresAt.toISOString(),
      max_uses: formModel.max_uses,
      email_restriction: formModel.email || undefined
    });

    if (data.success && data.data) {
      invitationData.value = data.data;
      inviteLink.value = generateInviteLink(data.data.id);
      linkGenerated.value = true;
      message.success($t('page.organization.invite.generateSuccess'));
    } else {
      handleApiError({ response: { data } }, message, $t('page.organization.invite.generateFailed'));
    }
  } catch (error) {
    console.error('Generate invitation error:', error);
    handleApiError(error, message, $t('page.organization.invite.generateFailed'));
  } finally {
    loading.value = false;
  }
}
</script>

<template>
  <NModal
    :show="visible"
    preset="card"
    :title="$t('page.organization.invite.title')"
    class="invite-modal"
    :class="isMobile ? 'w-full max-w-none' : 'w-600px'"
    @update:show="emit('update:visible', $event)"
  >
    <div class="space-y-4">
      <!-- 组织信息 -->
      <div v-if="organization" class="organization-info">
        <div class="mb-2 flex items-center gap-2">
          <SvgIcon icon="mdi:office-building" class="text-16px text-primary" />
          <span class="font-medium">{{ $t('page.organization.invite.inviteToOrganization') }}</span>
        </div>
        <div class="rounded-lg bg-gray-50 p-3 dark:bg-gray-800">
          <div class="text-primary font-semibold">{{ organization.name }}</div>
          <div v-if="organization.description" class="mt-1 text-sm text-gray-600 dark:text-gray-400">
            {{ organization.description }}
          </div>
        </div>
      </div>

      <!-- 邀请表单 -->
      <NForm
        ref="formRef"
        :model="formModel"
        :rules="rules"
        label-placement="top"
        label-width="auto"
        require-mark-placement="right-hanging"
      >
        <NFormItem :label="$t('page.organization.invite.inviteeEmail')" path="email">
          <NInput
            v-model:value="formModel.email"
            :placeholder="$t('page.organization.invite.emailPlaceholder')"
            type="email"
          >
            <template #prefix>
              <SvgIcon icon="mdi:email" class="text-gray-400" />
            </template>
          </NInput>
        </NFormItem>

        <NFormItem :label="$t('page.organization.invite.inviteeRole')" path="role">
          <NSelect
            v-model:value="formModel.role"
            :options="roleOptions"
            :placeholder="$t('page.organization.invite.rolePlaceholder')"
          />
        </NFormItem>
      </NForm>

      <!-- 生成链接按钮 -->
      <div v-if="!linkGenerated" class="generate-button-section">
        <NButton
          type="primary"
          size="large"
          block
          :loading="loading"
          :disabled="!isFormValid"
          @click="handleGenerateLink"
        >
          <template #icon>
            <SvgIcon icon="mdi:link-variant" />
          </template>
          {{ $t('page.organization.invite.generate') }}
        </NButton>
      </div>

      <!-- 邀请链接区域 -->
      <div v-if="linkGenerated" class="invite-link-section">
        <div class="mb-2 flex items-center gap-2">
          <SvgIcon icon="mdi:link" class="text-16px text-success" />
          <span class="font-medium">{{ $t('page.organization.invite.inviteLink') }}</span>
        </div>

        <div class="link-container">
          <NInput :value="inviteLink" readonly type="textarea" :rows="3" class="link-input" />

          <div class="link-actions">
            <NButton type="primary" ghost :loading="copyLoading" @click="handleCopyLink">
              <template #icon>
                <SvgIcon icon="mdi:content-copy" />
              </template>
              {{ $t('page.organization.invite.copyLink') }}
            </NButton>

            <NButton type="success" :loading="sendEmailLoading" @click="handleSendEmail">
              <template #icon>
                <SvgIcon icon="mdi:email-send" />
              </template>
              {{ $t('page.organization.invite.sendEmail') }}
            </NButton>
          </div>
        </div>

        <div class="link-hint">
          <SvgIcon icon="mdi:information" class="text-warning" />
          <span class="text-sm text-gray-600 dark:text-gray-400">
            {{ $t('page.organization.invite.linkExpireHint') }}
          </span>
        </div>

        <!-- 重新生成按钮 -->
        <div class="regenerate-section">
          <NButton
            type="primary"
            ghost
            :loading="loading"
            :disabled="!isFormValid"
            @click="handleGenerateLink"
          >
            <template #icon>
              <SvgIcon icon="mdi:refresh" />
            </template>
            {{ $t('page.organization.invite.regenerate') }}
          </NButton>
        </div>
      </div>
    </div>
  </NModal>
</template>

<style scoped>
.invite-modal {
  max-height: 90vh;
  overflow-y: auto;
}

.organization-info {
  border-left: 4px solid var(--primary-color);
  padding-left: 12px;
}

.invite-link-section {
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 16px;
  background: var(--card-color);
}

.link-container {
  position: relative;
}

.link-input {
  margin-bottom: 12px;
}

.generate-button-section {
  margin: 20px 0;
}

.link-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.regenerate-section {
  margin-top: 16px;
  text-align: center;
}

.link-hint {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 12px;
  padding: 8px 12px;
  background: rgba(var(--warning-color-rgb), 0.1);
  border-radius: 6px;
}

@media (max-width: 768px) {
  .link-actions {
    flex-direction: column;
  }

  .link-actions .n-button {
    width: 100%;
  }
}
</style>
