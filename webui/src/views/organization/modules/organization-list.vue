<script setup lang="tsx">
import { computed, h, onMounted, reactive, ref } from 'vue';
import type { DataTableColumns } from 'naive-ui';
import { NButton, NSpace, NTag, NSelect, useMessage, useDialog } from 'naive-ui';
import { useAppStore } from '@/store/modules/app';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';
import ButtonIcon from '@/components/custom/button-icon.vue';
import { fetchOrganizations, fetchSuspendOrganization, fetchOrganizationDetail } from '@/service/api';
import { handleApiError } from '@/utils/error-handler';
import OrganizationFormModal from './organization-form-modal.vue';
import OrganizationUserModal from './organization-user-modal-simple.vue';
import OrganizationInviteModal from './organization-invite-modal.vue';

defineOptions({
  name: 'OrganizationList'
});

const searchValue = ref('');
const loading = ref(false);
const message = useMessage();
const dialog = useDialog();

const appStore = useAppStore();
const isMobile = computed(() => appStore.isMobile);

// 机构表单模态框状态
const organizationFormVisible = ref(false);
const editingOrganization = ref<Api.Organization.Organization | null>(null);

// 人员管理模态框状态
const userManagementVisible = ref(false);
const currentOrganization = ref<Api.Organization.Organization | null>(null);

// 邀请用户模态框状态
const inviteUserVisible = ref(false);
const inviteOrganization = ref<Api.Organization.Organization | null>(null);

// 数据状态
const tableData = ref<Api.Organization.Organization[]>([]);
const totalCount = ref(0);
const statusFilter = ref<Api.Organization.OrganizationStatus | ''>('');

const columns = computed<DataTableColumns<Api.Organization.Organization>>(() => [
  {
    title: 'ID',
    key: 'id',
    width: 80
  },
  {
    title: $t('page.organization.organizationName'),
    key: 'name',
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: $t('page.organization.organizationCode'),
    key: 'code',
    width: 200,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: $t('page.organization.createTime'),
    key: 'created_at',
    width: 150,
    render: row => new Date(row.created_at).toLocaleDateString()
  },
  {
    title: $t('page.organization.userCount'),
    key: 'users_count',
    width: 100,
    render: row => `${row.users_count || 0}`
  },
  {
    title: $t('page.organization.status'),
    key: 'status',
    width: 100,
    render: row => {
      const statusMap = {
        pending: { type: 'warning' as const, text: $t('page.organization.pending') },
        active: { type: 'success' as const, text: $t('page.organization.active') },
        suspended: { type: 'error' as const, text: $t('page.organization.suspended') }
      };
      const status = statusMap[row.status];
      return h(
        NTag,
        {
          type: status.type
        },
        {
          default: () => status.text
        }
      );
    }
  },
  {
    title: $t('page.organization.actions'),
    key: 'actions',
    width: 160,
    render: row => {
      const actions = [
        h(ButtonIcon, {
          icon: 'mdi:pencil',
          tooltipContent: $t('page.organization.edit'),
          class: 'text-primary',
          onClick: () => handleEditOrganization(row)
        }),
        h(ButtonIcon, {
          icon: 'mdi:account-group',
          tooltipContent: $t('page.organization.manageUsers'),
          class: 'text-success',
          onClick: () => handleManageUsers(row)
        }),
        h(ButtonIcon, {
          icon: 'mdi:email-send',
          tooltipContent: $t('page.organization.inviteUser'),
          class: 'text-warning',
          onClick: () => handleInviteUser(row)
        })
      ];

      // 根据状态添加暂停/激活按钮
      if (row.status === 'active') {
        actions.push(
          h(ButtonIcon, {
            icon: 'mdi:pause',
            tooltipContent: $t('page.organization.suspend'),
            class: 'text-error',
            onClick: () => handleSuspendOrganization(row)
          })
        );
      }

      return h(
        NSpace,
        { size: 8 },
        {
          default: () => actions
        }
      );
    }
  }
]);

const pagination = reactive({
  page: 1,
  pageSize: 15,
  showSizePicker: true,
  pageSizes: [10, 15, 20, 50],
  itemCount: 0,
  onChange: (page: number) => {
    pagination.page = page;
    loadOrganizations();
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
    loadOrganizations();
  }
});

// 加载机构列表
async function loadOrganizations() {
  try {
    loading.value = true;
    const params: any = {
      per_page: pagination.pageSize
    };

    if (statusFilter.value) {
      params.status = statusFilter.value;
    }

    const { data } = await fetchOrganizations(params);
    if (data.success) {
      tableData.value = data.data?.data || [];
      pagination.itemCount = data.data?.meta.total || 0;
      totalCount.value = data.data?.meta.total || 0;
    } else {
      handleApiError({ response: { data } }, message, $t('page.organization.loadFailed'));
    }
  } catch (error) {
    console.error('Load organizations error:', error);
    handleApiError(error, message, $t('page.organization.loadFailed'));
  } finally {
    loading.value = false;
  }
}

function handleSearch() {
  pagination.page = 1;
  loadOrganizations();
}

function handleAddOrganization() {
  editingOrganization.value = null;
  organizationFormVisible.value = true;
}

async function handleEditOrganization(organization: Api.Organization.Organization) {
  try {
    loading.value = true;
    // 通过API获取机构详情
    const { data } = await fetchOrganizationDetail(organization.id);
    if (data.success) {
      editingOrganization.value = data.data;
      organizationFormVisible.value = true;
    } else {
      handleApiError({ response: { data } }, message, $t('page.organization.fetchDetailFailed'));
    }
  } catch (error) {
    console.error('Fetch organization detail error:', error);
    handleApiError(error, message, $t('page.organization.fetchDetailFailed'));
  } finally {
    loading.value = false;
  }
}

function handleManageUsers(organization: Api.Organization.Organization) {
  currentOrganization.value = organization;
  userManagementVisible.value = true;
}

function handleInviteUser(organization: Api.Organization.Organization) {
  inviteOrganization.value = organization;
  inviteUserVisible.value = true;
}

function handleOrganizationSubmitted() {
  // 重新加载数据
  loadOrganizations();
}

function handleRefresh() {
  pagination.page = 1;
  loadOrganizations();
}

// 状态筛选变化
function handleStatusFilterChange() {
  pagination.page = 1;
  loadOrganizations();
}

// 暂停机构
function handleSuspendOrganization(organization: Api.Organization.Organization) {
  dialog.warning({
    title: $t('page.organization.suspendConfirmTitle'),
    content: $t('page.organization.suspendConfirmContent', { name: organization.name }),
    positiveText: $t('page.organization.confirmSuspendButton'),
    negativeText: $t('common.cancel'),
    onPositiveClick: async () => {
      try {
        const { data } = await fetchSuspendOrganization(organization.id);
        if (data.success) {
          message.success($t('page.organization.suspendSuccess'));
          loadOrganizations();
        } else {
          handleApiError({ response: { data } }, message, $t('page.organization.suspendFailed'));
        }
      } catch (error) {
        console.error('Suspend organization error:', error);
        handleApiError(error, message, $t('page.organization.suspendFailed'));
      }
    }
  });
}

// 组件挂载时加载数据
onMounted(() => {
  loadOrganizations();
});
</script>

<template>
  <div class="space-y-3">
    <!-- 紧凑搜索区域 -->
    <div
      class="rounded-lg bg-gray-50 p-3 dark:bg-gray-800"
      :class="isMobile ? 'space-y-3' : 'flex items-center justify-between'"
    >
      <div :class="isMobile ? 'space-y-3' : 'flex items-center gap-3'">
        <div :class="isMobile ? '' : 'flex items-center gap-3'">
          <span class="whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
            {{ $t('page.organization.organizationName') }}:
          </span>
          <div class="flex items-center gap-2" :class="isMobile ? 'mt-2' : ''">
            <NInput
              v-model:value="searchValue"
              :placeholder="$t('page.organization.searchPlaceholder')"
              size="small"
              :class="isMobile ? 'flex-1' : 'w-48'"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <SvgIcon icon="mdi:office-building" class="text-gray-400" />
              </template>
            </NInput>
          </div>
        </div>

        <div :class="isMobile ? '' : 'flex items-center gap-3'">
          <span class="whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
            {{ $t('page.organization.status') }}:
          </span>
          <NSelect
            v-model:value="statusFilter"
            :placeholder="$t('page.organization.allStatus')"
            size="small"
            :class="isMobile ? 'mt-2' : 'w-32'"
            clearable
            :options="[
              { value: 'pending', label: $t('page.organization.pending') },
              { value: 'active', label: $t('page.organization.active') },
              { value: 'suspended', label: $t('page.organization.suspended') }
            ]"
            @update:value="handleStatusFilterChange"
          />
        </div>

        <NButton type="primary" size="small" :loading="loading" @click="handleSearch">
          <template #icon>
            <SvgIcon icon="mdi:magnify" />
          </template>
          {{ $t('common.search') }}
        </NButton>
      </div>

      <div class="flex items-center gap-2" :class="isMobile ? 'w-full' : ''">
        <NButton :loading="loading" size="small" @click="handleRefresh">
          <template #icon>
            <SvgIcon icon="mdi:refresh" />
          </template>
          {{ $t('common.refresh') }}
        </NButton>
        <NButton type="primary" size="small" :class="isMobile ? 'flex-1' : ''" @click="handleAddOrganization">
          <template #icon>
            <SvgIcon icon="mdi:plus" />
          </template>
          {{ $t('page.organization.addOrganization') }}
        </NButton>
      </div>
    </div>

    <NDataTable
      :columns="columns"
      :data="tableData"
      :loading="loading"
      :pagination="pagination"
      :bordered="false"
      size="small"
      flex-height
      style="height: 500px"
    />

    <!-- 机构表单模态框 -->
    <OrganizationFormModal
      v-model:visible="organizationFormVisible"
      :editing-organization="editingOrganization"
      @submitted="handleOrganizationSubmitted"
    />

    <!-- 人员管理模态框 -->
    <OrganizationUserModal v-model:visible="userManagementVisible" :organization="currentOrganization" />

    <!-- 邀请用户模态框 -->
    <OrganizationInviteModal v-model:visible="inviteUserVisible" :organization="inviteOrganization" />
  </div>
</template>

<style scoped></style>
