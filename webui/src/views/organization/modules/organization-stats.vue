<script setup lang="tsx">
import { onMounted, ref } from 'vue';
import { $t } from '@/locales';
import StatsCard from '@/components/custom/stats-card.vue';
import StatsGrid from '@/components/custom/stats-grid.vue';
import { fetchOrganizations } from '@/service/api';

defineOptions({
  name: 'OrganizationStats'
});

// 统计数据
const stats = ref({
  total: 0,
  active: 0,
  pending: 0,
  suspended: 0,
  totalUsers: 0
});

const loading = ref(false);

// 加载统计数据
async function loadStats() {
  try {
    loading.value = true;

    // 获取所有机构
    const allResponse = await fetchOrganizations({ per_page: 1000 });
    if (allResponse.data.success) {
      const organizations = allResponse.data.data?.data || [];
      stats.value.total = organizations.length;

      // 计算各状态数量
      stats.value.active = organizations.filter(org => org.status === 'active').length;
      stats.value.pending = organizations.filter(org => org.status === 'pending').length;
      stats.value.suspended = organizations.filter(org => org.status === 'suspended').length;

      // 计算总用户数
      stats.value.totalUsers = organizations.reduce((sum, org) => sum + (org.users_count || 0), 0);
    }
  } catch (error) {
    console.error('Load stats error:', error);
  } finally {
    loading.value = false;
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadStats();
});
</script>

<template>
  <StatsGrid>
    <StatsCard
      :title="$t('page.organization.totalOrganizations')"
      :value="stats.total"
      :unit="$t('common.unit.organizations')"
      :color="{ start: '#007aff', end: '#0056cc' }"
      :loading="loading"
      icon="mdi:office-building"
    />
    <StatsCard
      :title="$t('page.organization.activeOrganizations')"
      :value="stats.active"
      :unit="$t('common.unit.organizations')"
      :color="{ start: '#52c41a', end: '#389e0d' }"
      :loading="loading"
      icon="mdi:check-circle"
    />
    <StatsCard
      :title="$t('page.organization.totalUsers')"
      :value="stats.totalUsers"
      :unit="$t('common.unit.people')"
      :color="{ start: '#722ed1', end: '#531dab' }"
      :loading="loading"
      icon="mdi:account-group"
    />
    <StatsCard
      :title="$t('page.organization.pendingOrganizations')"
      :value="stats.pending"
      :unit="$t('common.unit.organizations')"
      :color="{ start: '#fa8c16', end: '#d46b08' }"
      :loading="loading"
      icon="mdi:clock-outline"
    />
  </StatsGrid>
</template>
