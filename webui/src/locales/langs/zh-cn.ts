const local: App.I18n.Schema = {
  system: {
    title: 'Jast<PERSON>art<PERSON>',
    updateTitle: '系统版本更新通知',
    updateContent: '检测到系统有新版本发布，是否立即刷新页面？',
    updateConfirm: '立即刷新',
    updateCancel: '稍后再说'
  },
  common: {
    action: '操作',
    add: '新增',
    addSuccess: '添加成功',
    backToHome: '返回首页',
    batchDelete: '批量删除',
    cancel: '取消',
    close: '关闭',
    check: '勾选',
    expandColumn: '展开列',
    columnSetting: '列设置',
    config: '配置',
    confirm: '确认',
    delete: '删除',
    deleteSuccess: '删除成功',
    confirmDelete: '确认删除吗？',
    edit: '编辑',
    warning: '警告',
    error: '错误',
    index: '序号',
    keywordSearch: '请输入关键词搜索',
    logout: '退出登录',
    logoutConfirm: '确认退出登录吗？',
    lookForward: '敬请期待',
    modify: '修改',
    modifySuccess: '修改成功',
    noData: '无数据',
    operate: '操作',
    pleaseCheckValue: '请检查输入的值是否合法',
    refresh: '刷新',
    reset: '重置',
    save: '保存',
    search: '搜索',
    status: '状态',
    switch: '切换',
    tip: '提示',
    trigger: '触发',
    update: '更新',
    updateSuccess: '更新成功',
    view: '查看',
    userCenter: '个人中心',
    selectAll: '全选',
    deselectAll: '取消全选',
    selectOrganization: '请选择机构',
    selectMultipleOrganizations: '请选择机构',
    allOrganizations: '全部机构',
    searchOrganizations: '搜索机构',
    noOrganizations: '暂无机构',
    noSearchResults: '无搜索结果',
    unit: {
      reports: '份',
      orders: '笔',
      organizations: '个',
      people: '人',
      games: '款',
      copies: '份'
    },
    yesOrNo: {
      yes: '是',
      no: '否'
    },
    verified: '已验证',
    unverified: '未验证',
    pending: '待确认',
    selectOrganization: '选择机构',
    selectMultipleOrganizations: '选择多个机构',
    allOrganizations: '全部机构'
  },
  request: {
    logout: '请求失败后登出用户',
    logoutMsg: '用户状态失效，请重新登录',
    logoutWithModal: '请求失败后弹出模态框再登出用户',
    logoutWithModalMsg: '用户状态失效，请重新登录',
    refreshToken: '请求的token已过期，刷新token',
    tokenExpired: 'token已过期'
  },
  theme: {
    themeSchema: {
      title: '主题模式',
      light: '亮色模式',
      dark: '暗黑模式',
      auto: '跟随系统'
    },
    grayscale: '灰色模式',
    colourWeakness: '色弱模式',
    layoutMode: {
      title: '布局模式',
      vertical: '左侧菜单模式',
      'vertical-mix': '左侧菜单混合模式',
      horizontal: '顶部菜单模式',
      'horizontal-mix': '顶部菜单混合模式',
      reverseHorizontalMix: '一级菜单与子级菜单位置反转'
    },
    recommendColor: '应用推荐算法的颜色',
    recommendColorDesc: '推荐颜色的算法参照',
    themeColor: {
      title: '主题颜色',
      primary: '主色',
      info: '信息色',
      success: '成功色',
      warning: '警告色',
      error: '错误色',
      followPrimary: '跟随主色'
    },
    scrollMode: {
      title: '滚动模式',
      wrapper: '外层滚动',
      content: '主体滚动'
    },
    page: {
      animate: '页面切换动画',
      mode: {
        title: '页面切换动画类型',
        'fade-slide': '滑动',
        fade: '淡入淡出',
        'fade-bottom': '底部消退',
        'fade-scale': '缩放消退',
        'zoom-fade': '渐变',
        'zoom-out': '闪现',
        none: '无'
      }
    },
    fixedHeaderAndTab: '固定头部和标签栏',
    header: {
      height: '头部高度',
      breadcrumb: {
        visible: '显示面包屑',
        showIcon: '显示面包屑图标'
      },
      multilingual: {
        visible: '显示多语言按钮'
      },
      globalSearch: {
        visible: '显示全局搜索按钮'
      }
    },
    tab: {
      visible: '显示标签栏',
      cache: '标签栏信息缓存',
      height: '标签栏高度',
      mode: {
        title: '标签栏风格',
        chrome: '谷歌风格',
        button: '按钮风格'
      }
    },
    sider: {
      inverted: '深色侧边栏',
      width: '侧边栏宽度',
      collapsedWidth: '侧边栏折叠宽度',
      mixWidth: '混合布局侧边栏宽度',
      mixCollapsedWidth: '混合布局侧边栏折叠宽度',
      mixChildMenuWidth: '混合布局子菜单宽度'
    },
    footer: {
      visible: '显示底部',
      fixed: '固定底部',
      height: '底部高度',
      right: '底部局右'
    },
    watermark: {
      visible: '显示全屏水印',
      text: '水印文本'
    },
    themeDrawerTitle: '主题配置',
    pageFunTitle: '页面功能',
    resetCacheStrategy: {
      title: '重置缓存策略',
      close: '关闭页面',
      refresh: '刷新页面'
    },
    configOperation: {
      copyConfig: '复制配置',
      copySuccessMsg: '复制成功，请替换 src/theme/settings.ts 中的变量 themeSettings',
      resetConfig: '重置配置',
      resetSuccessMsg: '重置成功'
    }
  },
  route: {
    login: '登录',
    403: '无权限',
    404: '页面不存在',
    500: '服务器错误',
    'iframe-page': '外链页面',
    home: '首页',
    organization: '机构管理',
    user: '用户管理',
    game: '游戏管理',
    sales: '销售统计',
    finance: '财务报表',
    finance_report_detail: '报表详情',
    admin: '管理员功能',
    join: '加入机构'
  },
  invitation: {
    title: '组织邀请',
    loading: '正在加载邀请信息...',
    invalid: '邀请无效',
    expired: '邀请已过期',
    usageLimitReached: '邀请已达使用上限',
    invalidLink: '邀请链接无效',
    expiredLink: '此邀请链接已过期，无法使用。',
    usageLimitLink: '此邀请链接已达到最大使用次数。',
    invitedToJoin: '您被邀请加入以下组织',
    organizationInfo: '组织信息',
    inviterRole: '邀请角色',
    inviter: '邀请人',
    expirationTime: '过期时间',
    restrictedEmail: '限制邮箱',
    cannotAccept: '无法接受邀请',
    acceptInvitation: '接受邀请',
    loginToAccept: '登录接受邀请',
    registerToAccept: '注册接受邀请',
    backToHome: '返回首页',
    joinSuccess: '成功加入组织！',
    joinFailed: '接受邀请失败',
    emailRestriction: '此邀请仅限 {email} 使用',
    getVerificationCode: '获取验证码',
    resendCode: '重新发送 ({time}s)',
    verificationCodeSent: '验证码已发送到您的邮箱',
    sendCodeFailed: '发送验证码失败',
    enterEmail: '请输入邮箱地址',
    enterValidEmail: '请输入有效的邮箱地址',
    enterVerificationCode: '请输入验证码',
    enterPassword: '请输入密码',
    enterPasswordAgain: '请再次输入密码',
    emailPlaceholder: '请输入邮箱地址',
    codePlaceholder: '请输入验证码',
    passwordPlaceholder: '请输入密码',
    confirmPasswordPlaceholder: '请再次输入密码',
    register: '注册',
    codeLogin: '验证码登录',
    roles: {
      owner: '所有者',
      member: '成员'
    }
  },
  page: {
    login: {
      common: {
        loginOrRegister: '登录 / 注册',
        userNamePlaceholder: '请输入用户名或邮箱',
        phonePlaceholder: '请输入手机号',
        codePlaceholder: '请输入验证码',
        passwordPlaceholder: '请输入密码',
        confirmPasswordPlaceholder: '请再次输入密码',
        codeLogin: '验证码登录',
        confirm: '确定',
        back: '返回',
        validateSuccess: '验证成功',
        loginSuccess: '登录成功',
        welcomeBack: '欢迎回来，{userName} ！'
      },
      pwdLogin: {
        title: '密码登录',
        rememberMe: '记住我',
        forgetPassword: '忘记密码？',
        register: '注册账号',
        otherAccountLogin: '其他账号登录',
        otherLoginMode: '其他登录方式',
        superAdmin: '超级管理员',
        admin: '管理员',
        user: '普通用户'
      },
      codeLogin: {
        title: '验证码登录',
        getCode: '获取验证码',
        reGetCode: '{time}秒后重新获取',
        sendCodeSuccess: '验证码发送成功',
        imageCodePlaceholder: '请输入图片验证码'
      },
      register: {
        title: '注册账号',
        agreement: '我已经仔细阅读并接受',
        protocol: '《用户协议》',
        policy: '《隐私权政策》'
      },
      resetPwd: {
        title: '重置密码'
      },
      bindWeChat: {
        title: '绑定微信'
      }
    },
    home: {
      branchDesc:
        '为了方便大家开发和更新合并，我们对main分支的代码进行了精简，只保留了首页菜单，其余内容已移至example分支进行维护。预览地址显示的内容即为example分支的内容。',
      greeting: {
        morning: '早上好，{userName}！',
        afternoon: '下午好，{userName}！',
        evening: '晚上好，{userName}！'
      },
      weatherDesc: '今日多云转晴，20℃ - 25℃!',
      projectCount: '项目数',
      todo: '待办',
      message: '消息',
      downloadCount: '下载量',
      registerCount: '注册量',
      schedule: '作息安排',
      study: '学习',
      work: '工作',
      rest: '休息',
      entertainment: '娱乐',
      userCount: '用户数',
      gameCount: '游戏数',
      orderCount: '订单数',
      reportCount: '报告数',
      totalSalesAmount: '总销售额',
      dailyOrderSales: '订货单销售日统计',
      dailySalesAmount: '日销售额统计',
      dailySalesQuantity: '日销量统计',
      salesAmountByRegion: '分区域销售金额',
      salesQuantityByRegion: '分区域销量',
      salesByRegion: '销售按区域汇总',
      orderAmount: '订单金额',
      orderQuantity: '订单数量',
      salesAmount: '销售额',
      salesQuantity: '销量',
      topSalesAmountRanking: '日销售金额TOP10',
      topSalesQuantityRanking: '日销量TOP10',
      gameName: '游戏名称',
      ranking: '排名',
      viewGameStats: '查看游戏统计',
      businessStats: '业务统计概览',
      totalRevenue: '总收入',
      monthlyGrowth: '月增长率',
      activeUsers: '活跃用户',
      systemMessages: '系统消息',
      notifications: '通知消息',
      viewAll: '查看全部',
      markAsRead: '标记已读',
      newOrder: '新订单',
      systemUpdate: '系统更新',
      securityAlert: '安全提醒',
      maintenanceNotice: '维护通知',
      minutes: '分钟前',
      hours: '小时前',
      days: '天前',
      projectNews: {
        title: '项目动态',
        moreNews: '更多动态',
        desc1: 'Soybean 在2021年5月28日创建了开源项目 soybean-admin!',
        desc2: 'Yanbowe 向 soybean-admin 提交了一个bug，多标签栏不会自适应。',
        desc3: 'Soybean 准备为 soybean-admin 的发布做充分的准备工作!',
        desc4: 'Soybean 正在忙于为soybean-admin写项目说明文档！',
        desc5: 'Soybean 刚才把工作台页面随便写了一些，凑合能看了！'
      },
      creativity: '创意'
    },
    organization: {
      title: '机构管理',
      list: '机构列表',
      stats: '机构统计',
      totalOrganizations: '机构总数',
      activeOrganizations: '活跃机构',
      pendingOrganizations: '待审核机构',
      totalUsers: '总用户数',
      newOrganizations: '本月新增',
      searchPlaceholder: '按机构名称搜索',
      addOrganization: '新增机构',
      editOrganization: '编辑机构',
      organizationName: '机构名称',
      organizationNamePlaceholder: '请输入机构名称',
      organizationNameRequired: '请输入机构名称',
      organizationCode: '机构代码',
      organizationCodePlaceholder: '请输入机构代码（可选）',
      organizationCodeRequired: '请输入机构代码',
      description: '机构描述',
      descriptionPlaceholder: '请输入机构描述（可选）',
      remarks: '备注',
      remarksPlaceholder: '请输入备注（可选）',
      allStatus: '全部状态',
      statusPlaceholder: '请选择状态',
      statusRequired: '请选择状态',
      createTime: '创建时间',
      userCount: '用户数量',
      status: '状态',
      actions: '操作',
      edit: '编辑',
      viewDetails: '查看详情',
      manageUsers: '管理用户',
      inviteUser: '邀请用户',
      pending: '待审核',
      active: '正常',
      suspended: '暂停',
      suspend: '暂停',
      removeUser: '移除用户',
      confirmSuspend: '确认暂停',
      suspendConfirmTitle: '确认暂停',
      suspendConfirmContent: '确定要暂停机构 "{name}" 吗？暂停后该机构将无法正常使用系统功能。',
      confirmSuspendButton: '确认暂停',
      addSuccess: '机构添加成功',
      editSuccess: '机构修改成功',
      addFailed: '机构添加失败',
      editFailed: '机构修改失败',
      loadFailed: '获取机构列表失败',
      fetchDetailFailed: '获取机构详情失败',
      suspendSuccess: '机构已暂停',
      suspendFailed: '暂停机构失败',
      userManagement: {
        title: '企业人员管理',
        currentOrganization: '当前企业',
        userList: '员工列表',
        confirmRemoveTitle: '确认移除',
        removeUserConfirmContent: '确定要将用户 "{userName}" 从机构 "{organizationName}" 中移除吗？',
        confirmRemoveButton: '确认移除',
        loadUsersFailed: '获取用户列表失败',
        removeUserSuccess: '用户已移除',
        removeUserFailed: '移除用户失败'
      },
      invite: {
        title: '邀请用户加入组织',
        inviteToOrganization: '邀请加入组织',
        inviteeEmail: '被邀请人邮箱',
        emailPlaceholder: '请输入被邀请人的邮箱地址',
        inviteeRole: '被邀请人角色',
        rolePlaceholder: '请选择角色',
        roleRequired: '请选择角色',
        inviteLink: '邀请链接',
        generate: '生成邀请链接',
        regenerate: '重新生成链接',
        copyLink: '复制链接',
        sendEmail: '发送邮件',
        linkGenerated: '邀请链接已生成',
        copySuccess: '链接已复制到剪贴板',
        emailSentSuccess: '邀请邮件发送成功',
        emailSentFailed: '邀请邮件发送失败',
        linkExpireHint: '邀请链接将在7天后过期',
        generateSuccess: '邀请链接生成成功',
        generateFailed: '生成邀请链接失败'
      }
    },
    user: {
      title: '用户管理',
      list: '用户列表',
      stats: '用户统计',
      totalUsers: '用户总数',
      activeUsers: '活跃用户',
      pendingActivation: '待激活用户',
      newUsers: '本月新增',
      searchConditions: '搜索条件',
      username: '用户名',
      email: '邮箱',
      status: '状态',
      organization: '所属机构',
      avatar: '头像',
      role: '角色',
      createTime: '创建时间',
      actions: '操作',
      addUser: '新建用户',
      batchOperations: '批量操作',
      batchEnable: '批量启用',
      batchDisable: '批量停用',
      selectedCount: '已选择',
      users: '个用户',
      selectAll: '全选',
      clearSelection: '清空选择',
      batchEnableConfirm: '确认批量启用选中的用户？',
      batchDisableConfirm: '确认批量禁用选中的用户？',
      batchOperationSuccess: '批量操作完成',
      noUsersSelected: '请先选择要操作的用户',
      confirmOperation: '确认操作',
      noUsersToEnable: '所选用户中没有需要启用的用户',
      noUsersToDisable: '所选用户中没有需要禁用的用户',
      usernamePlaceholder: '请输入用户名',
      emailPlaceholder: '请输入邮箱',
      statusPlaceholder: '请选择状态',
      organizationPlaceholder: '请输入机构名称',
      active: '正常',
      inactive: '停用',
      pending: '待激活',
      disable: '禁用',
      enable: '启用',
      sendActivationEmail: '发送激活邮件',
      activationEmailSent: '激活邮件已发送',
      confirmDisableUser: '确认禁用用户 {username} 吗？',
      confirmEnableUser: '确认启用用户 {username} 吗？',
      confirmSendActivationEmail: '确认向 {email} 发送激活邮件吗？',
      userStatusUpdated: '用户状态已更新',
      admin: '管理员',
      normalUser: '普通用户',
      owner: '所有者',
      member: '成员',
      name: '姓名',
      basicInfo: '基本信息',
      userId: '用户ID',
      createdAt: '创建时间',
      updatedAt: '更新时间',
      roles: '角色',
      systemRoles: '系统角色',
      organisationRoles: '组织角色',
      organisations: '所属组织',
      noUserInfo: '暂无用户信息',
      userForm: {
        title: '用户信息',
        addTitle: '新建用户',
        editTitle: '编辑用户',
        username: '用户名',
        email: '邮箱',
        password: '密码',
        confirmPassword: '确认密码',
        role: '角色',
        organization: '所属机构',
        status: '状态',
        avatar: '头像',
        usernamePlaceholder: '请输入用户名',
        emailPlaceholder: '请输入邮箱地址',
        passwordPlaceholder: '请输入密码',
        confirmPasswordPlaceholder: '请再次输入密码',
        rolePlaceholder: '请选择角色',
        organizationPlaceholder: '请输入所属机构',
        statusPlaceholder: '请选择状态',
        submit: '提交',
        cancel: '取消',
        required: '此项为必填项',
        usernameRequired: '请输入用户名',
        emailRequired: '请输入邮箱地址',
        passwordRequired: '请输入密码',
        confirmPasswordRequired: '请再次输入密码',
        roleRequired: '请选择角色',
        organizationRequired: '请输入所属机构',
        statusRequired: '请选择状态',
        emailInvalid: '邮箱格式不正确',
        passwordInvalid: '密码长度至少6位',
        confirmPasswordInvalid: '两次输入的密码不一致',
        usernameInvalid: '用户名长度应在2-50个字符之间',
        uploadAvatar: '上传头像',
        changeAvatar: '更换头像',
        removeAvatar: '删除头像',
        avatarHint: '支持 JPG、PNG、GIF，不超过 2MB',
        avatarTypeError: '头像格式不正确，请上传 JPG、PNG、GIF 格式的图片',
        avatarSizeError: '头像文件大小不能超过 2MB',
        avatarUploadSuccess: '头像上传成功',
        avatarUploadError: '头像上传失败',
        avatarRemoveSuccess: '头像删除成功',
        addOrganization: '添加机构',
        organizationAddSuccess: '机构添加成功',
        noOrganization: '暂无所属机构',
        addSuccess: '用户添加成功',
        editSuccess: '用户修改成功',
        addFailed: '用户添加失败',
        editFailed: '用户修改失败'
      },
      // 用户角色管理相关翻译
      manageUserRoles: '管理用户角色 - {name}',
      assignNewRole: '分配新角色',
      selectRole: '选择角色',
      selectOrganization: '选择机构',
      assignRole: '分配角色',
      currentRoles: '当前角色',
      transferOwnership: '转移所有权',
      noRolesAssigned: '暂无分配角色',
      loadUserRolesFailed: '加载用户角色失败',
      // 新增的角色管理翻译
      systemPermissions: '系统权限',
      organizationPermissions: '机构权限',
      currentSystemRoles: '当前系统权限',
      currentOrgRoles: '当前机构权限',
      assignSystemRole: '分配系统权限',
      assignOrgRole: '分配机构权限',
      selectSystemRole: '选择系统角色',
      selectOrgRole: '选择机构角色',
      noSystemPermissions: '暂无系统权限',
      noOrgRolesAssigned: '暂无机构权限',
      deleteUser: '删除用户',
      removeFromOrganization: '移除机构',
      confirmDeleteUser: '确认删除用户 {username} 吗？此操作不可恢复！',
      confirmRemoveFromOrganization: '确认将用户 {username} 从机构 {organization} 中移除吗？',
      userDeleted: '用户已删除',
      removedFromOrganization: '用户已从机构中移除',
      deleteFailed: '删除用户失败',
      removeFromOrganizationFailed: '移除用户失败',
      deleteUserNotImplemented: '删除用户功能暂未实现',
      noRemovableOrganizations: '没有可移除的机构',
      selectOrganizationToRemove: '请选择要移除的机构',
      assignRoleSuccess: '角色分配成功',
      assignRoleFailed: '角色分配失败',
      confirmRemoveRole: '确认移除用户 {user} 的 {role} 角色吗？',
      removeRoleSuccess: '角色移除成功',
      removeRoleFailed: '角色移除失败',
      confirmTransferOwner: '确认将所有者权限转移给用户 {user} 吗？',
      transferOwnerSuccess: '所有者权限转移成功',
      transferOwnerFailed: '所有者权限转移失败',
      // 用户机构管理相关翻译
      manageUserOrganizations: '管理用户机构 - {name}',
      loadUserOrganizationsFailed: '加载用户机构失败',
      syncOrganizationsSuccess: '机构关联同步成功',
      syncOrganizationsFailed: '机构关联同步失败',
      selectUserOrganizations: '选择用户所属机构',
      selectOrganizationsPlaceholder: '请选择机构',
      currentOrganizations: '当前机构',
      // 用户列表操作按钮
      manageOrganizations: '管理机构',
      manageRoles: '管理角色',
      // 用户操作相关翻译
      loadFailed: '加载用户列表失败',
      loadUserDetailFailed: '加载用户详情失败',
      updateFailed: '更新用户状态失败',
      sendEmailFailed: '发送邮件失败',
      confirmSuspendUser: '确认暂停用户 {username} 吗？',
      userSuspended: '用户已暂停',
      suspendFailed: '暂停用户失败',
      batchOperationFailed: '批量操作失败'
    },
    game: {
      title: '游戏管理',
      list: '游戏列表',
      stats: '游戏统计',
      totalGames: '游戏总数',
      enabledGames: '已启用游戏',
      totalInventory: '总库存',
      newGames: '本月新增',
      filterConditions: '筛选条件',
      gameName: '游戏名称',
      gameCode: '游戏Code',
      priceRange: '价格范围',
      package: 'Package',
      coverImage: '游戏封面',
      name: 'Name',
      code: 'Code',
      multiLangName: '多语言名称',
      onHand: '库存(On-hand)',
      enabled: '状态(Enabled)',
      supportedLanguages: '支持语言',
      sales: '销量',
      price: '价格',
      actions: '操作',
      importGame: '导入游戏',
      batchOperations: '批量操作',
      batchEnable: '批量启用',
      batchDisable: '批量禁用',
      batchDelete: '批量删除',
      gameNamePlaceholder: '请输入游戏名称',
      gameCodePlaceholder: '请输入游戏Code',
      statusPlaceholder: '请选择状态',
      enable: '启用',
      disable: '禁用',
      details: '详情',
      variants: '变体',
      languages: '种语言',
      import: {
        title: '导入游戏',
        searchPlaceholder: '搜索游戏名称',
        codeSearchPlaceholder: '搜索游戏编码',
        selectCategory: '选择分类',
        selectPlatform: '选择平台',
        importing: '导入中...',
        importSelected: '导入选中游戏',
        selectAll: '全选可用游戏',
        deselectAll: '取消全选',
        selectedCount: '已选择 {count} 个游戏',
        availableGames: '可导入游戏',
        category: '分类',
        platform: '平台',
        description: '描述',
        available: '可导入',
        unavailable: '不可用',
        importSuccess: '成功导入 {count} 个游戏',
        importFailed: '导入失败，请重试',
        importing_: '正在导入游戏...',
        noGamesSelected: '请选择要导入的游戏',
        expandAll: '展开全部',
        collapseAll: '收起全部'
      },
      statistics: {
        title: '游戏销售统计',
        gameBasicInfo: '游戏基本信息',
        filterConditions: '筛选条件',
        timeRange: '时间范围',
        region: '地区',
        currency: '币种',
        orderStatus: '订单状态',
        allRegions: '全部地区',
        northAmerica: '北美',
        europe: '欧洲',
        asia: '亚洲',
        others: '其他',
        allCurrencies: '全部币种',
        allStatuses: '全部状态',
        completed: '已完成',
        processing: '处理中',
        cancelled: '已取消',
        refunded: '退款',
        totalSales: '总销量',
        price: '价格',
        regionDistribution: '按地区销售分布',
        regionVolumeDistribution: '按地区订单量分布',
        regionAmountDistribution: '按地区销售金额分布',
        currencyDistribution: '按币种销售分布',
        currencyVolumeDistribution: '按币种订单量分布',
        currencyAmountDistribution: '按币种销售金额分布',
        statusDistribution: '订单状态分布',
        dailySales: '每日销售情况',
        hourlySales: '当日分时销售曲线',
        salesVolume: '销售量',
        salesAmount: '销售额',
        orderCount: '订单数量',
        selectGame: '请选择一个游戏查看统计信息',
        noDataAvailable: '暂无数据',
        date: '日期',
        hour: '时间',
        quantity: '数量',
        amount: '金额',
        count: '数量',
        overview: '数据概览',
        gameTotalSalesAmount: '总销售金额',
        gameTotalSalesVolume: '总销量',
        gameRefundCount: '退单数量',
        gameRefundRate: '退单率'
      },
      orders: {
        title: '销售订单详情',
        gameBasicInfo: '游戏基本信息',
        variantBasicInfo: '变体基本信息',
        filterConditions: '筛选条件',
        region: '销售地区',
        distributor: '机构',
        dateRange: '时间区间',
        allRegions: '全部地区',
        allDistributors: '全部机构',
        orderList: '订单列表',
        orderId: '订单ID',
        orderDate: '订单日期',
        buyerName: '购买者',
        quantity: '数量',
        unitPrice: '单价',
        totalAmount: '总金额',
        orderStatus: '订单状态',
        actions: '操作',
        viewDetails: '查看详情',
        noOrdersFound: '暂无订单数据',
        selectGameOrVariant: '请选择一个游戏或变体查看订单详情',
        totalOrders: '订单总数',
        totalQuantity: '销售总量',
        totalRevenue: '销售总额',
        completed: '已完成',
        processing: '处理中',
        cancelled: '已取消',
        refunded: '已退款'
      }
    },
    sales: {
      title: '销售统计',
      stats: '销售统计',
      charts: '销售图表',
      totalSales: '总销售额',
      totalOrders: '订单总数',
      todaySales: '今日销售',
      monthSales: '本月销售',
      regionDistribution: '地区分布',
      currencyDistribution: '币种分布',
      orderStatus: '订单状态',
      dailySales: '日销售统计',
      hourlyTrend: '分时销售曲线',
      exportData: '导出数据',
      selectRegion: '选择地区',
      selectCurrency: '选择币种',
      allRegions: '全部地区',
      allCurrencies: '全部币种',
      northAmerica: '北美',
      europe: '欧洲',
      asia: '亚洲',
      others: '其他',
      completed: '已完成',
      processing: '处理中',
      cancelled: '已取消',
      refunded: '退款',
      timeRange: '时间范围',
      region: '地区',
      currency: '货币',
      tabs: {
        overview: '销售概览',
        orders: '订单分析',
        refunds: '退单统计',
        regional: '区域分析',
        customers: '用户分析',
        topRanking: '游戏销售Top10'
      },
      topSalesRanking: '销售单数Top10',
      topRefundRanking: '退单单数Top10',
      ordersUnit: '单',
      orderCompletionRate: '订单完成率',
      avgOrderValue: '平均订单价值',
      orderTimeDistribution: '订单时段分布',
      refundRate: '退单率',
      refundAmount: '退单金额',
      refundCount: '退单数量',
      refundReason: '退单原因',
      refundReasonDistribution: '退单原因分布',
      refundRateTrend: '退单率趋势',
      refundAmountTrend: '退单金额趋势',
      regionalRefundRate: '各地区退单率对比',
      regionalSalesComparison: '各地区销售额对比',
      regionalVolumeComparison: '各地区销量对比',
      regionalGrowthRate: '地区销售增长率对比',
      regionalAvgOrderValue: '地区客单价对比',
      newVsOldUsers: '新老用户销售贡献对比',
      repurchaseRate: '用户复购率趋势',
      orderValueDistribution: '客单价分布',
      userRegionDistribution: '用户地区分布',
      productQualityIssue: '产品质量问题',
      notMeetExpectation: '不符合预期',
      technicalIssue: '技术问题',
      priceFactor: '价格因素',
      otherReasons: '其他原因',
      newUsers: '新用户',
      oldUsers: '老用户',
      salesAmount: '销售额',
      orderVolume: '订单量',
      statisticalPeriod: '统计周期',
      monthly: '按月统计',
      quarterly: '按季度统计',
      yearly: '按年统计',
      growthRate: '增长率',
      userCount: '用户数量',
      hour: '时',
      orderUnit: '笔',
      peopleUnit: '人',
      decreaseFromLastMonth: '较上月下降',
      increaseFromLastMonth: '较上月增长',
      decreaseOrdersFromLastMonth: '较上月减少',
      avgOrderValue: '客单价'
    },
    finance: {
      title: '财务报表',
      reports: '财务报表',
      stats: '财务统计',
      orders: '订单列表',
      totalRevenue: '总收入',
      monthlyRevenue: '本月收入',
      pendingReports: '待审核报表',
      publishedReports: '已发布报表',
      filterConditions: '筛选条件',
      reportId: '报表ID',
      organization: '机构',
      period: '报表期间',
      status: '状态',
      createTime: '创建时间',
      auditTime: '审核时间',
      publishTime: '发布时间',
      totalAmount: '总金额',
      actions: '操作',
      generateReport: '生成报表',
      editReport: '编辑财务报告',
      updateReport: '更新报告',
      editFinancialReport: '编辑财务报告',
      generateFinancialReport: '生成财务报告',
      batchAudit: '批量审核',
      items: '项',
      selectAll: '全选',
      clearSelection: '清空选择',
      batchAuditConfirm: '确认批量审核选中的报告？',
      batchAuditSuccess: '批量审核完成',
      noReportsSelected: '请先选择要审核的报告',
      noPendingReports: '所选报告中没有待审核的报告',
      confirmOperation: '确认操作',
      view: '查看',
      audit: '审核',
      draft: '草稿',
      pending: '待审核',
      published: '已发布',
      organizationPlaceholder: '请输入机构名称',
      statusPlaceholder: '请选择状态',
      selectMonth: '请选择月份',
      // 订单相关翻译
      orderId: '订单ID',
      orderDate: '订单日期',
      buyerName: '购买者姓名',
      distributor: '机构',
      gameName: '游戏名称',
      quantity: '数量',
      originalPrice: '原价',
      discount: '折扣',
      finalPrice: '成交价',
      unitPrice: '单价',
      orderAmount: '订单金额',
      orderStatus: '订单状态',
      selectOrders: '选择订单',
      selectedCount: '已选择 {count} 个订单',
      timeFilter: '时间筛选',
      thisWeek: '本周',
      thisMonth: '本月',
      thisQuarter: '本季度',
      thisYear: '本年',
      customRange: '自定义时间段',
      distributorFilter: '机构筛选',
      allDistributors: '全部机构',
      selectDistributor: '选择机构',
      distributorPlaceholder: '请选择机构',
      startDate: '开始日期',
      endDate: '结束日期',
      selectDateRange: '选择日期范围',
      resetToOrderDateRange: '重置为订单时间范围',
      dateRangeResetSuccess: '日期范围已重置为订单时间范围',
      // 统计概览
      totalOrders: '总订单数',
      totalQuantity: '总数量',
      distributors: '机构',
      games: '游戏',
      generateReportFromOrders: '从选中订单生成报表',
      noOrdersSelected: '请选择要生成报表的订单',
      pleaseSelectOrders: '请先选择订单',
      reportGenerated: '报表生成成功',
      reportGenerationFailed: '报表生成失败',
      reportUpdateSuccess: '财务报告更新成功',
      reportGenerateSuccess: '财务报告生成成功',
      reportOperationFailed: '报告操作失败',
      reportTitle: '报表标题',
      reportType: '报表类型',
      includeDetails: '包含详细信息',
      notes: '备注',
      viewOrderDetails: '查看订单详情',
      // 订单状态
      allStatus: '全部状态',
      orderStatusPlaceholder: '请选择订单状态',
      completed: '已完成',
      processing: '处理中',
      cancelled: '已取消',
      refunded: '已退款',
      // 国别和货币
      buyerCountry: '购买者国别',
      allCountries: '全部国家',
      countryPlaceholder: '请选择国家',
      currency: '货币代码',
      allCurrencies: '全部货币',
      currencyPlaceholder: '请选择货币',
      // 报表详情相关
      reportDetail: '报表详情',
      reportBasicInfo: '报表基本信息',
      reportName: '报表名称',
      reportPeriod: '报表周期',
      reportNotes: '备注信息',
      orderSummary: '订单汇总',
      gameSummary: '游戏汇总',
      orderDetails: '订单明细',
      productCode: '产品代码',
      productName: '产品名称',
      country: '国家',
      orderCount: '订单数量',
      quantitySold: '销售数量',
      backToReports: '返回报表列表',
      exportReport: '导出报表',
      reportSummaryStats: '报表统计概览',
      // 订单详情相关翻译
      orderDetail: '订单详情',
      orderBasicInfo: '订单基本信息',
      buyerInfo: '购买者信息',
      gameInfo: '游戏信息',
      priceInfo: '价格信息',
      paymentInfo: '支付信息',
      orderNotes: '订单备注',
      buyerEmail: '邮箱地址',
      buyerPhone: '联系电话',
      shippingAddress: '收货地址',
      paymentMethod: '支付方式',
      transactionId: '交易ID',
      gameVersion: '游戏版本',
      gameCategory: '游戏分类',
      createdAt: '创建时间',
      updatedAt: '更新时间',
      // 订单子项目相关
      orderItems: '订单项目',
      itemName: '项目名称',
      itemType: '类型',
      itemVersion: '版本',
      itemCategory: '分类',
      itemQuantity: '数量',
      itemOriginalPrice: '原价',
      itemDiscount: '折扣',
      itemFinalPrice: '成交价',
      itemTotalAmount: '小计',
      game: '游戏',
      dlc: 'DLC',
      expansion: '扩展包',
      // 统计栏相关
      statisticsSummary: '统计汇总',
      totalOrdersCount: '总订单数',
      totalAmountSum: '总金额',
      // 排序相关
      sortByDate: '按日期排序',
      sortByGame: '按游戏名称排序',
      sortByCountry: '按国别排序',
      ascending: '升序',
      descending: '降序',
      // 新增功能相关翻译
      addCorrectionRecord: '添加修正记录',
      addOrderRecord: '添加订单记录',
      orderList: '订单列表',
      recordsCount: '条记录',
      searchAndAddOrder: '搜索并添加订单',
      selectedCount: '已选择',
      orders: '条订单',
      foundOrders: '共找到',
      availableOrders: '条可用订单',
      addSelectedOrders: '添加选中订单',
      pleaseSelectOrdersToAdd: '请选择要添加的订单',
      orderAddedSuccess: '已添加',
      duplicateOrderWarning: '所选订单已存在，未添加重复记录',
      orderDeletedSuccess: '订单记录已删除',
      // 报表详情页面新增翻译
      selectedItem: '已选中项目',
      filteredOrders: '已筛选订单',
      allOrders: '全部订单'
    },
    admin: {
      title: '管理员功能',
      settings: '管理员设置',
      systemLogs: '系统日志',
      emailSettings: '邮件设置',
      reportSettings: '报表设置',
      notificationSettings: '通知设置',
      smtpConfig: 'SMTP配置',
      smtpHost: 'SMTP主机',
      smtpPort: 'SMTP端口',
      smtpUser: '用户名',
      smtpPassword: '密码',
      enableTLS: '启用TLS',
      emailTemplates: '邮件模板',
      inviteTemplate: '邀请邮件模板',
      activationTemplate: '激活邮件模板',
      testConnection: '测试连接',
      saveConfig: '保存配置',
      autoGenerate: '启用自动生成',
      generateDay: '生成日期',
      generateTime: '生成时间',
      autoAudit: '自动审核',
      autoPublish: '自动发布',
      notifyUsers: '通知用户',
      notificationChannels: '通知渠道',
      emailNotification: '邮件通知',
      smsNotification: '短信通知',
      pushNotification: '推送通知',
      notificationTypes: '通知类型',
      orderNotification: '订单通知',
      reportNotification: '报表通知',
      systemNotification: '系统通知',
      operationTime: '操作时间',
      operationUser: '操作用户',
      operationType: '操作类型',
      operationContent: '操作内容',
      ipAddress: 'IP地址',
      operationResult: '操作结果',
      success: '成功',
      failed: '失败',
      exportLogs: '导出日志',
      clearLogs: '清理日志',
      userManagement: '用户管理',
      organizationManagement: '机构管理',
      gameManagement: '游戏管理',
      login: '登录',
      systemSettings: '系统设置',
      timeRange: '时间范围',
      userPlaceholder: '请输入用户名',
      typePlaceholder: '请选择操作类型',
      resultPlaceholder: '请选择操作结果',
      monthlyDay: '每月第几天',
      // 游戏导入日志相关
      gameImportLogs: '游戏导入日志',
      importLogs: '导入日志',
      taskId: '任务ID',
      importTime: '导入时间',
      dataCount: '数据条数',
      importStatus: '导入状态',
      retry: '重试',
      pending: '待处理',
      processing: '处理中',
      completed: '已完成',
      failed: '失败',
      cancelled: '已取消',
      taskIdPlaceholder: '请输入任务ID',
      statusPlaceholder: '请选择导入状态',
      retryImport: '重试导入',
      retrySuccess: '重试任务已提交',
      retryFailed: '重试失败，请稍后再试',
      importDetails: '导入详情',
      totalItems: '总条数',
      successItems: '成功条数',
      failedItems: '失败条数',
      errorMessage: '错误信息',
      importSource: '导入来源',
      importType: '导入类型',
      gameImport: '游戏导入',
      variantImport: '变体导入',
      priceImport: '价格导入',
      manualImport: '手动导入',
      autoImport: '自动导入',
      scheduledImport: '定时导入',
      totalTasks: '总任务数',
      completedTasks: '已完成',
      failedTasks: '失败',
      successRate: '成功率'
    }
  },
  errorHandler: {
    // Common error messages
    operationFailed: '操作失败',
    networkError: '网络连接失败，请检查网络设置',
    validationFailed: '验证失败，共 {count} 个错误',

    // Field display names
    fieldNames: {
      // Organization related fields
      name: '机构名称',
      code: '机构代码',
      details: '机构详情',
      remarks: '备注',
      status: '状态',

      // User related fields
      email: '邮箱',
      password: '密码',
      password_confirmation: '确认密码',
      username: '用户名',
      user_name: '用户名',
      organisation_ids: '组织',
      role_name: '角色名称',
      organisation_id: '组织ID',
      verification_code: '验证码',

      // Invitation related fields
      role: '角色',
      model_type: '模型类型',
      model_id: '模型ID',
      expires_at: '过期时间',
      max_uses: '最大使用次数',
      email_restriction: '邮箱限制',

      // Common fields
      id: 'ID',
      created_at: '创建时间',
      updated_at: '更新时间'
    }
  },
  form: {
    required: '不能为空',
    userName: {
      required: '请输入用户名',
      invalid: '用户名格式不正确'
    },
    phone: {
      required: '请输入手机号',
      invalid: '手机号格式不正确'
    },
    pwd: {
      required: '请输入密码',
      invalid: '密码格式不正确，6-18位字符，包含字母、数字、下划线'
    },
    confirmPwd: {
      required: '请输入确认密码',
      invalid: '两次输入密码不一致'
    },
    code: {
      required: '请输入验证码',
      invalid: '验证码格式不正确'
    },
    email: {
      required: '请输入邮箱',
      invalid: '邮箱格式不正确'
    },
    userNameOrEmail: {
      required: '请输入用户名或邮箱',
      invalid: '请输入有效的用户名或邮箱地址'
    }
  },
  dropdown: {
    closeCurrent: '关闭',
    closeOther: '关闭其它',
    closeLeft: '关闭左侧',
    closeRight: '关闭右侧',
    closeAll: '关闭所有'
  },
  icon: {
    themeConfig: '主题配置',
    themeSchema: '主题模式',
    lang: '切换语言',
    fullscreen: '全屏',
    fullscreenExit: '退出全屏',
    reload: '刷新页面',
    collapse: '折叠菜单',
    expand: '展开菜单',
    pin: '固定',
    unpin: '取消固定'
  },
  datatable: {
    itemCount: '共 {total} 条'
  }
};

export default local;
