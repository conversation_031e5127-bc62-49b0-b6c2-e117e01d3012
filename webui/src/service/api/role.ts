import { request } from '../request';

/**
 * Get roles list
 * @param params Query parameters
 */
export function fetchRolesList(params?: {
  organisation_id?: number;
}) {
  return request<Api.ApiResponse<Api.Auth.Role[]>>({
    url: '/api/v1/roles',
    method: 'get',
    params
  });
}

/**
 * Create role
 * @param data Role data
 */
export function fetchCreateRole(data: {
  name: string;
  guard_name?: string;
  organisation_id?: number;
}) {
  return request<Api.ApiResponse<Api.Auth.Role>>({
    url: '/api/v1/roles',
    method: 'post',
    data
  });
}

/**
 * Get role details
 * @param id Role ID
 */
export function fetchRoleDetail(id: number) {
  return request<Api.ApiResponse<Api.Auth.Role>>({
    url: `/api/v1/roles/${id}`,
    method: 'get'
  });
}

/**
 * Update role
 * @param id Role ID
 * @param data Role data
 */
export function fetchUpdateRole(id: number, data: {
  name?: string;
  guard_name?: string;
}) {
  return request<Api.ApiResponse<Api.Auth.Role>>({
    url: `/api/v1/roles/${id}`,
    method: 'put',
    data
  });
}

/**
 * Delete role
 * @param id Role ID
 */
export function fetchDeleteRole(id: number) {
  return request<Api.ApiResponse<any>>({
    url: `/api/v1/roles/${id}`,
    method: 'delete'
  });
}
