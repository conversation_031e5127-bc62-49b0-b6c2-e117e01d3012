import { request } from '../request';

/**
 * Get organizations list
 * @param params Query parameters
 */
export function fetchOrganizations(params?: {
  per_page?: number;
  status?: 'pending' | 'active' | 'suspended';
  search?: string;
  page?: number;
  organization_ids?: string; // Comma-separated organization IDs
}) {
  return request<Api.ApiResponse<Api.Organization.OrganizationListResponse>>({
    url: '/api/v1/organisations',
    method: 'get',
    params
  });
}

/**
 * Get organization details
 * @param id Organization ID
 */
export function fetchOrganizationDetail(id: number) {
  return request<Api.ApiResponse<Api.Organization.Organization>>({
    url: `/api/v1/organisations/${id}`,
    method: 'get'
  });
}

/**
 * Create organization
 * @param data Organization data
 */
export function fetchCreateOrganization(data: Api.Organization.CreateOrganizationRequest) {
  return request<Api.ApiResponse<Api.Organization.Organization>>({
    url: '/api/v1/organisations',
    method: 'post',
    data
  });
}

/**
 * Update organization
 * @param id Organization ID
 * @param data Organization data
 */
export function fetchUpdateOrganization(id: number, data: Api.Organization.UpdateOrganizationRequest) {
  return request<Api.ApiResponse<Api.Organization.Organization>>({
    url: `/api/v1/organisations/${id}`,
    method: 'put',
    data
  });
}

/**
 * Suspend organization
 * @param id Organization ID
 */
export function fetchSuspendOrganization(id: number) {
  return request<Api.ApiResponse<Api.Organization.Organization>>({
    url: `/api/v1/organisations/${id}/suspend`,
    method: 'post'
  });
}

/**
 * Get organization users
 * @param organizationId Organization ID
 * @param params Query parameters
 */
export function fetchOrganizationUsers(organizationId: number, params?: {
  per_page?: number;
}) {
  return request<Api.ApiResponse<Api.Organization.OrganizationUsersResponse>>({
    url: '/api/v1/users',
    method: 'get',
    params: {
      ...params,
      organisation_id: organizationId
    }
  });
}

/**
 * Add user to organization
 * @param userId User ID
 * @param organizationId Organization ID
 */
export function fetchAddUserToOrganization(userId: number, organizationId: number) {
  return request<Api.ApiResponse<Api.Auth.ApiUserInfo>>({
    url: `/api/v1/users/${userId}/organisations/${organizationId}`,
    method: 'post'
  });
}

/**
 * Remove user from organization
 * @param userId User ID
 * @param organizationId Organization ID
 */
export function fetchRemoveUserFromOrganization(userId: number, organizationId: number) {
  return request<Api.ApiResponse<Api.Auth.ApiUserInfo>>({
    url: `/api/v1/users/${userId}/organisations/${organizationId}`,
    method: 'delete'
  });
}

/**
 * Get invitations list
 * @param params Query parameters
 */
export function fetchInvitations(params?: {
  per_page?: number;
  model_type?: string;
  model_id?: number;
  role?: string;
  status?: string;
}) {
  return request<Api.ApiResponse<Api.Organization.InvitationListResponse>>({
    url: '/api/v1/invitations',
    method: 'get',
    params
  });
}

/**
 * Create invitation
 * @param data Invitation data
 */
export function fetchCreateInvitation(data: Api.Organization.CreateInvitationRequest) {
  return request<Api.ApiResponse<Api.Organization.Invitation>>({
    url: '/api/v1/invitations',
    method: 'post',
    data
  });
}

/**
 * Get invitation details
 * @param id Invitation ID
 */
export function fetchInvitationDetail(id: string) {
  return request<Api.ApiResponse<Api.Organization.Invitation>>({
    url: `/api/v1/invitations/${id}`,
    method: 'get'
  });
}

/**
 * Accept invitation
 * @param id Invitation ID
 */
export function fetchAcceptInvitation(id: string) {
  return request<Api.ApiResponse<Api.Organization.AcceptInvitationResponse>>({
    url: `/api/v1/invitations/${id}/accept`,
    method: 'post'
  });
}

// User-related APIs have been moved to user.ts
