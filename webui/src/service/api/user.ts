import { request } from '../request';

/**
 * Get users list
 * @param params Query parameters
 */
export function fetchUsersList(params?: {
  per_page?: number;
  organisation_id?: number;
  organisation_ids?: string; // Comma-separated organisation IDs
  status?: string;
  name?: string;
  email?: string;
  only_owner?: boolean; // If true, only return users from organizations where current user is owner
}) {
  return request<Api.ApiResponse<Api.Organization.UserListResponse>>({
    url: '/api/v1/users',
    method: 'get',
    params
  });
}



/**
 * Create user
 * @param data User data
 */
export function fetchCreateUser(data: Api.Organization.CreateUserRequest) {
  return request<Api.ApiResponse<Api.Auth.ApiUserInfo>>({
    url: '/api/v1/users',
    method: 'post',
    data
  });
}

/**
 * Get user details
 * @param id User ID
 */
export function fetchUserDetail(id: number) {
  return request<Api.ApiResponse<Api.Auth.ApiUserInfo>>({
    url: `/api/v1/users/${id}`,
    method: 'get'
  });
}

/**
 * Update user
 * @param id User ID
 * @param data Update data
 */
export function fetchUpdateUser(id: number, data: Api.Organization.UpdateUserRequest) {
  return request<Api.ApiResponse<Api.Auth.ApiUserInfo>>({
    url: `/api/v1/users/${id}`,
    method: 'put',
    data
  });
}

/**
 * Suspend user
 * @param id User ID
 */
export function fetchSuspendUser(id: number) {
  return request<Api.ApiResponse<Api.Auth.ApiUserInfo>>({
    url: `/api/v1/users/${id}/suspend`,
    method: 'post'
  });
}

/**
 * Activate user
 * @param id User ID
 */
export function fetchActivateUser(id: number) {
  return request<Api.ApiResponse<Api.Auth.ApiUserInfo>>({
    url: `/api/v1/users/${id}/activate`,
    method: 'post'
  });
}

/**
 * Sync user organizations
 * @param userId User ID
 * @param data Organization IDs
 */
export function fetchSyncUserOrganizations(userId: number, data: { organisation_ids: number[] }) {
  return request<Api.ApiResponse<Api.Auth.ApiUserInfo>>({
    url: `/api/v1/users/${userId}/organisations`,
    method: 'put',
    data
  });
}



import { cacheManager } from '@/utils/cache-manager';

// Cache configuration for assignable roles
const ASSIGNABLE_ROLES_CACHE_KEY = 'assignable_roles_cache';
const ASSIGNABLE_ROLES_CACHE_DURATION = 30 * 60 * 1000; // 30 minutes

// In-flight request promise to prevent concurrent requests
let assignableRolesPromise: Promise<any> | null = null;

/**
 * Get assignable roles (with 30-minute persistent cache and concurrent request prevention)
 */
export async function fetchAssignableRoles() {
  // Check if we have a valid cached response
  const cached = cacheManager.getCache(ASSIGNABLE_ROLES_CACHE_KEY, ASSIGNABLE_ROLES_CACHE_DURATION);
  if (cached) {
    return Promise.resolve(cached.data);
  }

  // Check if there's already a request in flight
  if (assignableRolesPromise) {
    console.log('🔄 [Cache] Waiting for in-flight assignable roles request');
    return assignableRolesPromise;
  }

  // Make API request and cache the result
  console.log('🌐 [Cache] Fetching assignable roles from API');
  assignableRolesPromise = request<Api.ApiResponse<{
    roles: Record<string, string[]>;
    details: Api.Auth.Role[];
  }>>({
    url: '/api/v1/users/assignable-roles',
    method: 'get'
  }).then(response => {
    // Cache the response (user-specific cache)
    cacheManager.setCache(ASSIGNABLE_ROLES_CACHE_KEY, response);
    return response;
  }).finally(() => {
    // Clear the in-flight promise
    assignableRolesPromise = null;
  });

  return assignableRolesPromise;
}

/**
 * Clear assignable roles cache (useful for manual cache invalidation)
 */
export function clearAssignableRolesCache() {
  cacheManager.removeCache(ASSIGNABLE_ROLES_CACHE_KEY);
  assignableRolesPromise = null;
}

/**
 * Assign role to user
 * @param userId User ID
 * @param data Role assignment data
 */
export function fetchAssignUserRole(userId: number, data: {
  role_name: string;
  organisation_id?: number;
}) {
  return request<Api.ApiResponse<{
    user_id: number;
    role_name: string;
    organisation_id?: number;
  }>>({
    url: `/api/v1/users/${userId}/roles`,
    method: 'post',
    data
  });
}

/**
 * Remove user role
 * @param userId User ID
 * @param roleId Role ID
 */
export function fetchRemoveUserRole(userId: number, roleId: number) {
  return request<Api.ApiResponse<{
    user_id: number;
    role_id: number;
    role_name: string;
  }>>({
    url: `/api/v1/users/${userId}/roles/${roleId}`,
    method: 'delete'
  });
}

/**
 * Get user roles
 * @param userId User ID
 */
export function fetchUserRoles(userId: number) {
  return request<Api.ApiResponse<{
    user_id: number;
    user_name: string;
    user_email: string;
    organisations: Api.Auth.Organisation[];
    roles: Api.Auth.UserRoles;
  }>>({
    url: `/api/v1/users/${userId}/roles`,
    method: 'get'
  });
}

/**
 * Transfer owner role
 * @param userId User ID
 * @param data Transfer data
 */
export function fetchTransferOwnerRole(userId: number, data: { organisation_id: number }) {
  return request<Api.ApiResponse<{
    new_owner: {
      id: number;
      name: string;
      email: string;
    };
    organisation: {
      id: number;
      name: string;
      code: string;
    };
    previous_owner: {
      id: number;
      name: string;
      email: string;
    };
  }>>({
    url: `/api/v1/users/${userId}/transfer-owner`,
    method: 'put',
    data
  });
}

/**
 * Send verification code
 * @param email Email address
 */
export function fetchSendVerificationCode(email: string) {
  return request<Api.ApiResponse<{
    message: string;
    expires_in_seconds: number;
  }>>({
    url: '/api/v1/users/send-verification-code',
    method: 'post',
    data: { email }
  });
}

/**
 * Register user (guest registration)
 * @param data Registration data
 */
export function fetchRegisterUser(data: {
  name: string;
  email: string;
  password: string;
  password_confirmation: string;
  verification_code: string;
  organisation_ids?: number[];
}) {
  return request<Api.ApiResponse<Api.Auth.ApiUserInfo>>({
    url: '/api/v1/users/register',
    method: 'post',
    data
  });
}
