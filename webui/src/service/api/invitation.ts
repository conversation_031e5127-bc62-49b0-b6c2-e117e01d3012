import { request } from '../request';

/**
 * Get invitation information by ID
 * @param invitationId Invitation ID
 */
export function fetchInvitationInfo(invitationId: string) {
  return request<Api.ApiResponse<Api.Organization.InvitationInfo>>({
    url: `/api/v1/invitations/${invitationId}`,
    method: 'get'
  });
}

/**
 * Accept invitation
 * @param invitationId Invitation ID
 */
export function fetchAcceptInvitation(invitationId: string) {
  return request<Api.ApiResponse<Api.Organization.AcceptInvitationResponse>>({
    url: `/api/v1/invitations/${invitationId}/accept`,
    method: 'post'
  });
}

