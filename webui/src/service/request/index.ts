import { BACKEND_ERROR_CODE, createFlatRequest, createRequest } from '@sa/axios';
import { useAuthStore } from '@/store/modules/auth';
import { localStg } from '@/utils/storage';
import { getServiceBaseURL } from '@/utils/service';
import { handleApiError } from '@/utils/error-handler';
import { $t } from '@/locales';
import { getAuthorization, showErrorMsg } from './shared';
import type { RequestInstanceState } from './type';

const isHttpProxy = import.meta.env.DEV && import.meta.env.VITE_HTTP_PROXY === 'Y';
const { baseURL, otherBaseURL } = getServiceBaseURL(import.meta.env, isHttpProxy);

/**
 * Get locale header value for API requests
 * Maps frontend locale to backend locale format
 */
function getLocaleHeader(): string {
  const currentLocale = localStg.get('lang') || 'zh-CN';

  // Map frontend locale to backend locale
  const localeMap: Record<string, string> = {
    'zh-CN': 'zh',
    'en-US': 'en'
  };

  return localeMap[currentLocale] || 'en';
}

export const request = createFlatRequest<App.Service.Response, RequestInstanceState>(
  {
    baseURL,
    headers: {
      apifoxToken: 'XL299LiMEDZ0H5h3A29PxwQXdMJqWyY2'
    }
  },
  {
    async onRequest(config) {
      const Authorization = getAuthorization();
      const locale = getLocaleHeader();

      Object.assign(config.headers, {
        Authorization,
        'X-Locale': locale
      });

      return config;
    },
    isBackendSuccess(response) {
      // New API uses 'success' field to indicate success/failure
      // Fallback to legacy code check for backward compatibility
      if (response.data.success !== undefined) {
        return response.data.success === true;
      }
      // Legacy check for old API format
      return String(response.data.code) === import.meta.env.VITE_SERVICE_SUCCESS_CODE;
    },
    async onBackendFail(response, instance) {
      const authStore = useAuthStore();

      // Handle new API format (success: false) and legacy format (code-based)
      const responseCode = response.data.code ? String(response.data.code) : '';
      const isNewApiFormat = response.data.success !== undefined;

      function handleLogout() {
        authStore.resetStore();
      }

      function logoutAndCleanup() {
        handleLogout();
        window.removeEventListener('beforeunload', handleLogout);

        const errorMsg = response.data.message || response.data.msg || '';
        request.state.errMsgStack = request.state.errMsgStack.filter(msg => msg !== errorMsg);
      }

      // Handle HTTP 401 Unauthorized for new API
      if (isNewApiFormat && response.status === 401) {
        handleLogout();
        return null;
      }

      // Legacy: when the backend response code is in `logoutCodes`, it means the user will be logged out and redirected to login page
      const logoutCodes = import.meta.env.VITE_SERVICE_LOGOUT_CODES?.split(',') || [];
      if (responseCode && logoutCodes.includes(responseCode)) {
        handleLogout();
        return null;
      }

      // Handle HTTP 403 Forbidden for new API (modal logout)
      if (isNewApiFormat && response.status === 403) {
        const errorMsg = response.data.message || 'Access denied';
        if (!request.state.errMsgStack?.includes(errorMsg)) {
          request.state.errMsgStack = [...(request.state.errMsgStack || []), errorMsg];

          // prevent the user from refreshing the page
          window.addEventListener('beforeunload', handleLogout);

          window.$dialog?.error({
            title: $t('common.error'),
            content: errorMsg,
            positiveText: $t('common.confirm'),
            maskClosable: false,
            closeOnEsc: false,
            onPositiveClick() {
              logoutAndCleanup();
            },
            onClose() {
              logoutAndCleanup();
            }
          });

          return null;
        }
      }

      // Legacy: when the backend response code is in `modalLogoutCodes`, it means the user will be logged out by displaying a modal
      const modalLogoutCodes = import.meta.env.VITE_SERVICE_MODAL_LOGOUT_CODES?.split(',') || [];
      if (responseCode && modalLogoutCodes.includes(responseCode) && !request.state.errMsgStack?.includes(response.data.msg)) {
        request.state.errMsgStack = [...(request.state.errMsgStack || []), response.data.msg];

        // prevent the user from refreshing the page
        window.addEventListener('beforeunload', handleLogout);

        window.$dialog?.error({
          title: $t('common.error'),
          content: response.data.msg,
          positiveText: $t('common.confirm'),
          maskClosable: false,
          closeOnEsc: false,
          onPositiveClick() {
            logoutAndCleanup();
          },
          onClose() {
            logoutAndCleanup();
          }
        });

        return null;
      }

      // New API: tokens don't expire, but handle legacy expired token codes for backward compatibility
      const expiredTokenCodes = import.meta.env.VITE_SERVICE_EXPIRED_TOKEN_CODES?.split(',') || [];
      if (responseCode && expiredTokenCodes.includes(responseCode)) {
        // Since new API doesn't support token refresh, just logout
        handleLogout();
        return null;
      }

      return null;
    },
    transformBackendResponse(response) {
      // New API format: return the entire response for API.ApiResponse<T> handling
      // Legacy format: return response.data.data
      if (response.data.success !== undefined) {
        return response.data; // Return full API response for new format
      }
      return response.data.data; // Legacy format
    },
    onError(error) {
      // when the request is fail, you can show error message

      let message = error.message;
      let backendErrorCode = '';

      // get backend error message and code
      if (error.code === BACKEND_ERROR_CODE) {
        const responseData = error.response?.data;
        // Handle new API format (message field) and legacy format (msg field)
        message = responseData?.message || responseData?.msg || message;
        backendErrorCode = String(responseData?.code || '');
      }

      // Handle HTTP status-based errors for new API
      if (error.response?.status === 401 || error.response?.status === 403) {
        return; // These are handled in onBackendFail
      }

      // Handle validation errors (422)
      if (error.response?.status === 422) {
        if (window.$message) {
          handleApiError(error, window.$message);
        }
        return;
      }
      // the error message is displayed in the modal
      const modalLogoutCodes = import.meta.env.VITE_SERVICE_MODAL_LOGOUT_CODES?.split(',') || [];
      if (modalLogoutCodes.includes(backendErrorCode)) {
        return;
      }

      // when the token is expired, refresh token and retry request, so no need to show error message
      const expiredTokenCodes = import.meta.env.VITE_SERVICE_EXPIRED_TOKEN_CODES?.split(',') || [];
      if (expiredTokenCodes.includes(backendErrorCode)) {
        return;
      }

      showErrorMsg(request.state, message);
    }
  }
);

export const demoRequest = createRequest<App.Service.DemoResponse>(
  {
    baseURL: otherBaseURL.demo
  },
  {
    async onRequest(config) {
      const { headers } = config;

      // set token
      const token = localStg.get('token');
      const Authorization = token ? `Bearer ${token}` : null;
      const locale = getLocaleHeader();

      Object.assign(headers, {
        Authorization,
        'X-Locale': locale
      });

      return config;
    },
    isBackendSuccess(response) {
      // when the backend response code is "200", it means the request is success
      // you can change this logic by yourself
      return response.data.status === '200';
    },
    async onBackendFail(_response) {
      // when the backend response code is not "200", it means the request is fail
      // for example: the token is expired, refresh token and retry request
    },
    transformBackendResponse(response) {
      return response.data.result;
    },
    onError(error) {
      // when the request is fail, you can show error message

      let message = error.message;

      // show backend error message
      if (error.code === BACKEND_ERROR_CODE) {
        message = error.response?.data?.message || message;
      }

      window.$message?.error(message);
    }
  }
);
