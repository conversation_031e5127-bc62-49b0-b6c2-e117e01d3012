import type { MessageApi } from 'naive-ui';
import { $t } from '@/locales';

/**
 * Backend API error response interface
 */
interface ApiErrorResponse {
  success: false;
  message: string;
  errors?: Record<string, string[]> | Array<Array<{message: string; field: string; fieldValue: any}>>;
  timestamp: string;
  error_code: string;
}

/**
 * Handle backend validation errors and display detailed error messages
 * @param errorResponse Backend error response
 * @param message Naive UI message instance
 * @param fallbackMessage Default error message
 */
export function handleValidationErrors(
  errorResponse: ApiErrorResponse,
  message: MessageApi,
  fallbackMessage = $t('errorHandler.operationFailed')
) {
  // If there are specific field validation errors
  if (errorResponse.errors && Object.keys(errorResponse.errors).length > 0) {
    const errorMessages: string[] = [];

    // Collect all field error messages
    Object.entries(errorResponse.errors).forEach(([field, messages]) => {
      if (Array.isArray(messages) && messages.length > 0) {
        // Convert field names to user-friendly display names
        const fieldDisplayName = getFieldDisplayName(field);
        messages.forEach(msg => {
          errorMessages.push(`${fieldDisplayName}: ${msg}`);
        });
      } else if (typeof messages === 'string') {
        // Handle single string error message case
        const fieldDisplayName = getFieldDisplayName(field);
        errorMessages.push(`${fieldDisplayName}: ${messages}`);
      }
    });

    if (errorMessages.length > 0) {
      // If there are few error messages, display them directly
      if (errorMessages.length <= 3) {
        errorMessages.forEach(msg => {
          message.error(msg);
        });
      } else {
        // If there are many error messages, display summary information
        message.error($t('errorHandler.validationFailed', { count: errorMessages.length }));
        // Consider outputting detailed information to console
        console.error('Validation errors:', errorMessages);
      }
      return;
    }
  }

  // If there are no specific field errors, display general error message
  const displayMessage = errorResponse.message || fallbackMessage;
  message.error(displayMessage);
}

/**
 * Convert backend field names to user-friendly display names
 * @param field Backend field name
 * @returns Display name
 */
function getFieldDisplayName(field: string): string {
  // Try to get the field name from i18n first
  const i18nKey = `errorHandler.fieldNames.${field}` as App.I18n.I18nKey;

  // Check if the translation key exists by comparing with the key itself
  const translatedName = $t(i18nKey);
  if (translatedName !== i18nKey) {
    return translatedName;
  }

  // If no translation found, return the original field name
  return field;
}

/**
 * Handle general API errors
 * @param error Error object
 * @param message Naive UI message instance
 * @param fallbackMessage Default error message
 */
export function handleApiError(
  error: any,
  message: MessageApi,
  fallbackMessage = $t('errorHandler.operationFailed')
) {
  console.error('API Error:', error);

  // If it's a network error
  if (!error.response) {
    message.error($t('errorHandler.networkError'));
    return;
  }

  // If it's an error returned by the backend
  const errorData = error.response?.data;
  if (errorData && !errorData.success) {
    // If it's a validation error (determined by status code or error code)
    if (error.response?.status === 422 || errorData.error_code === 'VALIDATION_ERROR') {
      handleValidationErrors(errorData, message, fallbackMessage);
      return;
    }

    // Other types of errors
    const displayMessage = errorData.message || fallbackMessage;
    message.error(displayMessage);
    return;
  }

  // Unknown error
  message.error(fallbackMessage);
}

/**
 * Extract error message from API response
 * @param response API response
 * @returns Error message string
 */
export function extractErrorMessage(response: any): string {
  if (response?.data?.message) {
    return response.data.message;
  }

  if (response?.data?.errors) {
    const errors = response.data.errors;
    const firstError = Object.values(errors)[0];
    if (Array.isArray(firstError) && firstError.length > 0) {
      return firstError[0] as string;
    }
  }

  return $t('errorHandler.operationFailed');
}

/**
 * Check if response is a validation error
 * @param response API response
 * @returns Whether it's a validation error
 */
export function isValidationError(response: any): boolean {
  return response?.data?.error_code === 'VALIDATION_ERROR';
}
