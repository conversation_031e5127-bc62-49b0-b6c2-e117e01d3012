/**
 * Global Cache Manager
 * Manages all application caches with user-specific and global cache support
 */

// Cache key prefixes for different types of caches
const CACHE_PREFIXES = {
  USER_SPECIFIC: 'user_cache_',
  GLOBAL: 'global_cache_',
  API: 'api_cache_'
} as const;

// Known cache keys that should be cleared on logout
const USER_SPECIFIC_CACHE_KEYS = [
  'assignable_roles_cache',
  'user_profile_cache',
  'user_permissions_cache'
] as const;

// Global cache keys that persist across user sessions
const GLOBAL_CACHE_KEYS = [
  'app_config_cache',
  'system_settings_cache'
] as const;

export interface CacheItem {
  data: any;
  timestamp: number;
  userId?: string; // Optional user ID for user-specific caches
}

export class CacheManager {
  private static instance: CacheManager;

  private constructor() {}

  public static getInstance(): CacheManager {
    if (!CacheManager.instance) {
      CacheManager.instance = new CacheManager();
    }
    return CacheManager.instance;
  }

  /**
   * Get cache item with automatic expiration check
   */
  public getCache(key: string, maxAge: number = 30 * 60 * 1000): CacheItem | null {
    try {
      const cached = localStorage.getItem(key);
      if (cached) {
        const parsed: CacheItem = JSON.parse(cached);
        const now = Date.now();

        // Check if cache is still valid
        if (parsed.timestamp && (now - parsed.timestamp) < maxAge) {
          const ageInSeconds = Math.round((now - parsed.timestamp) / 1000);
          console.log(`✅ [Cache Hit] ${key} (age: ${ageInSeconds}s)`);
          return parsed;
        } else {
          // Cache expired, remove it
          this.removeCache(key);
          console.log(`⏰ [Cache Expired] ${key} removed`);
        }
      }
    } catch (error) {
      console.warn(`Failed to read cache ${key}:`, error);
      this.removeCache(key);
    }
    return null;
  }

  /**
   * Set cache item with optional user ID
   */
  public setCache(key: string, data: any, userId?: string): void {
    try {
      const cacheItem: CacheItem = {
        data,
        timestamp: Date.now(),
        userId
      };
      localStorage.setItem(key, JSON.stringify(cacheItem));
      console.log(`💾 [Cache Set] ${key}${userId ? ` (user: ${userId})` : ''}`);
    } catch (error) {
      console.warn(`Failed to save cache ${key}:`, error);
    }
  }

  /**
   * Remove specific cache item
   */
  public removeCache(key: string): void {
    try {
      localStorage.removeItem(key);
      console.log(`🗑️ [Cache Removed] ${key}`);
    } catch (error) {
      console.warn(`Failed to remove cache ${key}:`, error);
    }
  }

  /**
   * Clear all user-specific caches (called on logout)
   */
  public clearUserCaches(userId?: string): void {
    console.log(`🧹 [Cache Clear] Clearing user-specific caches${userId ? ` for user: ${userId}` : ''}`);

    try {
      // Clear known user-specific cache keys
      USER_SPECIFIC_CACHE_KEYS.forEach(key => {
        this.removeCache(key);
      });

      // Clear any cache items that have the current user ID
      if (userId) {
        const allKeys = Object.keys(localStorage);
        allKeys.forEach(key => {
          try {
            const cached = localStorage.getItem(key);
            if (cached) {
              const parsed: CacheItem = JSON.parse(cached);
              if (parsed.userId === userId) {
                this.removeCache(key);
              }
            }
          } catch (error) {
            // Ignore parsing errors for non-cache items
          }
        });
      }

      console.log('✅ [Cache Clear] User-specific caches cleared');
    } catch (error) {
      console.error('Failed to clear user caches:', error);
    }
  }

  /**
   * Clear all caches (nuclear option)
   */
  public clearAllCaches(): void {
    console.log('💥 [Cache Clear] Clearing ALL caches');

    try {
      // Get all localStorage keys
      const allKeys = Object.keys(localStorage);

      // Remove cache-related keys (keep non-cache items like tokens, settings)
      allKeys.forEach(key => {
        if (key.includes('cache') || key.endsWith('_cache')) {
          this.removeCache(key);
        }
      });

      console.log('✅ [Cache Clear] All caches cleared');
    } catch (error) {
      console.error('Failed to clear all caches:', error);
    }
  }

  /**
   * Get cache statistics for debugging
   */
  public getCacheStats(): { key: string; size: number; age: number; userId?: string }[] {
    const stats: { key: string; size: number; age: number; userId?: string }[] = [];

    try {
      const allKeys = Object.keys(localStorage);
      const now = Date.now();

      allKeys.forEach(key => {
        if (key.includes('cache') || key.endsWith('_cache')) {
          try {
            const cached = localStorage.getItem(key);
            if (cached) {
              const parsed: CacheItem = JSON.parse(cached);
              const size = new Blob([cached]).size;
              const age = Math.round((now - parsed.timestamp) / 1000);

              stats.push({
                key,
                size,
                age,
                userId: parsed.userId
              });
            }
          } catch (error) {
            // Ignore parsing errors
          }
        }
      });
    } catch (error) {
      console.error('Failed to get cache stats:', error);
    }

    return stats.sort((a, b) => b.size - a.size); // Sort by size descending
  }

  /**
   * Log cache statistics to console
   */
  public logCacheStats(): void {
    const stats = this.getCacheStats();
    console.log('📊 [Cache Stats]');
    console.table(stats);

    const totalSize = stats.reduce((sum, stat) => sum + stat.size, 0);
    console.log(`Total cache size: ${(totalSize / 1024).toFixed(2)} KB`);
  }
}

// Export singleton instance
export const cacheManager = CacheManager.getInstance();

// Export convenience functions
export const clearUserCaches = (userId?: string) => cacheManager.clearUserCaches(userId);
export const clearAllCaches = () => cacheManager.clearAllCaches();
export const getCacheStats = () => cacheManager.getCacheStats();
export const logCacheStats = () => cacheManager.logCacheStats();

// Development helper: expose cache manager to window for debugging
if (import.meta.env.DEV) {
  (window as any).__cacheManager = {
    stats: () => cacheManager.logCacheStats(),
    clear: () => cacheManager.clearAllCaches(),
    clearUser: (userId?: string) => cacheManager.clearUserCaches(userId),
    get: (key: string) => cacheManager.getCache(key),
    set: (key: string, data: any, userId?: string) => cacheManager.setCache(key, data, userId),
    remove: (key: string) => cacheManager.removeCache(key)
  };

  console.log('🔧 [Dev] Cache manager available at window.__cacheManager');
  console.log('Available commands:');
  console.log('  __cacheManager.stats() - Show cache statistics');
  console.log('  __cacheManager.clear() - Clear all caches');
  console.log('  __cacheManager.clearUser(userId) - Clear user-specific caches');
}
