import { createLocalforage, createStorage } from '@sa/utils';
import { safeStorage } from './storage-helper';

const storagePrefix = import.meta.env.VITE_STORAGE_PREFIX || '';

// Create a wrapper for localStorage that falls back to memory storage in private mode
const createSafeStorage = <T extends StorageType.Local | StorageType.Session>(
  type: T,
  prefix: string
) => {
  const originalStorage = createStorage<T>(type, prefix);

  return {
    set: (key: string, value: any) => {
      try {
        originalStorage.set(key, value);
      } catch (e) {
        // Fallback to safe storage if original fails
        safeStorage.set(`${prefix}${key}`, value);
      }
    },
    get: (key: string) => {
      try {
        const value = originalStorage.get(key);
        if (value !== null) return value;
      } catch (e) {
        // Try to get from safe storage
      }
      return safeStorage.get(`${prefix}${key}`);
    },
    remove: (key: string) => {
      try {
        originalStorage.remove(key);
      } catch (e) {
        // Also remove from safe storage
      }
      safeStorage.remove(`${prefix}${key}`);
    },
    clear: () => {
      try {
        originalStorage.clear();
      } catch (e) {
        // Clear safe storage as well
      }
      safeStorage.clear();
    }
  };
};

export const localStg = createSafeStorage<StorageType.Local>('local', storagePrefix);

export const sessionStg = createSafeStorage<StorageType.Session>('session', storagePrefix);

export const localforage = createLocalforage<StorageType.Local>('local');
