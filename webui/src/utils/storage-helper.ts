/**
 * Storage helper to handle private/incognito mode gracefully
 */

interface MemoryStorage {
  [key: string]: any;
}

// In-memory storage fallback for private mode
const memoryStorage: MemoryStorage = {};

/**
 * Check if localStorage is available and working
 */
export function isLocalStorageAvailable(): boolean {
  try {
    const testKey = '__localStorage_test__';
    localStorage.setItem(testKey, 'test');
    localStorage.removeItem(testKey);
    return true;
  } catch (e) {
    return false;
  }
}

/**
 * Safe storage wrapper that falls back to memory storage in private mode
 */
export class SafeStorage {
  private useMemory: boolean;

  constructor() {
    this.useMemory = !isLocalStorageAvailable();
  }

  set(key: string, value: any): void {
    if (this.useMemory) {
      memoryStorage[key] = value;
    } else {
      try {
        localStorage.setItem(key, JSON.stringify(value));
      } catch (e) {
        // Fallback to memory if localStorage fails
        console.warn('localStorage failed, using memory storage:', e);
        memoryStorage[key] = value;
        this.useMemory = true;
      }
    }
  }

  get(key: string): any {
    if (this.useMemory) {
      return memoryStorage[key];
    }

    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : null;
    } catch (e) {
      // Fallback to memory if localStorage fails
      return memoryStorage[key] || null;
    }
  }

  remove(key: string): void {
    if (this.useMemory) {
      delete memoryStorage[key];
    } else {
      try {
        localStorage.removeItem(key);
      } catch (e) {
        delete memoryStorage[key];
      }
    }
  }

  clear(): void {
    if (this.useMemory) {
      Object.keys(memoryStorage).forEach(key => delete memoryStorage[key]);
    } else {
      try {
        localStorage.clear();
      } catch (e) {
        Object.keys(memoryStorage).forEach(key => delete memoryStorage[key]);
      }
    }
  }
}

// Export a singleton instance
export const safeStorage = new SafeStorage();
