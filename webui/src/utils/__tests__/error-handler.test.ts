import { describe, it, expect, vi, beforeEach } from 'vitest';
import type { MessageApi } from 'naive-ui';
import { handleValidationErrors, handleApiError, extractErrorMessage } from '../error-handler';

// Mock the i18n module
vi.mock('@/locales', () => ({
  $t: vi.fn((key: string, params?: any) => {
    const translations: Record<string, string> = {
      'errorHandler.operationFailed': 'Operation failed',
      'errorHandler.networkError': 'Network connection failed, please check network settings',
      'errorHandler.validationFailed': `Validation failed, ${params?.count || 0} errors found`,
      'errorHandler.fieldNames.email': 'Email',
      'errorHandler.fieldNames.password': 'Password',
      'errorHandler.fieldNames.name': 'Organization Name',
      'errorHandler.fieldNames.code': 'Organization Code'
    };
    
    return translations[key] || key;
  })
}));

describe('error-handler', () => {
  let mockMessage: MessageApi;

  beforeEach(() => {
    mockMessage = {
      error: vi.fn(),
      success: vi.fn(),
      warning: vi.fn(),
      info: vi.fn(),
      loading: vi.fn(),
      create: vi.fn(),
      destroyAll: vi.fn()
    } as any;
  });

  describe('handleValidationErrors', () => {
    it('should handle validation errors with field names translation', () => {
      const errorResponse = {
        success: false as const,
        message: 'Validation failed',
        errors: {
          email: ['Email is required'],
          password: ['Password must be at least 8 characters']
        },
        timestamp: '2023-01-01T00:00:00Z',
        error_code: 'VALIDATION_ERROR'
      };

      handleValidationErrors(errorResponse, mockMessage);

      expect(mockMessage.error).toHaveBeenCalledTimes(2);
      expect(mockMessage.error).toHaveBeenCalledWith('Email: Email is required');
      expect(mockMessage.error).toHaveBeenCalledWith('Password: Password must be at least 8 characters');
    });

    it('should handle multiple validation errors with summary message', () => {
      const errorResponse = {
        success: false as const,
        message: 'Validation failed',
        errors: {
          field1: ['Error 1'],
          field2: ['Error 2'],
          field3: ['Error 3'],
          field4: ['Error 4'],
          field5: ['Error 5']
        },
        timestamp: '2023-01-01T00:00:00Z',
        error_code: 'VALIDATION_ERROR'
      };

      handleValidationErrors(errorResponse, mockMessage);

      expect(mockMessage.error).toHaveBeenCalledTimes(1);
      expect(mockMessage.error).toHaveBeenCalledWith('Validation failed, 5 errors found');
    });

    it('should use fallback message when no specific errors', () => {
      const errorResponse = {
        success: false as const,
        message: 'Custom error message',
        timestamp: '2023-01-01T00:00:00Z',
        error_code: 'VALIDATION_ERROR'
      };

      handleValidationErrors(errorResponse, mockMessage);

      expect(mockMessage.error).toHaveBeenCalledWith('Custom error message');
    });
  });

  describe('handleApiError', () => {
    it('should handle network errors', () => {
      const error = {
        message: 'Network Error'
      };

      handleApiError(error, mockMessage);

      expect(mockMessage.error).toHaveBeenCalledWith('Network connection failed, please check network settings');
    });

    it('should handle validation errors from API', () => {
      const error = {
        response: {
          status: 422,
          data: {
            success: false,
            message: 'Validation failed',
            errors: {
              email: ['Email is required']
            },
            timestamp: '2023-01-01T00:00:00Z',
            error_code: 'VALIDATION_ERROR'
          }
        }
      };

      handleApiError(error, mockMessage);

      expect(mockMessage.error).toHaveBeenCalledWith('Email: Email is required');
    });

    it('should handle general API errors', () => {
      const error = {
        response: {
          status: 500,
          data: {
            success: false,
            message: 'Internal server error',
            timestamp: '2023-01-01T00:00:00Z',
            error_code: 'INTERNAL_ERROR'
          }
        }
      };

      handleApiError(error, mockMessage);

      expect(mockMessage.error).toHaveBeenCalledWith('Internal server error');
    });
  });

  describe('extractErrorMessage', () => {
    it('should extract message from response data', () => {
      const response = {
        data: {
          message: 'Custom error message'
        }
      };

      const result = extractErrorMessage(response);

      expect(result).toBe('Custom error message');
    });

    it('should extract first error from errors array', () => {
      const response = {
        data: {
          errors: {
            email: ['Email is required', 'Email format is invalid']
          }
        }
      };

      const result = extractErrorMessage(response);

      expect(result).toBe('Email is required');
    });

    it('should return fallback message when no specific error found', () => {
      const response = {};

      const result = extractErrorMessage(response);

      expect(result).toBe('Operation failed');
    });
  });
});
