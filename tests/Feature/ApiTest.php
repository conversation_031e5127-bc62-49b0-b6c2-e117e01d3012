<?php

declare(strict_types=1);

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

/**
 * API Feature Tests
 * 
 * Tests for API endpoints and functionality
 */
final class ApiTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test health check endpoint
     */
    public function test_health_endpoint_returns_success(): void
    {
        $response = $this->getJson('/api/health');

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'status',
                     'checks' => [
                         'database' => ['status', 'message'],
                         'cache' => ['status', 'message'],
                         'storage' => ['status', 'message'],
                     ],
                     'timestamp',
                 ]);
    }

    /**
     * Test API status endpoint
     */
    public function test_status_endpoint_returns_success(): void
    {
        $response = $this->getJson('/api/v1/status');

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'success',
                     'message',
                     'data' => [
                         'service',
                         'version',
                         'environment',
                         'debug',
                         'timezone',
                         'locale',
                         'uptime',
                         'memory_usage',
                     ],
                     'timestamp',
                 ])
                 ->assertJson([
                     'success' => true,
                     'message' => 'API is running successfully',
                 ]);
    }

    /**
     * Test API v1 health endpoint
     */
    public function test_v1_health_endpoint_returns_success(): void
    {
        $response = $this->getJson('/api/v1/health');

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'status',
                     'checks' => [
                         'database' => ['status', 'message'],
                         'cache' => ['status', 'message'],
                         'storage' => ['status', 'message'],
                     ],
                     'timestamp',
                 ]);
    }

    /**
     * Test that non-existent API endpoints return 404
     */
    public function test_non_existent_endpoint_returns_404(): void
    {
        $response = $this->getJson('/api/v1/non-existent');

        $response->assertStatus(404)
                 ->assertJsonStructure([
                     'success',
                     'message',
                     'errors',
                     'timestamp',
                 ])
                 ->assertJson([
                     'success' => false,
                     'message' => 'Resource not found',
                 ]);
    }

    /**
     * Test that API responses have correct headers
     */
    public function test_api_responses_have_correct_headers(): void
    {
        $response = $this->getJson('/api/v1/status');

        $response->assertHeader('Content-Type', 'application/json')
                 ->assertHeader('X-API-Version', '1.0');
    }

    /**
     * Test CORS headers are present
     */
    public function test_cors_headers_are_present(): void
    {
        $response = $this->getJson('/api/v1/status');

        $response->assertHeader('Access-Control-Allow-Origin', '*')
                 ->assertHeader('Access-Control-Allow-Methods')
                 ->assertHeader('Access-Control-Allow-Headers');
    }

    /**
     * Test OPTIONS request for CORS preflight
     */
    public function test_options_request_for_cors_preflight(): void
    {
        $response = $this->call('OPTIONS', '/api/v1/status');

        $response->assertStatus(200)
                 ->assertHeader('Access-Control-Allow-Origin', '*');
    }
}
