<?php

declare(strict_types=1);

namespace Tests\Feature\Api\V1;

use App\Models\User;
use App\Services\PermissionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Lara<PERSON>\Sanctum\Sanctum;
use Tests\TestCase;

/**
 * Test API exception handling and error responses
 */
final class ExceptionHandlingTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test 404 not found error response format
     */
    public function test_not_found_error_response_format(): void
    {
        $response = $this->getJson('/api/v1/non-existent-endpoint');

        $response->assertStatus(404)
            ->assertJsonStructure([
                'success',
                'message',
                'errors',
                'timestamp',
                'error_code'
            ])
            ->assertJson([
                'success' => false,
                'message' => 'Resource not found',
                'errors' => null,
                'error_code' => 'NOT_FOUND_ERROR'
            ]);
    }

    /**
     * Test 401 authentication error response format
     */
    public function test_authentication_error_response_format(): void
    {
        $response = $this->getJson('/api/v1/user');

        $response->assertStatus(401)
            ->assertJsonStructure([
                'success',
                'message',
                'errors',
                'timestamp',
                'error_code'
            ])
            ->assert<PERSON>son([
                'success' => false,
                'message' => 'Authentication required',
                'errors' => null,
                'error_code' => 'AUTHENTICATION_ERROR'
            ]);
    }

    /**
     * Test 403 authorization error response format
     */
    public function test_authorization_error_response_format(): void
    {
        // Create a user without admin permissions
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        // Try to access admin-only endpoint (assuming roles endpoint requires admin)
        $response = $this->getJson('/api/v1/roles');

        $response->assertStatus(403)
            ->assertJsonStructure([
                'success',
                'message',
                'errors',
                'timestamp',
                'error_code'
            ])
            ->assertJson([
                'success' => false,
                'errors' => null,
            ]);

        // Should have either authorization or permission error
        $this->assertTrue(
            in_array($response->json('error_code'), ['AUTHORIZATION_ERROR', 'PERMISSION_ERROR'])
        );
    }

    /**
     * Test 405 method not allowed error response format
     */
    public function test_method_not_allowed_error_response_format(): void
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        // Try to use POST on a GET-only endpoint
        $response = $this->postJson('/api/v1/user');

        $response->assertStatus(405)
            ->assertJsonStructure([
                'success',
                'message',
                'errors',
                'timestamp',
                'error_code'
            ])
            ->assertJson([
                'success' => false,
                'message' => 'Method not allowed',
                'errors' => null,
                'error_code' => 'METHOD_NOT_ALLOWED_ERROR'
            ]);
    }

    /**
     * Test validation error response format using auth login endpoint
     */
    public function test_validation_error_response_format(): void
    {
        // Try to login with invalid data (no authentication required for this endpoint)
        $response = $this->postJson('/api/v1/auth/login', [
            'email' => 'invalid-email', // Invalid email format
            'password' => '', // Empty password
        ]);

        $response->assertStatus(422)
            ->assertJsonStructure([
                'success',
                'message',
                'errors',
                'timestamp',
                'error_code'
            ])
            ->assertJson([
                'success' => false,
                'message' => 'Validation failed',
                'error_code' => 'VALIDATION_ERROR'
            ]);

        // Should have validation errors
        $this->assertNotNull($response->json('errors'));

        // Should have specific field errors
        $errors = $response->json('errors');
        $this->assertArrayHasKey('email', $errors);
        $this->assertArrayHasKey('password', $errors);
    }

    /**
     * Test that error responses don't contain sensitive information
     */
    public function test_error_responses_dont_contain_sensitive_information(): void
    {
        $response = $this->getJson('/api/v1/non-existent-endpoint');

        $responseData = $response->json();

        // Should not contain these sensitive fields
        $this->assertArrayNotHasKey('exception', $responseData);
        $this->assertArrayNotHasKey('file', $responseData);
        $this->assertArrayNotHasKey('line', $responseData);
        $this->assertArrayNotHasKey('trace', $responseData);
    }

    /**
     * Test that all error responses have consistent structure
     */
    public function test_error_responses_have_consistent_structure(): void
    {
        $endpoints = [
            '/api/v1/non-existent-endpoint' => 404,
            '/api/v1/user' => 401, // Requires authentication
        ];

        foreach ($endpoints as $endpoint => $expectedStatus) {
            $response = $this->getJson($endpoint);

            $response->assertStatus($expectedStatus)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'errors',
                    'timestamp',
                    'error_code'
                ]);

            // All error responses should have success = false
            $this->assertFalse($response->json('success'));
            
            // Should have a timestamp in ISO format
            $this->assertMatchesRegularExpression(
                '/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{6}Z$/',
                $response->json('timestamp')
            );
        }
    }
}
