<?php

declare(strict_types=1);

namespace Tests\Feature\Api\V1;

use App\Models\Organisation;
use App\Models\Role;
use App\Models\User;
use App\Services\PermissionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

final class RoleControllerTest extends TestCase
{
    use RefreshDatabase;

    private PermissionService $permissionService;
    private Organisation $organisation;

    // System roles
    private Role $rootRole;
    private Role $adminRole;

    // Organisation roles
    private Role $ownerRole;
    private Role $memberRole;

    // Test users
    private User $rootUser;
    private User $adminUser;
    private User $orgAdminUser;
    private User $orgRootUser;
    private User $ownerUser;
    private User $memberUser;
    private User $userWithoutRoles;

    protected function setUp(): void
    {
        parent::setUp();

        $this->permissionService = app(PermissionService::class);

        // Create test organisation
        $this->organisation = Organisation::factory()->create([
            'name' => 'Test Organisation',
            'status' => 'active',
        ]);

        // Create roles
        $this->createRoles();

        // Create test users
        $this->createTestUsers();
    }

    private function createRoles(): void
    {
        // Create system roles
        $systemRoles = $this->permissionService->createSystemRoles();
        $this->rootRole = collect($systemRoles)->firstWhere('name', 'root');
        $this->adminRole = collect($systemRoles)->firstWhere('name', 'admin');

        // Create organisation roles
        $orgRoles = $this->permissionService->createDefaultRoles($this->organisation->id);
        $this->ownerRole = collect($orgRoles)->firstWhere('name', 'owner');
        $this->memberRole = collect($orgRoles)->firstWhere('name', 'member');
    }

    private function createTestUsers(): void
    {
        // Create system users (no organisation)
        $this->rootUser = User::factory()->create();
        $this->adminUser = User::factory()->create();

        // Create organisation users with system roles
        $this->orgAdminUser = User::factory()->create();
        $this->orgAdminUser->organisations()->attach($this->organisation->id);

        $this->orgRootUser = User::factory()->create();
        $this->orgRootUser->organisations()->attach($this->organisation->id);

        // Create organisation users with organisation roles
        $this->ownerUser = User::factory()->create();
        $this->ownerUser->organisations()->attach($this->organisation->id);

        $this->memberUser = User::factory()->create();
        $this->memberUser->organisations()->attach($this->organisation->id);

        $this->userWithoutRoles = User::factory()->create();
        $this->userWithoutRoles->organisations()->attach($this->organisation->id);

        // Assign system roles
        $this->assignSystemRoles();

        // Assign organisation roles
        $this->assignOrganisationRoles();
    }

    private function assignSystemRoles(): void
    {
        // Set team context to null for system roles
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);

        // Assign system roles to system users
        $this->rootUser->guard_name = 'system';
        $this->rootUser->assignRole($this->rootRole);

        $this->adminUser->guard_name = 'system';
        $this->adminUser->assignRole($this->adminRole);

        // Assign system roles to organisation users
        $this->orgAdminUser->guard_name = 'system';
        $this->orgAdminUser->assignRole($this->adminRole);

        $this->orgRootUser->guard_name = 'system';
        $this->orgRootUser->assignRole($this->rootRole);
    }

    private function assignOrganisationRoles(): void
    {
        // Set team context for organisation roles
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);

        $this->ownerUser->assignRole($this->ownerRole);
        $this->memberUser->assignRole($this->memberRole);
    }

    // ========================================
    // System Admin Access Tests
    // ========================================

    public function test_root_user_can_access_role_management(): void
    {
        Sanctum::actingAs($this->rootUser);

        $response = $this->getJson('/api/v1/roles');
        $response->assertStatus(200);
    }

    public function test_admin_user_can_access_role_management(): void
    {
        Sanctum::actingAs($this->adminUser);

        $response = $this->getJson('/api/v1/roles');
        $response->assertStatus(200);
    }

    public function test_organisation_admin_can_access_role_management(): void
    {
        Sanctum::actingAs($this->orgAdminUser);

        $response = $this->getJson('/api/v1/roles');
        $response->assertStatus(200);
    }

    public function test_organisation_root_can_access_role_management(): void
    {
        Sanctum::actingAs($this->orgRootUser);

        $response = $this->getJson('/api/v1/roles');
        $response->assertStatus(200);
    }

    // ========================================
    // Access Denied Tests
    // ========================================

    public function test_owner_user_cannot_access_role_management(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->getJson('/api/v1/roles');
        $response->assertStatus(403);
    }

    public function test_member_user_cannot_access_role_management(): void
    {
        Sanctum::actingAs($this->memberUser);

        $response = $this->getJson('/api/v1/roles');
        $response->assertStatus(403);
    }

    public function test_unauthenticated_user_cannot_access_role_management(): void
    {
        $response = $this->getJson('/api/v1/roles');
        $response->assertStatus(401);
    }

    public function test_user_without_roles_cannot_access_role_management(): void
    {
        Sanctum::actingAs($this->userWithoutRoles);

        $response = $this->getJson('/api/v1/roles');
        $response->assertStatus(403);
    }

    // ========================================
    // Role Middleware Security Tests
    // ========================================

    public function test_role_middleware_allows_system_admin_roles(): void
    {
        Sanctum::actingAs($this->adminUser);

        // Test route that requires admin role
        $response = $this->getJson('/api/v1/roles');
        $response->assertStatus(200);
    }

    public function test_role_middleware_allows_system_root_roles(): void
    {
        Sanctum::actingAs($this->rootUser);

        // Test route that requires admin role (root should also work)
        $response = $this->getJson('/api/v1/roles');
        $response->assertStatus(200);
    }

    public function test_role_middleware_denies_unauthorized_access(): void
    {
        Sanctum::actingAs($this->memberUser);

        // Try to access admin-only route
        $response = $this->getJson('/api/v1/roles');
        $response->assertStatus(403);
    }

    public function test_role_middleware_handles_mixed_guard_scenarios(): void
    {
        // User with both system and organization roles should be able to access admin routes
        Sanctum::actingAs($this->orgAdminUser);

        // Should be able to access admin routes (system role)
        $response = $this->getJson('/api/v1/roles');
        $response->assertStatus(200);
    }

    // ========================================
    // Role Creation Tests
    // ========================================

    public function test_can_create_organisation_role(): void
    {
        Sanctum::actingAs($this->orgRootUser);

        $response = $this->postJson('/api/v1/roles', [
            'name' => 'test-role',
            'organisation_id' => $this->organisation->id,
            'guard_name' => 'api',
        ]);

        $response->assertStatus(201)
            ->assertJson([
                'success' => true,
                'message' => 'Role created successfully',
            ]);

        $this->assertDatabaseHas('roles', [
            'name' => 'test-role',
            'organisation_id' => $this->organisation->id,
            'guard_name' => 'api',
        ]);
    }

    public function test_can_create_system_role(): void
    {
        Sanctum::actingAs($this->rootUser);

        $response = $this->postJson('/api/v1/roles', [
            'name' => 'test-system-role',
            'guard_name' => 'system',
        ]);

        $response->assertStatus(201)
            ->assertJson([
                'success' => true,
                'message' => 'Role created successfully',
            ]);

        $this->assertDatabaseHas('roles', [
            'name' => 'test-system-role',
            'organisation_id' => null,
            'guard_name' => 'system',
        ]);
    }

    public function test_cannot_create_organisation_role_with_system_guard(): void
    {
        Sanctum::actingAs($this->orgRootUser);

        $response = $this->postJson('/api/v1/roles', [
            'name' => 'invalid-role',
            'organisation_id' => $this->organisation->id,
            'guard_name' => 'system',
        ]);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => 'Organisation roles cannot use system guard',
            ]);
    }

    public function test_can_show_role(): void
    {
        Sanctum::actingAs($this->rootUser);

        $response = $this->getJson("/api/v1/roles/{$this->ownerRole->id}");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Role retrieved successfully',
                'data' => [
                    'id' => $this->ownerRole->id,
                    'name' => 'owner',
                ],
            ]);
    }

    // ========================================
    // Authentication and Authorization Tests
    // ========================================

    public function test_role_api_endpoints_require_authentication(): void
    {
        $response = $this->getJson('/api/v1/roles');
        $response->assertStatus(401);
    }

    public function test_can_list_roles_with_admin_role(): void
    {
        Sanctum::actingAs($this->adminUser);

        $response = $this->getJson('/api/v1/roles');
        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'success',
                     'data',
                     'message',
                     'timestamp'
                 ]);
    }

    public function test_cannot_list_roles_without_admin_role(): void
    {
        Sanctum::actingAs($this->memberUser);

        $response = $this->getJson('/api/v1/roles');
        $response->assertStatus(403);
    }
}
