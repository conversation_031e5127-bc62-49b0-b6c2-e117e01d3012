<?php

declare(strict_types=1);

namespace Tests\Feature\Api\V1;

use App\Models\Organisation;
use App\Models\Role;
use App\Models\User;
use App\Services\PermissionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\App;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

final class UserRoleControllerTest extends TestCase
{
    use RefreshDatabase;

    private PermissionService $permissionService;
    private Organisation $organisation;
    private User $rootUser;
    private User $adminUser;
    private User $ownerUser;
    private User $memberUser;
    private User $regularUser;

    protected function setUp(): void
    {
        parent::setUp();

        $this->permissionService = app(PermissionService::class);

        // Create test organisation
        $this->organisation = Organisation::factory()->create();

        // Create system roles
        $this->permissionService->createSystemRoles();

        // Create organisation roles
        $this->permissionService->createDefaultRoles($this->organisation->id);

        // Create test users
        $this->rootUser = User::factory()->create();
        $this->adminUser = User::factory()->create();

        $this->ownerUser = User::factory()->create();
        $this->ownerUser->organisations()->attach($this->organisation->id);

        $this->memberUser = User::factory()->create();
        $this->memberUser->organisations()->attach($this->organisation->id);

        $this->regularUser = User::factory()->create();
        $this->regularUser->organisations()->attach($this->organisation->id);

        // Assign roles
        $rootRole = Role::where('name', 'root')->where('guard_name', 'system')->whereNull('organisation_id')->first();
        $adminRole = Role::where('name', 'admin')->where('guard_name', 'system')->whereNull('organisation_id')->first();
        $ownerRole = Role::where('name', 'owner')->where('organisation_id', $this->organisation->id)->first();
        $memberRole = Role::where('name', 'member')->where('organisation_id', $this->organisation->id)->first();

        // Assign system roles using PermissionService
        $this->permissionService->assignRoleToUser($this->rootUser, $rootRole);
        $this->permissionService->assignRoleToUser($this->adminUser, $adminRole);

        // Assign organisation roles using PermissionService
        $this->permissionService->assignRoleToUser($this->ownerUser, $ownerRole);
        $this->permissionService->assignRoleToUser($this->memberUser, $memberRole);
    }

    public function test_root_can_assign_any_role(): void
    {
        Sanctum::actingAs($this->rootUser);

        $memberRole = Role::where('name', 'member')
            ->where('organisation_id', $this->organisation->id)
            ->first();

        $response = $this->postJson("/api/v1/users/{$this->regularUser->id}/roles", [
            'role_id' => $memberRole->id,
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Role assigned successfully',
            ]);
    }

    public function test_admin_can_assign_organisation_roles(): void
    {
        Sanctum::actingAs($this->adminUser);

        $memberRole = Role::where('name', 'member')
            ->where('organisation_id', $this->organisation->id)
            ->first();

        $response = $this->postJson("/api/v1/users/{$this->regularUser->id}/roles", [
            'role_id' => $memberRole->id,
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Role assigned successfully',
            ]);
    }

    public function test_owner_can_assign_member_role_only(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $memberRole = Role::where('name', 'member')
            ->where('organisation_id', $this->organisation->id)
            ->first();

        $response = $this->postJson("/api/v1/users/{$this->regularUser->id}/roles", [
            'role_id' => $memberRole->id,
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Role assigned successfully',
            ]);
    }

    public function test_owner_cannot_assign_owner_role(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $ownerRole = Role::where('name', 'owner')
            ->where('organisation_id', $this->organisation->id)
            ->first();

        $response = $this->postJson("/api/v1/users/{$this->regularUser->id}/roles", [
            'role_id' => $ownerRole->id,
        ]);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => 'Role assignment failed',
            ]);
    }

    public function test_member_cannot_assign_any_role(): void
    {
        Sanctum::actingAs($this->memberUser);

        $memberRole = Role::where('name', 'member')
            ->where('organisation_id', $this->organisation->id)
            ->first();

        $response = $this->postJson("/api/v1/users/{$this->regularUser->id}/roles", [
            'role_id' => $memberRole->id,
        ]);

        // Should return 403 (Forbidden) because member doesn't have permission to assign roles
        $response->assertStatus(403);
    }

    public function test_cannot_assign_duplicate_owner_role(): void
    {
        Sanctum::actingAs($this->rootUser);

        $ownerRole = Role::where('name', 'owner')
            ->where('organisation_id', $this->organisation->id)
            ->first();

        $response = $this->postJson("/api/v1/users/{$this->regularUser->id}/roles", [
            'role_id' => $ownerRole->id,
        ]);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => 'Role assignment failed',
            ])
            ->assertJsonPath('errors.role.0', 'This organization already has an owner. Please transfer the owner role first.');
    }

    public function test_owner_role_transfer(): void
    {
        Sanctum::actingAs($this->ownerUser);

        // Ensure regularUser belongs to the organisation
        $this->regularUser->refresh();
        $this->assertTrue($this->regularUser->organisations->contains($this->organisation));

        $response = $this->putJson("/api/v1/users/{$this->regularUser->id}/transfer-owner", [
            'organisation_id' => $this->organisation->id,
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Owner role transferred successfully',
            ]);

        // Verify the transfer using our custom methods that handle team context
        $this->assertFalse($this->ownerUser->fresh()->hasOwnerRoleForOrganisation($this->organisation->id));
        $this->assertTrue($this->regularUser->fresh()->hasOwnerRoleForOrganisation($this->organisation->id));
    }

    public function test_get_user_roles_with_complete_info(): void
    {
        Sanctum::actingAs($this->rootUser);

        $response = $this->getJson("/api/v1/user");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'name',
                    'email',
                    'email_verified_at',
                    'organisations',
                    'created_at',
                    'updated_at',
                    'roles' => [
                        'system_roles',
                        'organisation_roles',
                        'all_role_names',
                    ],
                ],
                'timestamp',
            ])
            ->assertJson([
                'success' => true,
                'message' => 'User information retrieved successfully',
            ]);
    }

    public function test_get_assignable_roles(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->getJson('/api/v1/users/assignable-roles');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Assignable roles list retrieved successfully',
            ]);

        // Check the new response structure
        $data = $response->json('data');
        $this->assertIsArray($data);
        $this->assertArrayHasKey('roles', $data);
        $this->assertArrayHasKey('details', $data);

        // Owner user should have details filled (not root/admin)
        $this->assertNotEmpty($data['details']);

        // Verify the details structure matches API documentation
        foreach ($data['details'] as $role) {
            $this->assertArrayHasKey('id', $role);
            $this->assertArrayHasKey('name', $role);
            $this->assertArrayHasKey('guard_name', $role);
            $this->assertArrayHasKey('organisation_id', $role);
            $this->assertArrayHasKey('created_at', $role);
            $this->assertArrayHasKey('updated_at', $role);
            $this->assertArrayHasKey('type', $role);
        }

        // Owner should only be able to assign member roles in their organization
        $memberRoles = collect($data['details'])->where('name', 'member')->where('guard_name', 'api');
        $this->assertGreaterThan(0, $memberRoles->count());

        // Should not contain system roles for owner user
        $systemRoles = collect($data['details'])->where('guard_name', 'system');
        $this->assertEquals(0, $systemRoles->count());

        // Check roles structure - should contain api guard with member role
        $this->assertArrayHasKey('api', $data['roles']);
        $this->assertContains('member', $data['roles']['api']);

        // Should not contain system guard for owner user
        $this->assertArrayNotHasKey('system', $data['roles']);
    }

    public function test_get_assignable_roles_for_root_user(): void
    {
        Sanctum::actingAs($this->rootUser);

        $response = $this->getJson('/api/v1/users/assignable-roles');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Assignable roles list retrieved successfully',
            ]);

        // Check the new response structure
        $data = $response->json('data');
        $this->assertIsArray($data);
        $this->assertArrayHasKey('roles', $data);
        $this->assertArrayHasKey('details', $data);

        // Root user should have empty details
        $this->assertEmpty($data['details']);

        // Check roles structure - should contain both system and api guards
        $this->assertArrayHasKey('system', $data['roles']);
        $this->assertArrayHasKey('api', $data['roles']);

        // System roles should contain admin
        $this->assertContains('admin', $data['roles']['system']);

        // API roles should contain organization roles that root can assign
        // Root can assign owner, member, and visitor
        $this->assertContains('owner', $data['roles']['api']);
        $this->assertContains('member', $data['roles']['api']);
        $this->assertContains('visitor', $data['roles']['api']);
    }

    public function test_assign_role_using_role_name_and_organisation_id(): void
    {
        Sanctum::actingAs($this->rootUser);

        $response = $this->postJson("/api/v1/users/{$this->regularUser->id}/roles", [
            'role_name' => 'member',
            'organisation_id' => $this->organisation->id,
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Role assigned successfully',
            ])
            ->assertJsonStructure([
                'data' => [
                    'user_id',
                    'role' => [
                        'id',
                        'name',
                        'organisation_id',
                    ],
                ],
            ]);

        $responseData = $response->json('data');
        $this->assertEquals('member', $responseData['role']['name']);
        $this->assertEquals($this->organisation->id, $responseData['role']['organisation_id']);
    }

    public function test_assign_system_role_using_role_name(): void
    {
        Sanctum::actingAs($this->rootUser);

        $response = $this->postJson("/api/v1/users/{$this->regularUser->id}/roles", [
            'role_name' => 'admin',
            'organisation_id' => null,
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Role assigned successfully',
            ])
            ->assertJsonStructure([
                'data' => [
                    'user_id',
                    'role' => [
                        'id',
                        'name',
                        'organisation_id',
                    ],
                ],
            ]);

        $responseData = $response->json('data');
        $this->assertEquals('admin', $responseData['role']['name']);
        $this->assertNull($responseData['role']['organisation_id']);
    }

    public function test_assign_role_with_nonexistent_role_name_creates_role(): void
    {
        Sanctum::actingAs($this->rootUser);

        // Ensure the role doesn't exist initially
        $this->assertDatabaseMissing('roles', [
            'name' => 'nonexistent_role',
            'organisation_id' => $this->organisation->id,
            'guard_name' => 'api',
        ]);

        $response = $this->postJson("/api/v1/users/{$this->regularUser->id}/roles", [
            'role_name' => 'nonexistent_role',
            'organisation_id' => $this->organisation->id,
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Role assigned successfully',
            ]);

        // Verify the role was created
        $this->assertDatabaseHas('roles', [
            'name' => 'nonexistent_role',
            'organisation_id' => $this->organisation->id,
            'guard_name' => 'api',
        ]);
    }

    public function test_assign_role_with_nonexistent_system_role_creates_role(): void
    {
        Sanctum::actingAs($this->rootUser);

        // Ensure the role doesn't exist initially
        $this->assertDatabaseMissing('roles', [
            'name' => 'nonexistent_system_role',
            'organisation_id' => null,
            'guard_name' => 'system',
        ]);

        $response = $this->postJson("/api/v1/users/{$this->regularUser->id}/roles", [
            'role_name' => 'nonexistent_system_role',
            'organisation_id' => null,
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Role assigned successfully',
            ]);

        // Verify the role was created
        $this->assertDatabaseHas('roles', [
            'name' => 'nonexistent_system_role',
            'organisation_id' => null,
            'guard_name' => 'system',
        ]);
    }

    public function test_assign_role_validation_requires_either_role_id_or_role_name(): void
    {
        Sanctum::actingAs($this->rootUser);

        // Test without any role identifier
        $response = $this->postJson("/api/v1/users/{$this->regularUser->id}/roles", [
            'organisation_id' => $this->organisation->id,
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['role_name']);
    }

    public function test_assign_role_validation_role_name_without_organisation_id_creates_system_role(): void
    {
        Sanctum::actingAs($this->rootUser);

        // Ensure the system role doesn't exist initially
        $this->assertDatabaseMissing('roles', [
            'name' => 'member',
            'organisation_id' => null,
            'guard_name' => 'system',
        ]);

        // Test role_name without organisation_id - should be treated as system role
        // Since 'member' is not a system role, it should fail
        $response = $this->postJson("/api/v1/users/{$this->regularUser->id}/roles", [
            'role_name' => 'member',
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Role assigned successfully',
            ]);

        // Verify the system role was created
        $this->assertDatabaseHas('roles', [
            'name' => 'member',
            'organisation_id' => null,
            'guard_name' => 'system',
        ]);
    }

    public function test_assign_role_error_messages_support_multiple_languages(): void
    {
        Sanctum::actingAs($this->rootUser);

        // Test English error messages (default)
        App::setLocale('en');

        $ownerRole = Role::where('name', 'owner')
            ->where('organisation_id', $this->organisation->id)
            ->first();

        // Try to assign duplicate owner role
        $response = $this->postJson("/api/v1/users/{$this->regularUser->id}/roles", [
            'role_id' => $ownerRole->id,
        ]);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => 'Role assignment failed',
            ]);

        $errors = $response->json('errors.role');
        $this->assertStringContainsString('This organization already has an owner', $errors[0]);

        // Test Chinese error messages
        App::setLocale('zh');

        $response = $this->postJson("/api/v1/users/{$this->regularUser->id}/roles", [
            'role_id' => $ownerRole->id,
        ], ['X-Locale' => 'zh']);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => '角色赋予失败',
            ]);

        $errors = $response->json('errors.role');
        $this->assertStringContainsString('该组织已经有一个所有者', $errors[0]);

        // Reset to default locale
        App::setLocale('en');
    }

    public function test_assign_duplicate_role_error_messages_support_multiple_languages(): void
    {
        Sanctum::actingAs($this->rootUser);

        // First assign a member role
        $memberRole = Role::where('name', 'member')
            ->where('organisation_id', $this->organisation->id)
            ->first();

        $this->postJson("/api/v1/users/{$this->regularUser->id}/roles", [
            'role_id' => $memberRole->id,
        ])->assertStatus(200);

        // Test English error messages (default)
        App::setLocale('en');

        // Try to assign the same role again
        $response = $this->postJson("/api/v1/users/{$this->regularUser->id}/roles", [
            'role_id' => $memberRole->id,
        ]);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => 'Role assignment failed',
            ]);

        $errors = $response->json('errors.role');
        $this->assertStringContainsString('User already has this role assigned', $errors[0]);

        // Test Chinese error messages
        App::setLocale('zh');

        $response = $this->postJson("/api/v1/users/{$this->regularUser->id}/roles", [
            'role_id' => $memberRole->id,
        ], ['X-Locale' => 'zh']);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => '角色赋予失败',
            ]);

        $errors = $response->json('errors.role');
        $this->assertStringContainsString('用户已经被分配了此角色', $errors[0]);

        // Reset to default locale
        App::setLocale('en');
    }

    public function test_assign_role_authorization_error_handling(): void
    {
        Sanctum::actingAs($this->rootUser);

        // Create a user that doesn't belong to the organization
        $outsideUser = User::factory()->create();

        $memberRole = Role::where('name', 'member')
            ->where('organisation_id', $this->organisation->id)
            ->first();

        // Try to assign role to user not in organization - should still work for root user
        $response = $this->postJson("/api/v1/users/{$outsideUser->id}/roles", [
            'role_id' => $memberRole->id,
        ]);

        // Root user should be able to assign roles even to users not in organization
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Role assigned successfully',
            ]);
    }

    public function test_auto_create_organization_role_when_not_exists(): void
    {
        Sanctum::actingAs($this->rootUser);

        // Ensure the role doesn't exist initially
        $this->assertDatabaseMissing('roles', [
            'name' => 'custom-org-role',
            'organisation_id' => $this->organisation->id,
            'guard_name' => 'api',
        ]);

        $response = $this->postJson("/api/v1/users/{$this->regularUser->id}/roles", [
            'role_name' => 'custom-org-role',
            'organisation_id' => $this->organisation->id,
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Role assigned successfully',
            ])
            ->assertJsonStructure([
                'data' => [
                    'user_id',
                    'role' => [
                        'id',
                        'name',
                        'organisation_id',
                    ],
                ],
            ]);

        // Verify the role was created in the database
        $this->assertDatabaseHas('roles', [
            'name' => 'custom-org-role',
            'organisation_id' => $this->organisation->id,
            'guard_name' => 'api',
        ]);

        $responseData = $response->json('data');
        $this->assertEquals('custom-org-role', $responseData['role']['name']);
        $this->assertEquals($this->organisation->id, $responseData['role']['organisation_id']);
    }

    public function test_auto_create_system_role_when_not_exists(): void
    {
        Sanctum::actingAs($this->rootUser);

        // Ensure the role doesn't exist initially
        $this->assertDatabaseMissing('roles', [
            'name' => 'custom-system-role',
            'organisation_id' => null,
            'guard_name' => 'system',
        ]);

        $response = $this->postJson("/api/v1/users/{$this->regularUser->id}/roles", [
            'role_name' => 'custom-system-role',
            'organisation_id' => null,
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Role assigned successfully',
            ])
            ->assertJsonStructure([
                'data' => [
                    'user_id',
                    'role' => [
                        'id',
                        'name',
                        'organisation_id',
                    ],
                ],
            ]);

        // Verify the role was created in the database
        $this->assertDatabaseHas('roles', [
            'name' => 'custom-system-role',
            'organisation_id' => null,
            'guard_name' => 'system',
        ]);

        $responseData = $response->json('data');
        $this->assertEquals('custom-system-role', $responseData['role']['name']);
        $this->assertNull($responseData['role']['organisation_id']);
    }

    public function test_auto_create_role_respects_authorization(): void
    {
        // Test that non-root users cannot create system roles
        Sanctum::actingAs($this->ownerUser);

        $response = $this->postJson("/api/v1/users/{$this->regularUser->id}/roles", [
            'role_name' => 'unauthorized-system-role',
            'organisation_id' => null,
        ]);

        // Should fail due to authorization, not create the role
        $response->assertStatus(403);

        // Verify the role was not created
        $this->assertDatabaseMissing('roles', [
            'name' => 'unauthorized-system-role',
            'organisation_id' => null,
            'guard_name' => 'system',
        ]);
    }

    public function test_auto_create_role_does_not_duplicate_existing_roles(): void
    {
        Sanctum::actingAs($this->rootUser);

        // First request - should create the role
        $response1 = $this->postJson("/api/v1/users/{$this->regularUser->id}/roles", [
            'role_name' => 'duplicate-test-role',
            'organisation_id' => $this->organisation->id,
        ]);

        $response1->assertStatus(200);

        // Count roles before second request
        $roleCountBefore = Role::where('name', 'duplicate-test-role')
            ->where('organisation_id', $this->organisation->id)
            ->count();

        $this->assertEquals(1, $roleCountBefore);

        // Create another user to assign the same role
        $anotherUser = User::factory()->create();

        // Second request - should use existing role, not create duplicate
        $response2 = $this->postJson("/api/v1/users/{$anotherUser->id}/roles", [
            'role_name' => 'duplicate-test-role',
            'organisation_id' => $this->organisation->id,
        ]);

        $response2->assertStatus(200);

        // Count roles after second request - should still be 1
        $roleCountAfter = Role::where('name', 'duplicate-test-role')
            ->where('organisation_id', $this->organisation->id)
            ->count();

        $this->assertEquals(1, $roleCountAfter);
    }
}
