<?php

declare(strict_types=1);

namespace Tests\Feature\Api\V1;

use Tests\TestCase;

/**
 * Test locale middleware functionality
 */
final class LocaleMiddlewareTest extends TestCase
{
    /**
     * Test default locale is English
     */
    public function test_default_locale_is_english(): void
    {
        $response = $this->getJson('/api/v1/status');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'API is running successfully',
            ]);
    }

    /**
     * Test Chinese locale via Accept-Language header
     */
    public function test_chinese_locale_via_accept_language_header(): void
    {
        $response = $this->getJson('/api/v1/status', [
            'Accept-Language' => 'zh-CN,zh;q=0.9,en;q=0.8'
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'API 运行正常',
            ]);
    }

    /**
     * Test Chinese locale via X-Locale header
     */
    public function test_chinese_locale_via_x_locale_header(): void
    {
        $response = $this->getJson('/api/v1/status', [
            'X-Locale' => 'zh'
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'API 运行正常',
            ]);
    }

    /**
     * Test English locale via X-Locale header
     */
    public function test_english_locale_via_x_locale_header(): void
    {
        $response = $this->getJson('/api/v1/status', [
            'X-Locale' => 'en'
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'API is running successfully',
            ]);
    }

    /**
     * Test invalid locale falls back to English
     */
    public function test_invalid_locale_falls_back_to_english(): void
    {
        $response = $this->getJson('/api/v1/status', [
            'X-Locale' => 'fr'
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'API is running successfully',
            ]);
    }

    /**
     * Test X-Locale header takes precedence over Accept-Language
     */
    public function test_x_locale_header_takes_precedence(): void
    {
        $response = $this->getJson('/api/v1/status', [
            'Accept-Language' => 'zh-CN,zh;q=0.9,en;q=0.8',
            'X-Locale' => 'en'
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'API is running successfully',
            ]);
    }
}
