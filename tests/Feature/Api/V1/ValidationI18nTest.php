<?php

declare(strict_types=1);

namespace Tests\Feature\Api\V1;

use App\Models\Organisation;
use App\Models\Role;
use App\Models\User;
use App\Services\PermissionService;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

final class ValidationI18nTest extends TestCase
{
    use RefreshDatabase;

    private PermissionService $permissionService;
    private User $adminUser;
    private Organisation $organisation;

    protected function setUp(): void
    {
        parent::setUp();

        $this->permissionService = app(PermissionService::class);

        // Create test organization
        $this->organisation = Organisation::factory()->create([
            'name' => 'Test Organization',
            'status' => 'active',
        ]);

        // Create admin user
        $this->adminUser = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        // Assign admin role to admin user
        $adminRole = $this->permissionService->createRole('admin', 'system');
        $this->permissionService->assignRoleToUser($this->adminUser, $adminRole);
    }

    public function test_invitation_validation_messages_in_english(): void
    {
        Sanctum::actingAs($this->adminUser);
        $this->withHeaders(['Accept-Language' => 'en']);

        $response = $this->postJson('/api/v1/invitations', []);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'model_type',
            'model_id',
            'role',
        ]);

        $errors = $response->json('errors');
        $this->assertStringContainsString('Model type is required', $errors['model_type'][0]);
        $this->assertStringContainsString('Organization ID is required', $errors['model_id'][0]);
        $this->assertStringContainsString('Role is required', $errors['role'][0]);
    }

    public function test_invitation_validation_messages_in_chinese(): void
    {
        Sanctum::actingAs($this->adminUser);
        $this->withHeaders(['Accept-Language' => 'zh']);

        $response = $this->postJson('/api/v1/invitations', []);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'model_type',
            'model_id',
            'role',
        ]);

        $errors = $response->json('errors');
        $this->assertStringContainsString('模型类型为必填项', $errors['model_type'][0]);
        $this->assertStringContainsString('组织ID为必填项', $errors['model_id'][0]);
        $this->assertStringContainsString('角色为必填项', $errors['role'][0]);
    }

    public function test_user_validation_messages_in_english(): void
    {
        $this->withHeaders(['Accept-Language' => 'en']);

        $response = $this->postJson('/api/v1/users/register', []);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'name',
            'email',
            'password',
            'verification_code',
        ]);

        $errors = $response->json('errors');
        $this->assertStringContainsString('Name is required', $errors['name'][0]);
        $this->assertStringContainsString('Email address is required', $errors['email'][0]);
        $this->assertStringContainsString('Password is required', $errors['password'][0]);
        $this->assertStringContainsString('Verification code is required', $errors['verification_code'][0]);
    }

    public function test_user_validation_messages_in_chinese(): void
    {
        $this->withHeaders(['Accept-Language' => 'zh']);

        $response = $this->postJson('/api/v1/users/register', []);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'name',
            'email',
            'password',
            'verification_code',
        ]);

        $errors = $response->json('errors');
        $this->assertStringContainsString('用户名称为必填项', $errors['name'][0]);
        $this->assertStringContainsString('邮箱地址为必填项', $errors['email'][0]);
        $this->assertStringContainsString('密码为必填项', $errors['password'][0]);
        $this->assertStringContainsString('验证码为必填项', $errors['verification_code'][0]);
    }

    public function test_organisation_validation_messages_in_english(): void
    {
        Sanctum::actingAs($this->adminUser);
        $this->withHeaders(['Accept-Language' => 'en']);

        $response = $this->postJson('/api/v1/organisations', []);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'name',
            'code',
        ]);

        $errors = $response->json('errors');
        $this->assertStringContainsString('Organization name is required', $errors['name'][0]);
        $this->assertStringContainsString('Organization code is required', $errors['code'][0]);
    }

    public function test_organisation_validation_messages_in_chinese(): void
    {
        Sanctum::actingAs($this->adminUser);
        $this->withHeaders(['Accept-Language' => 'zh']);

        $response = $this->postJson('/api/v1/organisations', []);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'name',
            'code',
        ]);

        $errors = $response->json('errors');
        $this->assertStringContainsString('组织名称是必填项', $errors['name'][0]);
        $this->assertStringContainsString('组织代码是必填项', $errors['code'][0]);
    }
}
