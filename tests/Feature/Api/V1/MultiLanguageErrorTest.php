<?php

declare(strict_types=1);

namespace Tests\Feature\Api\V1;

use App\Models\User;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

/**
 * Test multi-language error messages
 */
final class MultiLanguageErrorTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test 404 error in English
     */
    public function test_404_error_in_english(): void
    {
        $response = $this->getJson('/api/v1/non-existent-endpoint', [
            'X-Locale' => 'en'
        ]);

        $response->assertStatus(404)
            ->assertJson([
                'success' => false,
                'message' => 'Resource not found',
            ]);
    }

    /**
     * Test 404 error in Chinese
     */
    public function test_404_error_in_chinese(): void
    {
        $response = $this->getJson('/api/v1/non-existent-endpoint', [
            'X-Locale' => 'zh'
        ]);

        $response->assertStatus(404)
            ->assertJson([
                'success' => false,
                'message' => '资源未找到',
            ]);
    }

    /**
     * Test authentication error in English
     */
    public function test_authentication_error_in_english(): void
    {
        $response = $this->getJson('/api/v1/user', [
            'X-Locale' => 'en'
        ]);

        $response->assertStatus(401)
            ->assertJson([
                'success' => false,
                'message' => 'Authentication required',
            ]);
    }

    /**
     * Test authentication error in Chinese
     */
    public function test_authentication_error_in_chinese(): void
    {
        $response = $this->getJson('/api/v1/user', [
            'X-Locale' => 'zh'
        ]);

        $response->assertStatus(401)
            ->assertJson([
                'success' => false,
                'message' => '需要身份验证',
            ]);
    }

    /**
     * Test validation error in English
     */
    public function test_validation_error_in_english(): void
    {
        $response = $this->postJson('/api/v1/auth/login', [], [
            'X-Locale' => 'en'
        ]);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => 'Validation failed',
            ]);
    }

    /**
     * Test validation error in Chinese
     */
    public function test_validation_error_in_chinese(): void
    {
        $response = $this->postJson('/api/v1/auth/login', [], [
            'X-Locale' => 'zh'
        ]);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => '验证失败',
            ]);
    }

    /**
     * Test login with invalid credentials in English
     */
    public function test_login_invalid_credentials_in_english(): void
    {
        $response = $this->postJson('/api/v1/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'wrongpassword'
        ], [
            'X-Locale' => 'en'
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['email']);
        
        $errors = $response->json('errors.email');
        $this->assertContains('The provided credentials are incorrect.', $errors);
    }

    /**
     * Test login with invalid credentials in Chinese
     */
    public function test_login_invalid_credentials_in_chinese(): void
    {
        $response = $this->postJson('/api/v1/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'wrongpassword'
        ], [
            'X-Locale' => 'zh'
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['email']);
        
        $errors = $response->json('errors.email');
        $this->assertContains('提供的凭据不正确。', $errors);
    }
}
