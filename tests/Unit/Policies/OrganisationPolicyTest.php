<?php

declare(strict_types=1);

namespace Tests\Unit\Policies;

use App\Models\Organisation;
use App\Models\Role;
use App\Models\User;
use App\Policies\OrganisationPolicy;
use App\Services\PermissionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

final class OrganisationPolicyTest extends TestCase
{
    use RefreshDatabase;

    private OrganisationPolicy $policy;
    private PermissionService $permissionService;
    private Organisation $organisation;
    private Organisation $anotherOrganisation;

    // System roles
    private Role $systemRootRole;
    private Role $systemAdminRole;

    // Organisation roles
    private Role $ownerRole;
    private Role $memberRole;
    private Role $anotherOwnerRole;

    // Test users
    private User $systemRootUser;
    private User $systemAdminUser;
    private User $ownerUser;
    private User $memberUser;
    private User $anotherOwnerUser;
    private User $regularUser;

    protected function setUp(): void
    {
        parent::setUp();

        $this->policy = new OrganisationPolicy();
        $this->permissionService = app(PermissionService::class);

        // Create test organisations
        $this->organisation = Organisation::factory()->create([
            'name' => 'Test Organisation',
            'status' => 'active',
        ]);

        $this->anotherOrganisation = Organisation::factory()->create([
            'name' => 'Another Organisation',
            'status' => 'active',
        ]);

        // Create system roles
        $systemRoles = $this->permissionService->createSystemRoles();
        $this->systemRootRole = collect($systemRoles)->firstWhere('name', 'root');
        $this->systemAdminRole = collect($systemRoles)->firstWhere('name', 'admin');

        // Create organisation roles
        $orgRoles = $this->permissionService->createDefaultRoles($this->organisation->id);
        $this->ownerRole = collect($orgRoles)->firstWhere('name', 'owner');
        $this->memberRole = collect($orgRoles)->firstWhere('name', 'member');

        $anotherOrgRoles = $this->permissionService->createDefaultRoles($this->anotherOrganisation->id);
        $this->anotherOwnerRole = collect($anotherOrgRoles)->firstWhere('name', 'owner');

        // Create test users and assign roles
        $this->createTestUsers();
    }

    private function createTestUsers(): void
    {
        // Create system users (no organisation)
        $this->systemRootUser = User::factory()->create();
        $this->systemAdminUser = User::factory()->create();

        // Create organisation users
        $this->ownerUser = User::factory()->create();
        $this->ownerUser->organisations()->attach($this->organisation->id);

        $this->memberUser = User::factory()->create();
        $this->memberUser->organisations()->attach($this->organisation->id);

        $this->anotherOwnerUser = User::factory()->create();
        $this->anotherOwnerUser->organisations()->attach($this->anotherOrganisation->id);

        // Create user with no organisation
        $this->regularUser = User::factory()->create();

        // Assign system roles
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $this->systemRootUser->guard_name = 'system';
        $this->systemRootUser->assignRole($this->systemRootRole);

        $this->systemAdminUser->guard_name = 'system';
        $this->systemAdminUser->assignRole($this->systemAdminRole);

        // Assign organisation roles
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);
        $this->ownerUser->assignRole($this->ownerRole);
        $this->memberUser->assignRole($this->memberRole);

        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->anotherOrganisation->id);
        $this->anotherOwnerUser->assignRole($this->anotherOwnerRole);
    }

    /**
     * Helper method to clear team context for system role checks.
     */
    private function clearTeamContext(): void
    {
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
    }

    // ========================================
    // viewAny Tests
    // ========================================

    public function test_view_any_allows_system_root(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->viewAny($this->systemRootUser));
    }

    public function test_view_any_allows_system_admin(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->viewAny($this->systemAdminUser));
    }

    public function test_view_any_allows_organisation_owner(): void
    {
        $this->assertTrue($this->policy->viewAny($this->ownerUser));
    }

    public function test_view_any_denies_organisation_member(): void
    {
        $this->assertFalse($this->policy->viewAny($this->memberUser));
    }

    public function test_view_any_denies_regular_user(): void
    {
        $this->assertFalse($this->policy->viewAny($this->regularUser));
    }

    // ========================================
    // view Tests
    // ========================================

    public function test_view_allows_system_root_to_view_any_organisation(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->view($this->systemRootUser, $this->organisation));
        $this->assertTrue($this->policy->view($this->systemRootUser, $this->anotherOrganisation));
    }

    public function test_view_allows_system_admin_to_view_any_organisation(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->view($this->systemAdminUser, $this->organisation));
        $this->assertTrue($this->policy->view($this->systemAdminUser, $this->anotherOrganisation));
    }

    public function test_view_allows_organisation_owner_to_view_own_organisation(): void
    {
        // Set team context for organisation owner check
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);
        $this->assertTrue($this->policy->view($this->ownerUser, $this->organisation));
    }

    public function test_view_denies_organisation_owner_to_view_other_organisation(): void
    {
        // Set team context for organisation owner check
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);
        $this->assertFalse($this->policy->view($this->ownerUser, $this->anotherOrganisation));
    }

    public function test_view_denies_organisation_member(): void
    {
        $this->assertFalse($this->policy->view($this->memberUser, $this->organisation));
        $this->assertFalse($this->policy->view($this->memberUser, $this->anotherOrganisation));
    }

    public function test_view_denies_regular_user(): void
    {
        $this->assertFalse($this->policy->view($this->regularUser, $this->organisation));
        $this->assertFalse($this->policy->view($this->regularUser, $this->anotherOrganisation));
    }

    // ========================================
    // create Tests
    // ========================================

    public function test_create_allows_system_root(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->create($this->systemRootUser));
    }

    public function test_create_allows_system_admin(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->create($this->systemAdminUser));
    }

    public function test_create_denies_organisation_owner(): void
    {
        $this->assertFalse($this->policy->create($this->ownerUser));
    }

    public function test_create_denies_organisation_member(): void
    {
        $this->assertFalse($this->policy->create($this->memberUser));
    }

    public function test_create_denies_regular_user(): void
    {
        $this->assertFalse($this->policy->create($this->regularUser));
    }

    // ========================================
    // update Tests
    // ========================================

    public function test_update_allows_system_root_to_update_any_organisation(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->update($this->systemRootUser, $this->organisation));
        $this->assertTrue($this->policy->update($this->systemRootUser, $this->anotherOrganisation));
    }

    public function test_update_allows_system_admin_to_update_any_organisation(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->update($this->systemAdminUser, $this->organisation));
        $this->assertTrue($this->policy->update($this->systemAdminUser, $this->anotherOrganisation));
    }

    public function test_update_allows_organisation_owner_to_update_own_organisation(): void
    {
        // Set team context for organisation owner check
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);
        $this->assertTrue($this->policy->update($this->ownerUser, $this->organisation));
    }

    public function test_update_denies_organisation_owner_to_update_other_organisation(): void
    {
        // Set team context for organisation owner check
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);
        $this->assertFalse($this->policy->update($this->ownerUser, $this->anotherOrganisation));
    }

    public function test_update_denies_organisation_member(): void
    {
        $this->assertFalse($this->policy->update($this->memberUser, $this->organisation));
        $this->assertFalse($this->policy->update($this->memberUser, $this->anotherOrganisation));
    }

    public function test_update_denies_regular_user(): void
    {
        $this->assertFalse($this->policy->update($this->regularUser, $this->organisation));
        $this->assertFalse($this->policy->update($this->regularUser, $this->anotherOrganisation));
    }

    // ========================================
    // suspend Tests
    // ========================================

    public function test_suspend_allows_only_system_root(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->suspend($this->systemRootUser, $this->organisation));
        $this->assertTrue($this->policy->suspend($this->systemRootUser, $this->anotherOrganisation));
    }

    public function test_suspend_allows_only_system_admin(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->suspend($this->systemAdminUser, $this->organisation));
        $this->assertTrue($this->policy->suspend($this->systemAdminUser, $this->anotherOrganisation));
    }

    public function test_suspend_denies_organisation_owner(): void
    {
        $this->assertFalse($this->policy->suspend($this->ownerUser, $this->organisation));
        $this->assertFalse($this->policy->suspend($this->ownerUser, $this->anotherOrganisation));
    }

    public function test_suspend_denies_organisation_member(): void
    {
        $this->assertFalse($this->policy->suspend($this->memberUser, $this->organisation));
        $this->assertFalse($this->policy->suspend($this->memberUser, $this->anotherOrganisation));
    }

    public function test_suspend_denies_regular_user(): void
    {
        $this->assertFalse($this->policy->suspend($this->regularUser, $this->organisation));
        $this->assertFalse($this->policy->suspend($this->regularUser, $this->anotherOrganisation));
    }

    // ========================================
    // activate Tests
    // ========================================

    public function test_activate_allows_only_system_root(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->activate($this->systemRootUser, $this->organisation));
        $this->assertTrue($this->policy->activate($this->systemRootUser, $this->anotherOrganisation));
    }

    public function test_activate_allows_only_system_admin(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->activate($this->systemAdminUser, $this->organisation));
        $this->assertTrue($this->policy->activate($this->systemAdminUser, $this->anotherOrganisation));
    }

    public function test_activate_denies_organisation_owner(): void
    {
        $this->assertFalse($this->policy->activate($this->ownerUser, $this->organisation));
        $this->assertFalse($this->policy->activate($this->ownerUser, $this->anotherOrganisation));
    }

    public function test_activate_denies_organisation_member(): void
    {
        $this->assertFalse($this->policy->activate($this->memberUser, $this->organisation));
        $this->assertFalse($this->policy->activate($this->memberUser, $this->anotherOrganisation));
    }

    public function test_activate_denies_regular_user(): void
    {
        $this->assertFalse($this->policy->activate($this->regularUser, $this->organisation));
        $this->assertFalse($this->policy->activate($this->regularUser, $this->anotherOrganisation));
    }

    // ========================================
    // delete Tests
    // ========================================

    public function test_delete_allows_only_system_root(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->delete($this->systemRootUser, $this->organisation));
        $this->assertTrue($this->policy->delete($this->systemRootUser, $this->anotherOrganisation));
    }

    public function test_delete_allows_only_system_admin(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->delete($this->systemAdminUser, $this->organisation));
        $this->assertTrue($this->policy->delete($this->systemAdminUser, $this->anotherOrganisation));
    }

    public function test_delete_denies_organisation_owner(): void
    {
        $this->assertFalse($this->policy->delete($this->ownerUser, $this->organisation));
        $this->assertFalse($this->policy->delete($this->ownerUser, $this->anotherOrganisation));
    }

    public function test_delete_denies_organisation_member(): void
    {
        $this->assertFalse($this->policy->delete($this->memberUser, $this->organisation));
        $this->assertFalse($this->policy->delete($this->memberUser, $this->anotherOrganisation));
    }

    public function test_delete_denies_regular_user(): void
    {
        $this->assertFalse($this->policy->delete($this->regularUser, $this->organisation));
        $this->assertFalse($this->policy->delete($this->regularUser, $this->anotherOrganisation));
    }

    // ========================================
    // restore Tests
    // ========================================

    public function test_restore_allows_only_system_root(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->restore($this->systemRootUser, $this->organisation));
        $this->assertTrue($this->policy->restore($this->systemRootUser, $this->anotherOrganisation));
    }

    public function test_restore_allows_only_system_admin(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->restore($this->systemAdminUser, $this->organisation));
        $this->assertTrue($this->policy->restore($this->systemAdminUser, $this->anotherOrganisation));
    }

    public function test_restore_denies_organisation_owner(): void
    {
        $this->assertFalse($this->policy->restore($this->ownerUser, $this->organisation));
        $this->assertFalse($this->policy->restore($this->ownerUser, $this->anotherOrganisation));
    }

    public function test_restore_denies_organisation_member(): void
    {
        $this->assertFalse($this->policy->restore($this->memberUser, $this->organisation));
        $this->assertFalse($this->policy->restore($this->memberUser, $this->anotherOrganisation));
    }

    public function test_restore_denies_regular_user(): void
    {
        $this->assertFalse($this->policy->restore($this->regularUser, $this->organisation));
        $this->assertFalse($this->policy->restore($this->regularUser, $this->anotherOrganisation));
    }

    // ========================================
    // forceDelete Tests
    // ========================================

    public function test_force_delete_allows_only_system_root(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->forceDelete($this->systemRootUser, $this->organisation));
        $this->assertTrue($this->policy->forceDelete($this->systemRootUser, $this->anotherOrganisation));
    }

    public function test_force_delete_allows_only_system_admin(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->forceDelete($this->systemAdminUser, $this->organisation));
        $this->assertTrue($this->policy->forceDelete($this->systemAdminUser, $this->anotherOrganisation));
    }

    public function test_force_delete_denies_organisation_owner(): void
    {
        $this->assertFalse($this->policy->forceDelete($this->ownerUser, $this->organisation));
        $this->assertFalse($this->policy->forceDelete($this->ownerUser, $this->anotherOrganisation));
    }

    public function test_force_delete_denies_organisation_member(): void
    {
        $this->assertFalse($this->policy->forceDelete($this->memberUser, $this->organisation));
        $this->assertFalse($this->policy->forceDelete($this->memberUser, $this->anotherOrganisation));
    }

    public function test_force_delete_denies_regular_user(): void
    {
        $this->assertFalse($this->policy->forceDelete($this->regularUser, $this->organisation));
        $this->assertFalse($this->policy->forceDelete($this->regularUser, $this->anotherOrganisation));
    }

}
