<?php

declare(strict_types=1);

namespace Tests\Unit\Policies;

use App\Models\Organisation;
use App\Models\Role;
use App\Models\SyncLog;
use App\Models\User;
use App\Policies\SyncPolicy;
use App\Services\PermissionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

final class SyncPolicyTest extends TestCase
{
    use RefreshDatabase;

    private SyncPolicy $policy;
    private User $systemAdmin;
    private User $regularUser;
    private SyncLog $syncLog;
    private PermissionService $permissionService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->policy = new SyncPolicy();
        $this->permissionService = app(PermissionService::class);

        // Create test organisation
        $organisation = Organisation::factory()->create();

        // Create system admin user
        $this->systemAdmin = User::factory()->create();
        $adminRole = $this->permissionService->createRole('admin', 'system', null);
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $this->systemAdmin->guard_name = 'system';
        $this->systemAdmin->assignRole($adminRole);

        // Create regular user with organisation membership
        $this->regularUser = User::factory()->create();
        $this->regularUser->organisations()->attach($organisation);
        $memberRole = $this->permissionService->createRole('member', 'api', $organisation->id);
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($organisation->id);
        $this->regularUser->guard_name = 'api';
        $this->regularUser->assignRole($memberRole);

        // Create test sync log
        $this->syncLog = SyncLog::create([
            'sync_type' => 'product_sync',
            'batch_id' => 'test_batch_123',
            'status' => 'completed',
            'started_at' => now()->subMinutes(10),
            'completed_at' => now()->subMinutes(5),
        ]);
    }

    public function test_view_any_allows_system_admin(): void
    {
        $result = $this->policy->viewAny($this->systemAdmin);

        $this->assertTrue($result);
    }

    public function test_view_any_denies_regular_user(): void
    {
        $result = $this->policy->viewAny($this->regularUser);

        $this->assertFalse($result);
    }

    public function test_view_allows_system_admin(): void
    {
        $result = $this->policy->view($this->systemAdmin, $this->syncLog);

        $this->assertTrue($result);
    }

    public function test_view_denies_regular_user(): void
    {
        $result = $this->policy->view($this->regularUser, $this->syncLog);

        $this->assertFalse($result);
    }

    public function test_create_allows_system_admin(): void
    {
        $result = $this->policy->create($this->systemAdmin);

        $this->assertTrue($result);
    }

    public function test_create_denies_regular_user(): void
    {
        $result = $this->policy->create($this->regularUser);

        $this->assertFalse($result);
    }

    public function test_update_allows_system_admin(): void
    {
        $result = $this->policy->update($this->systemAdmin, $this->syncLog);

        $this->assertTrue($result);
    }

    public function test_update_denies_regular_user(): void
    {
        $result = $this->policy->update($this->regularUser, $this->syncLog);

        $this->assertFalse($result);
    }

    public function test_delete_allows_system_admin(): void
    {
        $result = $this->policy->delete($this->systemAdmin, $this->syncLog);

        $this->assertTrue($result);
    }

    public function test_delete_denies_regular_user(): void
    {
        $result = $this->policy->delete($this->regularUser, $this->syncLog);

        $this->assertFalse($result);
    }

    public function test_trigger_allows_system_admin(): void
    {
        $result = $this->policy->trigger($this->systemAdmin);

        $this->assertTrue($result);
    }

    public function test_trigger_denies_regular_user(): void
    {
        $result = $this->policy->trigger($this->regularUser);

        $this->assertFalse($result);
    }

    public function test_retry_allows_system_admin(): void
    {
        $result = $this->policy->retry($this->systemAdmin, $this->syncLog);

        $this->assertTrue($result);
    }

    public function test_retry_denies_regular_user(): void
    {
        $result = $this->policy->retry($this->regularUser, $this->syncLog);

        $this->assertFalse($result);
    }

    public function test_system_root_user_has_access(): void
    {
        // Create root user
        $rootUser = User::factory()->create();
        $rootRole = $this->permissionService->createRole('root', 'system', null);
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $rootUser->guard_name = 'system';
        $rootUser->assignRole($rootRole);

        $this->assertTrue($this->policy->viewAny($rootUser));
        $this->assertTrue($this->policy->view($rootUser, $this->syncLog));
        $this->assertTrue($this->policy->trigger($rootUser));
        $this->assertTrue($this->policy->retry($rootUser, $this->syncLog));
    }
}
