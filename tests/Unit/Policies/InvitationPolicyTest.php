<?php

declare(strict_types=1);

namespace Tests\Unit\Policies;

use App\Models\Invitation;
use App\Models\Organisation;
use App\Models\User;
use App\Policies\InvitationPolicy;
use App\Services\PermissionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

final class InvitationPolicyTest extends TestCase
{
    use RefreshDatabase;

    private InvitationPolicy $policy;
    private PermissionService $permissionService;
    private User $rootUser;
    private User $adminUser;
    private User $ownerUser;
    private User $memberUser;
    private User $regularUser;
    private Organisation $organisation;
    private Invitation $invitation;

    protected function setUp(): void
    {
        parent::setUp();

        $this->policy = new InvitationPolicy();
        $this->permissionService = app(PermissionService::class);

        // Create test users
        $this->rootUser = User::factory()->create(['email' => '<EMAIL>']);
        $this->adminUser = User::factory()->create(['email' => '<EMAIL>']);
        $this->ownerUser = User::factory()->create(['email' => '<EMAIL>']);
        $this->memberUser = User::factory()->create(['email' => '<EMAIL>']);
        $this->regularUser = User::factory()->create(['email' => '<EMAIL>']);

        // Create organisation
        $this->organisation = Organisation::factory()->create();

        // Assign roles
        $rootRole = $this->permissionService->createRole('root', 'system');
        $adminRole = $this->permissionService->createRole('admin', 'system');
        $ownerRole = $this->permissionService->createRole('owner', 'api', $this->organisation->id);
        $memberRole = $this->permissionService->createRole('member', 'api', $this->organisation->id);

        $this->permissionService->assignRoleToUser($this->rootUser, $rootRole);
        $this->permissionService->assignRoleToUser($this->adminUser, $adminRole);
        $this->permissionService->assignRoleToUser($this->ownerUser, $ownerRole);
        $this->permissionService->assignRoleToUser($this->memberUser, $memberRole);

        // Add users to organisation
        $this->ownerUser->organisations()->attach($this->organisation->id);
        $this->memberUser->organisations()->attach($this->organisation->id);

        // Create test invitation
        $this->invitation = Invitation::factory()->create([
            'model_type' => Organisation::class,
            'model_id' => $this->organisation->id,
            'role' => 'member',
            'created_by_user_id' => $this->ownerUser->id,
            'email_restriction' => null, // No email restriction for basic tests
        ]);
    }

    public function test_viewAny_allows_system_admins(): void
    {
        $this->assertTrue($this->policy->viewAny($this->rootUser));
        $this->assertTrue($this->policy->viewAny($this->adminUser));
    }

    public function test_viewAny_allows_organisation_members(): void
    {
        $this->assertTrue($this->policy->viewAny($this->ownerUser));
        $this->assertTrue($this->policy->viewAny($this->memberUser));
    }

    public function test_viewAny_denies_regular_users(): void
    {
        $this->assertFalse($this->policy->viewAny($this->regularUser));
    }

    public function test_view_allows_system_admins(): void
    {
        $this->assertTrue($this->policy->view($this->rootUser, $this->invitation));
        $this->assertTrue($this->policy->view($this->adminUser, $this->invitation));
    }

    public function test_view_allows_organisation_owners(): void
    {
        $this->assertTrue($this->policy->view($this->ownerUser, $this->invitation));
    }

    public function test_view_allows_regular_users_for_unrestricted_invitations(): void
    {
        // Regular users can view invitations without email restrictions
        $this->assertTrue($this->policy->view($this->regularUser, $this->invitation));
    }

    public function test_create_allows_system_admins(): void
    {
        $this->assertTrue($this->policy->create($this->rootUser));
        $this->assertTrue($this->policy->create($this->adminUser));
    }

    public function test_create_allows_organisation_members(): void
    {
        $this->assertTrue($this->policy->create($this->ownerUser));
        $this->assertTrue($this->policy->create($this->memberUser));
    }

    public function test_create_denies_regular_users(): void
    {
        $this->assertFalse($this->policy->create($this->regularUser));
    }

    public function test_update_allows_system_admins(): void
    {
        $this->assertTrue($this->policy->update($this->rootUser, $this->invitation));
        $this->assertTrue($this->policy->update($this->adminUser, $this->invitation));
    }

    public function test_update_allows_organisation_owners(): void
    {
        $this->assertTrue($this->policy->update($this->ownerUser, $this->invitation));
    }

    public function test_update_denies_non_organisation_members(): void
    {
        $this->assertFalse($this->policy->update($this->regularUser, $this->invitation));
    }

    public function test_delete_allows_system_admins(): void
    {
        $this->assertTrue($this->policy->delete($this->rootUser, $this->invitation));
        $this->assertTrue($this->policy->delete($this->adminUser, $this->invitation));
    }

    public function test_delete_allows_organisation_owners(): void
    {
        $this->assertTrue($this->policy->delete($this->ownerUser, $this->invitation));
    }

    public function test_delete_denies_non_organisation_members(): void
    {
        $this->assertFalse($this->policy->delete($this->regularUser, $this->invitation));
    }

    public function test_view_allows_authenticated_users_regardless_of_email_restriction(): void
    {
        $invitation = Invitation::factory()->create([
            'model_type' => Organisation::class,
            'model_id' => $this->organisation->id,
            'role' => 'member',
            'email_restriction' => '<EMAIL>', // Email restriction doesn't affect view anymore
        ]);

        $this->assertTrue($this->policy->view($this->regularUser, $invitation));
    }

    public function test_view_denies_system_role_invitations(): void
    {
        $invitation = Invitation::factory()->create([
            'model_type' => Organisation::class,
            'model_id' => $this->organisation->id,
            'role' => 'admin', // System role
        ]);

        $this->assertFalse($this->policy->view($this->regularUser, $invitation));
    }

    public function test_accept_allows_users_with_matching_email(): void
    {
        $invitation = Invitation::factory()->create([
            'model_type' => Organisation::class,
            'model_id' => $this->organisation->id,
            'role' => 'member',
            'email_restriction' => $this->regularUser->email,
        ]);

        $this->assertTrue($this->policy->accept($this->regularUser, $invitation));
    }

    public function test_accept_allow_users_with_non_matching_email(): void
    {
        $invitation = Invitation::factory()->create([
            'model_type' => Organisation::class,
            'model_id' => $this->organisation->id,
            'role' => 'member',
            'email_restriction' => '<EMAIL>',
        ]);

        $this->assertTrue($this->policy->accept($this->regularUser, $invitation));
    }

    public function test_accept_denies_system_role_invitations(): void
    {
        $invitation = Invitation::factory()->create([
            'model_type' => Organisation::class,
            'model_id' => $this->organisation->id,
            'role' => 'root', // System role
        ]);

        $this->assertFalse($this->policy->accept($this->regularUser, $invitation));
    }

    public function test_createForOrganisation_allows_system_admins(): void
    {
        $this->assertTrue($this->policy->createForOrganisation($this->rootUser, $this->organisation->id));
        $this->assertTrue($this->policy->createForOrganisation($this->adminUser, $this->organisation->id));
    }

    public function test_createForOrganisation_allows_organisation_owners(): void
    {
        $this->assertTrue($this->policy->createForOrganisation($this->ownerUser, $this->organisation->id));
    }

    public function test_createForOrganisation_denies_non_organisation_members(): void
    {
        $this->assertFalse($this->policy->createForOrganisation($this->regularUser, $this->organisation->id));
    }

    public function test_createForOrganisation_denies_organisation_members_without_admin_access(): void
    {
        $this->assertFalse($this->policy->createForOrganisation($this->memberUser, $this->organisation->id));
    }
}
