<?php

declare(strict_types=1);

namespace Tests\Unit\Policies;

use App\Models\Organisation;
use App\Models\Role;
use App\Models\User;
use App\Policies\RolePolicy;
use App\Services\PermissionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

final class RolePolicyTest extends TestCase
{
    use RefreshDatabase;

    private RolePolicy $policy;
    private PermissionService $permissionService;
    private Organisation $organisation;
    private Organisation $anotherOrganisation;

    // System roles
    private Role $systemRootRole;
    private Role $systemAdminRole;

    // Organisation roles
    private Role $ownerRole;
    private Role $memberRole;
    private Role $anotherOwnerRole;
    private Role $anotherMemberRole;

    // Test users
    private User $systemRootUser;
    private User $systemAdminUser;
    private User $ownerUser;
    private User $memberUser;
    private User $anotherOwnerUser;
    private User $regularUser;

    protected function setUp(): void
    {
        parent::setUp();

        $this->policy = new RolePolicy();
        $this->permissionService = app(PermissionService::class);

        // Create test organisations
        $this->organisation = Organisation::factory()->create([
            'name' => 'Test Organisation',
            'status' => 'active',
        ]);

        $this->anotherOrganisation = Organisation::factory()->create([
            'name' => 'Another Organisation',
            'status' => 'active',
        ]);

        // Create system roles
        $systemRoles = $this->permissionService->createSystemRoles();
        $this->systemRootRole = collect($systemRoles)->firstWhere('name', 'root');
        $this->systemAdminRole = collect($systemRoles)->firstWhere('name', 'admin');

        // Create organisation roles
        $orgRoles = $this->permissionService->createDefaultRoles($this->organisation->id);
        $this->ownerRole = collect($orgRoles)->firstWhere('name', 'owner');
        $this->memberRole = collect($orgRoles)->firstWhere('name', 'member');

        $anotherOrgRoles = $this->permissionService->createDefaultRoles($this->anotherOrganisation->id);
        $this->anotherOwnerRole = collect($anotherOrgRoles)->firstWhere('name', 'owner');
        $this->anotherMemberRole = collect($anotherOrgRoles)->firstWhere('name', 'member');

        // Create test users and assign roles
        $this->createTestUsers();
    }

    private function createTestUsers(): void
    {
        // Create system users (no organisation)
        $this->systemRootUser = User::factory()->create();
        $this->systemAdminUser = User::factory()->create();

        // Create organisation users
        $this->ownerUser = User::factory()->create();
        $this->ownerUser->organisations()->attach($this->organisation->id);

        $this->memberUser = User::factory()->create();
        $this->memberUser->organisations()->attach($this->organisation->id);

        $this->anotherOwnerUser = User::factory()->create();
        $this->anotherOwnerUser->organisations()->attach($this->anotherOrganisation->id);

        // Create user with no organisation
        $this->regularUser = User::factory()->create();

        // Assign system roles
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $this->systemRootUser->guard_name = 'system';
        $this->systemRootUser->assignRole($this->systemRootRole);

        $this->systemAdminUser->guard_name = 'system';
        $this->systemAdminUser->assignRole($this->systemAdminRole);

        // Assign organisation roles
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);
        $this->ownerUser->assignRole($this->ownerRole);
        $this->memberUser->assignRole($this->memberRole);

        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->anotherOrganisation->id);
        $this->anotherOwnerUser->assignRole($this->anotherOwnerRole);
    }

    /**
     * Helper method to clear team context for system role checks.
     */
    private function clearTeamContext(): void
    {
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
    }

    // ========================================
    // viewAny Tests
    // ========================================

    public function test_view_any_allows_system_root(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->viewAny($this->systemRootUser));
    }

    public function test_view_any_allows_system_admin(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->viewAny($this->systemAdminUser));
    }

    public function test_view_any_denies_organisation_owner(): void
    {
        $this->assertFalse($this->policy->viewAny($this->ownerUser));
    }

    public function test_view_any_denies_organisation_member(): void
    {
        $this->assertFalse($this->policy->viewAny($this->memberUser));
    }

    public function test_view_any_denies_regular_user(): void
    {
        $this->assertFalse($this->policy->viewAny($this->regularUser));
    }

    // ========================================
    // view Tests
    // ========================================

    public function test_view_allows_system_root_to_view_any_role(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->view($this->systemRootUser, $this->systemRootRole));
        $this->assertTrue($this->policy->view($this->systemRootUser, $this->systemAdminRole));
        $this->assertTrue($this->policy->view($this->systemRootUser, $this->ownerRole));
        $this->assertTrue($this->policy->view($this->systemRootUser, $this->memberRole));
    }

    public function test_view_allows_system_admin_to_view_any_role(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->view($this->systemAdminUser, $this->systemRootRole));
        $this->assertTrue($this->policy->view($this->systemAdminUser, $this->systemAdminRole));
        $this->assertTrue($this->policy->view($this->systemAdminUser, $this->ownerRole));
        $this->assertTrue($this->policy->view($this->systemAdminUser, $this->memberRole));
    }

    public function test_view_allows_organisation_owner_to_view_own_organisation_roles(): void
    {
        // Set team context for organisation owner check
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);
        $this->assertTrue($this->policy->view($this->ownerUser, $this->ownerRole));
        $this->assertTrue($this->policy->view($this->ownerUser, $this->memberRole));
    }

    public function test_view_denies_organisation_owner_to_view_other_organisation_roles(): void
    {
        // Set team context for organisation owner check
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);
        $this->assertFalse($this->policy->view($this->ownerUser, $this->anotherOwnerRole));
        $this->assertFalse($this->policy->view($this->ownerUser, $this->anotherMemberRole));
    }

    public function test_view_denies_organisation_owner_to_view_system_roles(): void
    {
        $this->assertFalse($this->policy->view($this->ownerUser, $this->systemRootRole));
        $this->assertFalse($this->policy->view($this->ownerUser, $this->systemAdminRole));
    }

    public function test_view_denies_organisation_member(): void
    {
        $this->assertFalse($this->policy->view($this->memberUser, $this->ownerRole));
        $this->assertFalse($this->policy->view($this->memberUser, $this->memberRole));
        $this->assertFalse($this->policy->view($this->memberUser, $this->systemRootRole));
        $this->assertFalse($this->policy->view($this->memberUser, $this->systemAdminRole));
    }

    public function test_view_denies_regular_user(): void
    {
        $this->assertFalse($this->policy->view($this->regularUser, $this->ownerRole));
        $this->assertFalse($this->policy->view($this->regularUser, $this->memberRole));
        $this->assertFalse($this->policy->view($this->regularUser, $this->systemRootRole));
        $this->assertFalse($this->policy->view($this->regularUser, $this->systemAdminRole));
    }

    // ========================================
    // create Tests
    // ========================================

    public function test_create_allows_system_root(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->create($this->systemRootUser));
    }

    public function test_create_allows_system_admin(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->create($this->systemAdminUser));
    }

    public function test_create_denies_organisation_owner(): void
    {
        $this->assertFalse($this->policy->create($this->ownerUser));
    }

    public function test_create_denies_organisation_member(): void
    {
        $this->assertFalse($this->policy->create($this->memberUser));
    }

    public function test_create_denies_regular_user(): void
    {
        $this->assertFalse($this->policy->create($this->regularUser));
    }

    // ========================================
    // update Tests
    // ========================================

    public function test_update_allows_system_root_to_update_any_role(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->update($this->systemRootUser, $this->systemRootRole));
        $this->assertTrue($this->policy->update($this->systemRootUser, $this->systemAdminRole));
        $this->assertTrue($this->policy->update($this->systemRootUser, $this->ownerRole));
        $this->assertTrue($this->policy->update($this->systemRootUser, $this->memberRole));
    }

    public function test_update_allows_system_admin_to_update_any_role(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->update($this->systemAdminUser, $this->systemRootRole));
        $this->assertTrue($this->policy->update($this->systemAdminUser, $this->systemAdminRole));
        $this->assertTrue($this->policy->update($this->systemAdminUser, $this->ownerRole));
        $this->assertTrue($this->policy->update($this->systemAdminUser, $this->memberRole));
    }

    public function test_update_allows_organisation_owner_to_update_own_organisation_roles(): void
    {
        // Set team context for organisation owner check
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);
        $this->assertTrue($this->policy->update($this->ownerUser, $this->ownerRole));
        $this->assertTrue($this->policy->update($this->ownerUser, $this->memberRole));
    }

    public function test_update_denies_organisation_owner_to_update_system_roles(): void
    {
        // Set team context for organisation owner check
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);
        $this->assertFalse($this->policy->update($this->ownerUser, $this->systemRootRole));
        $this->assertFalse($this->policy->update($this->ownerUser, $this->systemAdminRole));
    }

    public function test_update_denies_organisation_owner_to_update_other_organisation_roles(): void
    {
        $this->assertFalse($this->policy->update($this->ownerUser, $this->anotherOwnerRole));
        $this->assertFalse($this->policy->update($this->ownerUser, $this->anotherMemberRole));
    }

    public function test_update_denies_organisation_member(): void
    {
        $this->assertFalse($this->policy->update($this->memberUser, $this->ownerRole));
        $this->assertFalse($this->policy->update($this->memberUser, $this->memberRole));
        $this->assertFalse($this->policy->update($this->memberUser, $this->systemRootRole));
        $this->assertFalse($this->policy->update($this->memberUser, $this->systemAdminRole));
    }

    public function test_update_denies_regular_user(): void
    {
        $this->assertFalse($this->policy->update($this->regularUser, $this->ownerRole));
        $this->assertFalse($this->policy->update($this->regularUser, $this->memberRole));
        $this->assertFalse($this->policy->update($this->regularUser, $this->systemRootRole));
        $this->assertFalse($this->policy->update($this->regularUser, $this->systemAdminRole));
    }

    // ========================================
    // updateName Tests
    // ========================================

    public function test_update_name_allows_system_root_for_non_core_roles(): void
    {
        $this->clearTeamContext();
        // Create a custom role for testing
        $customRole = Role::create([
            'name' => 'custom_role',
            'guard_name' => 'api',
            'organisation_id' => $this->organisation->id,
        ]);

        $this->assertTrue($this->policy->updateName($this->systemRootUser, $customRole));
    }

    public function test_update_name_denies_system_root_for_core_roles(): void
    {
        $this->clearTeamContext();
        $this->assertFalse($this->policy->updateName($this->systemRootUser, $this->systemRootRole));
        $this->assertFalse($this->policy->updateName($this->systemRootUser, $this->systemAdminRole));
        $this->assertFalse($this->policy->updateName($this->systemRootUser, $this->ownerRole));
        $this->assertFalse($this->policy->updateName($this->systemRootUser, $this->memberRole));
    }

    public function test_update_name_allows_system_admin_for_non_core_roles(): void
    {
        $this->clearTeamContext();
        // Create a custom role for testing
        $customRole = Role::create([
            'name' => 'custom_role',
            'guard_name' => 'api',
            'organisation_id' => $this->organisation->id,
        ]);

        $this->assertTrue($this->policy->updateName($this->systemAdminUser, $customRole));
    }

    public function test_update_name_denies_system_admin_for_core_roles(): void
    {
        $this->clearTeamContext();
        $this->assertFalse($this->policy->updateName($this->systemAdminUser, $this->systemRootRole));
        $this->assertFalse($this->policy->updateName($this->systemAdminUser, $this->systemAdminRole));
        $this->assertFalse($this->policy->updateName($this->systemAdminUser, $this->ownerRole));
        $this->assertFalse($this->policy->updateName($this->systemAdminUser, $this->memberRole));
    }

    public function test_update_name_allows_organisation_owner_for_non_core_roles(): void
    {
        // Set team context for organisation owner check
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);
        // Create a custom role for testing
        $customRole = Role::create([
            'name' => 'custom_role',
            'guard_name' => 'api',
            'organisation_id' => $this->organisation->id,
        ]);

        $this->assertTrue($this->policy->updateName($this->ownerUser, $customRole));
    }

    public function test_update_name_denies_organisation_owner_for_core_roles(): void
    {
        // Set team context for organisation owner check
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);
        $this->assertFalse($this->policy->updateName($this->ownerUser, $this->ownerRole));
        $this->assertFalse($this->policy->updateName($this->ownerUser, $this->memberRole));
    }

    public function test_update_name_denies_organisation_member(): void
    {
        // Create a custom role for testing
        $customRole = Role::create([
            'name' => 'custom_role',
            'guard_name' => 'api',
            'organisation_id' => $this->organisation->id,
        ]);

        $this->assertFalse($this->policy->updateName($this->memberUser, $customRole));
        $this->assertFalse($this->policy->updateName($this->memberUser, $this->ownerRole));
        $this->assertFalse($this->policy->updateName($this->memberUser, $this->memberRole));
    }

    // ========================================
    // delete Tests
    // ========================================

    public function test_delete_allows_system_root_to_delete_any_role(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->delete($this->systemRootUser, $this->systemRootRole));
        $this->assertTrue($this->policy->delete($this->systemRootUser, $this->systemAdminRole));
        $this->assertTrue($this->policy->delete($this->systemRootUser, $this->ownerRole));
        $this->assertTrue($this->policy->delete($this->systemRootUser, $this->memberRole));
    }

    public function test_delete_allows_system_admin_to_delete_any_role(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->delete($this->systemAdminUser, $this->systemRootRole));
        $this->assertTrue($this->policy->delete($this->systemAdminUser, $this->systemAdminRole));
        $this->assertTrue($this->policy->delete($this->systemAdminUser, $this->ownerRole));
        $this->assertTrue($this->policy->delete($this->systemAdminUser, $this->memberRole));
    }

    public function test_delete_allows_organisation_owner_to_delete_own_organisation_roles(): void
    {
        // Set team context for organisation owner check
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);
        $this->assertTrue($this->policy->delete($this->ownerUser, $this->ownerRole));
        $this->assertTrue($this->policy->delete($this->ownerUser, $this->memberRole));
    }

    public function test_delete_denies_organisation_owner_to_delete_system_roles(): void
    {
        // Set team context for organisation owner check
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);
        $this->assertFalse($this->policy->delete($this->ownerUser, $this->systemRootRole));
        $this->assertFalse($this->policy->delete($this->ownerUser, $this->systemAdminRole));
    }

    public function test_delete_denies_organisation_owner_to_delete_other_organisation_roles(): void
    {
        $this->assertFalse($this->policy->delete($this->ownerUser, $this->anotherOwnerRole));
        $this->assertFalse($this->policy->delete($this->ownerUser, $this->anotherMemberRole));
    }

    public function test_delete_denies_organisation_member(): void
    {
        $this->assertFalse($this->policy->delete($this->memberUser, $this->ownerRole));
        $this->assertFalse($this->policy->delete($this->memberUser, $this->memberRole));
        $this->assertFalse($this->policy->delete($this->memberUser, $this->systemRootRole));
        $this->assertFalse($this->policy->delete($this->memberUser, $this->systemAdminRole));
    }

    public function test_delete_denies_regular_user(): void
    {
        $this->assertFalse($this->policy->delete($this->regularUser, $this->ownerRole));
        $this->assertFalse($this->policy->delete($this->regularUser, $this->memberRole));
        $this->assertFalse($this->policy->delete($this->regularUser, $this->systemRootRole));
        $this->assertFalse($this->policy->delete($this->regularUser, $this->systemAdminRole));
    }

    // ========================================
    // safeDelete Tests
    // ========================================

    public function test_safe_delete_denies_core_roles_even_for_system_root(): void
    {
        $this->clearTeamContext();
        $this->assertFalse($this->policy->safeDelete($this->systemRootUser, $this->systemRootRole));
        $this->assertFalse($this->policy->safeDelete($this->systemRootUser, $this->systemAdminRole));
        $this->assertFalse($this->policy->safeDelete($this->systemRootUser, $this->ownerRole));
        $this->assertFalse($this->policy->safeDelete($this->systemRootUser, $this->memberRole));
    }

    public function test_safe_delete_denies_core_roles_even_for_system_admin(): void
    {
        $this->clearTeamContext();
        $this->assertFalse($this->policy->safeDelete($this->systemAdminUser, $this->systemRootRole));
        $this->assertFalse($this->policy->safeDelete($this->systemAdminUser, $this->systemAdminRole));
        $this->assertFalse($this->policy->safeDelete($this->systemAdminUser, $this->ownerRole));
        $this->assertFalse($this->policy->safeDelete($this->systemAdminUser, $this->memberRole));
    }

    public function test_safe_delete_allows_non_core_roles_without_users(): void
    {
        $this->clearTeamContext();
        // Create a custom role for testing
        $customRole = Role::create([
            'name' => 'custom_role',
            'guard_name' => 'api',
            'organisation_id' => $this->organisation->id,
        ]);

        $this->assertTrue($this->policy->safeDelete($this->systemRootUser, $customRole));
        $this->assertTrue($this->policy->safeDelete($this->systemAdminUser, $customRole));

        // Set team context for organisation owner check
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);
        $this->assertTrue($this->policy->safeDelete($this->ownerUser, $customRole));
    }

    public function test_safe_delete_denies_roles_with_assigned_users(): void
    {
        // Create a custom role and assign it to a user
        $customRole = Role::create([
            'name' => 'custom_role',
            'guard_name' => 'api',
            'organisation_id' => $this->organisation->id,
        ]);

        // Assign the role to a user
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);
        $this->memberUser->assignRole($customRole);

        $this->assertFalse($this->policy->safeDelete($this->systemRootUser, $customRole));
        $this->assertFalse($this->policy->safeDelete($this->systemAdminUser, $customRole));
        $this->assertFalse($this->policy->safeDelete($this->ownerUser, $customRole));
    }

    // ========================================
    // restore Tests
    // ========================================

    public function test_restore_allows_system_root_to_restore_any_role(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->restore($this->systemRootUser, $this->systemRootRole));
        $this->assertTrue($this->policy->restore($this->systemRootUser, $this->systemAdminRole));
        $this->assertTrue($this->policy->restore($this->systemRootUser, $this->ownerRole));
        $this->assertTrue($this->policy->restore($this->systemRootUser, $this->memberRole));
    }

    public function test_restore_allows_system_admin_to_restore_any_role(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->restore($this->systemAdminUser, $this->systemRootRole));
        $this->assertTrue($this->policy->restore($this->systemAdminUser, $this->systemAdminRole));
        $this->assertTrue($this->policy->restore($this->systemAdminUser, $this->ownerRole));
        $this->assertTrue($this->policy->restore($this->systemAdminUser, $this->memberRole));
    }

    public function test_restore_allows_organisation_owner_to_restore_own_organisation_roles(): void
    {
        // Set team context for organisation owner check
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);
        $this->assertTrue($this->policy->restore($this->ownerUser, $this->ownerRole));
        $this->assertTrue($this->policy->restore($this->ownerUser, $this->memberRole));
    }

    public function test_restore_denies_organisation_owner_to_restore_system_roles(): void
    {
        // Set team context for organisation owner check
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);
        $this->assertFalse($this->policy->restore($this->ownerUser, $this->systemRootRole));
        $this->assertFalse($this->policy->restore($this->ownerUser, $this->systemAdminRole));
    }

    public function test_restore_denies_organisation_owner_to_restore_other_organisation_roles(): void
    {
        $this->assertFalse($this->policy->restore($this->ownerUser, $this->anotherOwnerRole));
        $this->assertFalse($this->policy->restore($this->ownerUser, $this->anotherMemberRole));
    }

    public function test_restore_denies_organisation_member(): void
    {
        $this->assertFalse($this->policy->restore($this->memberUser, $this->ownerRole));
        $this->assertFalse($this->policy->restore($this->memberUser, $this->memberRole));
        $this->assertFalse($this->policy->restore($this->memberUser, $this->systemRootRole));
        $this->assertFalse($this->policy->restore($this->memberUser, $this->systemAdminRole));
    }

    public function test_restore_denies_regular_user(): void
    {
        $this->assertFalse($this->policy->restore($this->regularUser, $this->ownerRole));
        $this->assertFalse($this->policy->restore($this->regularUser, $this->memberRole));
        $this->assertFalse($this->policy->restore($this->regularUser, $this->systemRootRole));
        $this->assertFalse($this->policy->restore($this->regularUser, $this->systemAdminRole));
    }

    // ========================================
    // forceDelete Tests
    // ========================================

    public function test_force_delete_allows_only_system_root(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->forceDelete($this->systemRootUser, $this->systemRootRole));
        $this->assertTrue($this->policy->forceDelete($this->systemRootUser, $this->systemAdminRole));
        $this->assertTrue($this->policy->forceDelete($this->systemRootUser, $this->ownerRole));
        $this->assertTrue($this->policy->forceDelete($this->systemRootUser, $this->memberRole));
    }

    public function test_force_delete_allows_only_system_admin(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->forceDelete($this->systemAdminUser, $this->systemRootRole));
        $this->assertTrue($this->policy->forceDelete($this->systemAdminUser, $this->systemAdminRole));
        $this->assertTrue($this->policy->forceDelete($this->systemAdminUser, $this->ownerRole));
        $this->assertTrue($this->policy->forceDelete($this->systemAdminUser, $this->memberRole));
    }

    public function test_force_delete_denies_organisation_owner(): void
    {
        $this->assertFalse($this->policy->forceDelete($this->ownerUser, $this->ownerRole));
        $this->assertFalse($this->policy->forceDelete($this->ownerUser, $this->memberRole));
        $this->assertFalse($this->policy->forceDelete($this->ownerUser, $this->systemRootRole));
        $this->assertFalse($this->policy->forceDelete($this->ownerUser, $this->systemAdminRole));
    }

    public function test_force_delete_denies_organisation_member(): void
    {
        $this->assertFalse($this->policy->forceDelete($this->memberUser, $this->ownerRole));
        $this->assertFalse($this->policy->forceDelete($this->memberUser, $this->memberRole));
        $this->assertFalse($this->policy->forceDelete($this->memberUser, $this->systemRootRole));
        $this->assertFalse($this->policy->forceDelete($this->memberUser, $this->systemAdminRole));
    }

    public function test_force_delete_denies_regular_user(): void
    {
        $this->assertFalse($this->policy->forceDelete($this->regularUser, $this->ownerRole));
        $this->assertFalse($this->policy->forceDelete($this->regularUser, $this->memberRole));
        $this->assertFalse($this->policy->forceDelete($this->regularUser, $this->systemRootRole));
        $this->assertFalse($this->policy->forceDelete($this->regularUser, $this->systemAdminRole));
    }
}
