<?php

declare(strict_types=1);

namespace Tests\Unit\Constants;

use App\Constants\ErrorCodes;
use PHPUnit\Framework\TestCase;

/**
 * Test ErrorCodes constants
 */
final class ErrorCodesTest extends TestCase
{
    /**
     * Test that all error codes are defined
     */
    public function test_all_error_codes_are_defined(): void
    {
        $expectedCodes = [
            'ORGANISATION_ALREADY_SUSPENDED',
            'INVITATION_EXPIRED',
            'INVITATION_USAGE_LIMIT_REACHED',
            'INVITATION_PROCESSING_ERROR',
            'VERIFICATION_CODE_RETRY_LIMIT',
            'VERIFICATION_CODE_SEND_ERROR',
            'ROLE_ASSIGNMENT_FAILED',
            'ROLE_REMOVAL_FAILED',
            'ROLE_TRANSFER_FAILED',
            'REPORT_ORGANISATION_IDS_REQUIRED',
            'REPORT_MULTIPLE_ORGANISATIONS_NOT_ALLOWED',
            'REPORT_ORGANISATION_ACCESS_DENIED',
            'REPORT_INVALID_ORGANISATION_ID',
            'BUSINESS_LOGIC_ERROR',
            'RESOURCE_STATE_CONFLICT',
        ];

        $allCodes = ErrorCodes::getAllCodes();

        foreach ($expectedCodes as $code) {
            $this->assertContains($code, $allCodes, "Error code {$code} should be defined");
        }
    }

    /**
     * Test that error codes exist method works correctly
     */
    public function test_exists_method_works_correctly(): void
    {
        $this->assertTrue(ErrorCodes::exists('ORGANISATION_ALREADY_SUSPENDED'));
        $this->assertTrue(ErrorCodes::exists('INVITATION_EXPIRED'));
        $this->assertFalse(ErrorCodes::exists('NON_EXISTENT_CODE'));
        $this->assertFalse(ErrorCodes::exists(''));
    }

    /**
     * Test that all error codes are strings
     */
    public function test_all_error_codes_are_strings(): void
    {
        $allCodes = ErrorCodes::getAllCodes();

        foreach ($allCodes as $code) {
            $this->assertIsString($code, 'All error codes should be strings');
            $this->assertNotEmpty($code, 'Error codes should not be empty');
        }
    }

    /**
     * Test that error codes follow naming convention
     */
    public function test_error_codes_follow_naming_convention(): void
    {
        $allCodes = ErrorCodes::getAllCodes();

        foreach ($allCodes as $code) {
            $this->assertMatchesRegularExpression(
                '/^[A-Z_]+$/',
                $code,
                "Error code {$code} should be in UPPER_CASE format"
            );
        }
    }
}
