<?php

declare(strict_types=1);

namespace Tests\Unit\Models;

use App\Models\Order;
use App\Models\OrderItem;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

final class OrderTest extends TestCase
{
    use RefreshDatabase;

    public function test_order_can_be_created(): void
    {
        $order = Order::create([
            'store_order_id' => 12345,
            'order_number' => 'ORD-2025-001',
            'state' => 'completed',
            'completed_at' => now(),
            'items_total' => 10000, // $100.00 in cents
            'adjustments_total' => -500, // -$5.00 discount
            'total_amount' => 9500, // $95.00 final total
            'currency_code' => 'USD',
            'payment_state' => 'completed',
            'shipping_country' => 'US',
            'customer_id' => 67890,
        ]);

        $this->assertInstanceOf(Order::class, $order);
        $this->assertEquals(12345, $order->store_order_id);
        $this->assertEquals('ORD-2025-001', $order->order_number);
        $this->assertEquals('completed', $order->state);
        $this->assertEquals(10000, $order->items_total);
        $this->assertEquals(-500, $order->adjustments_total);
        $this->assertEquals(9500, $order->total_amount);
        $this->assertEquals('USD', $order->currency_code);
    }

    public function test_order_currency_accessors(): void
    {
        $order = Order::create([
            'store_order_id' => 12345,
            'state' => 'completed',
            'completed_at' => now(),
            'items_total' => 10000,
            'adjustments_total' => -500,
            'total_amount' => 9500,
            'currency_code' => 'USD',
        ]);

        $this->assertEquals(100.0, $order->items_total_in_currency);
        $this->assertEquals(-5.0, $order->adjustments_total_in_currency);
        $this->assertEquals(95.0, $order->total_amount_in_currency);
    }

    public function test_order_status_methods(): void
    {
        $completedOrder = Order::create([
            'store_order_id' => 12345,
            'state' => 'completed',
            'completed_at' => now(),
            'items_total' => 10000,
            'total_amount' => 10000,
            'currency_code' => 'USD',
            'payment_state' => 'completed',
        ]);

        $this->assertTrue($completedOrder->isCompleted());
        $this->assertFalse($completedOrder->isCancelled());
        $this->assertTrue($completedOrder->isPaymentCompleted());

        $cancelledOrder = Order::create([
            'store_order_id' => 12346,
            'state' => 'cancelled',
            'completed_at' => now(),
            'items_total' => 5000,
            'total_amount' => 5000,
            'currency_code' => 'USD',
        ]);

        $this->assertFalse($cancelledOrder->isCompleted());
        $this->assertTrue($cancelledOrder->isCancelled());
    }

    public function test_order_refund_methods(): void
    {
        $orderWithRefund = Order::create([
            'store_order_id' => 12345,
            'state' => 'completed',
            'completed_at' => now(),
            'items_total' => 10000,
            'total_amount' => 10000,
            'currency_code' => 'USD',
            'refund_total' => 2000,
            'refund_comment' => 'Customer requested refund',
            'refund_status' => 'success',
            'refunded_at' => now(),
        ]);

        $this->assertTrue($orderWithRefund->hasRefund());
        $this->assertEquals(20.0, $orderWithRefund->refund_total_in_currency);

        $orderWithoutRefund = Order::create([
            'store_order_id' => 12346,
            'state' => 'completed',
            'completed_at' => now(),
            'items_total' => 5000,
            'total_amount' => 5000,
            'currency_code' => 'USD',
            'refund_status' => null, // No refund status
        ]);

        $this->assertFalse($orderWithoutRefund->hasRefund());
        $this->assertNull($orderWithoutRefund->refund_total_in_currency);
    }

    public function test_order_has_many_order_items(): void
    {
        $order = Order::create([
            'store_order_id' => 12345,
            'state' => 'completed',
            'completed_at' => now(),
            'items_total' => 10000,
            'total_amount' => 10000,
            'currency_code' => 'USD',
        ]);

        $orderItem = OrderItem::create([
            'order_id' => $order->id,
            'store_order_item_id' => 1,
            'store_variant_id' => 100,
            'product_name' => 'Test Product',
            'quantity' => 2,
            'unit_price' => 2500,
            'units_total' => 5000,
            'total' => 5000,
        ]);

        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Collection::class, $order->orderItems);
        $this->assertCount(1, $order->orderItems);
        $this->assertEquals($orderItem->id, $order->orderItems->first()->id);
    }

    public function test_scope_with_refunds_filters_correctly(): void
    {
        // Create order with successful refund
        $orderWithSuccessfulRefund = Order::create([
            'store_order_id' => 12345,
            'state' => 'completed',
            'completed_at' => now(),
            'items_total' => 10000,
            'total_amount' => 10000,
            'currency_code' => 'USD',
            'refund_total' => 2000,
            'refund_status' => 'success',
            'refunded_at' => now(),
        ]);

        // Create order with failed refund (should not be included)
        Order::create([
            'store_order_id' => 12346,
            'state' => 'completed',
            'completed_at' => now(),
            'items_total' => 5000,
            'total_amount' => 5000,
            'currency_code' => 'USD',
            'refund_total' => 1000,
            'refund_status' => 'failed',
            'refunded_at' => now(),
        ]);

        // Create order with pending refund (should not be included)
        Order::create([
            'store_order_id' => 12347,
            'state' => 'completed',
            'completed_at' => now(),
            'items_total' => 3000,
            'total_amount' => 3000,
            'currency_code' => 'USD',
            'refund_total' => 500,
            'refund_status' => 'pending',
            'refunded_at' => null,
        ]);

        // Create order without refund
        Order::create([
            'store_order_id' => 12348,
            'state' => 'completed',
            'completed_at' => now(),
            'items_total' => 8000,
            'total_amount' => 8000,
            'currency_code' => 'USD',
            'refund_status' => null,
        ]);

        // Test scope returns only orders with successful refunds
        $ordersWithRefunds = Order::withRefunds()->get();

        $this->assertCount(1, $ordersWithRefunds);
        $this->assertEquals($orderWithSuccessfulRefund->id, $ordersWithRefunds->first()->id);
        $this->assertEquals('success', $ordersWithRefunds->first()->refund_status);
    }

    public function test_has_refund_method_with_different_statuses(): void
    {
        // Test order with successful refund
        $orderWithSuccessfulRefund = Order::create([
            'store_order_id' => 12345,
            'state' => 'completed',
            'completed_at' => now(),
            'items_total' => 10000,
            'total_amount' => 10000,
            'currency_code' => 'USD',
            'refund_total' => 2000,
            'refund_status' => 'success',
            'refunded_at' => now(),
        ]);

        $this->assertTrue($orderWithSuccessfulRefund->hasRefund());

        // Test order with failed refund
        $orderWithFailedRefund = Order::create([
            'store_order_id' => 12346,
            'state' => 'completed',
            'completed_at' => now(),
            'items_total' => 5000,
            'total_amount' => 5000,
            'currency_code' => 'USD',
            'refund_total' => 1000,
            'refund_status' => 'failed',
            'refunded_at' => now(),
        ]);

        $this->assertFalse($orderWithFailedRefund->hasRefund());

        // Test order with pending refund
        $orderWithPendingRefund = Order::create([
            'store_order_id' => 12347,
            'state' => 'completed',
            'completed_at' => now(),
            'items_total' => 3000,
            'total_amount' => 3000,
            'currency_code' => 'USD',
            'refund_total' => 500,
            'refund_status' => 'pending',
            'refunded_at' => null,
        ]);

        $this->assertFalse($orderWithPendingRefund->hasRefund());

        // Test order with null refund status
        $orderWithNullRefundStatus = Order::create([
            'store_order_id' => 12348,
            'state' => 'completed',
            'completed_at' => now(),
            'items_total' => 8000,
            'total_amount' => 8000,
            'currency_code' => 'USD',
            'refund_status' => null,
        ]);

        $this->assertFalse($orderWithNullRefundStatus->hasRefund());
    }
}
