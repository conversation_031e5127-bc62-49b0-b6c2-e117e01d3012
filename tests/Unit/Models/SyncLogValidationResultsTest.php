<?php

declare(strict_types=1);

namespace Tests\Unit\Models;

use App\Models\SyncLog;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

final class SyncLogValidationResultsTest extends TestCase
{
    use RefreshDatabase;

    public function test_sync_log_can_store_validation_results(): void
    {
        $validationResults = [
            'sync_log_id' => 123,
            'batch_id' => 'test-batch-456',
            'validation_started_at' => now(),
            'overall_status' => 'passed',
            'data_integrity' => [
                'status' => 'passed',
                'record_counts' => [
                    'remote' => ['orders' => 100, 'order_items' => 250],
                    'local' => ['orders' => 100, 'order_items' => 250],
                    'matches' => true,
                ],
                'field_checksums' => [
                    'orders' => ['checksum_match' => true],
                    'order_items' => ['checksum_match' => true],
                ],
                'issues' => [],
            ],
            'sampling_validation' => [
                'status' => 'passed',
                'sample_size' => 10,
                'matched_records' => 10,
                'mismatched_records' => 0,
                'mismatches' => [],
            ],
            'business_logic' => [
                'status' => 'passed',
                'state_distribution' => ['new' => 50, 'fulfilled' => 50],
                'refund_status_distribution' => ['none' => 90, 'partial' => 10],
                'issues' => [],
            ],
            'relationship_consistency' => [
                'status' => 'passed',
                'order_items_consistency' => ['status' => 'passed'],
                'issues' => [],
            ],
            'issues' => [],
            'validation_completed_at' => now(),
        ];

        $syncLog = SyncLog::create([
            'sync_type' => 'order_sync',
            'batch_id' => 'test-batch-456',
            'status' => 'completed',
            'started_at' => now()->subMinutes(10),
            'completed_at' => now()->subMinutes(5),
            'total_records' => 100,
            'processed_records' => 100,
            'success_records' => 100,
            'failed_records' => 0,
            'validation_results' => $validationResults,
        ]);

        $this->assertNotNull($syncLog->validation_results);
        $this->assertIsArray($syncLog->validation_results);
        $this->assertEquals('passed', $syncLog->validation_results['overall_status']);
        $this->assertEquals(123, $syncLog->validation_results['sync_log_id']);
        $this->assertEquals('test-batch-456', $syncLog->validation_results['batch_id']);
        
        // Check that all validation sections are present
        $this->assertArrayHasKey('data_integrity', $syncLog->validation_results);
        $this->assertArrayHasKey('sampling_validation', $syncLog->validation_results);
        $this->assertArrayHasKey('business_logic', $syncLog->validation_results);
        $this->assertArrayHasKey('relationship_consistency', $syncLog->validation_results);
        $this->assertArrayHasKey('issues', $syncLog->validation_results);
    }

    public function test_sync_log_can_store_failed_validation_results(): void
    {
        $validationResults = [
            'sync_log_id' => 456,
            'batch_id' => 'test-batch-789',
            'validation_started_at' => now(),
            'overall_status' => 'failed',
            'data_integrity' => [
                'status' => 'failed',
                'record_counts' => [
                    'remote' => ['orders' => 100, 'order_items' => 250],
                    'local' => ['orders' => 95, 'order_items' => 240],
                    'matches' => false,
                ],
                'issues' => ['Record count mismatch: orders'],
            ],
            'sampling_validation' => [
                'status' => 'passed',
                'sample_size' => 5,
                'matched_records' => 5,
                'mismatched_records' => 0,
                'mismatches' => [],
            ],
            'business_logic' => [
                'status' => 'passed',
                'issues' => [],
            ],
            'relationship_consistency' => [
                'status' => 'passed',
                'issues' => [],
            ],
            'issues' => ['Critical data integrity issue'],
            'validation_completed_at' => now(),
        ];

        $syncLog = SyncLog::create([
            'sync_type' => 'order_sync',
            'batch_id' => 'test-batch-789',
            'status' => 'completed',
            'started_at' => now()->subMinutes(10),
            'completed_at' => now()->subMinutes(5),
            'total_records' => 100,
            'processed_records' => 100,
            'success_records' => 95,
            'failed_records' => 5,
            'validation_results' => $validationResults,
        ]);

        $this->assertNotNull($syncLog->validation_results);
        $this->assertEquals('failed', $syncLog->validation_results['overall_status']);
        $this->assertNotEmpty($syncLog->validation_results['issues']);
        $this->assertEquals('Critical data integrity issue', $syncLog->validation_results['issues'][0]);
        
        // Check that data integrity shows failure
        $this->assertEquals('failed', $syncLog->validation_results['data_integrity']['status']);
        $this->assertFalse($syncLog->validation_results['data_integrity']['record_counts']['matches']);
    }

    public function test_sync_log_can_store_error_validation_results(): void
    {
        $validationResults = [
            'sync_log_id' => 789,
            'batch_id' => 'test-batch-error',
            'validation_started_at' => now(),
            'overall_status' => 'error',
            'error_message' => 'Database connection failed',
            'validation_completed_at' => now(),
        ];

        $syncLog = SyncLog::create([
            'sync_type' => 'order_sync',
            'batch_id' => 'test-batch-error',
            'status' => 'completed',
            'started_at' => now()->subMinutes(10),
            'completed_at' => now()->subMinutes(5),
            'total_records' => 100,
            'processed_records' => 100,
            'success_records' => 100,
            'failed_records' => 0,
            'validation_results' => $validationResults,
        ]);

        $this->assertNotNull($syncLog->validation_results);
        $this->assertEquals('error', $syncLog->validation_results['overall_status']);
        $this->assertEquals('Database connection failed', $syncLog->validation_results['error_message']);
        $this->assertEquals(789, $syncLog->validation_results['sync_log_id']);
    }

    public function test_sync_log_validation_results_can_be_updated(): void
    {
        $syncLog = SyncLog::create([
            'sync_type' => 'order_sync',
            'batch_id' => 'test-batch-update',
            'status' => 'completed',
            'started_at' => now()->subMinutes(10),
            'completed_at' => now()->subMinutes(5),
            'total_records' => 100,
            'processed_records' => 100,
            'success_records' => 100,
            'failed_records' => 0,
        ]);

        $this->assertNull($syncLog->validation_results);

        $validationResults = [
            'sync_log_id' => $syncLog->id,
            'batch_id' => 'test-batch-update',
            'overall_status' => 'passed',
            'validation_completed_at' => now(),
        ];

        $syncLog->update(['validation_results' => $validationResults]);

        $this->assertNotNull($syncLog->validation_results);
        $this->assertEquals('passed', $syncLog->validation_results['overall_status']);
        $this->assertEquals($syncLog->id, $syncLog->validation_results['sync_log_id']);
    }

    public function test_sync_log_validation_results_field_is_fillable(): void
    {
        $fillableFields = (new SyncLog())->getFillable();
        
        $this->assertContains('validation_results', $fillableFields);
    }

    public function test_sync_log_validation_results_field_is_cast_to_array(): void
    {
        $casts = (new SyncLog())->getCasts();
        
        $this->assertArrayHasKey('validation_results', $casts);
        $this->assertEquals('array', $casts['validation_results']);
    }
}
