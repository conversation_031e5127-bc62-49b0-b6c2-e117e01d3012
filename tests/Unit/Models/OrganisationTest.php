<?php

declare(strict_types=1);

namespace Tests\Unit\Models;

use App\Models\Organisation;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

final class OrganisationTest extends TestCase
{
    use RefreshDatabase;

    public function test_organisation_can_be_created(): void
    {
        $organisation = Organisation::factory()->create([
            'name' => 'Test Organisation',
            'code' => 'TEST001',
            'status' => 'active',
        ]);

        $this->assertDatabaseHas('organisations', [
            'name' => 'Test Organisation',
            'code' => 'TEST001',
            'status' => 'active',
        ]);

        $this->assertEquals('Test Organisation', $organisation->name);
        $this->assertEquals('TEST001', $organisation->code);
        $this->assertEquals('active', $organisation->status);
    }

    public function test_organisation_has_users_relationship(): void
    {
        $organisation = Organisation::factory()->create();
        $user = User::factory()->create();
        $user->organisations()->attach($organisation->id);

        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Relations\BelongsToMany::class, $organisation->users());
        $this->assertTrue($organisation->users->contains($user));
        $this->assertTrue($user->organisations->contains($organisation));
    }

    public function test_organisation_status_methods(): void
    {
        $pendingOrg = Organisation::factory()->pending()->create();
        $activeOrg = Organisation::factory()->active()->create();
        $suspendedOrg = Organisation::factory()->suspended()->create();

        $this->assertTrue($pendingOrg->isPending());
        $this->assertFalse($pendingOrg->isActive());
        $this->assertFalse($pendingOrg->isSuspended());

        $this->assertTrue($activeOrg->isActive());
        $this->assertFalse($activeOrg->isPending());
        $this->assertFalse($activeOrg->isSuspended());

        $this->assertTrue($suspendedOrg->isSuspended());
        $this->assertFalse($suspendedOrg->isPending());
        $this->assertFalse($suspendedOrg->isActive());
    }



    public function test_organisation_can_be_suspended(): void
    {
        $organisation = Organisation::factory()->active()->create();

        $this->assertTrue($organisation->isActive());

        $result = $organisation->suspend();

        $this->assertTrue($result);
        $this->assertTrue($organisation->fresh()->isSuspended());
    }

    public function test_organisation_details_are_cast_to_array(): void
    {
        $details = ['industry' => 'Technology', 'size' => 'Large'];
        $organisation = Organisation::factory()->create(['details' => $details]);

        $this->assertIsArray($organisation->details);
        $this->assertEquals($details, $organisation->details);
    }

    public function test_organisation_fillable_attributes(): void
    {
        $data = [
            'name' => 'Test Org',
            'code' => 'TEST123',
            'details' => ['key' => 'value'],
            'remarks' => 'Test remarks',
            'status' => 'active',
        ];

        $organisation = Organisation::create($data);

        $this->assertEquals($data['name'], $organisation->name);
        $this->assertEquals($data['code'], $organisation->code);
        $this->assertEquals($data['details'], $organisation->details);
        $this->assertEquals($data['remarks'], $organisation->remarks);
        $this->assertEquals($data['status'], $organisation->status);
    }
}
