<?php

declare(strict_types=1);

namespace Tests\Unit\Models;

use App\Models\Order;
use App\Models\OrderItem;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

final class OrderItemTest extends TestCase
{
    use RefreshDatabase;

    public function test_order_item_can_be_created(): void
    {
        $order = Order::create([
            'store_order_id' => 12345,
            'state' => 'completed',
            'completed_at' => now(),
            'items_total' => 5000,
            'total_amount' => 5000,
            'currency_code' => 'USD',
        ]);

        $orderItem = OrderItem::create([
            'order_id' => $order->id,
            'store_order_item_id' => 1,
            'store_variant_id' => 100,
            'product_name' => 'Test Product',
            'variant_name' => 'Red - Large',
            'quantity' => 2,
            'unit_price' => 2500, // $25.00 in cents
            'units_total' => 5000, // $50.00
            'adjustments_total' => 0,
            'total' => 5000,
            'quantity_refunded' => 0,
        ]);

        $this->assertInstanceOf(OrderItem::class, $orderItem);
        $this->assertEquals($order->id, $orderItem->order_id);
        $this->assertEquals(1, $orderItem->store_order_item_id);
        $this->assertEquals(100, $orderItem->store_variant_id);
        $this->assertEquals('Test Product', $orderItem->product_name);
        $this->assertEquals('Red - Large', $orderItem->variant_name);
        $this->assertEquals(2, $orderItem->quantity);
        $this->assertEquals(2500, $orderItem->unit_price);
        $this->assertEquals(5000, $orderItem->units_total);
        $this->assertEquals(5000, $orderItem->total);
    }

    public function test_order_item_currency_accessors(): void
    {
        $order = Order::create([
            'store_order_id' => 12345,
            'state' => 'completed',
            'completed_at' => now(),
            'items_total' => 5000,
            'total_amount' => 5000,
            'currency_code' => 'USD',
        ]);

        $orderItem = OrderItem::create([
            'order_id' => $order->id,
            'store_order_item_id' => 1,
            'store_variant_id' => 100,
            'product_name' => 'Test Product',
            'quantity' => 2,
            'unit_price' => 2500,
            'units_total' => 5000,
            'adjustments_total' => -215, // $2.00 discount
            'total' => 4785, // $48.00 final
        ]);

        $this->assertEquals(25.0, $orderItem->unit_price_in_currency);
        $this->assertEquals(50.0, $orderItem->units_total_in_currency);
        $this->assertEquals(-2.15, $orderItem->adjustments_total_in_currency);
        $this->assertEquals(47.85, $orderItem->total_in_currency);
    }

    public function test_order_item_refund_methods(): void
    {
        $order = Order::create([
            'store_order_id' => 12345,
            'state' => 'completed',
            'completed_at' => now(),
            'items_total' => 5000,
            'total_amount' => 5000,
            'currency_code' => 'USD',
        ]);

        // Test partially refunded item
        $partiallyRefundedItem = OrderItem::create([
            'order_id' => $order->id,
            'store_order_item_id' => 1,
            'store_variant_id' => 100,
            'product_name' => 'Test Product',
            'quantity' => 3,
            'unit_price' => 1000,
            'units_total' => 3000,
            'total' => 3000,
            'quantity_refunded' => 1,
        ]);

        $this->assertTrue($partiallyRefundedItem->hasRefund());
        $this->assertFalse($partiallyRefundedItem->isFullyRefunded());
        $this->assertEquals(2, $partiallyRefundedItem->getRemainingQuantity());
        $this->assertEquals(33.33, round($partiallyRefundedItem->getRefundPercentage(), 2));
        $this->assertEquals(2000, $partiallyRefundedItem->getEffectiveTotal());
        $this->assertEquals(20.0, $partiallyRefundedItem->effective_total_in_currency);

        // Test fully refunded item
        $fullyRefundedItem = OrderItem::create([
            'order_id' => $order->id,
            'store_order_item_id' => 2,
            'store_variant_id' => 101,
            'product_name' => 'Another Product',
            'quantity' => 2,
            'unit_price' => 1000,
            'units_total' => 2000,
            'total' => 2000,
            'quantity_refunded' => 2,
        ]);

        $this->assertTrue($fullyRefundedItem->hasRefund());
        $this->assertTrue($fullyRefundedItem->isFullyRefunded());
        $this->assertEquals(0, $fullyRefundedItem->getRemainingQuantity());
        $this->assertEquals(100.0, $fullyRefundedItem->getRefundPercentage());
        $this->assertEquals(0, $fullyRefundedItem->getEffectiveTotal());

        // Test non-refunded item
        $nonRefundedItem = OrderItem::create([
            'order_id' => $order->id,
            'store_order_item_id' => 3,
            'store_variant_id' => 102,
            'product_name' => 'Third Product',
            'quantity' => 1,
            'unit_price' => 1500,
            'units_total' => 1500,
            'total' => 1500,
            'quantity_refunded' => 0,
        ]);

        $this->assertFalse($nonRefundedItem->hasRefund());
        $this->assertFalse($nonRefundedItem->isFullyRefunded());
        $this->assertEquals(1, $nonRefundedItem->getRemainingQuantity());
        $this->assertEquals(0.0, $nonRefundedItem->getRefundPercentage());
        $this->assertEquals(1500, $nonRefundedItem->getEffectiveTotal());
    }

    public function test_order_item_belongs_to_order(): void
    {
        $order = Order::create([
            'store_order_id' => 12345,
            'state' => 'completed',
            'completed_at' => now(),
            'items_total' => 5000,
            'total_amount' => 5000,
            'currency_code' => 'USD',
        ]);

        $orderItem = OrderItem::create([
            'order_id' => $order->id,
            'store_order_item_id' => 1,
            'store_variant_id' => 100,
            'product_name' => 'Test Product',
            'quantity' => 1,
            'unit_price' => 5000,
            'units_total' => 5000,
            'total' => 5000,
        ]);

        $this->assertInstanceOf(Order::class, $orderItem->order);
        $this->assertEquals($order->id, $orderItem->order->id);
        $this->assertEquals($order->store_order_id, $orderItem->order->store_order_id);
    }
}
