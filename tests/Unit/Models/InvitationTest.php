<?php

declare(strict_types=1);

namespace Tests\Unit\Models;

use App\Models\Invitation;
use App\Models\Organisation;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Carbon;
use Tests\TestCase;

final class InvitationTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Organisation $organisation;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->organisation = Organisation::factory()->create();
    }

    public function test_invitation_has_uuid_primary_key(): void
    {
        $invitation = Invitation::factory()->create();

        $this->assertIsString($invitation->id);
        $this->assertMatchesRegularExpression('/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/', $invitation->id);
    }

    public function test_invitation_belongs_to_created_by_user(): void
    {
        $invitation = Invitation::factory()->create([
            'created_by_user_id' => $this->user->id,
        ]);

        $this->assertInstanceOf(User::class, $invitation->createdBy);
        $this->assertEquals($this->user->id, $invitation->createdBy->id);
    }

    public function test_invitation_has_polymorphic_model_relationship(): void
    {
        $invitation = Invitation::factory()->create([
            'model_type' => Organisation::class,
            'model_id' => $this->organisation->id,
        ]);

        $this->assertInstanceOf(Organisation::class, $invitation->model);
        $this->assertEquals($this->organisation->id, $invitation->model->id);
    }

    public function test_isExpired_returns_true_when_expired(): void
    {
        $invitation = Invitation::factory()->create([
            'expires_at' => Carbon::now()->subDay(),
        ]);

        $this->assertTrue($invitation->isExpired());
    }

    public function test_isExpired_returns_false_when_not_expired(): void
    {
        $invitation = Invitation::factory()->create([
            'expires_at' => Carbon::now()->addDay(),
        ]);

        $this->assertFalse($invitation->isExpired());
    }

    public function test_hasReachedUsageLimit_returns_true_when_limit_reached(): void
    {
        $invitation = Invitation::factory()->create([
            'max_uses' => 5,
            'uses' => 5,
        ]);

        $this->assertTrue($invitation->hasReachedUsageLimit());
    }

    public function test_hasReachedUsageLimit_returns_false_when_limit_not_reached(): void
    {
        $invitation = Invitation::factory()->create([
            'max_uses' => 5,
            'uses' => 3,
        ]);

        $this->assertFalse($invitation->hasReachedUsageLimit());
    }

    public function test_isValid_returns_true_when_not_expired_and_not_reached_limit(): void
    {
        $invitation = Invitation::factory()->create([
            'expires_at' => Carbon::now()->addDay(),
            'max_uses' => 5,
            'uses' => 3,
        ]);

        $this->assertTrue($invitation->isValid());
    }

    public function test_isValid_returns_false_when_expired(): void
    {
        $invitation = Invitation::factory()->create([
            'expires_at' => Carbon::now()->subDay(),
            'max_uses' => 5,
            'uses' => 3,
        ]);

        $this->assertFalse($invitation->isValid());
    }

    public function test_isValid_returns_false_when_usage_limit_reached(): void
    {
        $invitation = Invitation::factory()->create([
            'expires_at' => Carbon::now()->addDay(),
            'max_uses' => 5,
            'uses' => 5,
        ]);

        $this->assertFalse($invitation->isValid());
    }

    public function test_isEmailAllowed_returns_true_when_no_restriction(): void
    {
        $invitation = Invitation::factory()->create([
            'email_restriction' => null,
        ]);

        $this->assertTrue($invitation->isEmailAllowed('<EMAIL>'));
        $this->assertTrue($invitation->isEmailAllowed(null));
    }

    public function test_isEmailAllowed_returns_true_when_email_matches_restriction(): void
    {
        $invitation = Invitation::factory()->create([
            'email_restriction' => '<EMAIL>',
        ]);

        $this->assertTrue($invitation->isEmailAllowed('<EMAIL>'));
    }

    public function test_isEmailAllowed_returns_false_when_email_does_not_match_restriction(): void
    {
        $invitation = Invitation::factory()->create([
            'email_restriction' => '<EMAIL>',
        ]);

        $this->assertFalse($invitation->isEmailAllowed('<EMAIL>'));
        $this->assertFalse($invitation->isEmailAllowed(null));
    }

    public function test_incrementUsage_increases_uses_count(): void
    {
        $invitation = Invitation::factory()->create([
            'uses' => 2,
        ]);

        $invitation->incrementUsage();

        $this->assertEquals(3, $invitation->fresh()->uses);
    }

    public function test_isSystemRole_returns_true_for_system_roles(): void
    {
        $rootInvitation = Invitation::factory()->create(['role' => 'root']);
        $adminInvitation = Invitation::factory()->create(['role' => 'admin']);

        $this->assertTrue($rootInvitation->isSystemRole());
        $this->assertTrue($adminInvitation->isSystemRole());
    }

    public function test_isSystemRole_returns_false_for_non_system_roles(): void
    {
        $ownerInvitation = Invitation::factory()->create(['role' => 'owner']);
        $memberInvitation = Invitation::factory()->create(['role' => 'member']);

        $this->assertFalse($ownerInvitation->isSystemRole());
        $this->assertFalse($memberInvitation->isSystemRole());
    }

    public function test_valid_scope_returns_only_valid_invitations(): void
    {
        // Create valid invitation
        $validInvitation = Invitation::factory()->create([
            'expires_at' => Carbon::now()->addDay(),
            'max_uses' => 5,
            'uses' => 3,
        ]);

        // Create expired invitation
        Invitation::factory()->create([
            'expires_at' => Carbon::now()->subDay(),
            'max_uses' => 5,
            'uses' => 3,
        ]);

        // Create invitation with reached usage limit
        Invitation::factory()->create([
            'expires_at' => Carbon::now()->addDay(),
            'max_uses' => 5,
            'uses' => 5,
        ]);

        $validInvitations = Invitation::valid()->get();

        $this->assertCount(1, $validInvitations);
        $this->assertEquals($validInvitation->id, $validInvitations->first()->id);
    }

    public function test_boot_sets_default_expiration_when_creating(): void
    {
        $invitation = new Invitation([
            'model_type' => Organisation::class,
            'model_id' => $this->organisation->id,
            'role' => 'member',
            'created_by_user_id' => $this->user->id,
        ]);

        $invitation->save();

        $this->assertNotNull($invitation->expires_at);
        $this->assertTrue($invitation->expires_at->isAfter(Carbon::now()));
        $this->assertTrue($invitation->expires_at->isBefore(Carbon::now()->addWeek()->addMinute()));
    }

    public function test_boot_does_not_override_provided_expiration(): void
    {
        $customExpiration = Carbon::now()->addDays(3);

        $invitation = new Invitation([
            'model_type' => Organisation::class,
            'model_id' => $this->organisation->id,
            'role' => 'member',
            'created_by_user_id' => $this->user->id,
            'expires_at' => $customExpiration,
        ]);

        $invitation->save();

        $this->assertEquals($customExpiration->format('Y-m-d H:i:s'), $invitation->expires_at->format('Y-m-d H:i:s'));
    }

    public function test_casts_are_properly_configured(): void
    {
        $invitation = Invitation::factory()->create([
            'expires_at' => '2025-12-31 23:59:59',
            'max_uses' => '10',
            'uses' => '5',
        ]);

        $this->assertInstanceOf(Carbon::class, $invitation->expires_at);
        $this->assertIsInt($invitation->max_uses);
        $this->assertIsInt($invitation->uses);
    }

    public function test_fillable_attributes_are_properly_configured(): void
    {
        $data = [
            'model_type' => Organisation::class,
            'model_id' => $this->organisation->id,
            'role' => 'member',
            'created_by_user_id' => $this->user->id,
            'expires_at' => Carbon::now()->addWeek(),
            'max_uses' => 10,
            'uses' => 0,
            'email_restriction' => '<EMAIL>',
        ];

        $invitation = Invitation::create($data);

        foreach ($data as $key => $value) {
            if ($key === 'expires_at') {
                $this->assertEquals($value->format('Y-m-d H:i:s'), $invitation->$key->format('Y-m-d H:i:s'));
            } else {
                $this->assertEquals($value, $invitation->$key);
            }
        }
    }
}
