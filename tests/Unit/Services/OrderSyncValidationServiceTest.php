<?php

declare(strict_types=1);

namespace Tests\Unit\Services;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\SyncLog;
use App\Services\OrderSyncValidationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

/**
 * Test class for OrderSyncValidationService
 *
 * This service requires database operations and external connections,
 * so these are integration tests rather than pure unit tests.
 */
final class OrderSyncValidationServiceTest extends TestCase
{
    use RefreshDatabase;

    private OrderSyncValidationService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new OrderSyncValidationService();
    }

    public function test_validate_sync_returns_proper_structure(): void
    {
        $syncLog = SyncLog::factory()->create([
            'sync_type' => 'order_sync',
            'status' => 'completed',
            'sync_config' => ['incremental' => false],
        ]);

        $result = $this->service->validateSync($syncLog);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('sync_log_id', $result);
        $this->assertArrayHasKey('batch_id', $result);
        $this->assertArrayHasKey('validation_started_at', $result);
        $this->assertArrayHasKey('data_integrity', $result);
        $this->assertArrayHasKey('sampling_validation', $result);
        $this->assertArrayHasKey('business_logic', $result);
        $this->assertArrayHasKey('relationship_consistency', $result);
        $this->assertArrayHasKey('overall_status', $result);
        $this->assertArrayHasKey('issues', $result);
        
        $this->assertEquals($syncLog->id, $result['sync_log_id']);
        $this->assertEquals($syncLog->batch_id, $result['batch_id']);
    }

    public function test_validate_sync_handles_exception(): void
    {
        // Create a sync log with invalid config to trigger exception
        $syncLog = SyncLog::factory()->create([
            'sync_type' => 'order_sync',
            'status' => 'completed',
            'sync_config' => null,
        ]);

        // Create a service that will throw an exception
        $mockService = $this->getMockBuilder(OrderSyncValidationService::class)
            ->onlyMethods(['validateDataIntegrity'])
            ->getMock();

        $mockService->method('validateDataIntegrity')
            ->willThrowException(new \Exception('Database connection failed'));

        $result = $mockService->validateSync($syncLog);

        $this->assertEquals('error', $result['overall_status']);
        $this->assertArrayHasKey('error_message', $result);
        $this->assertEquals('Database connection failed', $result['error_message']);
    }

    public function test_data_integrity_validation_structure(): void
    {
        $syncLog = SyncLog::factory()->create([
            'sync_type' => 'order_sync',
            'status' => 'completed',
            'sync_config' => ['incremental' => false],
        ]);

        // Create some test data
        Order::factory()->count(3)->create();
        OrderItem::factory()->count(5)->create();

        $result = $this->service->validateSync($syncLog);

        $this->assertArrayHasKey('data_integrity', $result);
        $dataIntegrity = $result['data_integrity'];
        
        $this->assertArrayHasKey('record_counts', $dataIntegrity);
        $this->assertArrayHasKey('field_checksums', $dataIntegrity);
        $this->assertArrayHasKey('status', $dataIntegrity);
        $this->assertArrayHasKey('issues', $dataIntegrity);

        // Check record counts structure
        $recordCounts = $dataIntegrity['record_counts'];
        $this->assertArrayHasKey('remote', $recordCounts);
        $this->assertArrayHasKey('local', $recordCounts);
        $this->assertArrayHasKey('matches', $recordCounts);
        
        // Check field checksums structure
        $fieldChecksums = $dataIntegrity['field_checksums'];
        $this->assertArrayHasKey('remote', $fieldChecksums);
        $this->assertArrayHasKey('local', $fieldChecksums);
        $this->assertArrayHasKey('matches', $fieldChecksums);
    }

    public function test_sampling_validation_structure(): void
    {
        $syncLog = SyncLog::factory()->create([
            'sync_type' => 'order_sync',
            'status' => 'completed',
            'sync_config' => ['incremental' => false],
        ]);

        $result = $this->service->validateSync($syncLog);

        $this->assertArrayHasKey('sampling_validation', $result);
        $samplingValidation = $result['sampling_validation'];
        
        $this->assertArrayHasKey('sample_size', $samplingValidation);
        $this->assertArrayHasKey('matched_records', $samplingValidation);
        $this->assertArrayHasKey('mismatched_records', $samplingValidation);
        $this->assertArrayHasKey('sample_percentage', $samplingValidation);
        $this->assertArrayHasKey('status', $samplingValidation);
        $this->assertArrayHasKey('issues', $samplingValidation);
        $this->assertArrayHasKey('mismatches', $samplingValidation);
        
        $this->assertEquals(0.1, $samplingValidation['sample_percentage']);
    }

    public function test_business_logic_validation_structure(): void
    {
        $syncLog = SyncLog::factory()->create([
            'sync_type' => 'order_sync',
            'status' => 'completed',
            'sync_config' => ['incremental' => false],
        ]);

        $result = $this->service->validateSync($syncLog);

        $this->assertArrayHasKey('business_logic', $result);
        $businessLogic = $result['business_logic'];
        
        $this->assertArrayHasKey('state_distribution', $businessLogic);
        $this->assertArrayHasKey('refund_status_distribution', $businessLogic);
        $this->assertArrayHasKey('status', $businessLogic);
        $this->assertArrayHasKey('issues', $businessLogic);

        // Check state distribution structure
        $stateDistribution = $businessLogic['state_distribution'];
        $this->assertArrayHasKey('remote', $stateDistribution);
        $this->assertArrayHasKey('local', $stateDistribution);
        $this->assertArrayHasKey('matches', $stateDistribution);

        // Check refund status distribution structure
        $refundStatusDistribution = $businessLogic['refund_status_distribution'];
        $this->assertArrayHasKey('remote', $refundStatusDistribution);
        $this->assertArrayHasKey('local', $refundStatusDistribution);
        $this->assertArrayHasKey('matches', $refundStatusDistribution);
    }

    public function test_relationship_consistency_validation_structure(): void
    {
        $syncLog = SyncLog::factory()->create([
            'sync_type' => 'order_sync',
            'status' => 'completed',
            'sync_config' => ['incremental' => false],
        ]);

        $result = $this->service->validateSync($syncLog);

        $this->assertArrayHasKey('relationship_consistency', $result);
        $relationshipConsistency = $result['relationship_consistency'];
        
        $this->assertArrayHasKey('order_item_counts', $relationshipConsistency);
        $this->assertArrayHasKey('status', $relationshipConsistency);
        $this->assertArrayHasKey('issues', $relationshipConsistency);

        // Check order item counts structure
        $orderItemCounts = $relationshipConsistency['order_item_counts'];
        $this->assertArrayHasKey('remote_orders_checked', $orderItemCounts);
        $this->assertArrayHasKey('local_orders_checked', $orderItemCounts);
        $this->assertArrayHasKey('mismatched_orders', $orderItemCounts);
    }

    public function test_validate_sync_with_incremental_config(): void
    {
        $syncLog = SyncLog::factory()->create([
            'sync_type' => 'order_sync',
            'status' => 'completed',
            'sync_config' => [
                'incremental' => true,
                'batch_size' => 100,
            ],
        ]);

        $result = $this->service->validateSync($syncLog);

        $this->assertIsArray($result);
        $this->assertEquals($syncLog->id, $result['sync_log_id']);
        $this->assertContains($result['overall_status'], ['passed', 'failed', 'error']);
    }

    public function test_validate_sync_with_force_time_range_config(): void
    {
        $syncLog = SyncLog::factory()->create([
            'sync_type' => 'order_sync',
            'status' => 'completed',
            'sync_config' => [
                'force_time_range' => true,
                'start_time' => '2024-01-01 00:00:00',
                'end_time' => '2024-01-31 23:59:59',
            ],
        ]);

        $result = $this->service->validateSync($syncLog);

        $this->assertIsArray($result);
        $this->assertEquals($syncLog->id, $result['sync_log_id']);
        $this->assertContains($result['overall_status'], ['passed', 'failed', 'error']);
    }

    public function test_overall_status_determination(): void
    {
        $syncLog = SyncLog::factory()->create([
            'sync_type' => 'order_sync',
            'status' => 'completed',
            'sync_config' => ['incremental' => false],
        ]);

        $result = $this->service->validateSync($syncLog);

        // The overall status should be one of the expected values
        $this->assertContains($result['overall_status'], ['passed', 'failed', 'error']);
        
        // If status is failed, there should be issues
        if ($result['overall_status'] === 'failed') {
            $this->assertNotEmpty($result['issues']);
        }
        
        // If status is error, there should be an error message
        if ($result['overall_status'] === 'error') {
            $this->assertArrayHasKey('error_message', $result);
        }
    }

    public function test_validation_timestamps(): void
    {
        $syncLog = SyncLog::factory()->create([
            'sync_type' => 'order_sync',
            'status' => 'completed',
            'sync_config' => ['incremental' => false],
        ]);

        $startTime = now();
        $result = $this->service->validateSync($syncLog);
        $endTime = now();

        $this->assertArrayHasKey('validation_started_at', $result);
        $this->assertArrayHasKey('validation_completed_at', $result);

        $validationStarted = \Carbon\Carbon::parse($result['validation_started_at']);
        $validationCompleted = \Carbon\Carbon::parse($result['validation_completed_at']);

        $this->assertTrue($validationStarted->between($startTime, $endTime));
        $this->assertTrue($validationCompleted->between($startTime, $endTime));
        $this->assertTrue($validationCompleted->greaterThanOrEqualTo($validationStarted));
    }

    public function test_data_integrity_validation_with_real_data(): void
    {
        $syncLog = SyncLog::factory()->create([
            'sync_type' => 'order_sync',
            'status' => 'completed',
            'sync_config' => ['incremental' => false],
        ]);

        // Create orders with specific totals for testing
        Order::factory()->create([
            'items_total' => 10000, // $100.00
            'adjustments_total' => -1000, // -$10.00
            'total_amount' => 9000, // $90.00
            'customer_id' => 1,
            'state' => 'completed',
        ]);

        Order::factory()->create([
            'items_total' => 20000, // $200.00
            'adjustments_total' => -2000, // -$20.00
            'total_amount' => 18000, // $180.00
            'customer_id' => 2,
            'state' => 'completed',
        ]);

        $result = $this->service->validateSync($syncLog);

        $this->assertArrayHasKey('data_integrity', $result);
        $dataIntegrity = $result['data_integrity'];

        // Check that local counts are calculated correctly
        $this->assertEquals(2, $dataIntegrity['record_counts']['local']['orders']);

        // Check that field checksums are calculated
        $this->assertArrayHasKey('field_checksums', $dataIntegrity);
        $this->assertArrayHasKey('local', $dataIntegrity['field_checksums']);

        $localChecksums = $dataIntegrity['field_checksums']['local'];
        $this->assertEquals(30000.0, $localChecksums['items_total_sum']); // 100 + 200
        $this->assertEquals(-3000.0, $localChecksums['adjustments_total_sum']); // -10 + -20
        $this->assertEquals(27000.0, $localChecksums['total_amount_sum']); // 90 + 180
        $this->assertEquals(2, $localChecksums['customer_count']); // 2 unique customers
    }

    public function test_business_logic_validation_with_different_states(): void
    {
        $syncLog = SyncLog::factory()->create([
            'sync_type' => 'order_sync',
            'status' => 'completed',
            'sync_config' => ['incremental' => false],
        ]);

        // Create orders with different states - use specific store_order_ids
        Order::factory()->create([
            'state' => 'completed',
            'store_order_id' => 1001
        ]);
        Order::factory()->create([
            'state' => 'completed',
            'store_order_id' => 1002
        ]);
        Order::factory()->create([
            'state' => 'completed',
            'store_order_id' => 1003
        ]);

        Order::factory()->create([
            'state' => 'cancelled',
            'store_order_id' => 2001
        ]);
        Order::factory()->create([
            'state' => 'cancelled',
            'store_order_id' => 2002
        ]);

        Order::factory()->create([
            'state' => 'pending',
            'store_order_id' => 3001
        ]);

        // Create orders with different refund statuses
        Order::factory()->create([
            'refund_status' => 'success',
            'store_order_id' => 4001
        ]);
        Order::factory()->create([
            'refund_status' => 'success',
            'store_order_id' => 4002
        ]);
        Order::factory()->create([
            'refund_status' => null,
            'store_order_id' => 5001
        ]);

        $result = $this->service->validateSync($syncLog);

        $this->assertArrayHasKey('business_logic', $result);
        $businessLogic = $result['business_logic'];

        // Check state distribution
        $this->assertArrayHasKey('state_distribution', $businessLogic);
        $stateDistribution = $businessLogic['state_distribution'];

        $this->assertArrayHasKey('local', $stateDistribution);
        $localStates = $stateDistribution['local'];

        // Check that we have at least the states we created
        // Note: The validation service reverse-maps local states to remote states for comparison
        // 'completed' -> 'fulfilled', 'cancelled' -> 'cancelled', 'pending' -> 'pending'
        $this->assertGreaterThanOrEqual(3, $localStates['fulfilled'] ?? 0); // 'completed' maps to 'fulfilled'
        $this->assertGreaterThanOrEqual(2, $localStates['cancelled'] ?? 0);
        $this->assertGreaterThanOrEqual(1, $localStates['pending'] ?? 0);
    }

    public function test_relationship_consistency_validation_with_real_data(): void
    {
        $syncLog = SyncLog::factory()->create([
            'sync_type' => 'order_sync',
            'status' => 'completed',
            'sync_config' => ['incremental' => false],
        ]);

        // Create orders with specific item counts
        $order1 = Order::factory()->create(['store_order_id' => 1001]);
        $order2 = Order::factory()->create(['store_order_id' => 1002]);
        $order3 = Order::factory()->create(['store_order_id' => 1003]);

        // Order 1: 3 items
        OrderItem::factory()->count(3)->create(['order_id' => $order1->id]);

        // Order 2: 2 items
        OrderItem::factory()->count(2)->create(['order_id' => $order2->id]);

        // Order 3: 1 item
        OrderItem::factory()->count(1)->create(['order_id' => $order3->id]);

        $result = $this->service->validateSync($syncLog);

        $this->assertArrayHasKey('relationship_consistency', $result);
        $relationshipConsistency = $result['relationship_consistency'];

        $this->assertArrayHasKey('order_item_counts', $relationshipConsistency);
        $orderItemCounts = $relationshipConsistency['order_item_counts'];

        // Should have checked at least our 3 orders
        $this->assertGreaterThanOrEqual(3, $orderItemCounts['local_orders_checked']);
    }

    public function test_validation_handles_empty_database(): void
    {
        $syncLog = SyncLog::factory()->create([
            'sync_type' => 'order_sync',
            'status' => 'completed',
            'sync_config' => ['incremental' => false],
        ]);

        // Don't create any orders or order items
        $result = $this->service->validateSync($syncLog);

        $this->assertIsArray($result);
        $this->assertEquals($syncLog->id, $result['sync_log_id']);
        $this->assertContains($result['overall_status'], ['passed', 'failed', 'error']);

        // Check that counts are zero
        if (isset($result['data_integrity']['record_counts']['local'])) {
            $localCounts = $result['data_integrity']['record_counts']['local'];
            $this->assertEquals(0, $localCounts['orders']);
            $this->assertEquals(0, $localCounts['order_items']);
        }
    }

    public function test_validation_performance_with_moderate_dataset(): void
    {
        $syncLog = SyncLog::factory()->create([
            'sync_type' => 'order_sync',
            'status' => 'completed',
            'sync_config' => ['incremental' => false],
        ]);

        // Create a moderate number of orders for performance testing
        $orders = Order::factory()->count(20)->create();

        foreach ($orders as $order) {
            OrderItem::factory()->count(3)->create(['order_id' => $order->id]);
        }

        $startTime = microtime(true);
        $result = $this->service->validateSync($syncLog);
        $endTime = microtime(true);

        $executionTime = $endTime - $startTime;

        $this->assertIsArray($result);
        $this->assertEquals($syncLog->id, $result['sync_log_id']);
        $this->assertContains($result['overall_status'], ['passed', 'failed', 'error']);

        // Validation should complete within reasonable time (5 seconds for this dataset)
        $this->assertLessThan(5.0, $executionTime, 'Validation took too long: ' . $executionTime . ' seconds');
    }

    public function test_order_state_mapping_is_applied_correctly(): void
    {
        // Test that the getOrderStateMapping method returns the correct mapping
        $service = new OrderSyncValidationService();
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod('getOrderStateMapping');
        $method->setAccessible(true);

        $mapping = $method->invoke($service);

        $this->assertIsArray($mapping);
        $this->assertEquals('completed', $mapping['fulfilled']);
        $this->assertEquals('cart', $mapping['cart']);
        $this->assertEquals('new', $mapping['new']);
        $this->assertEquals('cancelled', $mapping['cancelled']);
    }
}
