<?php

declare(strict_types=1);

namespace Tests\Unit\Services;

use App\Services\SyncProgressService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

final class SyncProgressServiceTest extends TestCase
{
    use RefreshDatabase;

    private SyncProgressService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new SyncProgressService();
        
        // Clear cache before each test
        Cache::flush();
    }

    public function test_initialize_progress_creates_initial_data(): void
    {
        $jobId = 'test-job-id';
        $batchId = 'test-batch-id';

        $this->service->initializeProgress($jobId, $batchId);

        $progress = $this->service->getProgress($jobId);

        $this->assertNotNull($progress);
        $this->assertEquals($jobId, $progress['job_id']);
        $this->assertEquals($batchId, $progress['batch_id']);
        $this->assertEquals('pending', $progress['status']);
        $this->assertEquals(0, $progress['percentage']);
        $this->assertEquals(0, $progress['processed_chunks']);
        $this->assertEquals(0, $progress['total_chunks']);
    }

    public function test_update_progress_modifies_existing_data(): void
    {
        $jobId = 'test-job-id';
        $batchId = 'test-batch-id';

        $this->service->initializeProgress($jobId, $batchId);
        
        $updateData = [
            'processed_chunks' => 5,
            'total_chunks' => 10,
            'percentage' => 50.0,
            'status' => 'processing',
        ];

        $this->service->updateProgress($jobId, $updateData);

        $progress = $this->service->getProgress($jobId);

        $this->assertEquals(5, $progress['processed_chunks']);
        $this->assertEquals(10, $progress['total_chunks']);
        $this->assertEquals(50.0, $progress['percentage']);
        $this->assertEquals('processing', $progress['status']);
    }

    public function test_mark_completed_sets_completion_data(): void
    {
        $jobId = 'test-job-id';
        $batchId = 'test-batch-id';

        $this->service->initializeProgress($jobId, $batchId);
        
        $completionData = [
            'sync_log_id' => 123,
            'total_records' => 1000,
            'success_records' => 950,
            'failed_records' => 50,
        ];

        $this->service->markCompleted($jobId, $completionData);

        $progress = $this->service->getProgress($jobId);

        $this->assertEquals('completed', $progress['status']);
        $this->assertEquals(100, $progress['percentage']);
        $this->assertEquals(123, $progress['sync_log_id']);
        $this->assertEquals(1000, $progress['total_records']);
        $this->assertArrayHasKey('completed_at', $progress);
    }

    public function test_mark_failed_sets_failure_data(): void
    {
        $jobId = 'test-job-id';
        $batchId = 'test-batch-id';

        $this->service->initializeProgress($jobId, $batchId);
        
        $errorData = [
            'error' => 'Something went wrong',
        ];

        $this->service->markFailed($jobId, $errorData);

        $progress = $this->service->getProgress($jobId);

        $this->assertEquals('failed', $progress['status']);
        $this->assertEquals('Something went wrong', $progress['error']);
        $this->assertArrayHasKey('failed_at', $progress);
    }

    public function test_get_progress_by_batch_id(): void
    {
        $jobId = 'test-job-id';
        $batchId = 'test-batch-id';

        $this->service->initializeProgress($jobId, $batchId);

        $progress = $this->service->getProgressByBatchId($batchId);

        $this->assertNotNull($progress);
        $this->assertEquals($jobId, $progress['job_id']);
        $this->assertEquals($batchId, $progress['batch_id']);
    }

    public function test_has_active_sync_jobs_returns_true_when_active(): void
    {
        $jobId = 'test-job-id';
        $batchId = 'test-batch-id';

        $this->service->initializeProgress($jobId, $batchId);

        $this->assertTrue($this->service->hasActiveSyncJobs());
    }

    public function test_has_active_sync_jobs_returns_false_when_no_active(): void
    {
        $this->assertFalse($this->service->hasActiveSyncJobs());
    }

    public function test_has_active_sync_jobs_returns_false_when_completed(): void
    {
        $jobId = 'test-job-id';
        $batchId = 'test-batch-id';

        $this->service->initializeProgress($jobId, $batchId);
        $this->service->markCompleted($jobId, []);

        $this->assertFalse($this->service->hasActiveSyncJobs());
    }

    public function test_get_active_sync_jobs_returns_only_active(): void
    {
        // Create active job
        $this->service->initializeProgress('active-job', 'active-batch');
        
        // Create completed job
        $this->service->initializeProgress('completed-job', 'completed-batch');
        $this->service->markCompleted('completed-job', []);

        $activeJobs = $this->service->getActiveSyncJobs();

        $this->assertCount(1, $activeJobs);
        $this->assertEquals('active-job', $activeJobs[0]['job_id']);
    }

    public function test_is_batch_active_returns_true_for_active_batch(): void
    {
        $jobId = 'test-job-id';
        $batchId = 'test-batch-id';

        $this->service->initializeProgress($jobId, $batchId);

        $this->assertTrue($this->service->isBatchActive($batchId));
    }

    public function test_is_batch_active_returns_false_for_completed_batch(): void
    {
        $jobId = 'test-job-id';
        $batchId = 'test-batch-id';

        $this->service->initializeProgress($jobId, $batchId);
        $this->service->markCompleted($jobId, []);

        $this->assertFalse($this->service->isBatchActive($batchId));
    }

    public function test_is_batch_active_returns_false_for_non_existent_batch(): void
    {
        $this->assertFalse($this->service->isBatchActive('non-existent-batch'));
    }

    public function test_cleanup_removes_old_progress_data(): void
    {
        $jobId = 'test-job-id';
        $batchId = 'test-batch-id';

        $this->service->initializeProgress($jobId, $batchId);

        // Manually set old timestamp
        $progressData = $this->service->getProgress($jobId);
        $progressData['updated_at'] = now()->subHours(25)->toISOString();
        Cache::put('sync_progress:' . $jobId, $progressData, 3600);

        $cleaned = $this->service->cleanup(24);

        // Should clean up both the active job entry and the batch index entry
        $this->assertEquals(2, $cleaned);
        $this->assertNull($this->service->getProgress($jobId));
        $this->assertNull($this->service->getProgressByBatchId($batchId));
    }

    public function test_mark_sync_completed_sets_validating_status(): void
    {
        $jobId = 'test-job-id';
        $batchId = 'test-batch-id';

        $this->service->initializeProgress($jobId, $batchId);

        $completionData = [
            'sync_log_id' => 123,
            'total_records' => 1000,
            'success_records' => 950,
            'failed_records' => 50,
        ];

        $this->service->markSyncCompleted($jobId, $completionData);

        $progress = $this->service->getProgress($jobId);

        $this->assertEquals('validating', $progress['status']);
        $this->assertEquals(90, $progress['percentage']);
        $this->assertEquals(123, $progress['sync_log_id']);
        $this->assertArrayHasKey('sync_completed_at', $progress);

        // Should still be considered active
        $this->assertTrue($this->service->hasActiveSyncJobs());
    }

    public function test_mark_validation_started_updates_status(): void
    {
        $jobId = 'test-job-id';
        $batchId = 'test-batch-id';

        $this->service->initializeProgress($jobId, $batchId);
        $this->service->markSyncCompleted($jobId, []);

        $validationData = [
            'validation_job_id' => 'validation-job-123',
            'validation_started_at' => now()->toISOString(),
        ];

        $this->service->markValidationStarted($jobId, $validationData);

        $progress = $this->service->getProgress($jobId);

        $this->assertEquals('validating', $progress['status']);
        $this->assertEquals(95, $progress['percentage']);
        $this->assertEquals('validation-job-123', $progress['validation_job_id']);
        $this->assertArrayHasKey('validation_started_at', $progress);
    }

    public function test_mark_validation_completed_sets_final_completion(): void
    {
        $jobId = 'test-job-id';
        $batchId = 'test-batch-id';

        $this->service->initializeProgress($jobId, $batchId);
        $this->service->markSyncCompleted($jobId, []);

        $validationData = [
            'validation_results' => ['status' => 'passed'],
        ];

        $this->service->markValidationCompleted($jobId, $validationData);

        $progress = $this->service->getProgress($jobId);

        $this->assertEquals('completed', $progress['status']);
        $this->assertEquals(100, $progress['percentage']);
        $this->assertArrayHasKey('validation_results', $progress);
        $this->assertArrayHasKey('completed_at', $progress);

        // Should no longer be considered active
        $this->assertFalse($this->service->hasActiveSyncJobs());
    }

    public function test_mark_validation_failed_sets_failure_status(): void
    {
        $jobId = 'test-job-id';
        $batchId = 'test-batch-id';

        $this->service->initializeProgress($jobId, $batchId);
        $this->service->markSyncCompleted($jobId, []);

        $errorData = [
            'error_message' => 'Validation failed',
            'validation_results' => ['status' => 'failed'],
        ];

        $this->service->markValidationFailed($jobId, $errorData);

        $progress = $this->service->getProgress($jobId);

        $this->assertEquals('failed', $progress['status']);
        $this->assertEquals('Validation failed', $progress['error_message']);
        $this->assertArrayHasKey('validation_results', $progress);
        $this->assertArrayHasKey('failed_at', $progress);

        // Should no longer be considered active
        $this->assertFalse($this->service->hasActiveSyncJobs());
    }

    public function test_has_active_sync_jobs_includes_validating_status(): void
    {
        $jobId = 'test-job-id';
        $batchId = 'test-batch-id';

        $this->service->initializeProgress($jobId, $batchId);
        $this->service->markSyncCompleted($jobId, []);

        // Should still be active when validating
        $this->assertTrue($this->service->hasActiveSyncJobs());

        $activeJobs = $this->service->getActiveSyncJobs();
        $this->assertCount(1, $activeJobs);
        $this->assertEquals('validating', $activeJobs[0]['status']);
    }

    public function test_is_batch_active_includes_validating_status(): void
    {
        $jobId = 'test-job-id';
        $batchId = 'test-batch-id';

        $this->service->initializeProgress($jobId, $batchId);
        $this->service->markSyncCompleted($jobId, []);

        // Should still be active when validating
        $this->assertTrue($this->service->isBatchActive($batchId));
    }

    protected function tearDown(): void
    {
        Cache::flush();
        parent::tearDown();
    }
}
