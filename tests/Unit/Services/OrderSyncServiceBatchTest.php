<?php

declare(strict_types=1);

namespace Tests\Unit\Services;

use Tests\TestCase;
use App\Services\OrderSyncService;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\SyncLog;
use App\Models\SyncRecord;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class OrderSyncServiceBatchTest extends TestCase
{
    use RefreshDatabase;

    private OrderSyncService $orderSyncService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->orderSyncService = app(OrderSyncService::class);
    }

    public function test_batch_processing_configuration_is_loaded(): void
    {
        $config = config('sync.performance');

        $this->assertIsArray($config);
        $this->assertArrayHasKey('batch_insert_size', $config);

        $this->assertEquals(300, $config['batch_insert_size']);
    }

    public function test_batch_optimization_implementation_exists(): void
    {
        // Test that the OrderSyncService has the batch processing methods
        $reflection = new \ReflectionClass(OrderSyncService::class);

        // Check that the processBatchOrders method exists
        $this->assertTrue($reflection->hasMethod('processBatchOrders'));

        // Check that the bulkUpsertOrders method exists
        $this->assertTrue($reflection->hasMethod('bulkUpsertOrders'));

        // Check that the prepareAndInsertOrderItems method exists
        $this->assertTrue($reflection->hasMethod('prepareAndInsertOrderItems'));

        // Check that the updateSyncRecordsWithResults method exists
        $this->assertTrue($reflection->hasMethod('updateSyncRecordsWithResults'));

        // Verify these methods are private (implementation details)
        $processBatchMethod = $reflection->getMethod('processBatchOrders');
        $this->assertTrue($processBatchMethod->isPrivate());

        $bulkUpsertMethod = $reflection->getMethod('bulkUpsertOrders');
        $this->assertTrue($bulkUpsertMethod->isPrivate());
    }

    public function test_environment_variables_are_properly_configured(): void
    {
        // Test that environment variables are properly loaded
        $this->assertEquals(300, config('sync.performance.batch_insert_size'));
    }

    public function test_sync_service_can_be_instantiated(): void
    {
        $this->assertInstanceOf(OrderSyncService::class, $this->orderSyncService);
    }

    public function test_sync_statistics_method_works(): void
    {
        $stats = $this->orderSyncService->getSyncStatistics();
        
        $this->assertIsArray($stats);
        $this->assertArrayHasKey('total_syncs', $stats);
        $this->assertArrayHasKey('successful_syncs', $stats);
        $this->assertArrayHasKey('failed_syncs', $stats);
        $this->assertArrayHasKey('success_rate', $stats);
        $this->assertArrayHasKey('last_sync', $stats);
        
        // Initially should be 0 syncs
        $this->assertEquals(0, $stats['total_syncs']);
        $this->assertEquals(0, $stats['successful_syncs']);
        $this->assertEquals(0, $stats['failed_syncs']);
        $this->assertEquals(0, $stats['success_rate']);
        $this->assertNull($stats['last_sync']);
    }

    public function test_cleanup_method_works(): void
    {
        $result = $this->orderSyncService->cleanup(30);
        
        $this->assertIsArray($result);
        $this->assertArrayHasKey('records_deleted', $result);
        $this->assertArrayHasKey('logs_deleted', $result);
        $this->assertArrayHasKey('cutoff_date', $result);
        
        // Initially should be 0 deletions
        $this->assertEquals(0, $result['records_deleted']);
        $this->assertEquals(0, $result['logs_deleted']);
    }

    public function test_incremental_sync_validation(): void
    {
        // Initially should not be able to perform incremental sync
        $this->assertFalse($this->orderSyncService->canPerformIncrementalSync());
        $this->assertFalse($this->orderSyncService->hasCompletedFullSync());
    }
}
