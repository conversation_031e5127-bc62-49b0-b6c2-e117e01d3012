<?php

declare(strict_types=1);

namespace Tests\Unit\Services;

use App\Models\Organisation;
use App\Models\Role;
use App\Models\User;
use App\Services\PermissionService;
use App\Services\UserService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

final class UserServiceTest extends TestCase
{
    use RefreshDatabase;

    private UserService $userService;
    private PermissionService $permissionService;
    private Organisation $organisation;
    private User $ownerUser;
    private User $memberUser;
    private Role $ownerRole;
    private Role $memberRole;

    protected function setUp(): void
    {
        parent::setUp();

        $this->userService = app(UserService::class);
        $this->permissionService = app(PermissionService::class);

        // Create test organisation
        $this->organisation = Organisation::factory()->create([
            'name' => 'Test Organisation',
            'status' => 'active',
        ]);

        // Create test users
        $this->ownerUser = User::factory()->create(['name' => 'Owner User']);
        $this->memberUser = User::factory()->create(['name' => 'Member User']);

        // Attach users to organisation
        $this->ownerUser->organisations()->attach($this->organisation->id);
        $this->memberUser->organisations()->attach($this->organisation->id);

        // Create roles
        $this->ownerRole = $this->permissionService->createRole('owner', 'api', $this->organisation->id);
        $this->memberRole = $this->permissionService->createRole('member', 'api', $this->organisation->id);

        // Assign roles
        $this->permissionService->assignRoleToUser($this->ownerUser, $this->ownerRole);
        $this->permissionService->assignRoleToUser($this->memberUser, $this->memberRole);
    }

    public function test_remove_from_organisation_prevents_removing_owner(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        // Use the actual Chinese error message from the application
        $this->expectExceptionMessage('Cannot remove organisation owner. Owner role must be transferred first.');

        $this->userService->removeFromOrganisation($this->ownerUser, $this->organisation->id);
    }

    public function test_remove_from_organisation_allows_removing_non_owner(): void
    {
        $result = $this->userService->removeFromOrganisation($this->memberUser, $this->organisation->id);

        $this->assertInstanceOf(User::class, $result);
        $this->assertFalse($this->memberUser->organisations()->where('organisations.id', $this->organisation->id)->exists());
    }

    public function test_sync_organisations_prevents_removing_owner_from_organisation(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        // Use the actual Chinese error message pattern from the application
        $this->expectExceptionMessage("Cannot remove user from organisation ID {$this->organisation->id}. User is the owner and owner role must be transferred first.");

        // Try to sync to empty array (removing from all organisations)
        $this->userService->syncOrganisations($this->ownerUser, []);
    }

    public function test_sync_organisations_allows_removing_non_owner_from_organisation(): void
    {
        $result = $this->userService->syncOrganisations($this->memberUser, []);

        $this->assertInstanceOf(User::class, $result);
        $this->assertFalse($this->memberUser->organisations()->where('organisations.id', $this->organisation->id)->exists());
    }

    public function test_sync_organisations_allows_owner_to_stay_in_organisation(): void
    {
        $result = $this->userService->syncOrganisations($this->ownerUser, [$this->organisation->id]);

        $this->assertInstanceOf(User::class, $result);
        $this->assertTrue($this->ownerUser->organisations()->where('organisations.id', $this->organisation->id)->exists());
    }

    public function test_sync_organisations_allows_adding_organisations(): void
    {
        $newOrganisation = Organisation::factory()->create(['name' => 'New Organisation']);

        $result = $this->userService->syncOrganisations($this->memberUser, [$this->organisation->id, $newOrganisation->id]);

        $this->assertInstanceOf(User::class, $result);
        $this->assertTrue($this->memberUser->organisations()->where('organisations.id', $this->organisation->id)->exists());
        $this->assertTrue($this->memberUser->organisations()->where('organisations.id', $newOrganisation->id)->exists());
    }

    public function test_get_users_returns_paginated_users(): void
    {
        // Create additional users and attach them to organisations so they appear in results
        $additionalUsers = User::factory()->count(5)->create();
        foreach ($additionalUsers as $user) {
            $user->organisations()->attach($this->organisation->id);
        }

        // Create a system admin user to bypass permission restrictions
        $systemAdminUser = User::factory()->create(['name' => 'System Admin']);
        $systemRole = Role::firstOrCreate([
            'name' => 'admin',
            'guard_name' => 'system',
            'organisation_id' => null,
        ]);

        // Clear team context for system role assignment
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $this->permissionService->assignRoleToUser($systemAdminUser, $systemRole);

        $result = $this->userService->getUsers(10, null, $systemAdminUser);

        $this->assertInstanceOf(\Illuminate\Contracts\Pagination\LengthAwarePaginator::class, $result);
        $this->assertEquals(10, $result->perPage());
        // Should have at least the 2 existing users (owner, member) + 5 new users + 1 admin = 8 total
        $this->assertGreaterThanOrEqual(8, $result->total());
    }

    public function test_get_users_filters_by_organisation_ids(): void
    {
        // Create another organisation and user
        $organisation2 = Organisation::factory()->create(['name' => 'Organisation 2']);
        $user2 = User::factory()->create(['name' => 'User 2']);
        $user2->organisations()->attach($organisation2->id);

        // Create a system admin user to bypass permission checks
        $systemAdminUser = User::factory()->create(['name' => 'System Admin']);
        $systemRole = Role::firstOrCreate([
            'name' => 'admin',
            'guard_name' => 'system',
            'organisation_id' => null,
        ]);

        // Clear team context for system role assignment
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $this->permissionService->assignRoleToUser($systemAdminUser, $systemRole);

        // Test filtering by specific organisation IDs with system admin user
        $result = $this->userService->getUsers(15, null, $systemAdminUser, [$this->organisation->id]);

        $users = $result->items();
        $userNames = array_column($users, 'name');

        // Should include users from organisation 1
        $this->assertContains('Owner User', $userNames);
        $this->assertContains('Member User', $userNames);
        // Should not include user from organisation 2
        $this->assertNotContains('User 2', $userNames);
    }

    public function test_get_users_system_admin_can_access_all_users(): void
    {
        // Create system admin user
        $systemAdminUser = User::factory()->create(['name' => 'System Admin']);
        $systemRole = Role::firstOrCreate([
            'name' => 'admin',
            'guard_name' => 'system',
            'organisation_id' => null,
        ]);

        // Clear team context for system role assignment
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);

        // Use the permission service to assign the system role properly
        $this->permissionService->assignRoleToUser($systemAdminUser, $systemRole);

        // Create additional users in different organisations
        $organisation2 = Organisation::factory()->create(['name' => 'Organisation 2']);
        $user2 = User::factory()->create(['name' => 'User 2']);
        $user2->organisations()->attach($organisation2->id);

        $result = $this->userService->getUsers(15, null, $systemAdminUser);

        $users = $result->items();
        $userNames = array_column($users, 'name');

        // System admin should see all users
        $this->assertContains('Owner User', $userNames);
        $this->assertContains('Member User', $userNames);
        $this->assertContains('User 2', $userNames);
        $this->assertContains('System Admin', $userNames);
    }

    public function test_get_users_non_admin_user_sees_only_organisation_users(): void
    {
        // Create another organisation and user
        $organisation2 = Organisation::factory()->create(['name' => 'Organisation 2']);
        $user2 = User::factory()->create(['name' => 'User 2']);
        $user2->organisations()->attach($organisation2->id);

        // Test with member user (non-admin)
        $result = $this->userService->getUsers(15, null, $this->memberUser);

        $users = $result->items();
        $userNames = array_column($users, 'name');

        // Should only see users from their own organisation
        $this->assertContains('Owner User', $userNames);
        $this->assertContains('Member User', $userNames);
        $this->assertNotContains('User 2', $userNames);
    }
}
