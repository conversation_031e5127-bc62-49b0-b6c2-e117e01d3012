<?php

declare(strict_types=1);

namespace Tests\Unit\Services;

use App\Models\Product;
use App\Models\SyncLog;
use App\Models\SyncRecord;
use App\Services\ProductSyncService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;
use Mockery;

final class ProductSyncServiceTest extends TestCase
{
    use RefreshDatabase;

    private ProductSyncService $productSyncService;

    protected function setUp(): void
    {
        parent::setUp();

        // Don't instantiate the service here - do it in each test after mocking
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_sync_creates_sync_log_with_range_info(): void
    {
        $this->mockStoreConnection();
        $this->productSyncService = app(ProductSyncService::class);

        $config = ['incremental' => false, 'batch_size' => 50];

        $syncLog = $this->productSyncService->sync($config);

        $this->assertInstanceOf(SyncLog::class, $syncLog);
        $this->assertEquals('product_sync', $syncLog->sync_type);
        $this->assertNotNull($syncLog->batch_id);
        $this->assertNotNull($syncLog->started_at);

        // Check that sync_config includes original config plus range info
        $syncConfig = $syncLog->sync_config;
        $this->assertEquals(50, $syncConfig['batch_size']);
        $this->assertFalse($syncConfig['incremental']);
        $this->assertArrayHasKey('sync_start_time', $syncConfig);
        $this->assertArrayHasKey('sync_end_time', $syncConfig);
        $this->assertEquals('full', $syncConfig['sync_type_detail']);
    }

    public function test_sync_saves_incremental_range_info(): void
    {
        // Create a previous successful sync to enable incremental sync
        SyncLog::create([
            'sync_type' => 'product_sync',
            'batch_id' => 'previous_batch',
            'status' => 'completed',
            'started_at' => now()->subHours(2),
            'completed_at' => now()->subHours(1),
        ]);

        $this->mockStoreConnection();
        $this->productSyncService = app(ProductSyncService::class);

        $config = ['incremental' => true, 'batch_size' => 25];

        $syncLog = $this->productSyncService->sync($config);

        $this->assertInstanceOf(SyncLog::class, $syncLog);

        // Check that sync_config includes incremental range info
        $syncConfig = $syncLog->sync_config;
        $this->assertEquals(25, $syncConfig['batch_size']);
        $this->assertTrue($syncConfig['incremental']);
        $this->assertArrayHasKey('sync_start_time', $syncConfig);
        $this->assertArrayHasKey('sync_end_time', $syncConfig);
        $this->assertEquals('incremental', $syncConfig['sync_type_detail']);

        // sync_start_time should be the completion time of the previous sync
        $this->assertNotEquals('1970-01-01 00:00:00', $syncConfig['sync_start_time']);
    }

    public function test_sync_processes_products_successfully(): void
    {
        // Mock successful variant data
        $this->mockStoreConnectionWithData([
            [
                'variant_id' => 1,
                'variant_code' => 'TEST-001-V1',
                'variant_enabled' => 1,
                'variant_updated_at' => '2023-01-01 00:00:00',
                'variant_name' => 'Test Variant',
                'product_id' => 1,
                'owner_id' => 1,
                'sku' => 'TEST-001',
                'slug' => 'test-product',
                'release_date' => '2023-01-01 00:00:00',
                'product_updated_at' => '2023-01-01 00:00:00',
                'pricing' => [
                    'price' => 1000,
                    'original_price' => 1200,
                    'minimum_price' => 800,
                    'lowest_price_before_discount' => 900
                ],
                'price_history' => [
                    [
                        'price' => 1000,
                        'original_price' => 1200,
                        'logged_at' => '2023-01-01 00:00:00'
                    ]
                ],
                'package' => '/images/test-package.jpg'
            ]
        ]);

        $this->productSyncService = app(ProductSyncService::class);
        $syncLog = $this->productSyncService->sync();

        $this->assertEquals('completed', $syncLog->status);
        $this->assertEquals(1, $syncLog->total_records);
        $this->assertEquals(1, $syncLog->success_records);
        $this->assertEquals(0, $syncLog->failed_records);

        // Check that product was created in database
        $product = Product::where('store_variant_id', 1)->first();
        $this->assertNotNull($product);
        $this->assertEquals('TEST-001-V1', $product->code);
        $this->assertEquals('Test Variant', $product->name);
        $this->assertTrue($product->enabled);
        $this->assertEquals(1000, $product->current_price);
        $this->assertEquals('/images/test-package.jpg', $product->package);

        // Check that sync record was created
        $syncRecord = SyncRecord::where('batch_id', $syncLog->batch_id)->first();
        $this->assertNotNull($syncRecord);
        $this->assertEquals('sylius_product_variant', $syncRecord->source_table);
        $this->assertEquals('1', $syncRecord->source_id);
        $this->assertEquals('products', $syncRecord->target_table);
        $this->assertEquals('success', $syncRecord->status);
    }

    public function test_sync_skips_unchanged_products(): void
    {
        // Create existing product
        $existingProduct = Product::create([
            'store_variant_id' => 1,
            'owner_id' => 1,
            'sku' => 'TEST-001',
            'code' => 'TEST-001-V1',
            'name' => 'Test Variant',
            'slug' => 'test-product',
            'enabled' => true,
            'current_price' => 1000,
            'store_product_updated_at' => '2023-01-01 00:00:00',
            'store_variant_updated_at' => '2023-01-01 00:00:00',
        ]);

        // Mock variant data with same update time
        $this->mockStoreConnectionWithData([
            [
                'variant_id' => 1,
                'variant_code' => 'TEST-001-V1',
                'variant_enabled' => 1,
                'variant_updated_at' => '2023-01-01 00:00:00', // Same as existing
                'variant_name' => 'Test Variant',
                'product_id' => 1,
                'owner_id' => 1,
                'sku' => 'TEST-001',
                'slug' => 'test-product',
                'release_date' => '2023-01-01 00:00:00',
                'product_updated_at' => '2023-01-01 00:00:00', // Same as existing
                'pricing' => [
                    'price' => 1000,
                    'original_price' => null,
                    'minimum_price' => null,
                    'lowest_price_before_discount' => null
                ],
                'price_history' => [],
                'package' => null
            ]
        ]);

        $this->productSyncService = app(ProductSyncService::class);
        $syncLog = $this->productSyncService->sync();

        $this->assertEquals('completed', $syncLog->status);
        $this->assertEquals(1, $syncLog->total_records);
        $this->assertEquals(0, $syncLog->success_records);
        $this->assertEquals(0, $syncLog->failed_records);

        // Check that sync record was marked as skipped
        $syncRecord = SyncRecord::where('batch_id', $syncLog->batch_id)->first();
        $this->assertNotNull($syncRecord);
        $this->assertEquals('1', $syncRecord->source_id);
        $this->assertEquals('skipped', $syncRecord->status);
    }

    public function test_sync_handles_errors_gracefully(): void
    {
        // Mock connection to throw exception when table is called
        $mockConnection = Mockery::mock();
        $mockConnection->shouldReceive('table')
            ->andThrow(new \Exception('Database connection failed'));

        DB::shouldReceive('connection')
            ->with('store')
            ->andReturn($mockConnection);

        $this->productSyncService = app(ProductSyncService::class);
        $syncLog = $this->productSyncService->sync();

        $this->assertEquals('failed', $syncLog->status);
        $this->assertNotNull($syncLog->error_message);
        $this->assertStringContainsString('Database connection failed', $syncLog->error_message);
    }

    public function test_re_sync_uses_original_time_range_for_incremental_sync(): void
    {
        // Create original sync log with time range info (simulating an incremental sync)
        $originalConfig = [
            'incremental' => true,
            'batch_size' => 25,
            'sync_start_time' => '2023-01-01 00:00:00',
            'sync_end_time' => '2023-01-01 12:00:00',
            'sync_type_detail' => 'incremental'
        ];
        $originalSyncLog = SyncLog::create([
            'sync_type' => 'product_sync',
            'batch_id' => 'test_batch_123',
            'status' => 'completed',
            'started_at' => now(),
            'completed_at' => now(),
            'sync_config' => $originalConfig,
        ]);

        $this->mockStoreConnectionWithData([]);
        $this->productSyncService = app(ProductSyncService::class);

        $newSyncLog = $this->productSyncService->reSync('test_batch_123');

        $this->assertInstanceOf(SyncLog::class, $newSyncLog);
        $this->assertNotEquals('test_batch_123', $newSyncLog->batch_id); // Should be new batch ID

        // Check that the re-sync config includes force_time_range and original time range
        $newConfig = $newSyncLog->sync_config;
        $this->assertTrue($newConfig['force_time_range'] ?? false);
        $this->assertEquals('2023-01-01 00:00:00', $newConfig['start_time']);
        $this->assertEquals('2023-01-01 12:00:00', $newConfig['end_time']);
    }

    public function test_re_sync_handles_full_sync_without_time_range(): void
    {
        // Create original sync log without time range info (simulating a full sync)
        $originalConfig = ['incremental' => false, 'batch_size' => 50];
        $originalSyncLog = SyncLog::create([
            'sync_type' => 'product_sync',
            'batch_id' => 'test_batch_456',
            'status' => 'failed',
            'started_at' => now(),
            'sync_config' => $originalConfig,
        ]);

        $this->mockStoreConnectionWithData([]);
        $this->productSyncService = app(ProductSyncService::class);

        $newSyncLog = $this->productSyncService->reSync('test_batch_456');

        $this->assertInstanceOf(SyncLog::class, $newSyncLog);
        $this->assertNotEquals('test_batch_456', $newSyncLog->batch_id); // Should be new batch ID

        // For full sync without original time range, should use original config
        $newConfig = $newSyncLog->sync_config;
        $this->assertEquals(50, $newConfig['batch_size']);
        $this->assertFalse($newConfig['incremental']);
    }

    public function test_re_sync_reproduces_exact_time_range(): void
    {
        // This test verifies that reSync actually queries the same time range as the original sync

        // Create original sync log with specific time range
        $originalConfig = [
            'incremental' => true,
            'batch_size' => 25,
            'sync_start_time' => '2023-06-01 10:00:00',
            'sync_end_time' => '2023-06-01 14:00:00',
            'sync_type_detail' => 'incremental'
        ];

        $originalSyncLog = SyncLog::create([
            'sync_type' => 'product_sync',
            'batch_id' => 'original_batch_789',
            'status' => 'failed',
            'started_at' => now(),
            'sync_config' => $originalConfig,
        ]);

        // Mock the store connection to verify the query uses the correct time range
        $mockQuery = Mockery::mock();
        $mockQuery->shouldReceive('leftJoin')->andReturnSelf();
        $mockQuery->shouldReceive('select')->andReturnSelf();

        // This is the key assertion - verify that the query uses the exact time range
        $mockQuery->shouldReceive('where')
            ->once()
            ->with(Mockery::on(function($callback) {
                // Create a mock query builder to test the callback
                $mockSubQuery = Mockery::mock();
                $mockSubQuery->shouldReceive('whereBetween')
                    ->with('pv.updated_at', ['2023-06-01 10:00:00', '2023-06-01 14:00:00'])
                    ->once()
                    ->andReturnSelf();
                $mockSubQuery->shouldReceive('orWhereBetween')
                    ->with('p.updated_at', ['2023-06-01 10:00:00', '2023-06-01 14:00:00'])
                    ->once()
                    ->andReturnSelf();

                // Execute the callback to verify it sets up the correct time range
                $callback($mockSubQuery);
                return true;
            }))
            ->andReturnSelf();

        $mockQuery->shouldReceive('get')->andReturn(collect([]));

        $mockConnection = Mockery::mock();
        $mockConnection->shouldReceive('table')
            ->with('sylius_product_variant as pv')
            ->andReturn($mockQuery);

        DB::shouldReceive('connection')
            ->with('store')
            ->andReturn($mockConnection);

        $this->productSyncService = app(ProductSyncService::class);
        $newSyncLog = $this->productSyncService->reSync('original_batch_789');

        $this->assertInstanceOf(SyncLog::class, $newSyncLog);

        // Verify the new sync config has force_time_range enabled
        $newConfig = $newSyncLog->sync_config;
        $this->assertTrue($newConfig['force_time_range']);
        $this->assertEquals('2023-06-01 10:00:00', $newConfig['start_time']);
        $this->assertEquals('2023-06-01 14:00:00', $newConfig['end_time']);
    }

    public function test_get_sync_statistics_returns_correct_data(): void
    {
        $this->productSyncService = app(ProductSyncService::class);

        // Create test sync logs with explicit timestamps
        $olderTime = now()->subHours(2);
        $newerTime = now()->subHour();

        // Create older sync log first
        $olderSync = SyncLog::create([
            'sync_type' => 'product_sync',
            'batch_id' => 'batch_1',
            'status' => 'completed',
            'started_at' => $olderTime,
            'completed_at' => $olderTime->copy()->addMinutes(30),
        ]);
        $olderSync->created_at = $olderTime;
        $olderSync->save();

        // Create newer sync log
        $newerSync = SyncLog::create([
            'sync_type' => 'product_sync',
            'batch_id' => 'batch_2',
            'status' => 'failed',
            'started_at' => $newerTime,
            'completed_at' => $newerTime->copy()->addMinutes(30),
        ]);
        $newerSync->created_at = $newerTime;
        $newerSync->save();

        $stats = $this->productSyncService->getSyncStatistics();

        $this->assertEquals(2, $stats['total_syncs']);
        $this->assertEquals(1, $stats['successful_syncs']);
        $this->assertEquals(1, $stats['failed_syncs']);
        $this->assertEquals(50.0, $stats['success_rate']);
        $this->assertNotNull($stats['last_sync']);
        $this->assertEquals('batch_2', $stats['last_sync']['batch_id']);
    }

    public function test_cleanup_removes_old_records(): void
    {
        $this->productSyncService = app(ProductSyncService::class);

        // Create old sync log and records
        $oldSyncLog = SyncLog::create([
            'sync_type' => 'product_sync',
            'batch_id' => 'old_batch',
            'status' => 'completed',
            'started_at' => now()->subDays(35),
            'completed_at' => now()->subDays(35),
        ]);
        $oldSyncLog->created_at = now()->subDays(35);
        $oldSyncLog->save();

        $syncRecord = SyncRecord::create([
            'batch_id' => 'old_batch',
            'source_table' => 'sylius_product_variant',
            'source_id' => '1',
            'target_table' => 'products',
            'status' => 'success',
        ]);
        $syncRecord->created_at = now()->subDays(35);
        $syncRecord->save();

        // Create recent sync log (should not be deleted)
        $recentSyncLog = SyncLog::create([
            'sync_type' => 'product_sync',
            'batch_id' => 'recent_batch',
            'status' => 'completed',
            'started_at' => now()->subDays(5),
            'completed_at' => now()->subDays(5),
        ]);
        $recentSyncLog->created_at = now()->subDays(5);
        $recentSyncLog->save();

        $result = $this->productSyncService->cleanup(30);

        $this->assertEquals(1, $result['records_deleted']);
        $this->assertEquals(1, $result['logs_deleted']);

        // Verify old records are deleted
        $this->assertDatabaseMissing('sync_logs', ['batch_id' => 'old_batch']);
        $this->assertDatabaseMissing('sync_records', ['batch_id' => 'old_batch']);

        // Verify recent records are kept
        $this->assertDatabaseHas('sync_logs', ['batch_id' => 'recent_batch']);
    }

    /**
     * Mock the store database connection.
     */
    private function mockStoreConnection(): void
    {
        $mockConnection = Mockery::mock();
        $mockConnection->shouldReceive('table')->andReturnSelf();
        $mockConnection->shouldReceive('leftJoin')->andReturnSelf();
        $mockConnection->shouldReceive('where')->andReturnSelf();
        $mockConnection->shouldReceive('select')->andReturnSelf();
        $mockConnection->shouldReceive('orderBy')->andReturnSelf();
        $mockConnection->shouldReceive('get')->andReturn(collect([]));
        $mockConnection->shouldReceive('first')->andReturn(null);

        DB::shouldReceive('connection')
            ->with('store')
            ->andReturn($mockConnection);
    }

    /**
     * Mock store connection with specific data.
     */
    private function mockStoreConnectionWithData(array $variants): void
    {
        $mockConnection = Mockery::mock();

        // Mock main variant query
        $variantData = array_map(function($variant) {
            // Remove nested data for main query
            $mainData = $variant;
            unset($mainData['pricing'], $mainData['price_history'], $mainData['package']);
            return (object) $mainData;
        }, $variants);

        $mockConnection->shouldReceive('table')
            ->with('sylius_product_variant as pv')
            ->andReturnSelf();
        $mockConnection->shouldReceive('leftJoin')->andReturnSelf();
        $mockConnection->shouldReceive('select')->andReturnSelf();
        $mockConnection->shouldReceive('where')->andReturnSelf();
        $mockConnection->shouldReceive('get')
            ->once()
            ->andReturn(collect($variantData));

        // Mock pricing queries for each variant
        foreach ($variants as $variant) {
            $pricingData = $variant['pricing'] ?? [];

            $mockConnection->shouldReceive('table')
                ->with('sylius_channel_pricing')
                ->andReturnSelf();
            $mockConnection->shouldReceive('where')
                ->with('product_variant_id', $variant['variant_id'])
                ->andReturnSelf();
            $mockConnection->shouldReceive('select')->andReturnSelf();
            $mockConnection->shouldReceive('first')
                ->once()
                ->andReturn($pricingData ? (object) $pricingData : null);

            // Mock price history queries
            $historyData = array_map(function($entry) {
                return (object) $entry;
            }, $variant['price_history'] ?? []);

            $mockConnection->shouldReceive('table')
                ->with('sylius_channel_pricing_log_entry as log')
                ->andReturnSelf();
            $mockConnection->shouldReceive('leftJoin')
                ->with('sylius_channel_pricing as cp', 'log.channel_pricing_id', '=', 'cp.id')
                ->andReturnSelf();
            $mockConnection->shouldReceive('where')
                ->with('cp.product_variant_id', $variant['variant_id'])
                ->andReturnSelf();
            $mockConnection->shouldReceive('select')->andReturnSelf();
            $mockConnection->shouldReceive('orderBy')
                ->with('log.logged_at', 'desc')
                ->andReturnSelf();
            $mockConnection->shouldReceive('get')
                ->once()
                ->andReturn(collect($historyData));

            // Mock package image queries
            $packagePath = $variant['package'] ?? null;
            $packageData = $packagePath ? (object) ['path' => $packagePath] : null;

            $mockConnection->shouldReceive('table')
                ->with('sylius_product_image')
                ->andReturnSelf();
            $mockConnection->shouldReceive('where')
                ->with('owner_id', $variant['product_id'])
                ->andReturnSelf();
            $mockConnection->shouldReceive('where')
                ->with('type', 'TAIL_PACKAGE_THUMBNAIL_PRODUCT')
                ->andReturnSelf();
            $mockConnection->shouldReceive('select')
                ->with('path')
                ->andReturnSelf();
            $mockConnection->shouldReceive('first')
                ->once()
                ->andReturn($packageData);
        }

        // Only mock the 'store' connection, leave default connection for test database
        DB::shouldReceive('connection')
            ->with('store')
            ->andReturn($mockConnection);
    }
}
