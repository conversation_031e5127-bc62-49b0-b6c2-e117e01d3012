<?php

declare(strict_types=1);

namespace Tests\Unit\Services;

use App\Services\ApiExceptionService;
use Illuminate\Http\JsonResponse;
use Tests\TestCase;

/**
 * Test ApiExceptionService functionality
 */
final class ApiExceptionServiceTest extends TestCase
{
    /**
     * Test createErrorResponse method
     */
    public function test_create_error_response(): void
    {
        $response = ApiExceptionService::createErrorResponse(
            'Test error message',
            400,
            ['field' => 'error'],
            'TEST_ERROR'
        );

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(400, $response->getStatusCode());

        $data = $response->getData(true);
        $this->assertEquals([
            'success' => false,
            'message' => 'Test error message',
            'errors' => ['field' => 'error'],
            'error_code' => 'TEST_ERROR',
            'timestamp' => $data['timestamp'], // Dynamic timestamp
        ], $data);
    }

    /**
     * Test validation error response
     */
    public function test_validation_error(): void
    {
        $errors = ['email' => ['The email field is required.']];
        $response = ApiExceptionService::validationError($errors);

        $this->assertEquals(422, $response->getStatusCode());
        
        $data = $response->getData(true);
        $this->assertEquals('Validation failed', $data['message']);
        $this->assertEquals($errors, $data['errors']);
        $this->assertEquals('VALIDATION_ERROR', $data['error_code']);
        $this->assertFalse($data['success']);
    }

    /**
     * Test authentication error response
     */
    public function test_authentication_error(): void
    {
        $response = ApiExceptionService::authenticationError();

        $this->assertEquals(401, $response->getStatusCode());
        
        $data = $response->getData(true);
        $this->assertEquals('Authentication required', $data['message']);
        $this->assertNull($data['errors']);
        $this->assertEquals('AUTHENTICATION_ERROR', $data['error_code']);
        $this->assertFalse($data['success']);
    }

    /**
     * Test authorization error response
     */
    public function test_authorization_error(): void
    {
        $response = ApiExceptionService::authorizationError();

        $this->assertEquals(403, $response->getStatusCode());
        
        $data = $response->getData(true);
        $this->assertEquals('Access denied', $data['message']);
        $this->assertNull($data['errors']);
        $this->assertEquals('AUTHORIZATION_ERROR', $data['error_code']);
        $this->assertFalse($data['success']);
    }

    /**
     * Test permission error response
     */
    public function test_permission_error(): void
    {
        $response = ApiExceptionService::permissionError();

        $this->assertEquals(403, $response->getStatusCode());
        
        $data = $response->getData(true);
        $this->assertEquals('Insufficient permissions', $data['message']);
        $this->assertNull($data['errors']);
        $this->assertEquals('PERMISSION_ERROR', $data['error_code']);
        $this->assertFalse($data['success']);
    }

    /**
     * Test not found error response
     */
    public function test_not_found_error(): void
    {
        $response = ApiExceptionService::notFoundError();

        $this->assertEquals(404, $response->getStatusCode());
        
        $data = $response->getData(true);
        $this->assertEquals('Resource not found', $data['message']);
        $this->assertNull($data['errors']);
        $this->assertEquals('NOT_FOUND_ERROR', $data['error_code']);
        $this->assertFalse($data['success']);
    }

    /**
     * Test method not allowed error response
     */
    public function test_method_not_allowed_error(): void
    {
        $response = ApiExceptionService::methodNotAllowedError();

        $this->assertEquals(405, $response->getStatusCode());
        
        $data = $response->getData(true);
        $this->assertEquals('Method not allowed', $data['message']);
        $this->assertNull($data['errors']);
        $this->assertEquals('METHOD_NOT_ALLOWED_ERROR', $data['error_code']);
        $this->assertFalse($data['success']);
    }

    /**
     * Test throttle error response
     */
    public function test_throttle_error(): void
    {
        $response = ApiExceptionService::throttleError();

        $this->assertEquals(429, $response->getStatusCode());
        
        $data = $response->getData(true);
        $this->assertEquals('Too many requests', $data['message']);
        $this->assertNull($data['errors']);
        $this->assertEquals('THROTTLE_ERROR', $data['error_code']);
        $this->assertFalse($data['success']);
    }

    /**
     * Test server error response
     */
    public function test_server_error(): void
    {
        $response = ApiExceptionService::serverError();

        $this->assertEquals(500, $response->getStatusCode());
        
        $data = $response->getData(true);
        $this->assertEquals('Internal server error', $data['message']);
        $this->assertNull($data['errors']);
        $this->assertEquals('SERVER_ERROR', $data['error_code']);
        $this->assertFalse($data['success']);
    }

    /**
     * Test generic error response
     */
    public function test_generic_error(): void
    {
        $response = ApiExceptionService::genericError();

        $this->assertEquals(500, $response->getStatusCode());
        
        $data = $response->getData(true);
        $this->assertEquals('An error occurred while processing your request', $data['message']);
        $this->assertNull($data['errors']);
        $this->assertEquals('GENERIC_ERROR', $data['error_code']);
        $this->assertFalse($data['success']);
    }

    /**
     * Test HTTP error response with different status codes
     */
    public function test_http_error_with_different_status_codes(): void
    {
        $testCases = [
            400 => ['Bad request', 'BAD_REQUEST_ERROR'],
            401 => ['Authentication required', 'AUTHENTICATION_ERROR'],
            403 => ['Access denied', 'AUTHORIZATION_ERROR'],
            404 => ['Resource not found', 'NOT_FOUND_ERROR'],
            405 => ['Method not allowed', 'METHOD_NOT_ALLOWED_ERROR'],
            422 => ['Validation failed', 'VALIDATION_ERROR'],
            429 => ['Too many requests', 'THROTTLE_ERROR'],
            500 => ['Internal server error', 'SERVER_ERROR'],
            502 => ['Bad gateway', 'BAD_GATEWAY_ERROR'],
            503 => ['Service temporarily unavailable', 'SERVICE_UNAVAILABLE_ERROR'],
        ];

        foreach ($testCases as $statusCode => [$expectedMessage, $expectedErrorCode]) {
            $response = ApiExceptionService::httpError($statusCode);

            $this->assertEquals($statusCode, $response->getStatusCode());
            
            $data = $response->getData(true);
            $this->assertEquals($expectedMessage, $data['message']);
            $this->assertEquals($expectedErrorCode, $data['error_code']);
            $this->assertFalse($data['success']);
        }
    }

    /**
     * Test custom message override
     */
    public function test_custom_message_override(): void
    {
        $customMessage = 'Custom error message';
        
        $response = ApiExceptionService::authenticationError($customMessage);
        
        $data = $response->getData(true);
        $this->assertEquals($customMessage, $data['message']);
    }

    /**
     * Test response structure consistency
     */
    public function test_response_structure_consistency(): void
    {
        $methods = [
            'validationError' => [['field' => 'error']],
            'authenticationError' => [],
            'authorizationError' => [],
            'permissionError' => [],
            'notFoundError' => [],
            'methodNotAllowedError' => [],
            'throttleError' => [],
            'serverError' => [],
            'genericError' => [],
        ];

        foreach ($methods as $method => $args) {
            $response = ApiExceptionService::$method(...$args);
            $data = $response->getData(true);

            // All responses should have these keys
            $this->assertArrayHasKey('success', $data);
            $this->assertArrayHasKey('message', $data);
            $this->assertArrayHasKey('errors', $data);
            $this->assertArrayHasKey('timestamp', $data);
            $this->assertArrayHasKey('error_code', $data);

            // Success should always be false for error responses
            $this->assertFalse($data['success']);

            // Timestamp should be in ISO format
            $this->assertMatchesRegularExpression(
                '/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{6}Z$/',
                $data['timestamp']
            );
        }
    }
}
