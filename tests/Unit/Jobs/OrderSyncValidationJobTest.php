<?php

declare(strict_types=1);

namespace Tests\Unit\Jobs;

use App\Jobs\OrderSyncValidationJob;
use App\Models\SyncLog;
use App\Services\OrderSyncValidationService;
use App\Services\SyncProgressService;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;
use Mockery;
use Tests\TestCase;

final class OrderSyncValidationJobTest extends TestCase
{
    use RefreshDatabase;

    private OrderSyncValidationService $validationService;
    private SyncProgressService $progressService;
    private SyncLog $syncLog;

    protected function setUp(): void
    {
        parent::setUp();

        $this->validationService = Mockery::mock(OrderSyncValidationService::class);
        $this->progressService = Mockery::mock(SyncProgressService::class);
        
        $this->app->instance(OrderSyncValidationService::class, $this->validationService);
        $this->app->instance(SyncProgressService::class, $this->progressService);

        // Create test sync log with unique batch_id
        $this->syncLog = SyncLog::factory()->create([
            'sync_type' => 'order_sync',
            'batch_id' => 'test-batch-' . uniqid(),
            'status' => 'completed',
        ]);
    }

    public function test_job_constructor_sets_properties(): void
    {
        $job = new OrderSyncValidationJob(
            syncLogId: 123,
            batchId: 'test-batch-456',
            originalJobId: 'original-job-789'
        );

        $this->assertEquals(123, $job->syncLogId);
        $this->assertEquals('test-batch-456', $job->batchId);
        $this->assertEquals('original-job-789', $job->originalJobId);
        $this->assertEquals(3600, $job->timeout);
        $this->assertEquals(1, $job->tries);
        $this->assertTrue($job->failOnTimeout);
    }

    public function test_handle_executes_validation_successfully_when_all_pass(): void
    {
        $validationResults = [
            'overall_status' => 'passed',
            'data_integrity' => ['status' => 'passed'],
            'sampling_validation' => ['status' => 'passed'],
            'business_logic' => ['status' => 'passed'],
            'relationship_consistency' => ['status' => 'passed'],
            'issues' => [],
        ];

        $this->validationService
            ->shouldReceive('validateSync')
            ->once()
            ->with(Mockery::type(SyncLog::class))
            ->andReturn($validationResults);

        $this->progressService
            ->shouldReceive('markValidationStarted')
            ->once()
            ->with('original-job-123', Mockery::type('array'));

        $this->progressService
            ->shouldReceive('markValidationCompleted')
            ->once()
            ->with('original-job-123', Mockery::on(function ($data) {
                return is_array($data) && array_key_exists('validation_results', $data);
            }));

        Log::shouldReceive('info')->atLeast()->once();

        $job = new OrderSyncValidationJob(
            syncLogId: $this->syncLog->id,
            batchId: $this->syncLog->batch_id,
            originalJobId: 'original-job-123'
        );

        // Mock the job ID
        $mockJob = Mockery::mock();
        $mockJob->shouldReceive('getJobId')->andReturn('validation-job-456');
        $job->job = $mockJob;

        $job->handle($this->validationService, $this->progressService);

        // Assert that validation results were saved to sync_logs table
        $this->syncLog->refresh();
        $this->assertNotNull($this->syncLog->validation_results);
        $this->assertEquals('passed', $this->syncLog->validation_results['overall_status']);
        $this->assertArrayHasKey('data_integrity', $this->syncLog->validation_results);
        $this->assertArrayHasKey('sampling_validation', $this->syncLog->validation_results);
        $this->assertArrayHasKey('business_logic', $this->syncLog->validation_results);
        $this->assertArrayHasKey('relationship_consistency', $this->syncLog->validation_results);
    }

    public function test_handle_executes_validation_and_fails_when_issues_exist(): void
    {
        $validationResults = [
            'overall_status' => 'failed',
            'data_integrity' => ['status' => 'failed', 'errors' => ['Record count mismatch']],
            'sampling_validation' => ['status' => 'passed'],
            'business_logic' => ['status' => 'passed'],
            'relationship_consistency' => ['status' => 'passed'],
            'issues' => ['Critical data integrity issue'],
        ];

        $this->validationService
            ->shouldReceive('validateSync')
            ->once()
            ->with(Mockery::type(SyncLog::class))
            ->andReturn($validationResults);

        $this->progressService
            ->shouldReceive('markValidationStarted')
            ->once()
            ->with('original-job-123', Mockery::type('array'));

        $this->progressService
            ->shouldReceive('markValidationFailed')
            ->once()
            ->with('original-job-123', Mockery::on(function ($data) {
                return isset($data['validation_results']) && 
                       isset($data['error_message']) &&
                       str_contains($data['error_message'], 'Critical data integrity issue');
            }));

        Log::shouldReceive('info')->atLeast()->once();
        Log::shouldReceive('warning')->atLeast()->once();

        $job = new OrderSyncValidationJob(
            syncLogId: $this->syncLog->id,
            batchId: $this->syncLog->batch_id,
            originalJobId: 'original-job-123'
        );

        // Mock the job ID
        $mockJob = Mockery::mock();
        $mockJob->shouldReceive('getJobId')->andReturn('validation-job-456');
        $job->job = $mockJob;

        $job->handle($this->validationService, $this->progressService);

        // Assert that validation results were saved to sync_logs table
        $this->syncLog->refresh();
        $this->assertNotNull($this->syncLog->validation_results);
        $this->assertEquals('failed', $this->syncLog->validation_results['overall_status']);
        $this->assertArrayHasKey('data_integrity', $this->syncLog->validation_results);
        $this->assertArrayHasKey('issues', $this->syncLog->validation_results);
        $this->assertNotEmpty($this->syncLog->validation_results['issues']);
    }

    public function test_handle_catches_exceptions_and_marks_validation_failed(): void
    {
        $exception = new Exception('Validation service error');

        $this->validationService
            ->shouldReceive('validateSync')
            ->once()
            ->with(Mockery::type(SyncLog::class))
            ->andThrow($exception);

        $this->progressService
            ->shouldReceive('markValidationStarted')
            ->once()
            ->with('original-job-123', Mockery::type('array'));

        $this->progressService
            ->shouldReceive('markValidationFailed')
            ->once()
            ->with('original-job-123', Mockery::on(function ($data) {
                return isset($data['error_message']) && 
                       str_contains($data['error_message'], 'Validation job failed: Validation service error');
            }));

        Log::shouldReceive('info')->atLeast()->once();
        Log::shouldReceive('error')->atLeast()->once();

        $job = new OrderSyncValidationJob(
            syncLogId: $this->syncLog->id,
            batchId: $this->syncLog->batch_id,
            originalJobId: 'original-job-123'
        );

        // Mock the job ID
        $mockJob = Mockery::mock();
        $mockJob->shouldReceive('getJobId')->andReturn('validation-job-456');
        $job->job = $mockJob;

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Validation service error');

        $job->handle($this->validationService, $this->progressService);

        // Assert that error validation results were saved to sync_logs table
        $this->syncLog->refresh();
        $this->assertNotNull($this->syncLog->validation_results);
        $this->assertEquals('error', $this->syncLog->validation_results['overall_status']);
        $this->assertArrayHasKey('error_message', $this->syncLog->validation_results);
        $this->assertEquals('Validation service error', $this->syncLog->validation_results['error_message']);
    }

    public function test_failed_method_logs_permanent_failure(): void
    {
        $exception = new Exception('Permanent failure');

        $this->progressService
            ->shouldReceive('markValidationFailed')
            ->once()
            ->with('original-job-123', Mockery::on(function ($data) {
                return isset($data['error_message']) && 
                       str_contains($data['error_message'], 'Validation job failed permanently');
            }));

        Log::shouldReceive('error')->atLeast()->once();

        $job = new OrderSyncValidationJob(
            syncLogId: $this->syncLog->id,
            batchId: $this->syncLog->batch_id,
            originalJobId: 'original-job-123'
        );

        // Mock the job
        $mockJob = Mockery::mock();
        $mockJob->shouldReceive('getJobId')->andReturn('validation-job-456');
        $job->job = $mockJob;

        $job->failed($exception);

        // Assert that all expectations were met
        $this->assertTrue(true); // This ensures the test has at least one assertion
    }

    public function test_tags_returns_expected_tags(): void
    {
        $job = new OrderSyncValidationJob(
            syncLogId: 123,
            batchId: 'test-batch-456',
            originalJobId: 'original-job-789'
        );

        $tags = $job->tags();

        $this->assertContains('validation', $tags);
        $this->assertContains('order-sync-validation', $tags);
        $this->assertContains('batch:test-batch-456', $tags);
        $this->assertContains('sync-log:123', $tags);
    }

    public function test_determine_overall_status_returns_failed_when_issues_exist(): void
    {
        $job = new OrderSyncValidationJob(
            syncLogId: 123,
            batchId: 'test-batch',
            originalJobId: 'original-job'
        );

        $validationResults = [
            'overall_status' => 'passed',
            'issues' => ['Some issue'],
        ];

        $reflection = new \ReflectionClass($job);
        $method = $reflection->getMethod('determineOverallStatus');
        $method->setAccessible(true);

        $result = $method->invoke($job, $validationResults);

        $this->assertEquals('failed', $result);
    }

    public function test_determine_overall_status_returns_failed_when_category_fails(): void
    {
        $job = new OrderSyncValidationJob(
            syncLogId: 123,
            batchId: 'test-batch',
            originalJobId: 'original-job'
        );

        $validationResults = [
            'overall_status' => 'passed',
            'issues' => [],
            'data_integrity' => ['status' => 'failed'],
        ];

        $reflection = new \ReflectionClass($job);
        $method = $reflection->getMethod('determineOverallStatus');
        $method->setAccessible(true);

        $result = $method->invoke($job, $validationResults);

        $this->assertEquals('failed', $result);
    }

    public function test_build_error_message_combines_all_errors(): void
    {
        $job = new OrderSyncValidationJob(
            syncLogId: 123,
            batchId: 'test-batch',
            originalJobId: 'original-job'
        );

        $validationResults = [
            'issues' => ['General issue 1', 'General issue 2'],
            'data_integrity' => [
                'status' => 'failed',
                'errors' => ['Data error 1', 'Data error 2']
            ],
            'sampling_validation' => [
                'status' => 'failed',
                'errors' => ['Sampling error']
            ],
        ];

        $reflection = new \ReflectionClass($job);
        $method = $reflection->getMethod('buildErrorMessage');
        $method->setAccessible(true);

        $result = $method->invoke($job, $validationResults);

        $this->assertStringContainsString('General issue 1', $result);
        $this->assertStringContainsString('General issue 2', $result);
        $this->assertStringContainsString('Data Integrity: Data error 1', $result);
        $this->assertStringContainsString('Data Integrity: Data error 2', $result);
        $this->assertStringContainsString('Sampling Validation: Sampling error', $result);
    }

    public function test_validation_results_are_saved_to_sync_log(): void
    {
        $validationResults = [
            'sync_log_id' => $this->syncLog->id,
            'batch_id' => $this->syncLog->batch_id,
            'validation_started_at' => now(),
            'overall_status' => 'passed',
            'data_integrity' => [
                'status' => 'passed',
                'record_counts' => [
                    'remote' => ['orders' => 100, 'order_items' => 250],
                    'local' => ['orders' => 100, 'order_items' => 250],
                    'matches' => true,
                ],
                'field_checksums' => [
                    'orders' => ['checksum_match' => true],
                    'order_items' => ['checksum_match' => true],
                ],
                'issues' => [],
            ],
            'sampling_validation' => [
                'status' => 'passed',
                'sample_size' => 10,
                'matched_records' => 10,
                'mismatched_records' => 0,
                'mismatches' => [],
            ],
            'business_logic' => [
                'status' => 'passed',
                'state_distribution' => ['new' => 50, 'fulfilled' => 50],
                'refund_status_distribution' => ['none' => 90, 'partial' => 10],
                'issues' => [],
            ],
            'relationship_consistency' => [
                'status' => 'passed',
                'order_items_consistency' => ['status' => 'passed'],
                'issues' => [],
            ],
            'issues' => [],
            'validation_completed_at' => now(),
        ];

        $this->validationService
            ->shouldReceive('validateSync')
            ->once()
            ->with(Mockery::type(SyncLog::class))
            ->andReturn($validationResults);

        $this->progressService
            ->shouldReceive('markValidationStarted')
            ->once()
            ->with('original-job-123', Mockery::type('array'));

        $this->progressService
            ->shouldReceive('markValidationCompleted')
            ->once()
            ->with('original-job-123', Mockery::type('array'));

        Log::shouldReceive('info')->atLeast()->once();

        $job = new OrderSyncValidationJob(
            syncLogId: $this->syncLog->id,
            batchId: $this->syncLog->batch_id,
            originalJobId: 'original-job-123'
        );

        // Mock the job ID
        $mockJob = Mockery::mock();
        $mockJob->shouldReceive('getJobId')->andReturn('validation-job-456');
        $job->job = $mockJob;

        $job->handle($this->validationService, $this->progressService);

        // Assert that detailed validation results were saved correctly
        $this->syncLog->refresh();
        $savedResults = $this->syncLog->validation_results;

        $this->assertNotNull($savedResults);
        $this->assertEquals($validationResults['overall_status'], $savedResults['overall_status']);
        $this->assertEquals($validationResults['sync_log_id'], $savedResults['sync_log_id']);
        $this->assertEquals($validationResults['batch_id'], $savedResults['batch_id']);

        // Check data integrity results
        $this->assertArrayHasKey('data_integrity', $savedResults);
        $this->assertEquals('passed', $savedResults['data_integrity']['status']);
        $this->assertArrayHasKey('record_counts', $savedResults['data_integrity']);
        $this->assertArrayHasKey('field_checksums', $savedResults['data_integrity']);

        // Check sampling validation results
        $this->assertArrayHasKey('sampling_validation', $savedResults);
        $this->assertEquals('passed', $savedResults['sampling_validation']['status']);
        $this->assertEquals(10, $savedResults['sampling_validation']['sample_size']);

        // Check business logic results
        $this->assertArrayHasKey('business_logic', $savedResults);
        $this->assertEquals('passed', $savedResults['business_logic']['status']);

        // Check relationship consistency results
        $this->assertArrayHasKey('relationship_consistency', $savedResults);
        $this->assertEquals('passed', $savedResults['relationship_consistency']['status']);

        // Check that issues array is empty for passed validation
        $this->assertEmpty($savedResults['issues']);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
