<?php

declare(strict_types=1);

namespace Tests\Unit\Jobs;

use App\Contracts\OrderSyncServiceInterface;
use App\Jobs\OrderSyncJob;
use App\Jobs\OrderSyncValidationJob;
use App\Models\SyncLog;
use App\Services\SyncProgressService;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Queue;
use Mockery;
use Tests\TestCase;

final class OrderSyncJobTest extends TestCase
{
    use RefreshDatabase;

    private OrderSyncServiceInterface $syncService;
    private SyncProgressService $progressService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->syncService = Mockery::mock(OrderSyncServiceInterface::class);
        $this->progressService = Mockery::mock(SyncProgressService::class);
        
        $this->app->instance(OrderSyncServiceInterface::class, $this->syncService);
        $this->app->instance(SyncProgressService::class, $this->progressService);
    }

    public function test_job_constructor_sets_properties(): void
    {
        $config = ['batch_size' => 100];
        $batchId = 'test-batch-123';

        $job = new OrderSyncJob($config, $batchId);

        $this->assertEquals($config, $job->config);
        $this->assertEquals($batchId, $job->batchId);
        $this->assertFalse($job->isRetry);
        $this->assertNull($job->originalBatchId);
        $this->assertEquals(7200, $job->timeout);
        $this->assertEquals(1, $job->tries);
        $this->assertTrue($job->failOnTimeout);
    }

    public function test_job_constructor_sets_retry_properties(): void
    {
        $config = ['batch_size' => 100];
        $batchId = 'test-batch-123';
        $originalBatchId = 'original-batch-456';

        $job = new OrderSyncJob($config, $batchId, true, $originalBatchId);

        $this->assertTrue($job->isRetry);
        $this->assertEquals($originalBatchId, $job->originalBatchId);
    }

    public function test_handle_dispatches_validation_job_when_sync_succeeds(): void
    {
        Queue::fake();

        $config = ['batch_size' => 100];
        $batchId = 'test-batch-123';
        $jobId = 'sync-job-456';

        // Create completed sync log
        $syncLog = SyncLog::factory()->create([
            'batch_id' => $batchId,
            'status' => 'completed',
            'total_records' => 1000,
            'success_records' => 950,
            'failed_records' => 50,
        ]);

        $this->syncService
            ->shouldReceive('syncInQueue')
            ->once()
            ->with($config, $batchId, Mockery::type('callable'))
            ->andReturn($syncLog);

        $this->progressService
            ->shouldReceive('initializeProgress')
            ->once()
            ->with($jobId, $batchId);

        $this->progressService
            ->shouldReceive('markSyncCompleted')
            ->once()
            ->with($jobId, Mockery::on(function ($data) use ($syncLog) {
                return $data['status'] === 'validating' &&
                       $data['sync_log_id'] === $syncLog->id;
            }));

        Log::shouldReceive('info')->atLeast()->once();
        Log::shouldReceive('debug')->atLeast()->once();

        $job = new OrderSyncJob($config, $batchId);

        // Mock the job ID
        $mockJob = Mockery::mock();
        $mockJob->shouldReceive('getJobId')->andReturn($jobId);
        $job->job = $mockJob;

        $job->handle($this->syncService, $this->progressService);

        // Assert validation job was dispatched
        Queue::assertPushed(OrderSyncValidationJob::class, function ($job) use ($syncLog, $batchId, $jobId) {
            return $job->syncLogId === $syncLog->id &&
                   $job->batchId === $batchId &&
                   $job->originalJobId === $jobId;
        });
    }

    public function test_handle_does_not_dispatch_validation_job_when_sync_fails(): void
    {
        Queue::fake();

        $config = ['batch_size' => 100];
        $batchId = 'test-batch-123';
        $jobId = 'sync-job-456';

        // Create failed sync log
        $syncLog = SyncLog::factory()->create([
            'batch_id' => $batchId,
            'status' => 'failed',
            'total_records' => 1000,
            'success_records' => 0,
            'failed_records' => 1000,
        ]);

        $this->syncService
            ->shouldReceive('syncInQueue')
            ->once()
            ->with($config, $batchId, Mockery::type('callable'))
            ->andReturn($syncLog);

        $this->progressService
            ->shouldReceive('initializeProgress')
            ->once()
            ->with($jobId, $batchId);

        $this->progressService
            ->shouldReceive('markCompleted')
            ->once()
            ->with($jobId, Mockery::on(function ($data) {
                return $data['status'] === 'failed';
            }));

        Log::shouldReceive('info')->atLeast()->once();
        Log::shouldReceive('debug')->atLeast()->once();

        $job = new OrderSyncJob($config, $batchId);

        // Mock the job ID
        $mockJob = Mockery::mock();
        $mockJob->shouldReceive('getJobId')->andReturn($jobId);
        $job->job = $mockJob;

        $job->handle($this->syncService, $this->progressService);

        // Assert validation job was NOT dispatched
        Queue::assertNotPushed(OrderSyncValidationJob::class);
    }

    public function test_handle_executes_retry_sync_when_is_retry(): void
    {
        Queue::fake();

        $config = ['batch_size' => 100];
        $batchId = 'test-batch-123';
        $originalBatchId = 'original-batch-456';
        $jobId = 'sync-job-789';

        // Create completed sync log
        $syncLog = SyncLog::factory()->create([
            'batch_id' => $batchId,
            'status' => 'completed',
        ]);

        $this->syncService
            ->shouldReceive('reSyncInQueue')
            ->once()
            ->with($originalBatchId, $config, Mockery::type('callable'))
            ->andReturn($syncLog);

        $this->progressService
            ->shouldReceive('initializeProgress')
            ->once()
            ->with($jobId, $batchId);

        $this->progressService
            ->shouldReceive('markSyncCompleted')
            ->once()
            ->with($jobId, Mockery::type('array'));

        Log::shouldReceive('info')->atLeast()->once();
        Log::shouldReceive('debug')->atLeast()->once();

        $job = new OrderSyncJob($config, $batchId, true, $originalBatchId);

        // Mock the job ID
        $mockJob = Mockery::mock();
        $mockJob->shouldReceive('getJobId')->andReturn($jobId);
        $job->job = $mockJob;

        $job->handle($this->syncService, $this->progressService);

        // Assert validation job was dispatched
        Queue::assertPushed(OrderSyncValidationJob::class);
    }

    public function test_handle_catches_exceptions_and_marks_failed(): void
    {
        $config = ['batch_size' => 100];
        $batchId = 'test-batch-123';
        $jobId = 'sync-job-456';
        $exception = new Exception('Sync service error');

        $this->syncService
            ->shouldReceive('syncInQueue')
            ->once()
            ->with($config, $batchId, Mockery::type('callable'))
            ->andThrow($exception);

        $this->progressService
            ->shouldReceive('initializeProgress')
            ->once()
            ->with($jobId, $batchId);

        $this->progressService
            ->shouldReceive('markFailed')
            ->once()
            ->with($jobId, Mockery::on(function ($data) {
                return isset($data['error_message']) && 
                       str_contains($data['error_message'], 'Sync service error');
            }));

        Log::shouldReceive('info')->atLeast()->once();
        Log::shouldReceive('error')->atLeast()->once();
        Log::shouldReceive('debug')->atLeast()->once();

        $job = new OrderSyncJob($config, $batchId);

        // Mock the job ID
        $mockJob = Mockery::mock();
        $mockJob->shouldReceive('getJobId')->andReturn($jobId);
        $job->job = $mockJob;

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Sync service error');

        $job->handle($this->syncService, $this->progressService);
    }

    public function test_failed_method_updates_sync_log_status(): void
    {
        $batchId = 'test-batch-123';
        $exception = new Exception('Permanent failure');

        // Create sync log
        $syncLog = SyncLog::factory()->create([
            'batch_id' => $batchId,
            'status' => 'processing',
        ]);

        Log::shouldReceive('error')->atLeast()->once();

        $job = new OrderSyncJob([], $batchId);

        // Mock the job
        $mockJob = Mockery::mock();
        $mockJob->shouldReceive('getJobId')->andReturn('sync-job-456');
        $job->job = $mockJob;

        $job->failed($exception);

        // Assert sync log was updated
        $syncLog->refresh();
        $this->assertEquals('failed', $syncLog->status);
        $this->assertEquals('Permanent failure', $syncLog->error_message);
        $this->assertNotNull($syncLog->completed_at);
    }

    public function test_tags_returns_expected_tags(): void
    {
        $batchId = 'test-batch-123';

        $job = new OrderSyncJob([], $batchId);
        $tags = $job->tags();

        $this->assertContains('sync', $tags);
        $this->assertContains('order-sync', $tags);
        $this->assertContains('batch:test-batch-123', $tags);
        $this->assertContains('initial', $tags);

        $retryJob = new OrderSyncJob([], $batchId, true);
        $retryTags = $retryJob->tags();

        $this->assertContains('retry', $retryTags);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
