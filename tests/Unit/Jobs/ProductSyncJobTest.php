<?php

declare(strict_types=1);

namespace Tests\Unit\Jobs;

use App\Contracts\ProductSyncServiceInterface;
use App\Jobs\ProductSyncJob;
use App\Models\SyncLog;
use App\Services\SyncProgressService;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Queue\Jobs\Job;
use Mockery;
use Tests\TestCase;

final class ProductSyncJobTest extends TestCase
{
    use RefreshDatabase;

    private ProductSyncServiceInterface $syncService;
    private SyncProgressService $progressService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->syncService = Mockery::mock(ProductSyncServiceInterface::class);
        $this->progressService = Mockery::mock(SyncProgressService::class);

        // Bind the mocks to the service container
        $this->app->instance(ProductSyncServiceInterface::class, $this->syncService);
        $this->app->instance(SyncProgressService::class, $this->progressService);
    }

    public function test_job_initializes_progress_tracking(): void
    {
        $config = ['incremental' => true];
        $batchId = 'test-batch-id';
        $jobId = 'test-job-id';

        $job = new ProductSyncJob($config, $batchId);
        
        // Mock the job instance
        $mockJob = Mockery::mock(Job::class);
        $mockJob->shouldReceive('getJobId')->andReturn($jobId);
        $job->job = $mockJob;

        $this->progressService
            ->shouldReceive('initializeProgress')
            ->once()
            ->with($jobId, $batchId);

        $this->progressService
            ->shouldReceive('updateProgress')
            ->atLeast()
            ->once();

        $syncLog = SyncLog::factory()->make([
            'id' => 1,
            'batch_id' => $batchId,
            'status' => 'completed',
            'total_records' => 100,
            'success_records' => 95,
            'failed_records' => 5,
            'progress_percentage' => 100,
        ]);

        $this->syncService
            ->shouldReceive('syncInQueue')
            ->once()
            ->with($config, $batchId, Mockery::type('callable'))
            ->andReturnUsing(function($config, $batchId, $callback) use ($syncLog) {
                // Simulate calling the progress callback
                if ($callback) {
                    $callback(5, 10, 30); // processed_chunks, total_chunks, elapsed_time
                }
                return $syncLog;
            });

        $this->progressService
            ->shouldReceive('markCompleted')
            ->once()
            ->with($jobId, Mockery::type('array'));

        $job->handle($this->syncService, $this->progressService);

        // Assert that the job completed successfully
        $this->assertTrue(true); // Mockery expectations are verified automatically
    }

    public function test_job_handles_retry_sync(): void
    {
        $config = ['incremental' => true];
        $batchId = 'new-batch-id';
        $originalBatchId = 'original-batch-id';
        $jobId = 'test-job-id';

        $job = new ProductSyncJob($config, $batchId, true, $originalBatchId);
        
        // Mock the job instance
        $mockJob = Mockery::mock(Job::class);
        $mockJob->shouldReceive('getJobId')->andReturn($jobId);
        $job->job = $mockJob;

        $this->progressService
            ->shouldReceive('initializeProgress')
            ->once()
            ->with($jobId, $batchId);

        $this->progressService
            ->shouldReceive('updateProgress')
            ->atLeast()
            ->once();

        $syncLog = SyncLog::factory()->make([
            'id' => 1,
            'batch_id' => $batchId,
            'status' => 'completed',
        ]);

        $this->syncService
            ->shouldReceive('reSyncInQueue')
            ->once()
            ->with($originalBatchId, $config, Mockery::type('callable'))
            ->andReturnUsing(function($originalBatchId, $config, $callback) use ($syncLog) {
                // Simulate calling the progress callback
                if ($callback) {
                    $callback(3, 6, 20); // processed_chunks, total_chunks, elapsed_time
                }
                return $syncLog;
            });

        $this->progressService
            ->shouldReceive('markCompleted')
            ->once()
            ->with($jobId, Mockery::type('array'));

        $job->handle($this->syncService, $this->progressService);

        // Assert that the job completed successfully
        $this->assertTrue(true); // Mockery expectations are verified automatically
    }

    public function test_job_handles_sync_failure(): void
    {
        $config = ['incremental' => true];
        $batchId = 'test-batch-id';
        $jobId = 'test-job-id';
        $exception = new Exception('Sync failed');

        $job = new ProductSyncJob($config, $batchId);
        
        // Mock the job instance
        $mockJob = Mockery::mock(Job::class);
        $mockJob->shouldReceive('getJobId')->andReturn($jobId);
        $job->job = $mockJob;

        $this->progressService
            ->shouldReceive('initializeProgress')
            ->once()
            ->with($jobId, $batchId);

        $this->syncService
            ->shouldReceive('syncInQueue')
            ->once()
            ->with($config, $batchId, Mockery::type('callable'))
            ->andThrow($exception);

        $this->progressService
            ->shouldReceive('markFailed')
            ->once()
            ->with($jobId, Mockery::type('array'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Sync failed');

        $job->handle($this->syncService, $this->progressService);
    }

    public function test_job_updates_sync_log_on_failure(): void
    {
        $config = ['incremental' => true];
        $batchId = 'test-batch-id';
        $exception = new Exception('Job failed');

        // Create a sync log
        $syncLog = SyncLog::factory()->create([
            'batch_id' => $batchId,
            'status' => 'processing',
        ]);

        $job = new ProductSyncJob($config, $batchId);
        
        // Mock the job instance
        $mockJob = Mockery::mock(Job::class);
        $mockJob->shouldReceive('getJobId')->andReturn('test-job-id');
        $job->job = $mockJob;

        $job->failed($exception);

        $syncLog->refresh();
        $this->assertEquals('failed', $syncLog->status);
        $this->assertEquals('Job failed', $syncLog->error_message);
        $this->assertNotNull($syncLog->completed_at);
    }

    public function test_job_has_correct_tags(): void
    {
        $config = ['incremental' => true];
        $batchId = 'test-batch-id';

        $job = new ProductSyncJob($config, $batchId);
        $tags = $job->tags();

        $this->assertContains('sync', $tags);
        $this->assertContains('product-sync', $tags);
        $this->assertContains("batch:{$batchId}", $tags);
        $this->assertContains('initial', $tags);
    }

    public function test_retry_job_has_correct_tags(): void
    {
        $config = ['incremental' => true];
        $batchId = 'test-batch-id';
        $originalBatchId = 'original-batch-id';

        $job = new ProductSyncJob($config, $batchId, true, $originalBatchId);
        $tags = $job->tags();

        $this->assertContains('sync', $tags);
        $this->assertContains('product-sync', $tags);
        $this->assertContains("batch:{$batchId}", $tags);
        $this->assertContains('retry', $tags);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
