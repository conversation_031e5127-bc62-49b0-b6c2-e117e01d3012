<?php

namespace Tests;

use Illuminate\Foundation\Testing\TestCase as BaseTestCase;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Config;

abstract class TestCase extends BaseTestCase
{
    protected function setUp(): void
    {
        parent::setUp();

        // Ensure all tests run with English locale by default
        App::setLocale('en');

        // Ensure tests always use the correct database configuration
        $this->ensureTestingEnvironment();
    }

    /**
     * Ensure we're running in a safe testing environment
     */
    protected function ensureTestingEnvironment(): void
    {
        // Force testing environment database configuration to use MySQL
        Config::set('database.default', 'mysql');

        // Configure main MySQL connection for testing
        Config::set('database.connections.mysql', [
            'driver' => 'mysql',
            'host' => env('DB_HOST', 'mysql'),
            'port' => env('DB_PORT', '3306'),
            'database' => env('DB_DATABASE', 'laravel_testing'),
            'username' => env('DB_USERNAME', 'root'),
            'password' => env('DB_PASSWORD', 'test123'),
            'unix_socket' => '',
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'prefix_indexes' => true,
            'strict' => true,
            'engine' => null,
        ]);

        // Configure store connection for testing (use same MySQL testing database)
        Config::set('database.connections.store', [
            'driver' => 'mysql',
            'host' => env('STORE_DB_HOST', env('DB_HOST', 'mysql')),
            'port' => env('STORE_DB_PORT', env('DB_PORT', '3306')),
            'database' => env('STORE_DB_DATABASE', env('DB_DATABASE', 'laravel_testing')),
            'username' => env('STORE_DB_USERNAME', env('DB_USERNAME', 'root')),
            'password' => env('STORE_DB_PASSWORD', env('DB_PASSWORD', 'test123')),
            'unix_socket' => '',
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'prefix_indexes' => true,
            'strict' => true,
            'engine' => null,
        ]);

        // Ensure we're not accidentally using production/development databases
        $currentEnv = app()->environment();
        if ($currentEnv !== 'testing') {
            // If not in testing environment, force safe database settings
            Config::set('database.connections.mysql.database', 'testing_' . time());
            Config::set('database.connections.mysql.host', 'localhost');
        }

        // Force cache to array driver for tests
        Config::set('cache.default', 'array');
        Config::set('session.driver', 'array');
        Config::set('queue.default', 'sync');
    }
}
