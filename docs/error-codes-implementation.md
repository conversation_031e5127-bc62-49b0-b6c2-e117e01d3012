# Error Codes Implementation

## 概述

本文档描述了为 API 错误响应添加 `error_code` 字段的实现，以便前端更好地处理和识别不同类型的错误。

## 实现的更改

### 1. 创建错误代码常量类

创建了 `App\Constants\ErrorCodes` 类来集中管理所有错误代码：

```php
// app/Constants/ErrorCodes.php
final class ErrorCodes
{
    // Organisation related errors
    public const ORGANISATION_ALREADY_SUSPENDED = 'ORGANISATION_ALREADY_SUSPENDED';
    
    // Invitation related errors
    public const INVITATION_EXPIRED = 'INVITATION_EXPIRED';
    public const INVITATION_USAGE_LIMIT_REACHED = 'INVITATION_USAGE_LIMIT_REACHED';
    public const INVITATION_PROCESSING_ERROR = 'INVITATION_PROCESSING_ERROR';
    
    // User related errors
    public const VERIFICATION_CODE_RETRY_LIMIT = 'VERIFICATION_CODE_RETRY_LIMIT';
    public const VERIFICATION_CODE_SEND_ERROR = 'VERIFICATION_CODE_SEND_ERROR';
    
    // User role related errors
    public const ROLE_ASSIGNMENT_FAILED = 'ROLE_ASSIGNMENT_FAILED';
    public const ROLE_REMOVAL_FAILED = 'ROLE_REMOVAL_FAILED';
    public const ROLE_TRANSFER_FAILED = 'ROLE_TRANSFER_FAILED';
    
    // Generic business logic errors
    public const BUSINESS_LOGIC_ERROR = 'BUSINESS_LOGIC_ERROR';
    public const RESOURCE_STATE_CONFLICT = 'RESOURCE_STATE_CONFLICT';
}
```

### 2. 更新 ApiController

修改了 `ApiController::errorResponse()` 方法以支持 `error_code` 参数：

```php
protected function errorResponse(
    string $message = 'Error occurred',
    mixed $errors = null,
    int $statusCode = 400,
    ?string $errorCode = null
): JsonResponse {
    return \App\Services\ApiExceptionService::createErrorResponse($message, $statusCode, $errors, $errorCode);
}
```

### 3. 更新控制器中的错误响应

#### OrganisationController
- 组织已暂停状态错误：`ORGANISATION_ALREADY_SUSPENDED`

#### InvitationController
- 邀请链接已过期：`INVITATION_EXPIRED`
- 邀请链接达到使用限制：`INVITATION_USAGE_LIMIT_REACHED`
- 邀请处理错误：`INVITATION_PROCESSING_ERROR`

#### UserController
- 验证码重发限制：`VERIFICATION_CODE_RETRY_LIMIT`
- 验证码发送错误：`VERIFICATION_CODE_SEND_ERROR`

#### UserRoleController
- 统一使用标准化的错误响应方法，确保包含 `error_code` 字段

### 4. 更新测试用例

为所有修改的错误响应添加了相应的测试断言，确保 `error_code` 字段正确返回。

## 错误响应格式

所有错误响应现在都包含以下字段：

```json
{
    "success": false,
    "message": "错误消息",
    "errors": null,
    "error_code": "ERROR_CODE_CONSTANT",
    "timestamp": "2025-06-20T08:00:00.000000Z"
}
```

## 最佳实践

### 1. 错误代码命名规范
- 使用 `UPPER_CASE` 格式
- 描述性强，易于理解
- 按功能模块分组

### 2. 维护性考虑
- 所有错误代码集中在 `ErrorCodes` 类中
- 提供了 `getAllCodes()` 和 `exists()` 方法用于验证
- 错误代码常量化，避免硬编码

### 3. 向后兼容性
- 现有的错误响应格式保持不变
- `error_code` 字段为可选，不会破坏现有功能
- 标准化的错误响应方法已经包含 `error_code` 支持

## 测试覆盖

- 创建了 `ErrorCodesTest` 单元测试
- 更新了所有相关的功能测试
- 确保所有错误响应都包含正确的 `error_code`

## 使用示例

### 前端处理示例

```javascript
// 处理 API 错误响应
if (!response.success) {
    switch (response.error_code) {
        case 'ORGANISATION_ALREADY_SUSPENDED':
            showMessage('组织已经处于暂停状态');
            break;
        case 'INVITATION_EXPIRED':
            showMessage('邀请链接已过期，请联系管理员');
            break;
        case 'VERIFICATION_CODE_RETRY_LIMIT':
            showMessage('请稍后再试');
            startCountdown();
            break;
        default:
            showMessage(response.message);
    }
}
```

## 总结

通过这次实现，我们：

1. ✅ 为所有控制器中的自定义错误添加了 `error_code` 字段
2. ✅ 创建了集中化的错误代码管理系统
3. ✅ 更新了相应的测试用例
4. ✅ 确保了向后兼容性
5. ✅ 提供了良好的维护性和扩展性

这为前端提供了更好的错误处理能力，同时保持了代码的整洁和可维护性。
