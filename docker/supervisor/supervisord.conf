[unix_http_server]
file=/var/www/storage/logs/supervisor.sock
chmod=0700

[supervisord]
logfile=/var/www/storage/logs/supervisord.log
pidfile=/var/www/storage/logs/supervisord.pid
childlogdir=/var/www/storage/logs
nodaemon=true

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisorctl]
serverurl=unix:///var/www/storage/logs/supervisor.sock

[include]
files = /etc/supervisor/conf.d/*.conf
