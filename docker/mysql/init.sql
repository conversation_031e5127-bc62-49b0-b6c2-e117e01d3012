-- Create testing database for development and testing environments
-- This script is only mounted in development and testing environments
-- Production environment uses docker-compose.production.yml which doesn't mount this script

CREATE DATABASE IF NOT EXISTS `laravel_testing` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Grant privileges to root user for testing database
GRANT ALL PRIVILEGES ON `laravel_testing`.* TO 'root'@'%';

-- Flush privileges
FLUSH PRIVILEGES;
