#!/bin/bash

# Ensure storage directories exist and have correct permissions
mkdir -p /var/www/storage/logs
chmod -R 755 /var/www/storage

# Wait for Redis to be available
echo "Waiting for Redis to be available..."
until nc -z redis 6379; do
  echo "Redis is unavailable - sleeping"
  sleep 1
done
echo "Redis is up - continuing"

# Start supervisor in the background
supervisord -c /etc/supervisor/conf.d/supervisord.conf &

# Start PHP-FPM in the foreground
exec php-fpm
