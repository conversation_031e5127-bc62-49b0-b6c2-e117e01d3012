<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Order;
use App\Models\OrderItem;
use Illuminate\Database\Seeder;
use Illuminate\Support\Carbon;

final class OrderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create orders with different states, countries, and time periods for analytics
        $this->createCompletedOrders();
        $this->createCancelledOrders();
        $this->createRefundedOrders();
        $this->createRecentOrders();
    }

    /**
     * Create completed orders from different time periods and regions
     */
    private function createCompletedOrders(): void
    {
        $orders = [
            // US orders - high value
            [
                'store_order_id' => 10001,
                'order_number' => 'ORD-2025-001',
                'state' => 'completed',
                'completed_at' => Carbon::now()->subDays(1),
                'items_total' => 15000, // $150.00
                'adjustments_total' => -1500, // $15.00 discount
                'total_amount' => 13500, // $135.00
                'currency_code' => 'USD',
                'payment_state' => 'completed',
                'shipping_country' => 'US',
                'customer_id' => 1001,
                'store_updated_at' => Carbon::now()->subDays(1),
                'items' => [
                    [
                        'store_order_item_id' => 20001,
                        'store_variant_id' => 1001,
                        'product_name' => 'Adventure Quest Deluxe',
                        'variant_name' => 'Standard Edition',
                        'quantity' => 2,
                        'unit_price' => 2999, // $29.99
                        'units_total' => 5998,
                        'adjustments_total' => -600, // discount
                        'total' => 5398,
                    ],
                    [
                        'store_order_item_id' => 20002,
                        'store_variant_id' => 1002,
                        'product_name' => 'Space Explorer Pro',
                        'variant_name' => 'Premium Edition',
                        'quantity' => 1,
                        'unit_price' => 4999, // $49.99
                        'units_total' => 4999,
                        'adjustments_total' => -500, // discount
                        'total' => 4499,
                    ],
                    [
                        'store_order_item_id' => 20003,
                        'store_variant_id' => 1003,
                        'product_name' => 'Racing Championship',
                        'variant_name' => 'Collector Edition',
                        'quantity' => 1,
                        'unit_price' => 3999, // $39.99
                        'units_total' => 3999,
                        'adjustments_total' => -400, // discount
                        'total' => 3599,
                    ],
                ],
            ],
            // UK orders - medium value
            [
                'store_order_id' => 10002,
                'order_number' => 'ORD-2025-002',
                'state' => 'completed',
                'completed_at' => Carbon::now()->subDays(3),
                'items_total' => 8000, // $80.00
                'adjustments_total' => 800, // $8.00 tax
                'total_amount' => 8800, // $88.00
                'currency_code' => 'GBP',
                'payment_state' => 'completed',
                'shipping_country' => 'GB',
                'customer_id' => 1002,
                'store_updated_at' => Carbon::now()->subDays(3),
                'items' => [
                    [
                        'store_order_item_id' => 20004,
                        'store_variant_id' => 1004,
                        'product_name' => 'Mystery Detective',
                        'variant_name' => 'Standard Edition',
                        'quantity' => 2,
                        'unit_price' => 1999, // $19.99
                        'units_total' => 3998,
                        'adjustments_total' => 400, // tax
                        'total' => 4398,
                    ],
                    [
                        'store_order_item_id' => 20005,
                        'store_variant_id' => 1005,
                        'product_name' => 'Fantasy Kingdom',
                        'variant_name' => 'Digital Download',
                        'quantity' => 1,
                        'unit_price' => 3999, // $39.99
                        'units_total' => 3999,
                        'adjustments_total' => 400, // tax
                        'total' => 4399,
                    ],
                ],
            ],
            // German orders - low value
            [
                'store_order_id' => 10003,
                'order_number' => 'ORD-2025-003',
                'state' => 'completed',
                'completed_at' => Carbon::now()->subWeek(),
                'items_total' => 2999, // $29.99
                'adjustments_total' => 300, // $3.00 tax
                'total_amount' => 3299, // $32.99
                'currency_code' => 'EUR',
                'payment_state' => 'completed',
                'shipping_country' => 'DE',
                'customer_id' => 1003,
                'store_updated_at' => Carbon::now()->subWeek(),
                'items' => [
                    [
                        'store_order_item_id' => 20006,
                        'store_variant_id' => 1006,
                        'product_name' => 'Puzzle Master',
                        'variant_name' => 'Basic Edition',
                        'quantity' => 1,
                        'unit_price' => 2999, // $29.99
                        'units_total' => 2999,
                        'adjustments_total' => 300, // tax
                        'total' => 3299,
                    ],
                ],
            ],
        ];

        foreach ($orders as $orderData) {
            $items = $orderData['items'];
            unset($orderData['items']);
            
            $order = Order::create($orderData);
            
            foreach ($items as $itemData) {
                $itemData['order_id'] = $order->id;
                OrderItem::create($itemData);
            }
        }
    }

    /**
     * Create cancelled orders for analytics
     */
    private function createCancelledOrders(): void
    {
        $cancelledOrder = Order::create([
            'store_order_id' => 10004,
            'order_number' => 'ORD-2025-004',
            'state' => 'cancelled',
            'completed_at' => Carbon::now()->subDays(2),
            'items_total' => 5999, // $59.99
            'adjustments_total' => 0,
            'total_amount' => 5999,
            'currency_code' => 'USD',
            'payment_state' => 'cancelled',
            'shipping_country' => 'CA',
            'customer_id' => 1004,
            'store_updated_at' => Carbon::now()->subDays(2),
        ]);

        OrderItem::create([
            'order_id' => $cancelledOrder->id,
            'store_order_item_id' => 20007,
            'store_variant_id' => 1007,
            'product_name' => 'Action Hero Saga',
            'variant_name' => 'Deluxe Edition',
            'quantity' => 1,
            'unit_price' => 5999,
            'units_total' => 5999,
            'adjustments_total' => 0,
            'total' => 5999,
        ]);
    }

    /**
     * Create orders with refunds for refund analytics
     */
    private function createRefundedOrders(): void
    {
        // Partially refunded order
        $partialRefundOrder = Order::create([
            'store_order_id' => 10005,
            'order_number' => 'ORD-2025-005',
            'state' => 'completed',
            'completed_at' => Carbon::now()->subDays(5),
            'items_total' => 12000, // $120.00
            'adjustments_total' => 0,
            'total_amount' => 12000,
            'currency_code' => 'USD',
            'payment_state' => 'completed',
            'shipping_country' => 'US',
            'customer_id' => 1005,
            'refund_total' => 4000, // $40.00 refunded
            'refund_comment' => 'Customer returned one item - defective product',
            'refund_status' => 'success',
            'refunded_at' => Carbon::now()->subDays(3),
            'store_updated_at' => Carbon::now()->subDays(5),
        ]);

        OrderItem::create([
            'order_id' => $partialRefundOrder->id,
            'store_order_item_id' => 20008,
            'store_variant_id' => 1008,
            'product_name' => 'Strategy Empire',
            'variant_name' => 'Standard Edition',
            'quantity' => 2,
            'unit_price' => 3999,
            'units_total' => 7998,
            'adjustments_total' => 0,
            'total' => 7998,
            'quantity_refunded' => 1, // One item refunded
        ]);

        OrderItem::create([
            'order_id' => $partialRefundOrder->id,
            'store_order_item_id' => 20009,
            'store_variant_id' => 1009,
            'product_name' => 'Simulation City',
            'variant_name' => 'Premium Edition',
            'quantity' => 1,
            'unit_price' => 3999,
            'units_total' => 3999,
            'adjustments_total' => 0,
            'total' => 3999,
            'quantity_refunded' => 0, // Not refunded
        ]);

        // Fully refunded order
        $fullRefundOrder = Order::create([
            'store_order_id' => 10006,
            'order_number' => 'ORD-2025-006',
            'state' => 'completed',
            'completed_at' => Carbon::now()->subDays(10),
            'items_total' => 6999, // $69.99
            'adjustments_total' => 700, // $7.00 tax
            'total_amount' => 7699, // $76.99
            'currency_code' => 'EUR',
            'payment_state' => 'completed',
            'shipping_country' => 'FR',
            'customer_id' => 1006,
            'refund_total' => 7699, // Full refund
            'refund_comment' => 'Order cancelled by customer within return period',
            'refund_status' => 'success',
            'refunded_at' => Carbon::now()->subDays(8),
            'store_updated_at' => Carbon::now()->subDays(10),
        ]);

        OrderItem::create([
            'order_id' => $fullRefundOrder->id,
            'store_order_item_id' => 20010,
            'store_variant_id' => 1010,
            'product_name' => 'Horror Mansion',
            'variant_name' => 'Collector Edition',
            'quantity' => 1,
            'unit_price' => 6999,
            'units_total' => 6999,
            'adjustments_total' => 700,
            'total' => 7699,
            'quantity_refunded' => 1, // Fully refunded
        ]);
    }

    /**
     * Create recent orders for current period analytics
     */
    private function createRecentOrders(): void
    {
        // Today's orders
        $todayOrder = Order::create([
            'store_order_id' => 10007,
            'order_number' => 'ORD-2025-007',
            'state' => 'completed',
            'completed_at' => Carbon::now()->subHours(2),
            'items_total' => 9999, // $99.99
            'adjustments_total' => -999, // $9.99 discount
            'total_amount' => 9000, // $90.00
            'currency_code' => 'USD',
            'payment_state' => 'completed',
            'shipping_country' => 'AU',
            'customer_id' => 1007,
            'store_updated_at' => Carbon::now()->subHours(2),
        ]);

        OrderItem::create([
            'order_id' => $todayOrder->id,
            'store_order_item_id' => 20011,
            'store_variant_id' => 1011,
            'product_name' => 'Sports Championship',
            'variant_name' => 'Ultimate Edition',
            'quantity' => 1,
            'unit_price' => 9999,
            'units_total' => 9999,
            'adjustments_total' => -999,
            'total' => 9000,
        ]);

        // This week's order
        $weekOrder = Order::create([
            'store_order_id' => 10008,
            'order_number' => 'ORD-2025-008',
            'state' => 'completed',
            'completed_at' => Carbon::now()->subDays(4),
            'items_total' => 4500, // $45.00
            'adjustments_total' => 450, // $4.50 tax
            'total_amount' => 4950, // $49.50
            'currency_code' => 'CAD',
            'payment_state' => 'completed',
            'shipping_country' => 'CA',
            'customer_id' => 1008,
            'store_updated_at' => Carbon::now()->subDays(4),
        ]);

        OrderItem::create([
            'order_id' => $weekOrder->id,
            'store_order_item_id' => 20012,
            'store_variant_id' => 1012,
            'product_name' => 'Educational Quiz',
            'variant_name' => 'Family Pack',
            'quantity' => 3,
            'unit_price' => 1499,
            'units_total' => 4497,
            'adjustments_total' => 450,
            'total' => 4947,
        ]);
    }
}
