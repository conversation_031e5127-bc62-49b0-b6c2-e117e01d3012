<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Organisation;
use App\Services\PermissionService;
use Illuminate\Database\Seeder;

final class PermissionSeeder extends Seeder
{
    public function __construct(
        private readonly PermissionService $permissionService
    ) {}

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create system-wide roles first
        $this->command->info('Creating system-wide roles...');
        $systemRoles = $this->permissionService->createSystemRoles();
        $this->command->info('Created ' . count($systemRoles) . ' system roles');

        // Create default roles for each organisation
        $organisations = Organisation::whereIn('status', ['active', 'pending'])->get();

        foreach ($organisations as $organisation) {
            $this->command->info("Creating default roles for organisation: {$organisation->name}");
            $roles = $this->permissionService->createDefaultRoles($organisation->id);
            $this->command->info('Created ' . count($roles) . ' roles for organisation: ' . $organisation->name);
        }

        $this->command->info('Role seeding completed!');
    }
}
