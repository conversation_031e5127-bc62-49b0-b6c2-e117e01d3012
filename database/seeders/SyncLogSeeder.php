<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\SyncLog;
use App\Models\SyncRecord;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

final class SyncLogSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create first sync log - completed with some failed records
        $batchId1 = 'sync_' . Str::random(10) . '_' . now()->format('YmdHis');
        $syncLog1 = SyncLog::create([
            'sync_type' => 'product_sync',
            'batch_id' => $batchId1,
            'status' => 'completed',
            'started_at' => now()->subHours(2),
            'completed_at' => now()->subHours(1)->subMinutes(30),
            'total_records' => 5,
            'processed_records' => 5,
            'success_records' => 3,
            'failed_records' => 2,
            'summary' => [
                'sync_type' => 'incremental',
                'source_table' => 'sylius_product_variant',
                'target_table' => 'products',
                'duration_seconds' => 1800,
                'memory_peak_mb' => 128,
            ],
            'sync_config' => [
                'incremental' => true,
                'batch_size' => 100,
                'timeout' => 3600,
                'last_sync_date' => now()->subDays(1)->toDateTimeString(),
            ],
            'validation_results' => [
                'sync_log_id' => null, // Will be set after creation
                'batch_id' => $batchId1,
                'validation_started_at' => now()->subHours(1)->subMinutes(25),
                'overall_status' => 'passed',
                'data_integrity' => [
                    'status' => 'passed',
                    'record_counts' => [
                        'remote' => ['products' => 5],
                        'local' => ['products' => 5],
                        'matches' => true,
                    ],
                    'field_checksums' => [
                        'products' => ['checksum_match' => true],
                    ],
                    'issues' => [],
                ],
                'sampling_validation' => [
                    'status' => 'passed',
                    'sample_size' => 2,
                    'matched_records' => 2,
                    'mismatched_records' => 0,
                    'mismatches' => [],
                ],
                'business_logic' => [
                    'status' => 'passed',
                    'issues' => [],
                ],
                'relationship_consistency' => [
                    'status' => 'passed',
                    'issues' => [],
                ],
                'issues' => [],
                'validation_completed_at' => now()->subHours(1)->subMinutes(20),
            ],
        ]);

        // Update validation_results with the actual sync_log_id
        $validationResults = $syncLog1->validation_results;
        $validationResults['sync_log_id'] = $syncLog1->id;
        $syncLog1->update(['validation_results' => $validationResults]);

        // Create sync records for first batch
        $syncRecords1 = [
            [
                'batch_id' => $batchId1,
                'source_table' => 'sylius_product_variant',
                'source_id' => '1001',
                'target_table' => 'products',
                'target_id' => '1',
                'status' => 'success',
                'source_data' => [
                    'id' => 1001,
                    'sku' => 'GAME-001',
                    'name' => 'Adventure Quest Deluxe',
                    'price' => 2999,
                    'enabled' => true,
                ],
                'transformed_data' => [
                    'store_variant_id' => 1001,
                    'sku' => 'GAME-001',
                    'name' => 'Adventure Quest Deluxe',
                    'current_price' => 2999,
                    'enabled' => true,
                ],
                'created_at' => now()->subHours(2)->addMinutes(5),
            ],
            [
                'batch_id' => $batchId1,
                'source_table' => 'sylius_product_variant',
                'source_id' => '1002',
                'target_table' => 'products',
                'target_id' => '2',
                'status' => 'success',
                'source_data' => [
                    'id' => 1002,
                    'sku' => 'GAME-002',
                    'name' => 'Space Explorer Premium',
                    'price' => 4999,
                    'enabled' => true,
                ],
                'transformed_data' => [
                    'store_variant_id' => 1002,
                    'sku' => 'GAME-002',
                    'name' => 'Space Explorer Premium',
                    'current_price' => 4999,
                    'enabled' => true,
                ],
                'created_at' => now()->subHours(2)->addMinutes(10),
            ],
            [
                'batch_id' => $batchId1,
                'source_table' => 'sylius_product_variant',
                'source_id' => '1003',
                'target_table' => 'products',
                'target_id' => null,
                'status' => 'failed',
                'source_data' => [
                    'id' => 1003,
                    'sku' => 'GAME-003',
                    'name' => 'Mystery Mansion Collector Edition',
                    'price' => 7999,
                    'enabled' => false,
                ],
                'transformed_data' => null,
                'error_message' => 'Validation failed: Product name exceeds maximum length limit',
                'created_at' => now()->subHours(2)->addMinutes(15),
            ],
            [
                'batch_id' => $batchId1,
                'source_table' => 'sylius_product_variant',
                'source_id' => '1004',
                'target_table' => 'products',
                'target_id' => '4',
                'status' => 'success',
                'source_data' => [
                    'id' => 1004,
                    'sku' => 'GAME-004',
                    'name' => 'Racing Legends Standard',
                    'price' => 1999,
                    'enabled' => true,
                ],
                'transformed_data' => [
                    'store_variant_id' => 1004,
                    'sku' => 'GAME-004',
                    'name' => 'Racing Legends Standard',
                    'current_price' => 1999,
                    'enabled' => true,
                ],
                'created_at' => now()->subHours(2)->addMinutes(20),
            ],
            [
                'batch_id' => $batchId1,
                'source_table' => 'sylius_product_variant',
                'source_id' => '1005',
                'target_table' => 'products',
                'target_id' => null,
                'status' => 'failed',
                'source_data' => [
                    'id' => 1005,
                    'sku' => 'GAME-005',
                    'name' => 'Fantasy Kingdom Ultimate',
                    'price' => 5499,
                    'enabled' => true,
                ],
                'transformed_data' => null,
                'error_message' => 'Database constraint violation: Duplicate SKU code detected',
                'created_at' => now()->subHours(2)->addMinutes(25),
            ],
        ];

        foreach ($syncRecords1 as $recordData) {
            SyncRecord::create($recordData);
        }

        // Create second sync log - failed with error
        $batchId2 = 'sync_' . Str::random(10) . '_' . now()->format('YmdHis');
        $syncLog2 = SyncLog::create([
            'sync_type' => 'product_sync',
            'batch_id' => $batchId2,
            'status' => 'failed',
            'started_at' => now()->subDays(1),
            'completed_at' => null,
            'total_records' => 3,
            'processed_records' => 1,
            'success_records' => 0,
            'failed_records' => 1,
            'summary' => [
                'sync_type' => 'full',
                'source_table' => 'sylius_product_variant',
                'target_table' => 'products',
                'duration_seconds' => 300,
                'memory_peak_mb' => 64,
            ],
            'error_message' => 'Remote database connection timeout after 300 seconds',
            'sync_config' => [
                'incremental' => false,
                'batch_size' => 50,
                'timeout' => 1800,
            ],
            'validation_results' => [
                'sync_log_id' => null, // Will be set after creation
                'batch_id' => $batchId2,
                'validation_started_at' => now()->subDays(1)->addMinutes(5),
                'overall_status' => 'error',
                'error_message' => 'Validation could not be completed due to sync failure',
                'validation_completed_at' => now()->subDays(1)->addMinutes(5),
            ],
        ]);

        // Update validation_results with the actual sync_log_id
        $validationResults2 = $syncLog2->validation_results;
        $validationResults2['sync_log_id'] = $syncLog2->id;
        $syncLog2->update(['validation_results' => $validationResults2]);

        // Create sync records for second batch (only one processed before failure)
        $syncRecords2 = [
            [
                'batch_id' => $batchId2,
                'source_table' => 'sylius_product_variant',
                'source_id' => '2001',
                'target_table' => 'products',
                'target_id' => null,
                'status' => 'failed',
                'source_data' => [
                    'id' => 2001,
                    'sku' => 'GAME-006',
                    'name' => 'Combat Arena Pro',
                    'price' => 3999,
                    'enabled' => true,
                ],
                'transformed_data' => null,
                'error_message' => 'Remote connection lost during data retrieval',
                'created_at' => now()->subDays(1)->addMinutes(5),
            ],
        ];

        foreach ($syncRecords2 as $recordData) {
            SyncRecord::create($recordData);
        }
    }
}
