<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Order;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Order>
 */
final class OrderFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Order::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $itemsTotal = fake()->numberBetween(1000, 50000); // $10.00 to $500.00
        $adjustmentsTotal = fake()->numberBetween(-5000, 2000); // -$50.00 to $20.00
        $totalAmount = $itemsTotal + $adjustmentsTotal;
        
        $completedAt = fake()->dateTimeBetween('-6 months', 'now');
        $storeUpdatedAt = fake()->dateTimeBetween($completedAt, 'now');

        return [
            'store_order_id' => fake()->unique()->numberBetween(1000, 999999),
            'order_number' => 'ORD-' . fake()->year() . '-' . fake()->unique()->numberBetween(1000, 9999),
            'state' => fake()->randomElement(['completed', 'cancelled', 'pending', 'processing']),
            'completed_at' => $completedAt,
            'items_total' => $itemsTotal,
            'adjustments_total' => $adjustmentsTotal,
            'total_amount' => $totalAmount,
            'currency_code' => fake()->randomElement(['USD', 'EUR', 'GBP', 'CAD', 'JPY']),
            'payment_state' => fake()->randomElement(['completed', 'pending', 'failed', 'cancelled']),
            'shipping_country' => fake()->countryCode(),
            'customer_id' => fake()->numberBetween(1, 10000),
            'refund_total' => fake()->optional(0.1)->numberBetween(100, $totalAmount),
            'refund_comment' => fake()->optional(0.1)->sentence(),
            'refund_status' => fake()->optional(0.1)->randomElement(['success', 'pending', 'failed']),
            'refunded_at' => fake()->optional(0.1)->dateTimeBetween($completedAt, 'now'),
            'store_updated_at' => $storeUpdatedAt,
        ];
    }

    /**
     * Indicate that the order is completed.
     */
    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'state' => 'completed',
            'payment_state' => 'completed',
            'completed_at' => fake()->dateTimeBetween('-6 months', 'now'),
        ]);
    }

    /**
     * Indicate that the order is cancelled.
     */
    public function cancelled(): static
    {
        return $this->state(fn (array $attributes) => [
            'state' => 'cancelled',
            'payment_state' => 'cancelled',
            'refund_total' => null,
            'refund_status' => null,
            'refunded_at' => null,
        ]);
    }

    /**
     * Indicate that the order is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'state' => 'pending',
            'payment_state' => 'pending',
            'completed_at' => null,
            'refund_total' => null,
            'refund_status' => null,
            'refunded_at' => null,
        ]);
    }

    /**
     * Indicate that the order has been refunded.
     */
    public function refunded(): static
    {
        return $this->state(function (array $attributes) {
            $refundTotal = fake()->numberBetween(100, $attributes['total_amount'] ?? 5000);
            
            return [
                'refund_total' => $refundTotal,
                'refund_comment' => fake()->sentence(),
                'refund_status' => 'success',
                'refunded_at' => fake()->dateTimeBetween($attributes['completed_at'] ?? '-1 month', 'now'),
            ];
        });
    }

    /**
     * Indicate that the order is in USD currency.
     */
    public function usd(): static
    {
        return $this->state(fn (array $attributes) => [
            'currency_code' => 'USD',
            'shipping_country' => fake()->randomElement(['US', 'CA']),
        ]);
    }

    /**
     * Indicate that the order is recent (within last week).
     */
    public function recent(): static
    {
        return $this->state(function (array $attributes) {
            $completedAt = fake()->dateTimeBetween('-1 week', 'now');
            
            return [
                'completed_at' => $completedAt,
                'store_updated_at' => fake()->dateTimeBetween($completedAt, 'now'),
            ];
        });
    }

    /**
     * Indicate that the order has a specific customer ID.
     */
    public function forCustomer(int $customerId): static
    {
        return $this->state(fn (array $attributes) => [
            'customer_id' => $customerId,
        ]);
    }

    /**
     * Indicate that the order is from a specific country.
     */
    public function fromCountry(string $countryCode): static
    {
        return $this->state(fn (array $attributes) => [
            'shipping_country' => $countryCode,
        ]);
    }

    /**
     * Indicate that the order has a specific total amount.
     */
    public function withTotal(int $totalAmount): static
    {
        return $this->state(function (array $attributes) use ($totalAmount) {
            $adjustmentsTotal = $attributes['adjustments_total'] ?? 0;
            $itemsTotal = $totalAmount - $adjustmentsTotal;
            
            return [
                'items_total' => $itemsTotal,
                'total_amount' => $totalAmount,
            ];
        });
    }
}
