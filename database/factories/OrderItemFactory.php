<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Order;
use App\Models\OrderItem;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\OrderItem>
 */
final class OrderItemFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = OrderItem::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $quantity = fake()->numberBetween(1, 5);
        $unitPrice = fake()->numberBetween(500, 10000); // $5.00 to $100.00
        $unitsTotal = $quantity * $unitPrice;
        $adjustmentsTotal = fake()->numberBetween(-1000, 500); // -$10.00 to $5.00
        $total = $unitsTotal + $adjustmentsTotal;
        $quantityRefunded = fake()->optional(0.1)->numberBetween(0, $quantity) ?? 0;

        return [
            'order_id' => Order::factory(),
            'store_order_item_id' => fake()->unique()->numberBetween(1000, 999999),
            'store_variant_id' => fake()->numberBetween(100, 9999),
            'product_name' => fake()->words(3, true),
            'variant_name' => fake()->optional(0.7)->words(2, true),
            'quantity' => $quantity,
            'unit_price' => $unitPrice,
            'units_total' => $unitsTotal,
            'adjustments_total' => $adjustmentsTotal,
            'total' => $total,
            'quantity_refunded' => $quantityRefunded,
        ];
    }

    /**
     * Indicate that the order item belongs to a specific order.
     */
    public function forOrder(Order $order): static
    {
        return $this->state(fn (array $attributes) => [
            'order_id' => $order->id,
        ]);
    }

    /**
     * Indicate that the order item has been partially refunded.
     */
    public function partiallyRefunded(): static
    {
        return $this->state(function (array $attributes) {
            $quantity = $attributes['quantity'] ?? 2;
            $quantityRefunded = fake()->numberBetween(1, $quantity - 1);
            
            return [
                'quantity_refunded' => $quantityRefunded,
            ];
        });
    }

    /**
     * Indicate that the order item has been fully refunded.
     */
    public function fullyRefunded(): static
    {
        return $this->state(function (array $attributes) {
            $quantity = $attributes['quantity'] ?? 2;
            
            return [
                'quantity_refunded' => $quantity,
            ];
        });
    }

    /**
     * Indicate that the order item has no refunds.
     */
    public function noRefunds(): static
    {
        return $this->state(fn (array $attributes) => [
            'quantity_refunded' => 0,
        ]);
    }

    /**
     * Indicate that the order item has a specific quantity.
     */
    public function withQuantity(int $quantity): static
    {
        return $this->state(function (array $attributes) use ($quantity) {
            $unitPrice = $attributes['unit_price'] ?? 1000;
            $unitsTotal = $quantity * $unitPrice;
            $adjustmentsTotal = $attributes['adjustments_total'] ?? 0;
            $total = $unitsTotal + $adjustmentsTotal;
            
            return [
                'quantity' => $quantity,
                'units_total' => $unitsTotal,
                'total' => $total,
                'quantity_refunded' => min($attributes['quantity_refunded'] ?? 0, $quantity),
            ];
        });
    }

    /**
     * Indicate that the order item has a specific unit price.
     */
    public function withUnitPrice(int $unitPrice): static
    {
        return $this->state(function (array $attributes) use ($unitPrice) {
            $quantity = $attributes['quantity'] ?? 1;
            $unitsTotal = $quantity * $unitPrice;
            $adjustmentsTotal = $attributes['adjustments_total'] ?? 0;
            $total = $unitsTotal + $adjustmentsTotal;
            
            return [
                'unit_price' => $unitPrice,
                'units_total' => $unitsTotal,
                'total' => $total,
            ];
        });
    }

    /**
     * Indicate that the order item has a specific product.
     */
    public function forProduct(string $productName, ?string $variantName = null): static
    {
        return $this->state(fn (array $attributes) => [
            'product_name' => $productName,
            'variant_name' => $variantName,
        ]);
    }

    /**
     * Indicate that the order item has a specific store variant ID.
     */
    public function forVariant(int $variantId): static
    {
        return $this->state(fn (array $attributes) => [
            'store_variant_id' => $variantId,
        ]);
    }

    /**
     * Indicate that the order item has no adjustments.
     */
    public function noAdjustments(): static
    {
        return $this->state(function (array $attributes) {
            $unitsTotal = $attributes['units_total'] ?? 1000;
            
            return [
                'adjustments_total' => 0,
                'total' => $unitsTotal,
            ];
        });
    }

    /**
     * Indicate that the order item has a discount.
     */
    public function withDiscount(int $discountAmount): static
    {
        return $this->state(function (array $attributes) use ($discountAmount) {
            $unitsTotal = $attributes['units_total'] ?? 1000;
            $adjustmentsTotal = -abs($discountAmount); // Ensure discount is negative
            $total = $unitsTotal + $adjustmentsTotal;
            
            return [
                'adjustments_total' => $adjustmentsTotal,
                'total' => $total,
            ];
        });
    }

    /**
     * Indicate that the order item has a tax.
     */
    public function withTax(int $taxAmount): static
    {
        return $this->state(function (array $attributes) use ($taxAmount) {
            $unitsTotal = $attributes['units_total'] ?? 1000;
            $adjustmentsTotal = abs($taxAmount); // Ensure tax is positive
            $total = $unitsTotal + $adjustmentsTotal;
            
            return [
                'adjustments_total' => $adjustmentsTotal,
                'total' => $total,
            ];
        });
    }

    /**
     * Create a gaming product order item.
     */
    public function gamingProduct(): static
    {
        $products = [
            ['Adventure Quest Deluxe', 'Digital Edition'],
            ['Racing Championship', 'Ultimate Edition'],
            ['Fantasy RPG', 'Collector\'s Edition'],
            ['Space Shooter', 'Standard Edition'],
            ['Strategy Empire', 'Premium Edition'],
        ];
        
        $product = fake()->randomElement($products);
        
        return $this->state(fn (array $attributes) => [
            'product_name' => $product[0],
            'variant_name' => $product[1],
            'unit_price' => fake()->randomElement([2999, 4999, 5999, 7999, 9999]), // $29.99 to $99.99
        ]);
    }
}
