<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\SyncLog;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\SyncLog>
 */
final class SyncLogFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = SyncLog::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $startedAt = fake()->dateTimeBetween('-1 week', 'now');
        $completedAt = fake()->optional(0.8)->dateTimeBetween($startedAt, 'now');
        $totalRecords = fake()->numberBetween(10, 1000);
        $processedRecords = $completedAt ? $totalRecords : fake()->numberBetween(0, $totalRecords);
        $successRecords = fake()->numberBetween(0, $processedRecords);
        $failedRecords = $processedRecords - $successRecords;

        return [
            'sync_type' => 'product_sync',
            'batch_id' => 'sync_' . Str::random(10) . '_' . now()->format('YmdHis'),
            'status' => fake()->randomElement(['pending', 'processing', 'completed', 'failed']),
            'started_at' => $startedAt,
            'completed_at' => $completedAt,
            'total_records' => $totalRecords,
            'processed_records' => $processedRecords,
            'success_records' => $successRecords,
            'failed_records' => $failedRecords,
            'summary' => [
                'sync_type' => fake()->randomElement(['full', 'incremental']),
                'source_table' => 'sylius_product_variant',
                'target_table' => 'products',
                'duration_seconds' => fake()->numberBetween(60, 3600),
                'memory_peak_mb' => fake()->numberBetween(64, 512),
            ],
            'sync_config' => [
                'incremental' => fake()->boolean(),
                'batch_size' => fake()->randomElement([50, 100, 200]),
                'timeout' => fake()->randomElement([300, 600, 1800, 3600]),
            ],
            'error_message' => fake()->optional(0.2)->sentence(),
        ];
    }

    /**
     * Indicate that the sync log is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
            'completed_at' => null,
            'processed_records' => 0,
            'success_records' => 0,
            'failed_records' => 0,
        ]);
    }

    /**
     * Indicate that the sync log is processing.
     */
    public function processing(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'processing',
            'completed_at' => null,
            'processed_records' => fake()->numberBetween(0, $attributes['total_records'] ?? 100),
        ]);
    }

    /**
     * Indicate that the sync log is completed.
     */
    public function completed(): static
    {
        return $this->state(function (array $attributes) {
            $totalRecords = $attributes['total_records'] ?? 100;
            $successRecords = fake()->numberBetween(intval($totalRecords * 0.8), $totalRecords);
            $failedRecords = $totalRecords - $successRecords;

            return [
                'status' => 'completed',
                'completed_at' => fake()->dateTimeBetween($attributes['started_at'] ?? '-1 hour', 'now'),
                'processed_records' => $totalRecords,
                'success_records' => $successRecords,
                'failed_records' => $failedRecords,
                'error_message' => null,
            ];
        });
    }

    /**
     * Indicate that the sync log has failed.
     */
    public function failed(): static
    {
        return $this->state(function (array $attributes) {
            $totalRecords = $attributes['total_records'] ?? 100;
            $processedRecords = fake()->numberBetween(0, intval($totalRecords * 0.5));

            return [
                'status' => 'failed',
                'completed_at' => fake()->dateTimeBetween($attributes['started_at'] ?? '-1 hour', 'now'),
                'processed_records' => $processedRecords,
                'success_records' => fake()->numberBetween(0, $processedRecords),
                'failed_records' => $processedRecords,
                'error_message' => fake()->sentence(),
            ];
        });
    }

    /**
     * Indicate that the sync log is for incremental sync.
     */
    public function incremental(): static
    {
        return $this->state(fn (array $attributes) => [
            'sync_config' => array_merge($attributes['sync_config'] ?? [], [
                'incremental' => true,
                'last_sync_date' => fake()->dateTimeBetween('-1 week', '-1 day')->format('Y-m-d H:i:s'),
            ]),
            'summary' => array_merge($attributes['summary'] ?? [], [
                'sync_type' => 'incremental',
            ]),
        ]);
    }

    /**
     * Indicate that the sync log is for full sync.
     */
    public function full(): static
    {
        return $this->state(fn (array $attributes) => [
            'sync_config' => array_merge($attributes['sync_config'] ?? [], [
                'incremental' => false,
            ]),
            'summary' => array_merge($attributes['summary'] ?? [], [
                'sync_type' => 'full',
            ]),
        ]);
    }
}
