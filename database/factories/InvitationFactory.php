<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Invitation;
use App\Models\Organisation;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Invitation>
 */
final class InvitationFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Invitation::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'model_type' => Organisation::class,
            'model_id' => Organisation::factory(),
            'role' => fake()->randomElement(['owner', 'member']),
            'created_by_user_id' => User::factory(),
            'expires_at' => Carbon::now()->addWeek(),
            'max_uses' => 5, // Fixed value instead of random to avoid test issues
            'uses' => 0,
            'email_restriction' => null, // Default to no restriction
        ];
    }

    /**
     * Indicate that the invitation is expired.
     */
    public function expired(): static
    {
        return $this->state(fn (array $attributes) => [
            'expires_at' => Carbon::now()->subDay(),
        ]);
    }

    /**
     * Indicate that the invitation has reached its usage limit.
     */
    public function usageLimitReached(): static
    {
        return $this->state(fn (array $attributes) => [
            'max_uses' => 1,
            'uses' => 1,
        ]);
    }

    /**
     * Indicate that the invitation has an email restriction.
     */
    public function withEmailRestriction(string $email): static
    {
        return $this->state(fn (array $attributes) => [
            'email_restriction' => $email,
        ]);
    }

    /**
     * Indicate that the invitation is for a specific role.
     */
    public function forRole(string $role): static
    {
        return $this->state(fn (array $attributes) => [
            'role' => $role,
        ]);
    }
}
