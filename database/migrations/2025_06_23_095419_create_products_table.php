<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->bigIncrements('id');

            // Association information
            $table->integer('store_variant_id')->comment('Remote variant ID');
            $table->integer('owner_id')->nullable()->comment('Owner ID');
            $table->string('sku', 255)->comment('SKU code');

            // Basic information
            $table->string('code', 255)->unique()->comment('Product code');
            $table->string('name', 255)->comment('Product name');
            $table->string('slug', 255)->comment('URL-friendly name');
            $table->boolean('enabled')->default(true)->comment('Whether enabled');

            // Release information
            $table->dateTime('release_date')->nullable()->comment('Release date');

            // Image information
            $table->string('package', 500)->nullable()->comment('Cover image path');

            // Price information
            $table->integer('current_price')->nullable()->comment('Current price (in cents)');
            $table->integer('original_price')->nullable()->comment('Original price (in cents)');
            $table->integer('minimum_price')->nullable()->comment('Minimum price (in cents)');
            $table->integer('lowest_price_before_discount')->nullable()->comment('Lowest price before discount');



            // Price history
            $table->json('price_history')->nullable()->comment('Price change history');

            // Timestamps
            $table->timestamp('store_product_updated_at')->nullable()->comment('Remote product updated time');
            $table->timestamp('store_variant_updated_at')->nullable()->comment('Remote variant updated time');
            $table->timestamps();

            // Indexes
            $table->index('code', 'idx_code');
            $table->index('owner_id', 'idx_owner_id');
            $table->index('sku', 'idx_sku');
            $table->index(['enabled', 'release_date'], 'idx_enabled_release_date');
            $table->index('store_product_updated_at', 'idx_store_product_updated_at');
            $table->index('store_variant_updated_at', 'idx_store_variant_updated_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
