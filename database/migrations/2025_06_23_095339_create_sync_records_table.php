<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sync_records', function (Blueprint $table) {
            $table->id();
            $table->string('batch_id')->comment('Batch ID');
            $table->string('source_table')->comment('Source table name');
            $table->string('source_id')->comment('Source record ID');
            $table->string('target_table')->comment('Target table name');
            $table->string('target_id')->nullable()->comment('Target record ID');
            $table->enum('status', ['pending', 'success', 'failed', 'skipped']);
            $table->json('source_data')->nullable()->comment('Source data');
            $table->json('transformed_data')->nullable()->comment('Transformed data');
            $table->text('error_message')->nullable();
            $table->timestamps();

            $table->index(['batch_id', 'status']);
            $table->index(['source_table', 'source_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sync_records');
    }
};
