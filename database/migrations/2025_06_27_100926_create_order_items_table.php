<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_items', function (Blueprint $table) {
            $table->id();

            // Relationships
            $table->foreignId('order_id')->constrained('orders')->onDelete('cascade')->comment('Local order ID');
            $table->integer('store_order_item_id')->comment('Remote order item ID');
            $table->integer('store_variant_id')->comment('Remote product variant ID');

            // Product information (snapshot for reporting)
            $table->string('product_name')->nullable()->comment('Product name at time of order');
            $table->string('variant_name')->nullable()->comment('Variant name at time of order');

            // Quantity and pricing (all amounts in cents)
            $table->integer('quantity')->comment('Ordered quantity');
            $table->integer('unit_price')->default(0)->comment('Unit price (cents)');
            $table->integer('units_total')->default(0)->comment('Units total (quantity × unit price)');
            $table->integer('adjustments_total')->default(0)->comment('Adjustments total (discounts, taxes, etc.)');
            $table->integer('total')->default(0)->comment('Total (units_total + adjustments_total)');

            // Refund tracking
            $table->integer('quantity_refunded')->default(0)->comment('Refunded quantity');

            // Standard timestamps
            $table->timestamps();

            // Reporting indexes
            $table->index('order_id', 'idx_order_id');
            $table->index('store_order_item_id', 'idx_store_order_item_id');
            $table->index('store_variant_id', 'idx_store_variant_id');
            $table->index('units_total', 'idx_units_total');
            $table->index('total', 'idx_total');
            $table->index('quantity_refunded', 'idx_quantity_refunded');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_items');
    }
};
