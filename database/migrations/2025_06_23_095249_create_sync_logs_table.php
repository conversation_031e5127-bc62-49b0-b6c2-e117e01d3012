<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sync_logs', function (Blueprint $table) {
            $table->id();
            $table->string('sync_type')->comment('Sync type, e.g., product_sync');
            $table->string('batch_id')->unique()->comment('Batch ID');
            $table->enum('status', ['pending', 'processing', 'completed', 'failed'])->default('pending');
            $table->datetime('started_at')->nullable();
            $table->datetime('completed_at')->nullable();
            $table->integer('total_records')->default(0);
            $table->integer('processed_records')->default(0);
            $table->integer('success_records')->default(0);
            $table->integer('failed_records')->default(0);
            $table->json('summary')->nullable()->comment('Sync summary information');
            $table->text('error_message')->nullable();
            $table->json('sync_config')->nullable()->comment('Sync configuration');
            $table->timestamps();

            $table->index(['sync_type', 'status']);
            $table->index('batch_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sync_logs');
    }
};
