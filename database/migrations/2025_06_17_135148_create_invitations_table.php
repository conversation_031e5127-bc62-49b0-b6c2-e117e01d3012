<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invitations', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('model_type'); // Associated model type, e.g., 'App\Models\Organisation'
            $table->unsignedBigInteger('model_id'); // Associated model instance ID
            $table->string('role'); // Role permission name for the invitation
            $table->foreignId('created_by_user_id')->constrained('users')->onDelete('cascade'); // User ID who issued the invitation
            $table->timestamp('expires_at'); // Expiration time, defaults to one week after creation
            $table->unsignedInteger('max_uses')->default(1); // Maximum usage count
            $table->unsignedInteger('uses')->default(0); // Current usage count
            $table->string('email_restriction')->nullable(); // Email restriction
            $table->timestamps();

            // Add indexes
            $table->index(['model_type', 'model_id']);
            $table->index('expires_at');
            $table->index('created_by_user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invitations');
    }
};
