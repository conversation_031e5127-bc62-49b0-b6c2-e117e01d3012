<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('organisations', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('code')->unique();
            $table->json('details')->nullable();
            $table->text('remarks')->nullable();
            $table->enum('status', ['pending', 'active', 'suspended'])->default('pending')->index();
            $table->timestamps();
        });

        // Create user_organisation pivot table for many-to-many relationship
        Schema::create('user_organisation', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('organisation_id')->constrained()->onDelete('cascade');
            $table->timestamps();

            // Ensure unique user-organisation combinations
            $table->unique(['user_id', 'organisation_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop user_organisation pivot table
        Schema::dropIfExists('user_organisation');

        Schema::dropIfExists('organisations');
    }
};
