<?php

declare(strict_types=1);

use App\Models\Organisation;
use App\Models\Role;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Get all existing organisations
        $organisations = Organisation::all();

        foreach ($organisations as $organisation) {
            // Check if visitor role already exists for this organisation
            $existingVisitorRole = Role::where('name', 'visitor')
                ->where('guard_name', 'api')
                ->where('organisation_id', $organisation->id)
                ->first();

            // Create visitor role if it doesn't exist
            if (!$existingVisitorRole) {
                Role::create([
                    'name' => 'visitor',
                    'guard_name' => 'api',
                    'organisation_id' => $organisation->id,
                ]);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove all visitor roles
        Role::where('name', 'visitor')
            ->where('guard_name', 'api')
            ->whereNotNull('organisation_id')
            ->delete();
    }
};
