<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();

            // Remote system reference
            $table->integer('store_order_id')->comment('Remote order ID');
            $table->string('order_number')->nullable()->comment('Remote system order number');

            // Order status and timing
            $table->string('state', 50)->comment('Order status (completed, cancelled, etc.)');
            $table->datetime('completed_at')->comment('Order completion date');

            // Financial data (all amounts in cents)
            $table->integer('items_total')->comment('Subtotal (cents)');
            $table->integer('adjustments_total')->default(0)->comment('Discounts/adjustments (cents)');
            $table->integer('total_amount')->comment('Final total amount (cents)');
            $table->string('currency_code', 3)->comment('Currency code');
            $table->string('payment_state', 50)->nullable()->comment('Payment status');

            // Regional data for analytics
            $table->string('shipping_country', 10)->nullable()->comment('Shipping country code');

            // Customer reference
            $table->integer('customer_id')->nullable()->comment('Remote system customer ID');

            // Refund data
            $table->integer('refund_total')->nullable()->default(0)->comment('Refund total amount (cents)');
            $table->string('refund_comment')->nullable()->comment('Refund comment');
            $table->string('refund_status')->nullable()->comment('Refund status');
            $table->datetime('refunded_at')->nullable()->comment('Refund time');

            // Sync tracking
            $table->timestamp('store_updated_at')->nullable()->comment('Remote order update time');

            // Standard timestamps
            $table->timestamps();

            // Performance indexes for reporting
            $table->index('store_order_id', 'idx_store_order_id');
            $table->index('completed_at', 'idx_completed_at');
            $table->index('state', 'idx_state');
            $table->index('shipping_country', 'idx_shipping_country');
            $table->index('total_amount', 'idx_total_amount');
            $table->index('store_updated_at', 'idx_store_updated_at');
            $table->index('payment_state', 'idx_payment_state');
            $table->index('refund_status', 'idx_refund_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
