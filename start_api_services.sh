#!/bin/bash

# Define log directory and file paths
LOG_DIR="storage/logs"
SCHEDULE_LOG="$LOG_DIR/schedule_work.log"
HORIZON_LOG="$LOG_DIR/horizon.log"
SERVE_LOG="$LOG_DIR/serve.log"

# Create log directory if it does not exist
mkdir -p "$LOG_DIR"

# Define a function to clean up background processes when the script exits
cleanup() {
    echo -e "\nStopping Laravel services..."
    # Use kill to terminate all background processes
    # 2>/dev/null suppresses error messages in case a PID does not exist
    kill "$SCHEDULE_PID" "$HORIZON_PID" "$SERVE_PID" 2>/dev/null
    echo "Services stopped."
    exit 0
}

# Catch Ctrl+C (SIGINT) signal and call the cleanup function when triggered
trap cleanup SIGINT

echo "Starting Laravel services..."

# Start php artisan schedule:work
echo "Starting php artisan schedule:work..."
# Use unbuffer to force color output to be preserved
unbuffer php artisan schedule:work > "$SCHEDULE_LOG" 2>&1 &
SCHEDULE_PID=$! # Get the PID of the background process
echo "schedule:work started with PID: $SCHEDULE_PID. Log: $SCHEDULE_LOG"

# Start php artisan horizon
echo "Starting php artisan horizon..."
unbuffer php artisan horizon > "$HORIZON_LOG" 2>&1 &
HORIZON_PID=$!
echo "horizon started with PID: $HORIZON_PID. Log: $HORIZON_LOG"

# Start php artisan serve
echo "Starting php artisan serve..."
unbuffer php artisan serve > "$SERVE_LOG" 2>&1 &
SERVE_PID=$!
echo "serve started with PID: $SERVE_PID. Log: $SERVE_LOG"

echo ""
echo "All services started. Monitoring logs (Press Ctrl+C to stop all services):"
echo "--------------------------------------------------------"

# Display the contents of all log files in real time
# tail -f will merge the latest content of all files and indicate the source file
tail -f "$SCHEDULE_LOG" "$HORIZON_LOG" "$SERVE_LOG"
