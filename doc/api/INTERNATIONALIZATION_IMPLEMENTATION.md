# API 响应信息多语言国际化（i18n）实现文档

## 概述

本文档描述了为 Laravel API 项目实现的完整多语言国际化支持，包括响应消息的翻译、语言设置中间件以及错误处理的国际化。

## 实现的功能

### 1. 语言文件结构

创建了完整的语言文件结构：

```
lang/
├── en/
│   ├── api.php      # 英文API响应消息
│   └── errors.php   # 英文错误消息
└── zh/
    ├── api.php      # 中文API响应消息
    └── errors.php   # 中文错误消息
```

### 2. 支持的语言

- **英文 (en)**: 默认语言
- **中文 (zh)**: 支持简体中文

### 3. 语言设置中间件

创建了 `LocaleMiddleware` 来处理语言设置：

**位置**: `app/Http/Middleware/LocaleMiddleware.php`

**功能**:
- 从请求头中检测语言偏好
- 支持 `X-Locale` 自定义头（优先级最高）
- 支持 `Accept-Language` 标准头
- 验证语言文件是否存在
- 默认回退到英文

**优先级**:
1. `X-Locale` 头
2. `Accept-Language` 头
3. 默认英文 (en)

### 4. API 控制器国际化

修改了 `ApiController` 中的响应方法：

- `successResponse()`: 支持翻译键
- `errorResponse()`: 支持翻译键
- `validationErrorResponse()`: 支持翻译键
- `notFoundResponse()`: 支持翻译键
- `unauthorizedResponse()`: 支持翻译键

### 5. 异常处理国际化

更新了 `bootstrap/app.php` 中的异常处理器：

- 在异常处理时设置正确的语言环境
- 支持验证错误、认证错误、授权错误等的多语言消息
- 404、422、401、403 等 HTTP 状态码的本地化错误消息

### 6. 服务层国际化

更新了 `ApiExceptionService`：

- 将硬编码的错误消息替换为翻译键
- 支持动态语言切换
- 保持向后兼容性

## 使用方法

### 客户端设置语言

#### 方法 1: 使用 X-Locale 头（推荐）

```bash
curl -H "X-Locale: zh" http://api.example.com/api/v1/status
curl -H "X-Locale: en" http://api.example.com/api/v1/status
```

#### 方法 2: 使用 Accept-Language 头

```bash
curl -H "Accept-Language: zh-CN,zh;q=0.9,en;q=0.8" http://api.example.com/api/v1/status
curl -H "Accept-Language: en-US,en;q=0.9" http://api.example.com/api/v1/status
```

### 响应示例

#### 英文响应 (默认)

```json
{
    "success": true,
    "message": "API is running successfully",
    "data": {...},
    "timestamp": "2025-06-20T12:00:00.000000Z"
}
```

#### 中文响应

```json
{
    "success": true,
    "message": "API 运行正常",
    "data": {...},
    "timestamp": "2025-06-20T12:00:00.000000Z"
}
```

### 错误响应示例

#### 英文错误响应

```json
{
    "success": false,
    "message": "Resource not found",
    "errors": null,
    "timestamp": "2025-06-20T12:00:00.000000Z",
    "error_code": "NOT_FOUND_ERROR"
}
```

#### 中文错误响应

```json
{
    "success": false,
    "message": "资源未找到",
    "errors": null,
    "timestamp": "2025-06-20T12:00:00.000000Z",
    "error_code": "NOT_FOUND_ERROR"
}
```

## 技术实现细节

### 中间件注册

在 `bootstrap/app.php` 中注册了语言中间件：

```php
$middleware->api(prepend: [
    \App\Http\Middleware\LocaleMiddleware::class,
    // ... 其他中间件
]);
```

### 翻译键结构

#### API 响应消息 (`lang/{locale}/api.php`)

```php
return [
    'success' => 'Success',
    'auth' => [
        'login_success' => 'Login successful',
        'logout_success' => 'Logout successful',
        // ...
    ],
    'user' => [
        'created' => 'User created successfully',
        'updated' => 'User updated successfully',
        // ...
    ],
    // ...
];
```

#### 错误消息 (`lang/{locale}/errors.php`)

```php
return [
    'validation' => 'Validation failed',
    'authentication' => 'Authentication required',
    'authorization' => 'Access denied',
    'not_found' => 'Resource not found',
    // ...
];
```

### 控制器使用示例

```php
// 在控制器中使用翻译键
return $this->successResponse($data, 'api.user.created');
return $this->errorResponse('errors.not_found');
```

## 测试

创建了完整的测试套件来验证多语言功能：

- `LocaleMiddlewareTest`: 测试语言中间件功能
- `MultiLanguageErrorTest`: 测试错误消息的多语言支持

### 运行测试

```bash
# 测试语言中间件
make artisan cmd="test --filter=LocaleMiddlewareTest" ENV=development

# 测试多语言错误处理
make artisan cmd="test --filter=MultiLanguageErrorTest" ENV=development
```

## 扩展支持新语言

要添加新语言支持：

1. 在 `LocaleMiddleware` 中添加语言代码到 `SUPPORTED_LOCALES`
2. 创建对应的语言文件目录 `lang/{locale}/`
3. 复制并翻译 `api.php` 和 `errors.php` 文件
4. 确保所有翻译键都有对应的翻译

## 注意事项

1. **默认语言**: 系统默认使用英文，确保所有翻译键在英文语言文件中都有定义
2. **回退机制**: 如果指定语言的翻译不存在，会自动回退到英文
3. **性能**: 语言文件会被 Laravel 缓存，生产环境中性能影响很小
4. **一致性**: 所有 API 响应都使用统一的翻译键结构

## 维护

- 添加新的 API 响应时，记得在语言文件中添加对应的翻译键
- 定期检查中英文翻译的一致性
- 更新测试以覆盖新添加的翻译内容
