# 数据统计图表数据结构说明

本文档详细说明了项目中所有数据统计图表的数据结构和配置。

## 目录

1. [首页统计图表](#首页统计图表)
2. [销售页面图表](#销售页面图表)
3. [游戏页面图表](#游戏页面图表)
4. [统计卡片组件](#统计卡片组件)
5. [排行榜组件](#排行榜组件)

---

## 首页统计图表

### 1. 每日销售金额趋势图 (daily-sales-amount-chart.vue)

**图表类型**: ECharts 折线图 (Line Chart)

**数据结构**:
```typescript
interface DailySalesAmountData {
  xAxis: {
    data: string[]  // 日期数组，格式: ["01/01", "01/02", ...]
  }
  series: [{
    data: number[]  // 销售金额数组，单位: 美元
  }]
}

// 示例数据
const mockData = {
  xAxis: { data: ["12/25", "12/26", "12/27", "12/28", "12/29", "12/30", "12/31"] },
  series: [{ data: [12450, 15680, 18920, 16750, 21340, 19580, 23120] }]
}
```

**图表配置**:
- 颜色: `#8e9dff` (蓝紫色)
- 面积填充: 渐变色 (从 `#8e9dff` 到白色)
- 平滑曲线: `smooth: true`

### 2. 每日销售数量趋势图 (daily-sales-quantity-chart.vue)

**图表类型**: ECharts 折线图 (Line Chart)

**数据结构**:
```typescript
interface DailySalesQuantityData {
  xAxis: {
    data: string[]  // 日期数组，格式: ["01/01", "01/02", ...]
  }
  series: [{
    data: number[]  // 销售数量数组
  }]
}

// 示例数据
const mockData = {
  xAxis: { data: ["12/25", "12/26", "12/27", "12/28", "12/29", "12/30", "12/31"] },
  series: [{ data: [45, 58, 67, 52, 73, 68, 81] }]
}
```

**图表配置**:
- 颜色: `#26deca` (青绿色)
- 面积填充: 渐变色 (从 `#26deca` 到白色)
- 平滑曲线: `smooth: true`

### 3. 每日订单销售双轴图 (line-chart.vue)

**图表类型**: ECharts 双轴折线图 (Dual-axis Line Chart)

**数据结构**:
```typescript
interface DailyOrderSalesData {
  xAxis: {
    data: string[]  // 日期数组
  }
  series: [
    {
      name: "订单金额"
      yAxisIndex: 0
      data: number[]  // 订单金额数组，单位: 美元
    },
    {
      name: "订单数量"
      yAxisIndex: 1
      data: number[]  // 订单数量数组
    }
  ]
}

// 示例数据
const mockData = {
  xAxis: { data: ["12/25", "12/26", "12/27", "12/28", "12/29", "12/30", "12/31"] },
  series: [
    { data: [12450, 15680, 18920, 16750, 21340, 19580, 23120] }, // 订单金额
    { data: [45, 58, 67, 52, 73, 68, 81] }  // 订单数量
  ]
}
```

**图表配置**:
- 左Y轴: 订单金额 (美元)，颜色: `#8e9dff`
- 右Y轴: 订单数量，颜色: `#26deca`
- 双轴显示，支持不同数值范围

### 4. 地区销售分布饼图 (pie-chart.vue)

**图表类型**: ECharts 环形饼图 (Doughnut Chart)

**数据结构**:
```typescript
interface RegionSalesData {
  series: [{
    data: Array<{
      name: string    // 地区名称
      value: number   // 销售金额，单位: 美元
    }>
  }]
}

// 示例数据
const mockData = {
  series: [{
    data: [
      { name: "北美", value: 125680 },
      { name: "欧洲", value: 98450 },
      { name: "亚洲", value: 156720 },
      { name: "其他", value: 45890 }
    ]
  }]
}
```

**图表配置**:
- 环形饼图: `radius: ['40%', '70%']`
- 配色方案: `['#5da8ff', '#8e9dff', '#fedc69', '#26deca', '#ff7875']`
- 标签显示: 外部显示，格式: `{地区名}\n${金额}`

### 5. 地区销售金额饼图 (regional-sales-amount-chart.vue)

**图表类型**: ECharts 环形饼图 (Doughnut Chart)

**数据结构**:
```typescript
interface RegionalSalesAmountData {
  series: [{
    data: Array<{
      name: string    // 地区名称
      value: number   // 销售金额，单位: 美元
    }>
  }]
}

// 示例数据
const mockData = {
  series: [{
    data: [
      { name: "北美", value: 125680 },
      { name: "欧洲", value: 98450 },
      { name: "亚洲", value: 156720 },
      { name: "其他", value: 45890 }
    ]
  }]
}
```

### 6. 地区销售数量饼图 (regional-sales-quantity-chart.vue)

**图表类型**: ECharts 环形饼图 (Doughnut Chart)

**数据结构**:
```typescript
interface RegionalSalesQuantityData {
  series: [{
    data: Array<{
      name: string    // 地区名称
      value: number   // 销售数量
    }>
  }]
}

// 示例数据
const mockData = {
  series: [{
    data: [
      { name: "北美", value: 456 },
      { name: "欧洲", value: 378 },
      { name: "亚洲", value: 623 },
      { name: "其他", value: 189 }
    ]
  }]
}
```

---

## 销售页面图表

### 1. 销售综合图表 (sales-charts.vue)

**包含多个子图表**:

#### 1.1 地区分布饼图
```typescript
interface RegionDistributionData {
  data: Array<{
    name: string    // 地区名称
    value: number   // 百分比值
    color: string   // 颜色值
  }>
}

// 示例数据
const regionData = [
  { name: "北美", value: 35, color: "#2a4a67" },
  { name: "欧洲", value: 28, color: "#52c41a" },
  { name: "亚洲", value: 25, color: "#722ed1" },
  { name: "其他", value: 12, color: "#fa8c16" }
]
```

#### 1.2 货币分布饼图
```typescript
interface CurrencyDistributionData {
  data: Array<{
    name: string    // 货币代码
    value: number   // 百分比值
    color: string   // 颜色值
  }>
}

// 示例数据
const currencyData = [
  { name: "USD", value: 45, color: "#2a4a67" },
  { name: "EUR", value: 25, color: "#52c41a" },
  { name: "CNY", value: 20, color: "#722ed1" },
  { name: "其他", value: 10, color: "#fa8c16" }
]
```

#### 1.3 订单状态饼图
```typescript
interface OrderStatusData {
  data: Array<{
    name: string    // 订单状态
    value: number   // 百分比值
    color: string   // 颜色值
  }>
}

// 示例数据
const orderStatusData = [
  { name: "已完成", value: 78, color: "#52c41a" },
  { name: "处理中", value: 15, color: "#fa8c16" },
  { name: "已取消", value: 5, color: "#ff4d4f" },
  { name: "已退款", value: 2, color: "#722ed1" }
]
```

#### 1.4 日销售柱状图
```typescript
interface DailySalesBarData {
  xAxis: {
    data: string[]  // 日期数组，格式: ["01-01", "01-02", ...]
  }
  series: [{
    data: number[]  // 销售金额数组，单位: 美元
  }]
}

// 示例数据
const dailySalesData = [
  { date: "01-01", sales: 12000 },
  { date: "01-02", sales: 15000 },
  { date: "01-03", sales: 18000 },
  { date: "01-04", sales: 14000 },
  { date: "01-05", sales: 22000 },
  { date: "01-06", sales: 19000 },
  { date: "01-07", sales: 25000 }
]
```

#### 1.5 分时趋势曲线图
```typescript
interface HourlyTrendData {
  xAxis: {
    data: string[]  // 时间数组，格式: ["00:00", "02:00", ...]
  }
  series: [{
    data: number[]  // 销售数值数组
  }]
}

// 示例数据
const hourlyData = [
  { time: "00:00", value: 120 },
  { time: "02:00", value: 80 },
  { time: "04:00", value: 60 },
  { time: "06:00", value: 100 },
  { time: "08:00", value: 180 },
  { time: "10:00", value: 220 },
  { time: "12:00", value: 280 },
  { time: "14:00", value: 320 },
  { time: "16:00", value: 290 },
  { time: "18:00", value: 250 },
  { time: "20:00", value: 200 },
  { time: "22:00", value: 150 }
]
```

---

## 游戏页面图表

### 1. 游戏统计抽屉 (game-stats-drawer.vue)

**包含多个统计图表**:

#### 1.1 核心统计数据
```typescript
interface GameStatistics {
  totalSalesAmount: number    // 总销售金额
  totalSalesVolume: number    // 总销售数量
  refundCount: number         // 退款数量
  refundRate: number          // 退款率 (百分比)
}

// 示例数据
const statistics = {
  totalSalesAmount: 75000,
  totalSalesVolume: 3500,
  refundCount: 125,
  refundRate: 3.57
}
```

#### 1.2 地区订单量分布饼图
```typescript
interface RegionVolumeData {
  data: Array<{
    name: string    // 地区名称
    value: number   // 订单数量
  }>
}

// 示例数据
const regionVolumeData = [
  { name: "北美", value: 850 },
  { name: "欧洲", value: 720 },
  { name: "亚洲", value: 1200 },
  { name: "其他", value: 430 }
]
```

#### 1.3 币种订单量分布饼图
```typescript
interface CurrencyVolumeData {
  data: Array<{
    name: string    // 币种代码
    value: number   // 订单数量
  }>
}

// 示例数据
const currencyVolumeData = [
  { name: "USD", value: 1500 },
  { name: "EUR", value: 800 },
  { name: "JPY", value: 600 },
  { name: "CNY", value: 600 }
]
```

#### 1.4 地区销售金额分布饼图
```typescript
interface RegionAmountData {
  data: Array<{
    name: string    // 地区名称
    value: number   // 销售金额，单位: 美元
  }>
}

// 示例数据
const regionAmountData = [
  { name: "北美", value: 25000 },
  { name: "欧洲", value: 18000 },
  { name: "亚洲", value: 22000 },
  { name: "其他", value: 10000 }
]
```

#### 1.5 币种销售金额分布饼图
```typescript
interface CurrencyAmountData {
  data: Array<{
    name: string    // 币种代码
    value: number   // 销售金额，单位: 美元
  }>
}

// 示例数据
const currencyAmountData = [
  { name: "USD", value: 35000 },
  { name: "EUR", value: 20000 },
  { name: "JPY", value: 12000 },
  { name: "CNY", value: 8000 }
]
```

#### 1.6 日销售柱状图
```typescript
interface DailySalesBarData {
  data: Array<{
    date: string    // 日期，格式: "MM/DD"
    sales: number   // 销售数量
  }>
}

// 示例数据 (30天)
const dailySales = Array.from({ length: 30 }, (_, i) => ({
  date: dayjs(new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000)).format('MM/DD'),
  sales: Math.floor(Math.random() * 200) + 50
}))
```

#### 1.7 分时销售曲线图
```typescript
interface HourlySalesLineData {
  data: Array<{
    hour: string    // 小时，格式: "HH:00"
    sales: number   // 销售数量
  }>
}

// 示例数据 (24小时)
const hourlySales = Array.from({ length: 24 }, (_, i) => ({
  hour: `${i.toString().padStart(2, '0')}:00`,
  sales: Math.floor(Math.random() * 50) + 10
}))
```

---

## 统计卡片组件

### 1. 销售统计卡片 (sales-stats.vue)

**数据结构**:
```typescript
interface SalesStatsData {
  totalSales: number      // 总销售额，单位: 美元
  totalOrders: number     // 总订单数
  todaySales: number      // 今日销售额，单位: 美元
  monthSales: number      // 本月销售额，单位: 美元
}

// 示例数据
const salesStats = {
  totalSales: 2456789,
  totalOrders: 15678,
  todaySales: 12456,
  monthSales: 456789
}
```

### 2. 游戏统计卡片 (game-stats.vue)

**数据结构**:
```typescript
interface GameStatsData {
  totalGames: number      // 游戏总数
  enabledGames: number    // 启用游戏数
  totalInventory: number  // 总库存数
  newGames: number        // 新游戏数
}

// 示例数据
const gameStats = {
  totalGames: 1256,
  enabledGames: 1089,
  totalInventory: 156789,
  newGames: 45
}
```

---

## 排行榜组件

### 1. 销售金额排行榜 (top-sales-amount-ranking.vue)

**数据结构**:
```typescript
interface GameRankingData {
  id: string
  name: string                    // 游戏名称
  code: string                    // 游戏代码
  coverImage: string              // 封面图片URL
  dailySalesAmount: number        // 日销售金额，单位: 美元
  price: number                   // 游戏价格，单位: 美元
  sales: number                   // 销售数量
  enabled: boolean                // 是否启用
  multiLangName: Record<string, string>  // 多语言名称
  languages: string[]             // 支持语言
  onHand: number                  // 库存数量
}

// 示例数据 (TOP 10)
const rankingData = [
  {
    id: "1",
    name: "Cyberpunk 2077",
    code: "CP2077",
    coverImage: "https://picsum.photos/200/300?random=1",
    dailySalesAmount: 125680,
    price: 59.99,
    sales: 2095,
    enabled: true,
    multiLangName: { "zh-CN": "赛博朋克2077", "en-US": "Cyberpunk 2077" },
    languages: ["en", "zh", "ja"],
    onHand: 5000
  }
  // ... 更多游戏数据
]
```

### 2. 销售数量排行榜 (top-sales-quantity-ranking.vue)

**数据结构**: 与销售金额排行榜相同，但按 `dailySalesQuantity` 字段排序

```typescript
interface GameRankingData {
  // ... 其他字段相同
  dailySalesQuantity: number      // 日销售数量 (用于排序)
}
```

---

## 图表配色方案

### 主要配色
```typescript
const colorScheme = {
  primary: '#2a4a67',    // 主蓝色
  success: '#52c41a',    // 绿色
  purple: '#722ed1',     // 紫色
  orange: '#fa8c16',     // 橙色
  red: '#ff4d4f',        // 红色
  gray: '#8c8c8c',       // 灰色
  lightBlue: '#5da8ff',  // 浅蓝色
  blueViolet: '#8e9dff', // 蓝紫色
  yellow: '#fedc69',     // 黄色
  cyan: '#26deca',       // 青色
  lightRed: '#ff7875'    // 浅红色
}
```

### 饼图配色数组
```typescript
const pieColors = ['#5da8ff', '#8e9dff', '#fedc69', '#26deca', '#ff7875']
```

---

## 图表通用配置

### ECharts 通用配置
```typescript
const commonEChartsConfig = {
  backgroundColor: 'transparent',  // 透明背景
  animation: true,                 // 启用动画
  animationDuration: 1000,        // 动画持续时间
  responsive: true,               // 响应式
  maintainAspectRatio: false      // 不保持宽高比
}
```

### 工具提示配置
```typescript
const tooltipConfig = {
  trigger: 'item',                // 触发类型
  backgroundColor: 'rgba(0,0,0,0.8)',  // 背景色
  borderColor: 'transparent',     // 边框色
  textStyle: {
    color: '#fff',                // 文字颜色
    fontSize: 12                  // 字体大小
  }
}
```

---

## 数据更新机制

### 1. 模拟数据生成
所有图表目前使用模拟数据，通过 `Math.random()` 生成随机数值。

### 2. 国际化支持
所有图表支持中英文切换，通过 `$t()` 函数实现国际化。

### 3. 响应式设计
图表支持移动端适配，通过 `isMobile` 计算属性调整布局。

### 4. 实时更新
图表支持数据实时更新，通过 `updateOptions()` 方法更新图表配置和数据。

---

## API 接口对接

### 数据获取接口
```typescript
// 首页统计数据
interface HomeStatsAPI {
  getDailySalesAmount(): Promise<DailySalesAmountData>
  getDailySalesQuantity(): Promise<DailySalesQuantityData>
  getRegionSalesDistribution(): Promise<RegionSalesData>
  getTopGamesByAmount(): Promise<GameRankingData[]>
  getTopGamesByQuantity(): Promise<GameRankingData[]>
}

// 销售页面数据
interface SalesStatsAPI {
  getSalesOverview(): Promise<SalesStatsData>
  getRegionDistribution(): Promise<RegionDistributionData>
  getCurrencyDistribution(): Promise<CurrencyDistributionData>
  getOrderStatusDistribution(): Promise<OrderStatusData>
  getDailySalesTrend(): Promise<DailySalesBarData>
  getHourlyTrend(): Promise<HourlyTrendData>
}

// 游戏统计数据
interface GameStatsAPI {
  getGameOverview(): Promise<GameStatsData>
  getGameStatistics(gameId: string): Promise<GameStatistics>
  getGameSalesDistribution(gameId: string): Promise<{
    regionVolume: RegionVolumeData
    currencyVolume: CurrencyVolumeData
    regionAmount: RegionAmountData
    currencyAmount: CurrencyAmountData
    dailySales: DailySalesBarData
    hourlySales: HourlySalesLineData
  }>
}
```

### 数据缓存策略
```typescript
// 缓存配置
const cacheConfig = {
  homeStats: { ttl: 5 * 60 * 1000 },      // 5分钟
  salesStats: { ttl: 10 * 60 * 1000 },    // 10分钟
  gameStats: { ttl: 15 * 60 * 1000 },     // 15分钟
  rankings: { ttl: 30 * 60 * 1000 }       // 30分钟
}
```

---

## 图表交互功能

### 1. 点击事件
```typescript
// 饼图点击事件
interface PieChartClickEvent {
  name: string      // 数据项名称
  value: number     // 数据项值
  percent: number   // 百分比
}

// 柱状图点击事件
interface BarChartClickEvent {
  name: string      // X轴标签
  value: number     // Y轴值
  dataIndex: number // 数据索引
}

// 排行榜点击事件
interface RankingClickEvent {
  game: GameRankingData  // 游戏数据
}
```

### 2. 筛选功能
```typescript
// 图表筛选选项
interface ChartFilters {
  dateRange: [number, number]  // 时间范围
  region: string               // 地区筛选
  currency: string             // 货币筛选
  orderStatus: string          // 订单状态筛选
}
```

### 3. 导出功能
```typescript
// 数据导出格式
interface ExportOptions {
  format: 'csv' | 'excel' | 'pdf'  // 导出格式
  includeCharts: boolean           // 是否包含图表
  dateRange: [number, number]      // 导出数据时间范围
}
```

---

## 移动端适配

### 响应式断点
```typescript
const breakpoints = {
  mobile: '(max-width: 768px)',
  tablet: '(min-width: 769px) and (max-width: 1024px)',
  desktop: '(min-width: 1025px)'
}
```

### 移动端图表配置
```typescript
const mobileChartConfig = {
  grid: {
    left: '5%',
    right: '5%',
    top: '15%',
    bottom: '15%'
  },
  legend: {
    orient: 'horizontal',
    bottom: '5%',
    itemWidth: 15,
    itemHeight: 10,
    textStyle: { fontSize: 10 }
  },
  tooltip: {
    textStyle: { fontSize: 10 }
  }
}
```

---

## 性能优化建议

### 1. 数据量优化
- **大数据集**: 超过1000个数据点时使用数据采样
- **实时更新**: 使用防抖机制，避免频繁更新
- **懒加载**: 非可视区域的图表延迟加载

### 2. 渲染优化
```typescript
// 图表懒加载
const useChartLazyLoad = () => {
  const { stop } = useIntersectionObserver(
    chartRef,
    ([{ isIntersecting }]) => {
      if (isIntersecting) {
        initChart()
        stop()
      }
    }
  )
}

// 数据采样
const sampleData = (data: number[], maxPoints: number = 100) => {
  if (data.length <= maxPoints) return data
  const step = Math.ceil(data.length / maxPoints)
  return data.filter((_, index) => index % step === 0)
}
```

### 3. 内存管理
```typescript
// 图表销毁
onBeforeUnmount(() => {
  chart?.dispose()
  chart = null
})

// 数据清理
const clearChartData = () => {
  chartData.value = null
  chartOptions.value = null
}
```

---

## 错误处理

### 1. 数据异常处理
```typescript
// 数据验证
const validateChartData = (data: any): boolean => {
  if (!data || !Array.isArray(data)) return false
  return data.every(item =>
    typeof item.name === 'string' &&
    typeof item.value === 'number' &&
    !isNaN(item.value)
  )
}

// 错误边界
const handleChartError = (error: Error) => {
  console.error('Chart error:', error)
  showErrorMessage('图表加载失败，请刷新页面重试')
  // 显示备用内容或空状态
}
```

### 2. 网络异常处理
```typescript
// 重试机制
const fetchWithRetry = async (url: string, retries: number = 3) => {
  for (let i = 0; i < retries; i++) {
    try {
      return await fetch(url)
    } catch (error) {
      if (i === retries - 1) throw error
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)))
    }
  }
}
```

---

## API 接口对接

### 数据获取接口
```typescript
// 首页统计数据
interface HomeStatsAPI {
  getDailySalesAmount(): Promise<DailySalesAmountData>
  getDailySalesQuantity(): Promise<DailySalesQuantityData>
  getRegionSalesDistribution(): Promise<RegionSalesData>
  getTopGamesByAmount(): Promise<GameRankingData[]>
  getTopGamesByQuantity(): Promise<GameRankingData[]>
}

// 销售页面数据
interface SalesStatsAPI {
  getSalesOverview(): Promise<SalesStatsData>
  getRegionDistribution(): Promise<RegionDistributionData>
  getCurrencyDistribution(): Promise<CurrencyDistributionData>
  getOrderStatusDistribution(): Promise<OrderStatusData>
  getDailySalesTrend(): Promise<DailySalesBarData>
  getHourlyTrend(): Promise<HourlyTrendData>
}

// 游戏统计数据
interface GameStatsAPI {
  getGameOverview(): Promise<GameStatsData>
  getGameStatistics(gameId: string): Promise<GameStatistics>
  getGameSalesDistribution(gameId: string): Promise<{
    regionVolume: RegionVolumeData
    currencyVolume: CurrencyVolumeData
    regionAmount: RegionAmountData
    currencyAmount: CurrencyAmountData
    dailySales: DailySalesBarData
    hourlySales: HourlySalesLineData
  }>
}
```

### 数据缓存策略
```typescript
// 缓存配置
const cacheConfig = {
  homeStats: { ttl: 5 * 60 * 1000 },      // 5分钟
  salesStats: { ttl: 10 * 60 * 1000 },    // 10分钟
  gameStats: { ttl: 15 * 60 * 1000 },     // 15分钟
  rankings: { ttl: 30 * 60 * 1000 }       // 30分钟
}
```

---

## 图表交互功能

### 1. 点击事件
```typescript
// 饼图点击事件
interface PieChartClickEvent {
  name: string      // 数据项名称
  value: number     // 数据项值
  percent: number   // 百分比
}

// 柱状图点击事件
interface BarChartClickEvent {
  name: string      // X轴标签
  value: number     // Y轴值
  dataIndex: number // 数据索引
}

// 排行榜点击事件
interface RankingClickEvent {
  game: GameRankingData  // 游戏数据
}
```

### 2. 筛选功能
```typescript
// 图表筛选选项
interface ChartFilters {
  dateRange: [number, number]  // 时间范围
  region: string               // 地区筛选
  currency: string             // 货币筛选
  orderStatus: string          // 订单状态筛选
}
```

### 3. 导出功能
```typescript
// 数据导出格式
interface ExportOptions {
  format: 'csv' | 'excel' | 'pdf'  // 导出格式
  includeCharts: boolean           // 是否包含图表
  dateRange: [number, number]      // 导出数据时间范围
}
```

---

## 移动端适配

### 响应式断点
```typescript
const breakpoints = {
  mobile: '(max-width: 768px)',
  tablet: '(min-width: 769px) and (max-width: 1024px)',
  desktop: '(min-width: 1025px)'
}
```

### 移动端图表配置
```typescript
const mobileChartConfig = {
  grid: {
    left: '5%',
    right: '5%',
    top: '15%',
    bottom: '15%'
  },
  legend: {
    orient: 'horizontal',
    bottom: '5%',
    itemWidth: 15,
    itemHeight: 10,
    textStyle: { fontSize: 10 }
  },
  tooltip: {
    textStyle: { fontSize: 10 }
  }
}
```

---

## 性能优化建议

### 1. 数据量优化
- **大数据集**: 超过1000个数据点时使用数据采样
- **实时更新**: 使用防抖机制，避免频繁更新
- **懒加载**: 非可视区域的图表延迟加载

### 2. 渲染优化
```typescript
// 图表懒加载
const useChartLazyLoad = () => {
  const { stop } = useIntersectionObserver(
    chartRef,
    ([{ isIntersecting }]) => {
      if (isIntersecting) {
        initChart()
        stop()
      }
    }
  )
}

// 数据采样
const sampleData = (data: number[], maxPoints: number = 100) => {
  if (data.length <= maxPoints) return data
  const step = Math.ceil(data.length / maxPoints)
  return data.filter((_, index) => index % step === 0)
}
```

### 3. 内存管理
```typescript
// 图表销毁
onBeforeUnmount(() => {
  chart?.dispose()
  chart = null
})

// 数据清理
const clearChartData = () => {
  chartData.value = null
  chartOptions.value = null
}
```

---

## 错误处理

### 1. 数据异常处理
```typescript
// 数据验证
const validateChartData = (data: any): boolean => {
  if (!data || !Array.isArray(data)) return false
  return data.every(item =>
    typeof item.name === 'string' &&
    typeof item.value === 'number' &&
    !isNaN(item.value)
  )
}

// 错误边界
const handleChartError = (error: Error) => {
  console.error('Chart error:', error)
  showErrorMessage('图表加载失败，请刷新页面重试')
  // 显示备用内容或空状态
}
```

### 2. 网络异常处理
```typescript
// 重试机制
const fetchWithRetry = async (url: string, retries: number = 3) => {
  for (let i = 0; i < retries; i++) {
    try {
      return await fetch(url)
    } catch (error) {
      if (i === retries - 1) throw error
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)))
    }
  }
}
```

---

## 注意事项

1. **数据格式**: 所有金额数据以美元为单位，数量数据为整数
2. **日期格式**: 日期统一使用 "MM/DD" 或 "MM-DD" 格式
3. **颜色一致性**: 相同类型的数据在不同图表中使用相同颜色
4. **性能优化**: 大数据量时建议使用数据采样或分页加载
5. **错误处理**: 所有图表都包含错误处理机制，避免数据异常导致页面崩溃
6. **国际化**: 所有文本内容支持中英文切换
7. **响应式**: 图表在不同设备上都能正常显示和交互
8. **可访问性**: 图表支持键盘导航和屏幕阅读器
6. **国际化**: 所有文本内容支持中英文切换
7. **响应式**: 图表在不同设备上都能正常显示和交互
8. **可访问性**: 图表支持键盘导航和屏幕阅读器

---

## 开发指南

### 1. 新增图表组件
```typescript
// 1. 创建图表组件
// components/charts/MyChart.vue

// 2. 定义数据接口
interface MyChartData {
  // 数据结构定义
}

// 3. 使用 useEcharts Hook
const { domRef, updateOptions } = useEcharts(() => ({
  // ECharts 配置
}))

// 4. 添加国际化支持
// locales/langs/zh-cn.ts
// locales/langs/en-us.ts

// 5. 添加响应式支持
const isMobile = computed(() => appStore.isMobile)

// 6. 更新本文档
```

### 2. 图表调试
```typescript
// 开发环境调试
if (process.env.NODE_ENV === 'development') {
  // 添加图表实例到全局对象
  (window as any).charts = charts

  // 输出图表配置
  console.log('Chart options:', chartOptions)

  // 输出图表数据
  console.log('Chart data:', chartData)
}
```

### 3. 单元测试
```typescript
// 图表组件测试示例
describe('MyChart', () => {
  it('should render chart with correct data', () => {
    const wrapper = mount(MyChart, {
      props: { data: mockData }
    })

    expect(wrapper.find('.chart-container')).toBeTruthy()
    expect(wrapper.vm.chartData).toEqual(mockData)
  })

  it('should handle empty data gracefully', () => {
    const wrapper = mount(MyChart, {
      props: { data: [] }
    })

    expect(wrapper.find('.empty-state')).toBeTruthy()
  })
})
```

---

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本，包含所有基础图表组件
- 支持中英文国际化
- 实现响应式设计

### v1.1.0 (2024-01-15)
- 新增游戏统计抽屉组件
- 优化图表性能和内存使用
- 添加数据导出功能

### v1.2.0 (2024-02-01)
- 新增销售综合图表页面
- 实现图表交互功能
- 添加移动端优化

### v1.3.0 (2024-12-30)
- 完善数据结构文档
- 添加API接口对接说明
- 新增开发指南和最佳实践

---

## 图表文件映射表

| 图表名称 | 文件路径 | 图表类型 | 主要数据字段 |
|---------|----------|----------|-------------|
| 每日销售金额趋势图 | `webui/src/views/home/<USER>/daily-sales-amount-chart.vue` | 折线图 | `dates[]`, `amounts[]` |
| 每日销售数量趋势图 | `webui/src/views/home/<USER>/daily-sales-quantity-chart.vue` | 折线图 | `dates[]`, `quantities[]` |
| 每日订单销售双轴图 | `webui/src/views/home/<USER>/line-chart.vue` | 双轴折线图 | `dates[]`, `amounts[]`, `quantities[]` |
| 地区销售分布饼图 | `webui/src/views/home/<USER>/pie-chart.vue` | 环形饼图 | `regions[]`, `values[]` |
| 地区销售金额饼图 | `webui/src/views/home/<USER>/regional-sales-amount-chart.vue` | 环形饼图 | `regions[]`, `amounts[]` |
| 地区销售数量饼图 | `webui/src/views/home/<USER>/regional-sales-quantity-chart.vue` | 环形饼图 | `regions[]`, `quantities[]` |
| 销售金额排行榜 | `webui/src/views/home/<USER>/top-sales-amount-ranking.vue` | 列表组件 | `GameRankingData[]` |
| 销售数量排行榜 | `webui/src/views/home/<USER>/top-sales-quantity-ranking.vue` | 列表组件 | `GameRankingData[]` |
| 销售综合图表 | `webui/src/views/sales/modules/sales-charts.vue` | 多图表组合 | 多种数据结构 |
| 销售统计卡片 | `webui/src/views/sales/modules/sales-stats.vue` | 统计卡片 | `SalesStatsData` |
| 游戏统计卡片 | `webui/src/views/game/modules/game-stats.vue` | 统计卡片 | `GameStatsData` |
| 游戏统计抽屉 | `webui/src/views/game/modules/game-stats-drawer.vue` | 多图表组合 | `GameStatistics` |

---

## 开发指南

### 1. 新增图表组件
```typescript
// 1. 创建图表组件
// components/charts/MyChart.vue

// 2. 定义数据接口
interface MyChartData {
  // 数据结构定义
}

// 3. 使用 useEcharts Hook
const { domRef, updateOptions } = useEcharts(() => ({
  // ECharts 配置
}))

// 4. 添加国际化支持
// locales/langs/zh-cn.ts
// locales/langs/en-us.ts

// 5. 添加响应式支持
const isMobile = computed(() => appStore.isMobile)

// 6. 更新本文档
```

### 2. 图表调试
```typescript
// 开发环境调试
if (process.env.NODE_ENV === 'development') {
  // 添加图表实例到全局对象
  (window as any).charts = charts

  // 输出图表配置
  console.log('Chart options:', chartOptions)

  // 输出图表数据
  console.log('Chart data:', chartData)
}
```

### 3. 单元测试
```typescript
// 图表组件测试示例
describe('MyChart', () => {
  it('should render chart with correct data', () => {
    const wrapper = mount(MyChart, {
      props: { data: mockData }
    })

    expect(wrapper.find('.chart-container')).toBeTruthy()
    expect(wrapper.vm.chartData).toEqual(mockData)
  })

  it('should handle empty data gracefully', () => {
    const wrapper = mount(MyChart, {
      props: { data: [] }
    })

    expect(wrapper.find('.empty-state')).toBeTruthy()
  })
})
```

---

*最后更新时间: 2024-12-30*
*文档版本: v1.3.0*

