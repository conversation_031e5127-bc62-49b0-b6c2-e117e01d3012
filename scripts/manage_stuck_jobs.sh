#!/bin/bash

# Queue Management Script for Stuck Jobs
# Usage: ./scripts/manage_stuck_jobs.sh [action] [options]

set -e

# Default environment
ENV=${ENV:-development}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if .env file exists
check_env_file() {
    if [ ! -f ".env.${ENV}" ]; then
        print_error ".env.${ENV} file not found!"
        exit 1
    fi
}

# Function to run artisan command
run_artisan() {
    local cmd="$1"
    print_info "Running: php artisan $cmd"
    HOST_UID=$(id -u) HOST_GID=$(id -g) docker compose run --rm app bash -c "export APP_ENV=${ENV} && php artisan $cmd --env=.env.${ENV}"
}

# Function to show usage
show_usage() {
    echo "Queue Management Script for Stuck Jobs"
    echo ""
    echo "Usage: $0 [action] [options]"
    echo ""
    echo "Actions:"
    echo "  list                    List all stuck jobs"
    echo "  delete <job_id>         Delete a specific job by ID"
    echo "  delete-sync <job_id>    Delete a stuck sync job by progress tracking ID (from API)"
    echo "  delete-all              Delete all stuck jobs"
    echo "  delete-old [hours]      Delete jobs stuck for more than X hours (default: 2)"
    echo "  status                  Show queue status"
    echo "  help                    Show this help message"
    echo ""
    echo "Options:"
    echo "  --env=ENV              Environment to use (default: development)"
    echo "  --force                Force deletion without confirmation"
    echo "  --cleanup-progress     Also cleanup progress tracking data"
    echo "  --queue=QUEUE          Specific queue to check"
    echo ""
    echo "Examples:"
    echo "  $0 list"
    echo "  $0 delete 123"
    echo "  $0 delete-sync 4d75e64e-2993-4f6b-b8fa-3b2835d46e24"
    echo "  $0 delete-all --force"
    echo "  $0 delete-old 4 --cleanup-progress"
    echo "  $0 status"
    echo ""
}

# Function to show queue status
show_queue_status() {
    print_info "Checking queue status for environment: ${ENV}"
    check_env_file
    
    run_artisan "tinker --execute=\"
        echo 'Queue Status Report:' . PHP_EOL;
        echo '===================' . PHP_EOL;
        echo 'Jobs in queue: ' . DB::table('jobs')->count() . PHP_EOL;
        echo 'Failed jobs: ' . DB::table('failed_jobs')->count() . PHP_EOL;
        echo 'Reserved jobs: ' . DB::table('jobs')->whereNotNull('reserved_at')->count() . PHP_EOL;
        echo 'Stuck jobs (reserved > 30min): ' . DB::table('jobs')->whereNotNull('reserved_at')->where('reserved_at', '<', now()->subMinutes(30)->timestamp)->count() . PHP_EOL;
        echo PHP_EOL;
        echo 'Sync Jobs Status:' . PHP_EOL;
        echo 'Pending sync logs: ' . DB::table('sync_logs')->where('status', 'pending')->count() . PHP_EOL;
        echo 'Processing sync logs: ' . DB::table('sync_logs')->where('status', 'processing')->count() . PHP_EOL;
    \""
}

# Function to list stuck jobs
list_stuck_jobs() {
    print_info "Listing stuck jobs for environment: ${ENV}"
    check_env_file
    
    local extra_options=""
    
    # Parse additional options
    for arg in "$@"; do
        case $arg in
            --queue=*)
                extra_options="$extra_options $arg"
                ;;
        esac
    done
    
    run_artisan "queue:manage-stuck-jobs list $extra_options"
}

# Function to delete specific job
delete_job() {
    local job_id="$1"

    if [ -z "$job_id" ]; then
        print_error "Job ID is required!"
        echo "Usage: $0 delete <job_id> [--force] [--cleanup-progress]"
        exit 1
    fi

    print_info "Deleting job ID: ${job_id} for environment: ${ENV}"
    check_env_file

    local extra_options=""

    # Parse additional options
    for arg in "${@:2}"; do
        case $arg in
            --force|--cleanup-progress)
                extra_options="$extra_options $arg"
                ;;
        esac
    done

    run_artisan "queue:delete-stuck-job $job_id $extra_options"
}

# Function to delete sync job by progress tracking ID
delete_sync_job() {
    local job_id="$1"

    if [ -z "$job_id" ]; then
        print_error "Progress tracking job ID is required!"
        echo "Usage: $0 delete-sync <progress_job_id> [--force] [--cleanup-all]"
        exit 1
    fi

    print_info "Deleting sync job with progress ID: ${job_id} for environment: ${ENV}"
    check_env_file

    local extra_options=""

    # Parse additional options
    for arg in "${@:2}"; do
        case $arg in
            --force|--cleanup-all)
                extra_options="$extra_options $arg"
                ;;
        esac
    done

    run_artisan "sync:delete-stuck-job $job_id $extra_options"
}

# Function to delete all stuck jobs
delete_all_jobs() {
    print_warning "This will delete ALL stuck jobs for environment: ${ENV}"
    check_env_file
    
    local extra_options=""
    
    # Parse additional options
    for arg in "$@"; do
        case $arg in
            --force|--cleanup-progress|--queue=*)
                extra_options="$extra_options $arg"
                ;;
        esac
    done
    
    run_artisan "queue:manage-stuck-jobs delete-all $extra_options"
}

# Function to delete old stuck jobs
delete_old_jobs() {
    local hours="${1:-2}"
    
    print_info "Deleting jobs stuck for more than ${hours} hours for environment: ${ENV}"
    check_env_file
    
    local extra_options="--hours=$hours"
    
    # Parse additional options
    for arg in "${@:2}"; do
        case $arg in
            --force|--cleanup-progress|--queue=*)
                extra_options="$extra_options $arg"
                ;;
        esac
    done
    
    run_artisan "queue:manage-stuck-jobs delete-old $extra_options"
}

# Main script logic
main() {
    # Parse environment option first
    for arg in "$@"; do
        case $arg in
            --env=*)
                ENV="${arg#*=}"
                ;;
        esac
    done
    
    # Check if no arguments provided
    if [ $# -eq 0 ]; then
        show_usage
        exit 1
    fi
    
    local action="$1"
    shift
    
    case $action in
        list)
            list_stuck_jobs "$@"
            ;;
        delete)
            delete_job "$@"
            ;;
        delete-sync)
            delete_sync_job "$@"
            ;;
        delete-all)
            delete_all_jobs "$@"
            ;;
        delete-old)
            delete_old_jobs "$@"
            ;;
        status)
            show_queue_status
            ;;
        help|--help|-h)
            show_usage
            ;;
        *)
            print_error "Unknown action: $action"
            show_usage
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
