# Visitor 角色实现方案

## 概述

本文档详细描述了如何在现有系统中实现 `visitor` 角色功能，使用户能够访问特定产品的报表数据，即使该用户不属于产品所在的组织。

## 当前系统分析

### 现有权限系统
- **系统级角色**: `root`, `admin` (guard: system)
- **组织级角色**: `owner`, `member` (guard: api, 绑定到特定组织)
- **权限检查**: 基于用户是否属于某个组织

### 现有数据流程
1. 用户请求报表时提供 `organisation_id`
2. 系统验证用户是否属于该组织
3. 通过组织的 `code` 获取该组织的所有产品ID
4. 使用产品ID构造查询条件获取报表数据

### 问题分析
1. **权限校验问题**: `ReportPolicy` 只检查组织成员身份，无法处理产品级权限
2. **数据获取问题**: 当前逻辑基于组织获取所有产品，无法支持单个产品访问

## 解决方案设计

### 核心思路
1. **扩展角色系统**: 添加产品级 `visitor` 角色
2. **增强权限检查**: 支持产品级权限验证
3. **优化数据获取**: 支持基于产品权限的数据过滤
4. **保持向后兼容**: 不影响现有组织级权限逻辑

### 架构设计原则
- **单一职责**: 每个组件只负责自己的职责
- **开闭原则**: 对扩展开放，对修改封闭
- **可维护性**: 代码结构清晰，易于理解和维护
- **性能优化**: 避免N+1查询，使用高效的数据库查询

## 详细实现步骤

### 第一步：数据库结构调整

#### 1.1 创建产品访问权限表
```sql
-- 创建用户产品访问权限表
CREATE TABLE user_product_access (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    product_id BIGINT UNSIGNED NOT NULL,
    role VARCHAR(50) NOT NULL DEFAULT 'visitor',
    granted_by BIGINT UNSIGNED NULL,
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE SET NULL,
    
    UNIQUE KEY unique_user_product (user_id, product_id),
    INDEX idx_user_id (user_id),
    INDEX idx_product_id (product_id),
    INDEX idx_expires_at (expires_at)
);
```

#### 1.2 创建迁移文件
```php
// database/migrations/xxxx_create_user_product_access_table.php
```

### 第二步：模型层扩展

#### 2.1 创建 UserProductAccess 模型
```php
// app/Models/UserProductAccess.php
```

#### 2.2 扩展 User 模型
添加产品访问权限相关方法：
- `getAccessibleProductIds()`: 获取用户可访问的产品ID
- `hasProductAccess()`: 检查用户是否有特定产品访问权限
- `canAccessReportsForProduct()`: 检查用户是否可以访问特定产品的报表

#### 2.3 扩展 Product 模型
添加访问权限相关方法：
- `visitors()`: 获取有访问权限的用户
- `grantAccessToUser()`: 为用户授予访问权限

### 第三步：权限系统增强

#### 3.1 扩展 ReportPolicy
增加产品级权限检查方法：
- `viewForProduct()`: 检查用户是否可以访问特定产品的报表
- `viewForOrganisationOrProducts()`: 综合检查组织和产品权限

#### 3.2 创建 ProductAccessService
专门处理产品访问权限的服务类：
- `grantProductAccess()`: 授予产品访问权限
- `revokeProductAccess()`: 撤销产品访问权限
- `getUserAccessibleProducts()`: 获取用户可访问的产品列表

### 第四步：控制器层调整

#### 4.1 修改 ReportController
- 保持现有API接口不变
- 增强权限检查逻辑，支持产品级权限
- 优化数据获取逻辑

#### 4.2 调整权限检查流程
```php
// 新的权限检查逻辑
$this->authorize('viewForOrganisationOrProducts', ['report', $organisationId]);
```

### 第五步：请求处理层优化

#### 5.1 扩展 ReportFilterRequest
- 增加产品权限验证
- 优化产品ID获取逻辑
- 支持混合权限模式（组织+产品）

#### 5.2 数据处理逻辑
```php
// 获取用户可访问的产品ID（组织产品 + visitor产品）
$accessibleProductIds = $this->getAccessibleProductIds($user, $organisationId);
```

### 第六步：服务层增强

#### 6.1 扩展 OrganisationService
添加混合权限支持：
- `getAccessibleProductIds()`: 获取用户可访问的产品ID（组织+产品权限）

#### 6.2 优化 ReportService
- 支持基于产品权限的数据过滤
- 保持现有缓存机制

### 第七步：API接口扩展

#### 7.1 产品访问权限管理接口
```php
// 新增API端点
POST   /api/v1/products/{product}/access     // 授予产品访问权限
DELETE /api/v1/products/{product}/access     // 撤销产品访问权限
GET    /api/v1/products/accessible           // 获取可访问的产品列表
```

#### 7.2 权限管理控制器
```php
// app/Http/Controllers/Api/V1/ProductAccessController.php
```

### 第八步：测试覆盖

#### 8.1 单元测试
- UserProductAccess 模型测试
- User 模型产品权限方法测试
- ReportPolicy 产品权限测试
- ProductAccessService 测试

#### 8.2 功能测试
- 产品访问权限授予/撤销测试
- 报表访问权限测试
- 混合权限场景测试

#### 8.3 集成测试
- 完整的报表访问流程测试
- 权限边界测试
- 性能测试

## 实现优势

### 1. 向后兼容
- 现有组织级权限逻辑完全保留
- API接口保持不变
- 现有用户权限不受影响

### 2. 灵活性
- 支持细粒度的产品级权限控制
- 可以设置权限过期时间
- 支持权限审计追踪

### 3. 性能优化
- 使用索引优化查询性能
- 避免N+1查询问题
- 保持现有缓存机制

### 4. 可维护性
- 代码结构清晰，职责分离
- 遵循Laravel最佳实践
- 完整的测试覆盖

## 风险评估与缓解

### 1. 数据一致性风险
**风险**: 产品删除时权限数据可能残留
**缓解**: 使用外键约束，级联删除

### 2. 性能风险
**风险**: 复杂权限查询可能影响性能
**缓解**: 合理使用索引，优化查询逻辑

### 3. 安全风险
**风险**: 权限提升攻击
**缓解**: 严格的权限验证，完整的审计日志

## 部署计划

### 阶段1：基础设施（1-2天）
- 数据库迁移
- 基础模型创建

### 阶段2：核心功能（3-4天）
- 权限系统扩展
- 服务层实现

### 阶段3：接口集成（2-3天）
- 控制器调整
- API接口实现

### 阶段4：测试验证（2-3天）
- 单元测试
- 集成测试
- 性能测试

### 阶段5：部署上线（1天）
- 生产环境部署
- 监控配置

## 总结

本方案通过扩展现有权限系统，在保持向后兼容的前提下，实现了产品级的访问权限控制。方案设计遵循了软件工程的最佳实践，确保了系统的可维护性、可扩展性和性能。通过分阶段的实施计划，可以有效控制实施风险，确保功能的稳定上线。
