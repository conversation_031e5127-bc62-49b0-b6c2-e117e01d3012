# Visitor 角色实现方案

## 概述

本文档详细描述了如何在现有系统中实现 `visitor` 角色功能，使用户能够访问特定产品的报表数据，即使该用户不属于产品所在的组织。

## 当前系统分析

### 现有权限系统
- **系统级角色**: `root`, `admin` (guard: system)
- **组织级角色**: `owner`, `member` (guard: api, 绑定到特定组织)
- **权限检查**: 基于用户是否属于某个组织

### 现有数据流程
1. 用户请求报表时提供 `organisation_id`
2. 系统验证用户是否属于该组织
3. 通过组织的 `code` 获取该组织的所有产品ID
4. 使用产品ID构造查询条件获取报表数据

### 问题分析
1. **权限校验问题**: `ReportPolicy` 只检查组织成员身份，无法处理产品级权限
2. **数据获取问题**: 当前逻辑基于组织获取所有产品，无法支持单个产品访问

## 解决方案设计（优化版 - 使用 Spatie Permission）

### 核心思路
1. **利用 Spatie Permission 系统**: 使用现有的 `permissions` 和 `model_has_permissions` 表
2. **创建产品级权限**: 为每个产品创建 `view-product-{product_id}` 权限
3. **增强权限检查**: 在现有组织权限基础上增加产品权限检查
4. **保持向后兼容**: 完全不影响现有组织级权限逻辑

### 架构设计原则
- **复用现有系统**: 最大化利用 Spatie Permission 的成熟功能
- **一致性**: 与现有权限系统保持完全一致
- **性能优化**: 利用 Spatie 的缓存和查询优化
- **可维护性**: 减少自定义逻辑，降低维护成本

## 详细实现步骤

### 第一步：权限系统扩展

#### 1.1 创建产品权限命名规范
```php
// 产品权限命名规范：view-product-{product_id}
// 例如：view-product-123, view-product-456
```

#### 1.2 扩展权限创建逻辑
```php
// 当产品创建时，自动创建对应的权限
// 当产品删除时，自动删除对应的权限
```

### 第二步：模型层扩展

#### 2.1 扩展 User 模型
添加产品权限相关方法：
```php
/**
 * 获取用户可访问的产品ID（组织产品 + 权限产品）
 */
public function getAccessibleProductIds(?int $organisationId = null): array

/**
 * 检查用户是否有特定产品的访问权限
 */
public function hasProductPermission(int $productId): bool

/**
 * 检查用户是否可以访问特定产品的报表
 */
public function canAccessReportsForProduct(int $productId): bool
```

#### 2.2 扩展 Product 模型
添加权限相关方法：
```php
/**
 * 获取产品的权限名称
 */
public function getPermissionName(): string

/**
 * 为用户授予产品访问权限
 */
public function grantAccessToUser(User $user): void

/**
 * 撤销用户的产品访问权限
 */
public function revokeAccessFromUser(User $user): void
```

### 第三步：权限系统增强

#### 3.1 扩展 ReportPolicy
增加产品级权限检查方法：
```php
/**
 * 检查用户是否可以访问特定产品的报表
 */
public function viewForProduct(User $user, int $productId): bool

/**
 * 综合检查组织和产品权限
 */
public function viewForOrganisationOrProducts(User $user, ?int $organisationId): bool
```

#### 3.2 创建 ProductPermissionService
专门处理产品权限的服务类：
```php
/**
 * 为产品创建权限
 */
public function createProductPermission(Product $product): Permission

/**
 * 授予用户产品访问权限
 */
public function grantProductAccess(User $user, Product $product): void

/**
 * 撤销用户产品访问权限
 */
public function revokeProductAccess(User $user, Product $product): void

/**
 * 获取用户可访问的产品列表
 */
public function getUserAccessibleProducts(User $user): Collection
```

### 第四步：控制器层调整

#### 4.1 修改 ReportController
保持现有API接口不变，只需调整权限检查：
```php
// 新的权限检查逻辑，支持组织和产品权限
$this->authorize('viewForOrganisationOrProducts', ['report', $organisationId]);
```

### 第五步：请求处理层优化

#### 5.1 扩展 ReportFilterRequest
修改 `getProcessedData()` 方法：
```php
public function getProcessedData(): array
{
    $data = $this->validated();

    // 处理日期格式
    // ...existing date processing code...

    // 获取用户可访问的产品ID（组织产品 + 权限产品）
    $user = $this->user();
    $organisationId = $this->input('organisation_id');
    $data['product_ids'] = $user->getAccessibleProductIds($organisationId);

    return $data;
}
```

### 第六步：服务层增强

#### 6.1 扩展现有服务
- **OrganisationService**: 保持现有逻辑不变
- **ReportService**: 无需修改，继续使用 `product_ids` 进行过滤
- **新增 ProductPermissionService**: 专门处理产品权限逻辑

### 第七步：API接口扩展

#### 7.1 产品权限管理接口
```php
// 新增API端点
POST   /api/v1/products/{product}/permissions  // 授予产品访问权限
DELETE /api/v1/products/{product}/permissions  // 撤销产品访问权限
GET    /api/v1/products/accessible             // 获取可访问的产品列表
GET    /api/v1/users/{user}/product-permissions // 获取用户的产品权限列表
```

#### 7.2 权限管理控制器
```php
// app/Http/Controllers/Api/V1/ProductPermissionController.php
```

### 第八步：测试覆盖

#### 8.1 单元测试
- User 模型产品权限方法测试
- Product 模型权限方法测试
- ReportPolicy 产品权限测试
- ProductPermissionService 测试

#### 8.2 功能测试
- 产品权限授予/撤销测试
- 报表访问权限测试（组织+产品混合场景）
- 权限边界测试

#### 8.3 集成测试
- 完整的报表访问流程测试
- Spatie Permission 集成测试
- 性能测试（利用 Spatie 缓存）

## 使用 Spatie Permission 的优势

### 1. 系统一致性
- 完全复用现有权限系统架构
- 与组织级权限使用相同的底层机制
- 统一的权限检查和缓存策略

### 2. 成熟稳定
- 利用 Spatie Permission 的成熟功能
- 自动权限缓存和性能优化
- 经过大量项目验证的稳定性

### 3. 开发效率
- 无需创建额外的数据表
- 减少自定义权限逻辑
- 利用现有的权限管理基础设施

### 4. 可扩展性
- 未来可以轻松扩展更多产品权限类型
- 支持权限继承和复杂权限组合
- 与现有角色系统无缝集成

### 5. 向后兼容
- 现有组织级权限逻辑完全保留
- API接口保持不变
- 现有用户权限不受影响

## 风险评估与缓解

### 1. 权限数据一致性
**风险**: 产品删除时权限数据可能残留
**缓解**: 利用 Spatie Permission 的自动清理机制，在产品删除时自动删除相关权限

### 2. 权限命名冲突
**风险**: 产品权限命名可能与其他权限冲突
**缓解**: 使用明确的命名规范 `view-product-{product_id}`，确保唯一性

### 3. 性能影响
**风险**: 增加权限检查可能影响性能
**缓解**: 利用 Spatie Permission 的内置缓存机制，性能影响最小

### 4. 权限管理复杂性
**风险**: 产品权限管理可能变得复杂
**缓解**: 提供专门的管理接口和服务类，封装复杂逻辑

## 部署计划

### 阶段1：权限基础设施（1天）
- 创建 ProductPermissionService
- 扩展 Product 模型的权限方法
- 为现有产品创建权限（可选）

### 阶段2：权限检查增强（1-2天）
- 扩展 User 模型的产品权限方法
- 修改 ReportPolicy 支持产品权限
- 调整 ReportFilterRequest 的数据处理逻辑

### 阶段3：API接口实现（1-2天）
- 创建 ProductPermissionController
- 实现权限管理接口
- 添加权限管理的请求验证

### 阶段4：测试验证（2天）
- 单元测试（模型方法、服务类）
- 功能测试（权限授予、报表访问）
- 集成测试（完整流程）

### 阶段5：部署上线（半天）
- 生产环境部署
- 权限数据初始化（如需要）

## 总结

**优化后的方案优势：**

1. **最大化复用**: 完全利用现有的 Spatie Permission 系统
2. **最小化改动**: 只需扩展现有逻辑，无需创建新表
3. **保持一致性**: 与现有权限系统架构完全一致
4. **降低复杂度**: 减少自定义逻辑，提高可维护性
5. **性能优化**: 利用 Spatie 的缓存和查询优化

这个方案通过巧妙地利用 Spatie Permission 的 `model_has_permissions` 表，实现了产品级权限控制，同时保持了系统的简洁性和一致性。相比创建新表的方案，这个方案更加优雅和高效。
