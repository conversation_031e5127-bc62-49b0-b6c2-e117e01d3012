<?php

declare(strict_types=1);

return [
    // Common messages
    'success' => '成功',
    'error_occurred' => '发生错误',
    'validation_failed' => '验证失败',
    'resource_not_found' => '资源未找到',
    'unauthorized' => '未授权',

    // Authentication messages
    'auth' => [
        'login_success' => '登录成功',
        'logout_success' => '退出登录成功',
        'all_tokens_revoked' => '所有令牌已撤销',
        'invalid_credentials' => '提供的凭据不正确。',
    ],

    // User messages
    'user' => [
        'list_retrieved' => '获取用户列表成功',
        'created' => '用户创建成功',
        'registered' => '用户注册成功',
        'details_retrieved' => '获取用户详情成功',
        'updated' => '用户更新成功',
        'suspended' => '用户暂停成功',
        'activated' => '用户激活成功',
        'not_found' => '用户不存在',
        'added_to_organisation' => '用户已添加到组织',
        'removed_from_organisation' => '用户已从组织中移除',
        'organisations_synced' => '用户组织关联已同步',
        'verification_code_sent' => '验证码已发送到您的邮箱',
        'verification_code_sent_success' => '验证码发送成功',
    ],

    // Organisation messages
    'organisation' => [
        'list_retrieved' => '获取组织列表成功',
        'created' => '组织创建成功',
        'details_retrieved' => '获取组织详情成功',
        'updated' => '组织更新成功',
        'suspended' => '组织暂停成功',
        'already_suspended' => '组织已经是暂停状态',
    ],

    // Role messages
    'role' => [
        'list_retrieved' => '获取角色列表成功',
        'created' => '角色创建成功',
        'retrieved' => '获取角色成功',
        'updated' => '角色更新成功',
        'deleted' => '角色删除成功',
        'assignment_success' => '角色赋予成功',
        'assignment_failed' => '角色赋予失败',
        'removal_success' => '角色移除成功',
        'removal_failed' => '角色移除失败',
        'transfer_success' => '所有者角色转移成功',
        'transfer_failed' => '所有者角色转移失败',
        'assignable_list_retrieved' => '可赋予角色列表获取成功',
        'user_roles_retrieved' => '用户角色信息获取成功',
    ],

    // Invitation messages
    'invitation' => [
        'list_retrieved' => '获取邀请列表成功',
        'created' => '邀请创建成功',
        'details_retrieved' => '获取邀请信息成功',
        'accepted' => '邀请接受成功，已加入组织',
        'expired' => '邀请链接已过期',
        'usage_limit_reached' => '邀请链接已达到使用次数限制',
        'email_not_allowed' => '您的邮箱地址不被允许接受此邀请',
        'processing_error' => '处理邀请时发生错误',
    ],

    // System messages
    'system' => [
        'api_running' => 'API 运行正常',
        'healthy' => '健康',
        'unhealthy' => '不健康',
        'database_connection_success' => '数据库连接成功',
        'database_connection_failed' => '数据库连接失败',
        'cache_check_completed' => '缓存检查完成',
        'cache_check_failed' => '缓存检查失败',
        'storage_check_completed' => '存储检查完成',
        'storage_check_failed' => '存储检查失败',
    ],

    // Report messages
    'reports' => [
        'sales_retrieved' => '销售报表获取成功',
        'volume_retrieved' => '销量报表获取成功',
        'refunds_retrieved' => '退款报表获取成功',
        'order_status_retrieved' => '订单状态报表获取成功',
        'export_started' => '报表导出开始成功',
        'export_completed' => '报表导出完成',
        'export_failed' => '报表导出失败',
        'no_data_found' => '未找到符合条件的数据',
        'insufficient_permissions' => '权限不足，无法访问此报表',
        'invalid_date_range' => '指定的日期范围无效',
        'data_processing_error' => '处理报表数据时发生错误',

        // Chart labels
        'chart_labels' => [
            'order_amount' => '订单金额',
            'order_quantity' => '订单数量',
            'refund_amount' => '退款金额',
            'refund_orders' => '退款订单数',
            'no_reason_provided' => '未提供原因',
        ],

        // Order status labels
        'order_status' => [
            'completed' => '已完成',
            'processing' => '处理中',
            'cancelled' => '已取消',
            'pending' => '待处理',
        ],

        // Payment status labels
        'payment_status' => [
            'completed' => '已支付',
            'pending' => '待支付',
            'failed' => '支付失败',
            'cancelled' => '已取消',
        ],

        // Region labels
        'regions' => [
            'US' => '北美',
            'CA' => '北美',
            'MX' => '北美',
            'GB' => '欧洲',
            'DE' => '欧洲',
            'FR' => '欧洲',
            'IT' => '欧洲',
            'ES' => '欧洲',
            'CN' => '亚洲',
            'JP' => '亚洲',
            'KR' => '亚洲',
            'IN' => '亚洲',
            'SG' => '亚洲',
            'other' => '其他',
        ],
    ],

    // Validation messages
    'validation' => [
        // Common validation messages
        'required' => '此字段为必填项',
        'string' => '此字段必须是字符串',
        'email' => '请输入有效的邮箱地址',
        'unique' => '此值已被使用',
        'integer' => '此字段必须是整数',
        'array' => '此字段必须是数组',
        'exists' => '所选值不存在',
        'date' => '此字段必须是有效日期',
        'after' => '此字段必须是当前时间之后的日期',
        'in' => '所选值无效',
        'size' => '此字段必须恰好为 :size 个字符',
        'min' => '此字段至少需要 :min 个字符',
        'max' => '此字段不能超过 :max 个字符',

        // User validation messages
        'user' => [
            'name_required' => '用户名称为必填项',
            'name_string' => '用户名称必须是字符串',
            'name_max' => '用户名称不能超过255个字符',
            'email_required' => '邮箱地址为必填项',
            'email_format' => '邮箱地址格式不正确',
            'email_unique' => '该邮箱地址已被使用',
            'email_max' => '邮箱地址不能超过255个字符',
            'password_required' => '密码为必填项',
            'password_string' => '密码必须是字符串',
            'password_min' => '密码至少需要8个字符',
            'password_max' => '密码不能超过255个字符',
            'organisation_ids_array' => '组织ID列表必须是数组',
            'organisation_ids_integer' => '组织ID必须是整数',
            'organisation_ids_exists' => '指定的组织不存在',
            'verification_code_required' => '验证码为必填项',
            'verification_code_string' => '验证码必须是字符串',
            'verification_code_size' => '验证码必须恰好为6个字符',
            'verification_code_invalid' => '验证码无效或已过期',
            'organisation_ids_not_allowed' => '访客注册不允许分配组织',
        ],

        // Organisation validation messages
        'organisation' => [
            'name_required' => '组织名称是必填项',
            'name_string' => '组织名称必须是字符串',
            'name_max' => '组织名称不能超过255个字符',
            'code_required' => '组织代码是必填项',
            'code_string' => '组织代码必须是字符串',
            'code_max' => '组织代码不能超过50个字符',
            'code_unique' => '组织代码已存在',
            'details_array' => '详细信息必须是数组格式',
            'remarks_string' => '备注必须是字符串',
            'remarks_max' => '备注不能超过1000个字符',
            'status_string' => '状态必须是字符串',
            'status_in' => '状态必须是pending、active或suspended之一',
        ],

        // Invitation validation messages
        'invitation' => [
            'model_type_required' => '模型类型为必填项',
            'model_type_in' => '模型类型必须是有效的组织',
            'model_id_required' => '组织ID为必填项',
            'model_id_exists' => '组织不存在',
            'role_required' => '角色为必填项',
            'role_in' => '角色必须是owner或member之一',
            'expires_at_date' => '过期日期必须是有效日期',
            'expires_at_after' => '过期日期必须是将来的时间',
            'max_uses_integer' => '最大使用次数必须是数字',
            'max_uses_min' => '最大使用次数至少为1',
            'max_uses_max' => '最大使用次数不能超过100',
            'email_restriction_email' => '邮箱限制必须是有效的邮箱地址',
        ],

        // Report validation messages
        'reports' => [
            'start_date_required' => '开始日期为必填项',
            'end_date_required' => '结束日期为必填项',
            'invalid_date_range' => '开始日期必须早于或等于结束日期',
            'end_date_future' => '结束日期不能是未来时间',
            'invalid_country_code' => '国家代码必须是2个字符',
            'invalid_group_by' => '分组方式必须是以下之一：day, week, month, quarter, year',
            'invalid_state' => '无效的订单状态',
            'invalid_payment_state' => '无效的支付状态',
            'invalid_organisation' => '组织不存在',
            'organisation_ids_required' => '组织ID为必填项',
            'organisation_ids_must_be_array' => '组织ID必须是数组',
            'organisation_ids_min_one' => '至少需要一个组织ID',
            'invalid_currency_code' => '货币代码必须是3个字符',
            'invalid_timezone' => '无效的时区',
            'report_type_required' => '报表类型为必填项',
            'invalid_report_type' => '报表类型必须是以下之一：sales, volume, refunds, order_status',
            'format_required' => '导出格式为必填项',
            'invalid_format' => '导出格式必须是以下之一：xlsx, csv, pdf',
            'invalid_filename' => '文件名包含无效字符',
            'filename_too_long' => '文件名不能超过255个字符',
            'max_records_exceeded' => '超过最大记录数限制（50,000）',
            'min_records_required' => '至少需要1条记录',
            'invalid_email' => '无效的邮箱地址',
        ],

        // Invitation attributes
        'invitation_attributes' => [
            'model_type' => '模型类型',
            'model_id' => '组织ID',
            'role' => '角色',
            'expires_at' => '过期日期',
            'max_uses' => '最大使用次数',
            'email_restriction' => '邮箱限制',
        ],
    ],
];
