<?php

return [
    'product_sync_started' => '产品同步已开始',
    'product_sync_completed' => '产品同步成功完成',
    'product_sync_failed' => '产品同步失败',
    'order_sync_started' => '订单同步已开始',
    'order_sync_completed' => '订单同步成功完成',
    'order_sync_failed' => '订单同步失败',
    'batch_processing' => '正在处理批次 :batch_id',
    'records_processed' => '已处理 :count 条记录',
    'sync_status' => [
        'pending' => '等待中',
        'processing' => '处理中',
        'completed' => '已完成',
        'failed' => '失败',
    ],
    'record_status' => [
        'pending' => '等待中',
        'success' => '成功',
        'failed' => '失败',
        'skipped' => '跳过',
    ],
    'logs_retrieved' => '同步日志获取成功',
    'log_not_found' => '同步日志未找到',
    'log_details_retrieved' => '同步日志详情获取成功',
    'sync_triggered' => '同步触发成功',
    'sync_trigger_failed' => '同步触发失败',
    'sync_queued' => '同步任务已加入队列',
    'sync_already_running' => '同步任务正在运行中',
    'retry_started' => '重试同步启动成功',
    'retry_failed' => '重试同步失败',
    'retry_queued' => '重试同步任务已加入队列',
    'retry_invalid_status' => '当前状态无法重试同步',
    'progress_retrieved' => '同步进度获取成功',
    'progress_not_found' => '同步进度未找到',
    'progress_retrieval_failed' => '同步进度获取失败',
    'active_jobs_retrieved' => '活跃同步任务获取成功',
    'active_jobs_retrieval_failed' => '活跃同步任务获取失败',
];
